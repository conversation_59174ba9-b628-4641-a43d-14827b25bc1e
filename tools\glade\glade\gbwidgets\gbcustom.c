/*  Gtk+ User Interface Builder
 *  Copyright (C) 1999  <PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this program; if not, write to the Free Software
 *  Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 */

#include <gtk/gtk.h>
#include "../gb.h"

/* This custom GbWidget provides a simple way of allowing external widgets
   to be used within an interface generated by <PERSON><PERSON>.
   The 'Create Function' property specifies a function which will be called
   to create the widget. 'String2', 'String2', 'Int1', 'Int2' are optional
   arguments which will be passed to the creation function along with the
   widget name. */

/* Include the 21x21 icon pixmap for this widget, to be used in the palette */
#include "../graphics/custom.xpm"

/* Include the background pixmap to use inside custom widgets. */
#include "../graphics/custom_bg.xpm"

/*
 * This is the GbWidget struct for this widget (see ../gbwidget.h).
 * It is initialized in the init() function at the end of this file
 */
static GbWidget gbwidget;

static gchar *CreationFunction = "Custom::creation_function";
static gchar *LastModTime = "Custom::last_modification_time";
static gchar *String1 = "Custom::string1";
static gchar *String2 = "Custom::string2";
static gchar *Int1 = "Custom::int1";
static gchar *Int2 = "Custom::int2";

static time_t gb_custom_get_last_mod_time (GtkWidget *widget);
static void gb_custom_set_last_mod_time (GtkWidget *widget,
					 time_t last_mod_time);
static void gb_custom_on_widget_realize (GtkWidget *widget, gpointer data);

/******
 * NOTE: To use these functions you need to uncomment them AND add a pointer
 * to the function in the GbWidget struct at the end of this file.
 ******/

/*
 * Creates a new GtkWidget of class Custom, performing any specialized
 * initialization needed for the widget to work correctly in this environment.
 * If a dialog box is used to initialize the widget, return NULL from this
 * function, and call data->callback with your new widget when it is done.
 * If the widget needs a special destroy handler, add a signal here.
 */
static GtkWidget*
gb_custom_new (GbWidgetNewData *data)
{
  GtkWidget *new_widget;

  new_widget = gtk_drawing_area_new ();
  gtk_object_set_data (GTK_OBJECT (new_widget), GLADE_CUSTOM_KEY, "True");

  gtk_signal_connect_after (GTK_OBJECT (new_widget), "realize",
			    GTK_SIGNAL_FUNC (gb_custom_on_widget_realize),
			    NULL);

  /* Set the initial last_mod_time. */
  gb_custom_set_last_mod_time (new_widget, -1);

  return new_widget;
}


/* We save the last modification time in the widget's datalist. */
static time_t
gb_custom_get_last_mod_time (GtkWidget *widget)
{
  time_t *current;

  current = gtk_object_get_data (GTK_OBJECT (widget), LastModTime);
  g_return_val_if_fail (current != NULL, 0);
  return *current;
}


/* This sets the last modification time of the creation function name.
   Use -1 to get the current time. */
static void
gb_custom_set_last_mod_time (GtkWidget *widget, time_t last_mod_time)
{
  time_t *current;

  current = gtk_object_get_data (GTK_OBJECT (widget), LastModTime);
  /* Create the time_t if it doesn't already exist. */
  if (current == NULL)
    {
      current = g_new (time_t, 1);
      gtk_object_set_data (GTK_OBJECT (widget), LastModTime, current);
    }

  if (last_mod_time == -1)
    last_mod_time = time (NULL);
  if (last_mod_time == (time_t) -1)
    g_warning ("Can't get current time");

  *current = last_mod_time;
}


static void
gb_custom_on_widget_realize (GtkWidget *widget, gpointer data)
{
  static GdkPixmap *background_pixmap = NULL;

#if 0
  g_print ("In gb_custom_on_widget_realize widget:%s (%p)\n",
	   gtk_widget_get_name (widget), widget);
#endif

  /* Create the background pixmap if it hasn't already been created. */
  if (background_pixmap == NULL)
    {
      background_pixmap = gdk_pixmap_create_from_xpm_d (widget->window,
							NULL, NULL,
							custom_bg_xpm);
      if (!background_pixmap)
	{
	  g_warning ("Couldn't create background pixmap\n");
	  /* FIXME: Use a color instead? */
	}
    }

  if (background_pixmap != NULL)
    gdk_window_set_back_pixmap (widget->window, background_pixmap, FALSE);
}


/*
 * Creates the components needed to edit the extra properties of this widget.
 */
static void
gb_custom_create_properties (GtkWidget * widget, GbWidgetCreateArgData * data)
{
  property_add_string (CreationFunction, _("Creation Function:"),
		       _("The function which creates the widget"));

  property_add_string (String1, _("String1:"),
		       _("The first string argument to pass to the function"));
  property_add_string (String2, _("String2:"),
		       _("The second string argument to pass to the function"));
  property_add_int (Int1, _("Int1:"),
		    _("The first integer argument to pass to the function"));
  property_add_int (Int2, _("Int2:"),
		    _("The second integer argument to pass to the function"));
}



/*
 * Gets the properties of the widget. This is used for both displaying the
 * properties in the property editor, and also for saving the properties.
 */
static void
gb_custom_get_properties (GtkWidget *widget, GbWidgetGetArgData * data)
{
  gchar *string1, *string2;

  gb_widget_output_string (data, CreationFunction, gtk_object_get_data (GTK_OBJECT (widget), CreationFunction));

  string1 = gtk_object_get_data (GTK_OBJECT (widget), String1);
  string2 = gtk_object_get_data (GTK_OBJECT (widget), String2);

  /* For empty strings output NULL (i.e. don't save in the XML). */
  if (string1 && *string1 == '\0')
    string1 = NULL;
  if (string2 && *string2 == '\0')
    string2 = NULL;

  gb_widget_output_string (data, String1, string1);
  gb_widget_output_string (data, String2, string2);

  gb_widget_output_int (data, Int1, GPOINTER_TO_INT (gtk_object_get_data (GTK_OBJECT (widget), Int1)));
  gb_widget_output_int (data, Int2, GPOINTER_TO_INT (gtk_object_get_data (GTK_OBJECT (widget), Int2)));

  /* When saving we also save the last modification time. */
  if (data->action == GB_SAVING)
    {
      time_t last_mod_time;

      last_mod_time = gb_custom_get_last_mod_time (widget);
      if (last_mod_time != 0)
	{
	  save_date (data, LastModTime, last_mod_time);
	}
    }
}



/*
 * Sets the properties of the widget. This is used for both applying the
 * properties changed in the property editor, and also for loading.
 */
static void
gb_custom_set_properties (GtkWidget * widget, GbWidgetSetArgData * data)
{
  gchar *creation_function, *string1, *string2;
  gint int1, int2;

  creation_function = gb_widget_input_string (data, CreationFunction);
  if (data->apply)
    {
      g_free (gtk_object_get_data (GTK_OBJECT (widget), CreationFunction));
      gtk_object_set_data (GTK_OBJECT (widget), CreationFunction,
			   g_strdup (creation_function));

      /* If we are applying the property, we set the last modification time. */
      if (data->action == GB_APPLYING)
	{
	  gb_custom_set_last_mod_time (widget, -1);
	}
    }

  string1 = gb_widget_input_string (data, String1);
  if (data->apply)
    {
      g_free (gtk_object_get_data (GTK_OBJECT (widget), String1));
      gtk_object_set_data (GTK_OBJECT (widget), String1, g_strdup (string1));
    }

  string2 = gb_widget_input_string (data, String2);
  if (data->apply)
    {
      g_free (gtk_object_get_data (GTK_OBJECT (widget), String2));
      gtk_object_set_data (GTK_OBJECT (widget), String2, g_strdup (string2));
    }

  int1 = gb_widget_input_int (data, Int1);
  if (data->apply)
    gtk_object_set_data (GTK_OBJECT (widget), Int1, GINT_TO_POINTER (int1));

  int2 = gb_widget_input_int (data, Int2);
  if (data->apply)
    gtk_object_set_data (GTK_OBJECT (widget), Int2, GINT_TO_POINTER (int2));

  /* If we are loading, load the last modification time. */
  if (data->action == GB_LOADING)
    {
      time_t last_mod_time;

      last_mod_time = load_date (data, LastModTime);
      if (data->apply)
	{
	  gb_custom_set_last_mod_time (widget, last_mod_time);
	}
    }
}



/*
 * Adds menu items to a context menu which is just about to appear!
 * Add commands to aid in editing a Custom, with signals pointing to
 * other functions in this file.
 */
/*
static void
gb_custom_create_popup_menu (GtkWidget * widget, GbWidgetCreateMenuData * data)
{

}
*/



/*
 * Writes the source code needed to create this widget.
 * You have to output everything necessary to create the widget here, though
 * there are some convenience functions to help.
 */
static void
gb_custom_write_source (GtkWidget * widget, GbWidgetWriteSourceData * data)
{
  gchar *creation_func, *string1, *string2;
  gint int1, int2;
  time_t last_mod_time;

  creation_func = gtk_object_get_data (GTK_OBJECT (widget), CreationFunction);
  string1 = gtk_object_get_data (GTK_OBJECT (widget), String1);
  string2 = gtk_object_get_data (GTK_OBJECT (widget), String2);
  int1 = GPOINTER_TO_INT (gtk_object_get_data (GTK_OBJECT (widget), Int1));
  int2 = GPOINTER_TO_INT (gtk_object_get_data (GTK_OBJECT (widget), Int2));

  /* If no creation function has been set, we skip the widget. */
  if (creation_func == NULL)
    return;

  creation_func = source_create_valid_identifier (creation_func);

  /* Output "" if the strings are NULL, for consistency. */
  if (string1 == NULL)
    string1 = "";
  if (string2 == NULL)
    string2 = "";

  if (string1)
    string1 = g_strdup (source_make_string (string1, FALSE));
  if (string2)
    string2 = g_strdup (source_make_string (string2, FALSE));
  /* Example output:
	custom1 = create_custom1 ("custom1", "string1", "string2", 5, 10);
  */
  source_add (data, "  %s = %s (%s, %s, %s, %i, %i);\n",
	      data->wname, creation_func,
	      source_make_string (data->real_wname, FALSE),
	      string1 ? string1 : "NULL", string2 ? string2 : "NULL",
	      int1, int2);
  gb_widget_write_standard_source (widget, data);

  /* Add declaration of creation function and empty function, just
     like a signal handler. We need a last_modification_time property. */
  last_mod_time = gb_custom_get_last_mod_time (widget);
  if (data->creating_callback_files
      || (last_mod_time > data->last_write_time))
    {
      source_add_to_buffer (data, GLADE_CALLBACK_DECLARATIONS,
			    "\nGtkWidget*\n"
			    "%s (gchar *widget_name, gchar *string1, gchar *string2,\n"
			    "                gint int1, gint int2);\n",
			    creation_func);

      source_add_to_buffer (data, GLADE_CALLBACK_SOURCE,
			    "\nGtkWidget*\n"
			    "%s (gchar *widget_name, gchar *string1, gchar *string2,\n"
			    "                gint int1, gint int2)\n"
			    "{\n\n}\n\n",
			    creation_func);
    }

  g_free (creation_func);
  g_free (string1);
  g_free (string2);
}


static void
gb_custom_destroy (GtkWidget * widget, GbWidgetDestroyData * data)
{
  /* We need to free the LastModTime. */
  g_free (gtk_object_get_data (GTK_OBJECT (widget), LastModTime));
}


/*
 * Initializes the GbWidget structure.
 * I've placed this at the end of the file so we don't have to include
 * declarations of all the functions.
 */
GbWidget*
gb_custom_init ()
{
  /* Initialise the GTK type. We don't need this. */
  /* gtk_custom_get_type(); */

  /* Initialize the GbWidget structure */
  gb_widget_init_struct(&gbwidget);

  /* Fill in the pixmap struct & tooltip */
  gbwidget.pixmap_struct = custom_xpm;
  gbwidget.tooltip = _("Custom Widget");

  /* Fill in any functions that this GbWidget has */
  gbwidget.gb_widget_new		= gb_custom_new;
  gbwidget.gb_widget_create_properties	= gb_custom_create_properties;
  gbwidget.gb_widget_get_properties	= gb_custom_get_properties;
  gbwidget.gb_widget_set_properties	= gb_custom_set_properties;
  gbwidget.gb_widget_write_source	= gb_custom_write_source;
  gbwidget.gb_widget_destroy		= gb_custom_destroy;
/*
  gbwidget.gb_widget_create_popup_menu	= gb_custom_create_popup_menu;
*/

  return &gbwidget;
}

