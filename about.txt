💛💙

DeaDBeeF - The Music Player
Copyright © 2009-2025 <PERSON><PERSON><PERSON><PERSON> and contributors
https://deadbeef.sourceforge.io

Some of the most significant contributors in alphabetical order:

    <PERSON> <<EMAIL>>
    <PERSON><PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>
    Derreck <<EMAIL>>
    <PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>
    <PERSON><PERSON><PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>
    Kryksyh <<EMAIL>>
    <PERSON> <<EMAIL>>
    <PERSON><PERSON><PERSON> <<EMAIL>>
    Sofias <<EMAIL>>
    starws <<EMAIL>>
    <PERSON><PERSON> <<EMAIL>>
    <PERSON>aister <<EMAIL>>
    V<PERSON><PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>

If you think your name is missing here, or needs to be removed, please send an email to Oleksiy Yakovenko <<EMAIL>>

Special Thanks To:

    James Lee
    Jan Marguc
    Olga Belozerova


The bundled 3rd party libraries:


Independent implementation of MD5 (RFC 1321).
Copyright © 1999, 2000, 2002 Aladdin Enterprises.  All rights reserved.

FFT Implementation from Audacious player
Copyright © 2011 John Lindgren

Fast FTOI routines from libvorbis
Copyright © 2002-2008 Xiph.org Foundation

The UTF-8 code is based on Basic UTF-8 manipulation routines by Jeff Bezanson
Copyright © Jeff Bezanson
placed in the public domain Fall 2005


For information on the libraries used by the plugins -- please see the specific plugin's copyright information.
