# Glade Czech translation.
# Copyright (C) 2002 Free Software Foundation, Inc.
# Copyright (C) 2004, 2005 <PERSON><PERSON> <<EMAIL>>
# <PERSON> (<PERSON><PERSON>) <PERSON><PERSON>l <<EMAIL>>, 2000.
# <PERSON><PERSON> <<EMAIL>>, 2002
# <PERSON><PERSON> <<EMAIL>>, 2003, 2004, 2005.
#
msgid ""
msgstr ""
"Project-Id-Version: glade VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2005-09-21 02:57+0200\n"
"PO-Revision-Date: 2005-09-21 10:42+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../glade-2.desktop.in.h:1
msgid "Design user interfaces"
msgstr "Navrhovat uživatelská rozhraní"

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Návrh<PERSON><PERSON> uživatelského rozhraní Glade"

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Volby mřížky"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Horizontální rozestup:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Vertikální rozestup:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Styl mřížky:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Tečky"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Linky"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Volby přitahování"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Horizontální přitahování:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Vlevo"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Vpravo"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Vertikální přitahování:"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Nahoru"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Dolu"

#: ../glade/editor.c:741
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "Widgety GtkToolItem lze pouze vložit do GtkToolbar."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "Nemohu vložit widget GtkScrolledWindow."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "Nemohu vložit widget GtkViewport."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "Nemohu přidat nový widget."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"Nemůžete přidat widget na vybranou pozici.\n"
"\n"
"Tip: GTK+ používá pro umístění widgetů kontejnery.\n"
"Zkuste odstranit existující widget a použít\n"
"místo něj box nebo tabulkový kontejner.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "Nemohu odstranit widget."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "Tento widget nemůže být odstraněn"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"Tento widget je vytvořen automaticky jako součást rodičovského widgetu a "
"nelze jej odstranit."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Šířka okraje:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "Šířka okraje okolo kontejneru"

#: ../glade/gbwidget.c:1751
msgid "Select"
msgstr "Vybrat"

#: ../glade/gbwidget.c:1773
msgid "Remove Scrolled Window"
msgstr "Odstranit posuvné okno"

#: ../glade/gbwidget.c:1782
msgid "Add Scrolled Window"
msgstr "Přidat posuvné okno"

#: ../glade/gbwidget.c:1803
msgid "Remove Alignment"
msgstr "Odstranit zarovnání"

#: ../glade/gbwidget.c:1811
msgid "Add Alignment"
msgstr "Přidat zarovnání"

#: ../glade/gbwidget.c:1826
msgid "Remove Event Box"
msgstr "Odstranit event box"

#: ../glade/gbwidget.c:1834
msgid "Add Event Box"
msgstr "Přidat event box"

#: ../glade/gbwidget.c:1844
msgid "Redisplay"
msgstr "Znovu zobrazit"

#: ../glade/gbwidget.c:1859
msgid "Cut"
msgstr "Vyjmout"

#: ../glade/gbwidget.c:1866 ../glade/property.c:892 ../glade/property.c:5141
msgid "Copy"
msgstr "Kopírovat"

#: ../glade/gbwidget.c:1875 ../glade/property.c:904
msgid "Paste"
msgstr "Vložit"

#: ../glade/gbwidget.c:1887 ../glade/property.c:1581 ../glade/property.c:5132
msgid "Delete"
msgstr "Odstranit"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2414 ../glade/gbwidget.c:2483
msgid "N/A"
msgstr "N/A"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3213
msgid "replacing child of container - not implemented yet\n"
msgstr "nahrazování potomka kontejneru - ještě neimplementováno\n"

#: ../glade/gbwidget.c:3441
msgid "Couldn't insert GtkAlignment widget."
msgstr "Nemohu vložit widget GtkAlignment."

#: ../glade/gbwidget.c:3481
msgid "Couldn't remove GtkAlignment widget."
msgstr "Nemohu odstranit widget GtkAlignment."

#: ../glade/gbwidget.c:3505
msgid "Couldn't insert GtkEventBox widget."
msgstr "Nemohu vložit widget GtkEventBox."

#: ../glade/gbwidget.c:3544
msgid "Couldn't remove GtkEventBox widget."
msgstr "Nemohu odstranit widget GtkEventBox."

#: ../glade/gbwidget.c:3579
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "Nemohu vložit widget GtkScrolledWindow."

#: ../glade/gbwidget.c:3618
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "Nemohu odstranit widget GtkScrolledWindow."

#: ../glade/gbwidget.c:3732
msgid "Remove Label"
msgstr "Odstranit nápis"

#: ../glade/gbwidgets/gbaboutdialog.c:79
msgid "Application Name"
msgstr "Název aplikace"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Logo:"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "Obrázek použitý jako logo"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Název programu:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
msgid "The name of the application"
msgstr "Název aplikace"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "Komentář:"

#: ../glade/gbwidgets/gbaboutdialog.c:106
msgid "Additional information, such as a description of the application"
msgstr "Dodatečné informace, jako třeba popis aplikace"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Copyright:"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "Poznámka o autorských právech"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "Website URL:"
msgstr "URL WWW stránek:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "The URL of the application's website"
msgstr "URL WWW stránek aplikace"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "Website Label:"
msgstr "Nápis WWW stránek:"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "The label to display for the link to the website"
msgstr "Nápis, který zobrazovat u odkazu na WWW stránky"

#: ../glade/gbwidgets/gbaboutdialog.c:112 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Licence:"

#: ../glade/gbwidgets/gbaboutdialog.c:112
msgid "The license details of the application"
msgstr "Podrobnosti o licenci aplikace"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "Wrap License:"
msgstr "Zalamovat licenci:"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "If the license text should be wrapped"
msgstr "Jestli má být text licence zalamován"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Autoři:"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Autoři balíčku, jeden na každý řádek"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Dokumentátoři:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Autoři dokumentace pro tento balíček, jeden na každý řádek"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid "Artists:"
msgstr "Umělci:"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr "Lidé, kteřé vytvořili grafiku balíčku, jeden na každý řádek"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Překladatelé:"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Překladatelé tohoto balíčku. Normálně by mělo být toto prázdné, aby mohli "
"překladatelé přidat svá jména v .po souborech"

#: ../glade/gbwidgets/gbaboutdialog.c:588
msgid "About Dialog"
msgstr "Dialog O aplikaci"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Nápis s akcelerátorem"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:181 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "Zarovnání X:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "Vodorovné zarovnání widgetu potomka"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:184 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Zarovnání Y:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "Svislé zarovnání widgetu potomka"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "Škála X:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "Vodorovné měřítko widgetu potomka"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Škála Y:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "Svislé měřítko widgetu potomka"

#: ../glade/gbwidgets/gbalignment.c:85
msgid "Top Padding:"
msgstr "Horní obložení:"

#: ../glade/gbwidgets/gbalignment.c:86
msgid "Space to put above the child widget"
msgstr "Místo, které přidat nad widget potomka"

#: ../glade/gbwidgets/gbalignment.c:89
msgid "Bottom Padding:"
msgstr "Spodní obložení:"

#: ../glade/gbwidgets/gbalignment.c:90
msgid "Space to put below the child widget"
msgstr "Místo, které přidat pod widget potomka"

#: ../glade/gbwidgets/gbalignment.c:93
msgid "Left Padding:"
msgstr "Levé obložení:"

#: ../glade/gbwidgets/gbalignment.c:94
msgid "Space to put to the left of the child widget"
msgstr "Místo, které přidat nalevo od widgetu potomka"

#: ../glade/gbwidgets/gbalignment.c:97
msgid "Right Padding:"
msgstr "Pravé obložení:"

#: ../glade/gbwidgets/gbalignment.c:98
msgid "Space to put to the right of the child widget"
msgstr "Místo, které přidat napravo od widgetu potomka"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Zarovnání"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "Směr:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "Směr šipky"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Stín:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "Typ stínu šipky"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "Vodorovné zarovnání šipky"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "Svislé zarovnání šipky"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187
msgid "X Pad:"
msgstr "Výplň X:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "Vodorovná výplň"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189
msgid "Y Pad:"
msgstr "Výplň Y:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "Svislá výplň"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Šipka"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "Zarovnání nápisu X:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "Vodorovné zarovnání widgetu nápisu rámu"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Zarovnání nápisu Y:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "Svislé zarovnání widgetu nápisu rámu"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "Typ stínování rámu"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "Vodorovné zarovnání potomka rámu"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Poměr:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "Poměr stran potomka rámu"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Řídit se potomkem:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Jestli má být poměr stran určen podle potomka"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Poměrný rám"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Standardní tlačítko:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "Použité standardní tlačítko"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:748
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Nápis:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "Zobrazený text"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:297
#: ../glade/glade_menu_editor.c:814
msgid "Icon:"
msgstr "Ikona:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "Zobrazená ikona"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Reliéf tlačítka:"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "Styl reliéfu tlačítka"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "ID odpovědi:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"Návratový kód odpovědi při stisku tlačítka. Vyberte jednu ze standardních "
"odpovědí nebo zadejte kladnou celočíselnou hodnotu"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr "Zaměřit při kliknutí:"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "If the button grabs focus when it is clicked"
msgstr "Jestli je tlačítko zaměřeno, když se na ně klikne"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "Odstranit obsah tlačítka"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Tlačítko"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Hlavička:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "Jestli mají být nahoře zobrazeny měsíc a den"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Názvy dnů:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Jestli mají být zobrazeny názvy dnů"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Fixní měsíc:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "Jestli měsíc a rok mají být neměnné"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Čísla týdnů:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Jestli mají být zobrazena čísla týdnů"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Začínat v pondělí:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "Jestli má týden začínat v pondělí"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Kalendář"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Barva pozadí:"

#: ../glade/gbwidgets/gbcellview.c:64
msgid "The background color"
msgstr "Barva pozadí"

#: ../glade/gbwidgets/gbcellview.c:192
msgid "Cell View"
msgstr "Buňkový pohled"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Implicitně zapnuto:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Jestli má být zaškrtávací tlačítko zapnuto"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Nekonzistentní:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Jestli má být tlačítko zobrazeno v nekonzistentním stavu"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "Indikátor:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "Jestli je vždy vykreslován indikátor"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Zaškrtávací tlačítko"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Jestli je zatržení položky menu implicitně zapnuto"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Zaškrtávací položka menu"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Nový seznam se sloupci"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Počet sloupců:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:128 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Režim výběru:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "Režim výběru seznamu se sloupci"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Zobrazovat nadpisy:"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Jestli se mají zobrazovat nadpisy"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "Typ stínování okraje seznamu se sloupci"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Seznam se sloupci"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Použít alfa:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Jestli má být použit alfa kanál"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:244
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Nadpis:"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "Titulek dialogu pro výběr barvy"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
msgid "Pick a Color"
msgstr "Vyberte barvu"

#: ../glade/gbwidgets/gbcolorbutton.c:211
msgid "Color Chooser Button"
msgstr "Tlačítko výběru barvy"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Ovládání krytí:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Jestli má být zobrazeno ovládání krytí"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Paleta:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Jestli má být zobrazena paleta"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Výběr barvy"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:71
msgid "Select Color"
msgstr "Vyberte barvu"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:316 ../glade/property.c:1276
msgid "Color Selection Dialog"
msgstr "Dialog výběru barev"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Hodnota v seznamu:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Jestli musí být hodnota přítomna v seznamu"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "Může být prázdný:"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr ""
"Jestli je přijatelná prázdná hodnota, pokud je nastavena volba 'Hodnota v "
"seznamu'"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "Rozlišovat malá a velká písmena:"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Jestli hledání rozlišuje malá a velká písmena"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Použít šipky:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "Jestli lze pro změnu hodnoty použít šipky"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Použít vždy:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "Jestli šipky fungují, i když hodnota není v seznamu"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Položky:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Položky v comboboxu, jedna na řádek"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Combo Box"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr "Přidat odtrhávátka:"

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr "Jestli mají mít menu položku pro odtržení"

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Jestli je combo box zaměřen, když se na něj klikne"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Má rámeček:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr "Jestli combo box kreslí rámeček okolo potomka"

#: ../glade/gbwidgets/gbcomboboxentry.c:302
msgid "Combo Box Entry"
msgstr "Položka combo boxu"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Nový strom se sloupci"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "Režim výběru stromu se sloupci"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "Typ stínování okraje stromu se sloupci"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Strom se sloupci"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:247
msgid "Type:"
msgstr "Typ:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "Typ křivky"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "Minimální X:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "Minimální horizontální hodnota"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "Maximální X:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "Maximální horizontální hodnota"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Minimální Y:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "Minimální vertikální hodnota"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Maximální Y:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "Maximální vertikální hodnota"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Křivka"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Vytvářecí funkce:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "Funkce vytvářející widget"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Řetězec1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "První parametr typu řetězec, který se předá funkci"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Řetězec2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "Druhý parametr typu řetězec, který se předá funkci"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Int1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "První parametr celočíselného typu, který se předá funkci"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Int2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "Druhý parametr celočíselného typu, který se předá funkci"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Vlastní widget"

#: ../glade/gbwidgets/gbdialog.c:293
msgid "New dialog"
msgstr "Nový dialog"

#: ../glade/gbwidgets/gbdialog.c:305
msgid "Cancel, OK"
msgstr "Zrušit, OK"

#: ../glade/gbwidgets/gbdialog.c:314 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1322 ../glade/property.c:5162
msgid "OK"
msgstr "OK"

#: ../glade/gbwidgets/gbdialog.c:323
msgid "Cancel, Apply, OK"
msgstr "Zrušit, Použít, OK"

#: ../glade/gbwidgets/gbdialog.c:332
msgid "Close"
msgstr "Zavřít"

#: ../glade/gbwidgets/gbdialog.c:341
msgid "_Standard Button Layout:"
msgstr "_Standardní rozvržení tlačítek:"

#: ../glade/gbwidgets/gbdialog.c:350
msgid "_Number of Buttons:"
msgstr "Počet tlačíte_k:"

#: ../glade/gbwidgets/gbdialog.c:367
msgid "Show Help Button"
msgstr "Zobrazovat tlačítko nápovědy"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "Has Separator:"
msgstr "Má oddělovač:"

#: ../glade/gbwidgets/gbdialog.c:399
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Jestli má dialog nad tlačítky vodorovný oddělovač"

#: ../glade/gbwidgets/gbdialog.c:606
msgid "Dialog"
msgstr "Dialog"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Kreslící oblast"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Upravitelný:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Jestli text může být upravován"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Viditelný text:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"Jestli bude text zadaný uživatelem zobrazen. Pokud je volba vypnuta, bude "
"zadaný text zobrazen jako hvězdičky, což je užitečné pro zadávání hesel"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Max délka:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "Maximální délka textu"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Text:"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Jestli má políčko okolo sebe rám"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Neviditelný znak:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"Znak použitý při zadávání neviditelného textu, např. při zadávání hesel"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "Aktivuje implicitní:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr "Jestli je aktivován implicitní widget okna při stisku klávesy Enter"

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Šířka ve znacích:"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "Počet znaků, pro které se má v políčku vyhradit prostor"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Textové políčko"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "Visible Window:"
msgstr "Viditelné okno:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr "Jestli event box používá viditelné okno"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "Above Child:"
msgstr "Nad potomkem:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr "Jestli je okno event boxu nad oknem widgetu potomka"

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Event Box"

#: ../glade/gbwidgets/gbexpander.c:54
msgid "Initially Expanded:"
msgstr "Na začátku rozbalen:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr "Jestli je rozbalovač na začátku rozbalen a zobrazuje widget potomka"

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Rozestup:"

#: ../glade/gbwidgets/gbexpander.c:58
msgid "Space to put between the label and the child"
msgstr "Místo, které přidat mezi nápis a potomka"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Přidat widget nápisu"

#: ../glade/gbwidgets/gbexpander.c:228
msgid "Expander"
msgstr "Rozbalovač"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
msgid "The window title of the file chooser dialog"
msgstr "Nadpis okna dialogu pro výběr souboru"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:158
#: ../glade/gnome/gnomefileentry.c:109
msgid "Action:"
msgstr "Akce:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:89
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr "Typ prováděné operace se souborem"

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
msgid "Local Only:"
msgstr "Jen místní:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether the selected files should be limited to local files"
msgstr "Jestli mají být vybrané souboru omezeny na místní soubory"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:165
msgid "Show Hidden:"
msgstr "Zobrazovat skryté:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:166
msgid "Whether the hidden files and folders should be displayed"
msgstr "Jestli mají být zobrazovány skryté soubory a adresáře"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#: ../glade/gbwidgets/gbfilechooserdialog.c:167
msgid "Confirm:"
msgstr "Potvrdit:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:96
#: ../glade/gbwidgets/gbfilechooserdialog.c:168
msgid ""
"Whether a confirmation dialog will be displayed if a file will be overwritten"
msgstr "Jestli bude zobrazen dialog potvrzení, když bude přepsán soubor"

#: ../glade/gbwidgets/gbfilechooserbutton.c:97
#: ../glade/gbwidgets/gblabel.c:201
msgid "Width in Chars:"
msgstr "Šířka ve znacích:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:98
msgid "The width of the button in characters"
msgstr "Šířka tlačítka ve znacích"

#: ../glade/gbwidgets/gbfilechooserbutton.c:296
msgid "File Chooser Button"
msgstr "Tlačítko výběru písma"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
msgid "Select Multiple:"
msgstr "Výběr více položek:"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether to allow multiple files to be selected"
msgstr "Jestli povolit výběr více souborů"

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
msgid "File Chooser"
msgstr "Výběr souborů"

#: ../glade/gbwidgets/gbfilechooserdialog.c:435
msgid "File Chooser Dialog"
msgstr "Dialog výběru souborů"

#: ../glade/gbwidgets/gbfileselection.c:72 ../glade/property.c:1366
msgid "Select File"
msgstr "Výběr souboru"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "File Ops.:"
msgstr "Operace se soubory:"

#: ../glade/gbwidgets/gbfileselection.c:115
msgid "If the file operation buttons are shown"
msgstr "Jestli jsou zobrazeny tlačítka pro operace se soubory"

#: ../glade/gbwidgets/gbfileselection.c:293
msgid "File Selection Dialog"
msgstr "Dialog výběru souboru"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "Souřadnice X widgetu ve widgetu GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "Souřadnice Y widgetu ve widgetu GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Fixní pozice"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "Nadpis dialogu pro výběr písma"

#: ../glade/gbwidgets/gbfontbutton.c:70
msgid "Show Style:"
msgstr "Zobrazovat styl:"

#: ../glade/gbwidgets/gbfontbutton.c:71
msgid "If the font style is shown as part of the font information"
msgstr "Jestli se jako součást informací o písmu zobrazuje styl písma"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Zobrazovat velikost:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "Jestli se jako součást informací o písmu zobrazuje velikost písma"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Použít písmo:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr "Jestli je vybrané písmo použito při zobrazení informace o písmu"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Použít velikost:"

#: ../glade/gbwidgets/gbfontbutton.c:77
msgid "if the selected font size is used when displaying the font information"
msgstr "jestli je vybraná velikost použita při zobrazování informací o písmu"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Vyberte písmo"

#: ../glade/gbwidgets/gbfontbutton.c:268
msgid "Font Chooser Button"
msgstr "Tlačítko výběru písma"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Text náhledu:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "Zobrazený text v náhledu"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Výběr písma"

#: ../glade/gbwidgets/gbfontselectiondialog.c:70
msgid "Select Font"
msgstr "Vyberte písmo"

#: ../glade/gbwidgets/gbfontselectiondialog.c:301
msgid "Font Selection Dialog"
msgstr "Dialog pro výběr písma"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Rámeček"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Počáteční typ:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "Počáteční typ křivky"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Křivka Gama"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "Typ stínování okolo boxu s uchopením"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "Pozice uchopení:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "Pozice uchopení"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Zapadnout do okraje:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "Okraj boxu s uchopením, který zapadne na místo"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "Box s uchopením"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Nový horizontální box"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Velikost:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "Počet widgetů v boxu"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Homogenní:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Jestli mají mít potomci stejnou velikost"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "Rozestup mezi potomky"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "Nemohu odstranit žádné potomky."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:256
msgid "Position:"
msgstr "Pozice:"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "Pozice widgetu vzhledem k jeho sourozencům"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Obložení:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "Obložení widgetu"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Expandovat:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "Nastavením hodnoty True se umožní expanze widgetu"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Vyplnit:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr ""
"Nastavte na True, pokud chcete, aby widget vyplnil celý svůj přiřazený "
"prostor"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Počátek balení:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "Nastavte na True, pokud chcete začít umísťovat widgety na počátku boxu"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Vložit před"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Vložit za"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Horizontální box"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Nový horizontální box s tlačítky"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "Počet tlačítek"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Rozvržení:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "Styl rozvržení tlačítek"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "Rozestup mezi tlačítky"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Horizontální box s tlačítky"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "Umístění oddělovače"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:285
msgid "Shrink:"
msgstr "Smršťovat:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "Nastavte na True, pokud chcete umožnit widgetu zmenšit se"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Změna velikosti:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "Nastavte na True, pokud má být možná změna velikosti widgetu"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Horizontální panely"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Metrika:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "Jednotky pravítka"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Nižší hodnota:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "Spodní hranice pravítka"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Vyšší hodnota:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "Horní hranice pravítka"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "Aktuální pozice pravítka"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4833
msgid "Max:"
msgstr "Max:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "Maximální hodnota pravítka"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Horizontální pravítko"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Zobrazovat hodnotu:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Jestli je zobrazena hodnota stupnice"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Číslic:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "Počet číslic, které jsou vidět"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Pozice hodnoty:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "Pozice hodnoty"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Politika:"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "Politika aktualizace stupnice"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Obrácená:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Hodnoty rozmezí jsou obráceny"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Horizontální stupnice"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "Politika aktualizace posuvníku"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Horizontální posuvník"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Horizontální oddělovač"

#: ../glade/gbwidgets/gbiconview.c:107
#, c-format
msgid "Icon %i"
msgstr "Ikona %i"

#: ../glade/gbwidgets/gbiconview.c:129
msgid "The selection mode of the icon view"
msgstr "Režim výběru ikonového pohledu"

#: ../glade/gbwidgets/gbiconview.c:131 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "Orientace:"

#: ../glade/gbwidgets/gbiconview.c:132
msgid "The orientation of the icons"
msgstr "Orientace ikon"

#: ../glade/gbwidgets/gbiconview.c:134 ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Lze měnit pořadí:"

#: ../glade/gbwidgets/gbiconview.c:135
msgid "If the view can be reordered using Drag and Drop"
msgstr "Jestli pohled může být přeuspořádán tažením myší"

#: ../glade/gbwidgets/gbiconview.c:308
msgid "Icon View"
msgstr "Ikonový pohled"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:301
msgid "Named Icon:"
msgstr "Pojmenovaná ikona:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:302
msgid "The named icon to use"
msgstr "Pojmenovaná ikona, kterou použít"

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Velikost ikony:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "Velikost standardní ikony"

#: ../glade/gbwidgets/gbimage.c:115
msgid "Pixel Size:"
msgstr "Velikost v pixelech:"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""
"Velikost pojmenované ikony v pixelech, nebo -1 pro použití vlastnosti "
"Velikost Ikony"

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "Vodorovné zarovnání"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "Svislé zarovnání"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Obrázek"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "Neplatná standardní položka menu"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Položka menu s obrázkem"

#: ../glade/gbwidgets/gbinputdialog.c:257
msgid "Input Dialog"
msgstr "Vstupní dialog"

#: ../glade/gbwidgets/gblabel.c:170
msgid "Use Underline:"
msgstr "Použít podtržení:"

#: ../glade/gbwidgets/gblabel.c:171
msgid "If the text includes an underlined access key"
msgstr "Jestli text obsahuje podtržený znak pro akcelerátor"

#: ../glade/gbwidgets/gblabel.c:172
msgid "Use Markup:"
msgstr "Použít značky:"

#: ../glade/gbwidgets/gblabel.c:173
msgid "If the text includes pango markup"
msgstr "Jestli text obsahuje značky Pango"

#: ../glade/gbwidgets/gblabel.c:174
msgid "Justify:"
msgstr "Zarovnat:"

#: ../glade/gbwidgets/gblabel.c:175
msgid "The justification of the lines of the label"
msgstr "Zarovnání řádků nápisu"

#: ../glade/gbwidgets/gblabel.c:177
msgid "Wrap Text:"
msgstr "Zalamovat text:"

#: ../glade/gbwidgets/gblabel.c:178
msgid "If the text is wrapped to fit within the width of the label"
msgstr "Jestli má být text zalamován, aby se přizpůsobil šířce widgetu"

#: ../glade/gbwidgets/gblabel.c:179
msgid "Selectable:"
msgstr "Lze vybrat:"

#: ../glade/gbwidgets/gblabel.c:180
msgid "If the label text can be selected with the mouse"
msgstr "Jestli je možné vybrat text nápisu myší"

#: ../glade/gbwidgets/gblabel.c:182
msgid "The horizontal alignment of the entire label"
msgstr "Vodorovné zarovnání celého nápisu"

#: ../glade/gbwidgets/gblabel.c:185
msgid "The vertical alignment of the entire label"
msgstr "Svislé zarovnání celého nápisu"

#: ../glade/gbwidgets/gblabel.c:191
msgid "Focus Target:"
msgstr "Cíl zaměření:"

#: ../glade/gbwidgets/gblabel.c:192
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"Widget, který má být zaměřen pro vstup z klávesnice po stisku podtržené "
"klávesy akcelerátoru"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:146
msgid "Ellipsize:"
msgstr "Zkracovat:"

#: ../glade/gbwidgets/gblabel.c:199 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr "Jak zkracovat řetězec"

#: ../glade/gbwidgets/gblabel.c:202
msgid "The width of the label in characters"
msgstr "Šířka nápisu ve znacích"

#: ../glade/gbwidgets/gblabel.c:204
msgid "Single Line Mode:"
msgstr "Režim jednoho řádku:"

#: ../glade/gbwidgets/gblabel.c:205
msgid "If the label is only given enough height for a single line"
msgstr "Jestli nápis dostane výšku jen pro jeden řádek"

#: ../glade/gbwidgets/gblabel.c:206
msgid "Angle:"
msgstr "Úhel:"

#: ../glade/gbwidgets/gblabel.c:207
msgid "The angle of the label text"
msgstr "Úhel textu nápisu"

#: ../glade/gbwidgets/gblabel.c:333 ../glade/gbwidgets/gblabel.c:348
#: ../glade/gbwidgets/gblabel.c:616
msgid "Auto"
msgstr "Auto"

#: ../glade/gbwidgets/gblabel.c:872 ../glade/glade_menu_editor.c:411
msgid "Label"
msgstr "Nápis"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Šířka oblasti:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "Šířka oblasti rozvržení"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Výška oblasti:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "Výška oblasti rozvržení"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "Souřadnice widgetu X v GtkLayout"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "Souřadnice widgetu Y v GtkLayout"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Rozvržení"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "Režim výběru seznamu"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Seznam"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Položka seznamu"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Vyskakovací menu"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:215
msgid "_File"
msgstr "_Soubor"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:223 ../glade/glade_project_window.c:692
msgid "_Edit"
msgstr "Ú_pravy"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:229 ../glade/glade_project_window.c:721
msgid "_View"
msgstr "_Zobrazit"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:231 ../glade/glade_project_window.c:834
msgid "_Help"
msgstr "Nápo_věda"

#: ../glade/gbwidgets/gbmenubar.c:232
msgid "_About"
msgstr "O _aplikaci"

#: ../glade/gbwidgets/gbmenubar.c:291
msgid "Pack Direction:"
msgstr "Směr balení:"

#: ../glade/gbwidgets/gbmenubar.c:292
msgid "The pack direction of the menubar"
msgstr "Směr balení lišty menu"

#: ../glade/gbwidgets/gbmenubar.c:294
msgid "Child Direction:"
msgstr "Směr potomků:"

#: ../glade/gbwidgets/gbmenubar.c:295
msgid "The child pack direction of the menubar"
msgstr "Směr balení potomků lišty menu"

#: ../glade/gbwidgets/gbmenubar.c:300 ../glade/gbwidgets/gbmenubar.c:418
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Upravit menu..."

#: ../glade/gbwidgets/gbmenubar.c:541
msgid "Menu Bar"
msgstr "Lišta menu"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Položka menu"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
msgid "Show Horizontal:"
msgstr "Zobrazovat vodorovně:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
msgid "If the item is visible when the toolbar is horizontal"
msgstr "Jestli je položka viditelná, když je lišta nástrojů vodorovná"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
msgid "Show Vertical:"
msgstr "Zobrazovat svisle:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
msgid "If the item is visible when the toolbar is vertical"
msgstr "Jestli je položka viditelná, když je lišta nástrojů svislá"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr "Je důležitá:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""
"Jestli má být text položky zobrazován, když režim lišty nástrojů je "
"GTK_TOOLBAR_BOTH_HORIZ"

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
msgid "Toolbar Button with Menu"
msgstr "Tlačítko na liště nástrojů s menu"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Nový sešit"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:125
msgid "Number of pages:"
msgstr "Počet stránek:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "Zobrazovat úchytky:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Jestli jsou zobrazeny úchytky sešitu"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Zobrazovat okraj:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "Jestli je zobrazen okraj sešitu, pokud nejsou zobrazovány úchytky"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Pozice úchytek:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "Umístění úchytek listů sešitu"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Posunovatelný:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Jestli je možné úchytky listů sešitu posouvat"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Hor. hranice úchytek:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "Velikost horizontální hranice úchytek listů sešitu"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Vert. hranice úchytek:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "Velikost vertikální hranice úchytek listů sešitu"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Zobrazovat menu:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "Jestli je vyskakovací menu povoleno"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Počet stránek:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "Počet stránek sešitu"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Předchozí stránka"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "Následující stránka"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Odstranit stránku"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "Přejít na následující"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Přejít na předchozí"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Vložit stránku za"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Vložit stránku před"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "Pozice stránky v seznamu stránek"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Nastavením hodnoty na True umožníte úchytce expanzi"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr ""
"Nastavením hodnoty na True umožníte úchytce vyplnit celou přiřazenou oblast"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr "Nastavení hodnoty na True způsobí sbalení úchytek na začátek sešitu"

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Nápis menu:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "Text zobrazený ve vyskakovacím menu"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Sešit"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "Nemohu přidat %s do widgetu GtkOptionMenu."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Menu s volbami"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Barva:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "Jestli je náhled barevný nebo v odstínech šedi"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "Jestli se má náhled zvětšit a vyplnit celou přiřazenou oblast"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Náhled"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "Orientace obsahu lišty s průběhem"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Podíl:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "Množství práce (podíl), která už je hotova"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Krok pulzu:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""
"Podíl z délky lišty s průběhem, o kterou se pohne skákající blok při pulzu"

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "Text zobrazený nad lištou s průběhem"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Zobrazovat text:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "Jestli má být v liště s průběhem zobrazen text"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Režim aktivity:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr ""
"Jestli se má lišta s průběhem chovat jako přední maska vozu Kit (seriál "
"Knight Rider)"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "Vodorovné zarovnání textu"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "Svislé zarovnání textu"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "Lišta s průběhem"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "Jestli je rádiové tlačítko na počátku zapnuto"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1039
msgid "Group:"
msgstr "Skupina:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr ""
"Skupina rádiového tlačítka (implicitně patří všechny tlačítka na stejném "
"rodiči do stejné skupiny)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Nová skupina"

#: ../glade/gbwidgets/gbradiobutton.c:465
msgid "Radio Button"
msgstr "Rádiové tlačítko"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "Jestli je rádiová položka menu na počátku zapnuta"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"Skupina rádiové položky menu (implicitně patří všechny rádiové položky menu "
"do jedné skupiny v rámci rodiče)"

#: ../glade/gbwidgets/gbradiomenuitem.c:388
msgid "Radio Menu Item"
msgstr "Rádiová položka menu"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr ""
"Skupina rádiových tlačítek (implicitní jsou všechny rádiová tlačítka v liště "
"nástrojů)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:530
msgid "Toolbar Radio Button"
msgstr "Rádiové tlačítko na liště nástrojů"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "Politika H:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Za jakých okolností je zobrazen horizontální posuvník"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "Politika V:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Za jakých okolností je zobrazen vertikální posuvník"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Umístění okna:"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr "Kde je umístěno okno potomka vzhledem k posuvníkům"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Typ stínu:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "Politika aktualizace vertikálního posuvníku"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Posuvné okno"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Oddělovač pro menu"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr "Vykreslovat:"

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr "Jestli je oddělovač vykreslován nebo jen prázdný"

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
msgid "Toolbar Separator Item"
msgstr "Položka oddělovače lišty nástrojů"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Rychlost růstu:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr ""
"Rychlost růstu rotačního tlačítka, používá se spolu s Přírůstkem stránky"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "Počet zobrazených desetinných míst"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Numerický:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr "Jestli je povolen jen číselný vstup"

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Politika aktualizace:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Za jakých okolností jsou vysílány signály value_changed"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Zaokrouhlit:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr ""
"Jestli je hodnota zaokrouhlena (posunuta) k nejbližšímu násobku přírůstku "
"kroku"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Překlopit:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr ""
"Jestli je hodnota překlopena při dosažená meze (změněna na mez opačnou)"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Rotační tlačítko"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Úchytka změny velikosti:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr "Jestli obsahuje stavová lišta úchytku pro změnu velikosti okna"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Stavová lišta"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Nová tabulka"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Počet řádků:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Řádků:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "Počet řádků v tabulce"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Sloupců:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "Počet sloupců v tabulce"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Jestli by měli být potomci stejné velikosti"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Rozestup řádek:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "Rozestup mezi jednotlivými řádky"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Rozestup sloupců:"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "Rozestup mezi jednotlivými sloupci"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Buňka X:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "Levý okraj widgetu v tabulce"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Buňka Y:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "Horní okraj widgetu v tabulce"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Rozpětí sloupců:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "Počet sloupců, přes které se klene widget v tabulce"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Rozpětí řádků:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "Počet řádků, přes které se klene widget v tabulce"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "Obložení H:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "Obložení V:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "Expanze X:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "Nastavte na True, pokud má widget expandovat horizontálně"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Expanze Y:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "Nastavte na True, pokud má widget expandovat vertikálně"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "Smrštění X:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "Nastavte na True, pokud se má widget zmenšovat horizontálně"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Smrštění Y:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "Nastavte na True, pokud se má widget zmenšovat vertikálně"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "Výplň X:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr ""
"Nastavte na True, pokud má widget vyplnit celou přiřazenou oblast v "
"horizontálním směru"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Výplň Y:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr ""
"Nastavte na True, pokud má widget vyplnit celou přiřazenou oblast ve "
"vertikálním směru"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Vložit řádek před"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Vložit řádek za"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Vložit sloupec před"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Vložit sloupec za"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Odstranit řádek"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Odstranit sloupec"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Tabulka"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Střed"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Výplň"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:543 ../glade/glade_menu_editor.c:830
#: ../glade/glade_menu_editor.c:1345 ../glade/glade_menu_editor.c:2255
#: ../glade/property.c:2432
msgid "None"
msgstr "Žádný"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Znak"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Slovo"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Viditelný kurzor:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Jestli je kurzor viditelný"

#: ../glade/gbwidgets/gbtextview.c:119
msgid "Overwrite:"
msgstr "Přepisovat:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr "Jestli zadaný text přepisuje existující text"

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr "Přijímá Tab:"

#: ../glade/gbwidgets/gbtextview.c:122
msgid "If tab characters can be entered"
msgstr "Jestli mohou být zadány znaky tab"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Zarovnání:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "Zarovnání textu"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Zalomení:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "Zalomení textu"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Prostor nad:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Množství prostoru v bodech nad odstavci"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Prostor pod:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Množství prostoru v bodech pod odstavci"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Prostor uvnitř:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Množství prostoru v bodech mezi zalomenými řádky odstavce"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Levý okraj:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Šířka levého okraje v bodech"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Pravý okraj:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Šířka pravého okraje v bodech"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Odsazení:"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Počet bodů pro odsazení odstavců"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Textový pohled"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "Jestli je přepínač na počátku zapnut"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Přepínač"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
msgid "Toolbar Toggle Button"
msgstr "Přepínač na liště nástrojů"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Nová lišta nástrojů"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Počet položek:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "Počet položek v liště nástrojů"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "Orientace lišty nástrojů"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Styl:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "Styl lišty nástrojů"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Tipy:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Jestli jsou povoleny tipy"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "Show Arrow:"
msgstr "Zobrazovat šipku:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""
"Jestli má být zobrazována šipka pro zobrazení menu, když se lišta nástrojů "
"nevejde"

#: ../glade/gbwidgets/gbtoolbar.c:427
msgid "If the item should be the same size as other homogeneous items"
msgstr "Jestli má mít položka stejnou velikost jako jiné homogenní položky"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Vložit položku před"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Vložit položku za"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Lišta nástrojů"

#: ../glade/gbwidgets/gbtoolbutton.c:586
msgid "Toolbar Button"
msgstr "Tlačítko na liště nástrojů"

#: ../glade/gbwidgets/gbtoolitem.c:201
msgid "Toolbar Item"
msgstr "Položka lišty nástrojů"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Sloupec 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Sloupec 2"

#: ../glade/gbwidgets/gbtreeview.c:87
msgid "Column 3"
msgstr "Sloupec 3"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr "Řádek %i"

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Viditelné záhlaví:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Jestli jsou zobrazovány tlačítka záhlaví sloupců"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Návod pro střídání:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""
"Jestli je nastaven návod pro střídání, takže vykreslování témat může kreslit "
"řádky ve střídavých barvách"

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Jestli lze v pohledu měnit pořadí"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Povolit hledání:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr "Jestli může uživatel interaktivně prohledávat ve sloupcích"

#: ../glade/gbwidgets/gbtreeview.c:123
msgid "Fixed Height Mode:"
msgstr "Režim pevné výšky:"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr "Nastaví všechny řádky na stejnou výšku pro zlepšení výkonu"

#: ../glade/gbwidgets/gbtreeview.c:125
msgid "Hover Selection:"
msgstr "Výběr počkáním:"

#: ../glade/gbwidgets/gbtreeview.c:126
msgid "Whether the selection should follow the pointer"
msgstr "Jestli má výběr následovat kurzor"

#: ../glade/gbwidgets/gbtreeview.c:127
msgid "Hover Expand:"
msgstr "Expanze počkáním:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""
"Jestli mají být řádky rozbaleny nebo sbaleny, když se přes ně pohybuje kurzor"

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Seznam nebo Strom"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Nový vertikální box"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Vertikální box"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Nový vertikální box tlačítek"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Vertikální box tlačítek"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "Typ stínování výřezu"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Výřez"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Vertikální panely"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Vertikální pravítko"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Vertikální stupnice"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Vertikální posuvník"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Vertikální oddělovač"

#: ../glade/gbwidgets/gbwindow.c:244
msgid "The title of the window"
msgstr "Titulek okna"

#: ../glade/gbwidgets/gbwindow.c:247
msgid "The type of the window"
msgstr "Typ okna"

#: ../glade/gbwidgets/gbwindow.c:251
msgid "Type Hint:"
msgstr "Nápověda typu:"

#: ../glade/gbwidgets/gbwindow.c:252
msgid "Tells the window manager how to treat the window"
msgstr "Říká správci oken, jak s oknem pracovat"

#: ../glade/gbwidgets/gbwindow.c:257
msgid "The initial position of the window"
msgstr "Počáteční pozice okna"

#: ../glade/gbwidgets/gbwindow.c:261 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Modální:"

#: ../glade/gbwidgets/gbwindow.c:261
msgid "If the window is modal"
msgstr "Jestli je okno modální"

#: ../glade/gbwidgets/gbwindow.c:266
msgid "Default Width:"
msgstr "Implicitní šířka:"

#: ../glade/gbwidgets/gbwindow.c:267
msgid "The default width of the window"
msgstr "Implicitní šířka okna"

#: ../glade/gbwidgets/gbwindow.c:271
msgid "Default Height:"
msgstr "Implicitní výška:"

#: ../glade/gbwidgets/gbwindow.c:272
msgid "The default height of the window"
msgstr "Implicitní výška okna"

#: ../glade/gbwidgets/gbwindow.c:278
msgid "Resizable:"
msgstr "Lze měnit velikost:"

#: ../glade/gbwidgets/gbwindow.c:279
msgid "If the window can be resized"
msgstr "Jestli lze měnit velikost okna"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be shrunk"
msgstr "Jestli lze okno zmenšovat"

#: ../glade/gbwidgets/gbwindow.c:287
msgid "Grow:"
msgstr "Růst:"

#: ../glade/gbwidgets/gbwindow.c:288
msgid "If the window can be enlarged"
msgstr "Jestli lze okno zvětšovat"

#: ../glade/gbwidgets/gbwindow.c:293
msgid "Auto-Destroy:"
msgstr "Automaticky zničit:"

#: ../glade/gbwidgets/gbwindow.c:294
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Jestli je okno zničeno, když je zničen i rodič, na němž je závislé"

#: ../glade/gbwidgets/gbwindow.c:298
msgid "The icon for this window"
msgstr "Ikona pro toto okno"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "Role:"
msgstr "Role:"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "A unique identifier for the window to be used when restoring a session"
msgstr "Jedinečný identifikátor okna, používaný při obnovování sezení"

#: ../glade/gbwidgets/gbwindow.c:308
msgid "Decorated:"
msgstr "Dekorováno:"

#: ../glade/gbwidgets/gbwindow.c:309
msgid "If the window should be decorated by the window manager"
msgstr "Jestli má být okno dekorováno správcem oken"

#: ../glade/gbwidgets/gbwindow.c:312
msgid "Skip Taskbar:"
msgstr "Přeskočit lištu úloh"

#: ../glade/gbwidgets/gbwindow.c:313
msgid "If the window should not appear in the task bar"
msgstr "Jestli se okno nemá objevit v liště úloh"

#: ../glade/gbwidgets/gbwindow.c:316
msgid "Skip Pager:"
msgstr "Přeskočit pager:"

#: ../glade/gbwidgets/gbwindow.c:317
msgid "If the window should not appear in the pager"
msgstr "Jestli se okno nemá objevit v pageru"

#: ../glade/gbwidgets/gbwindow.c:320
msgid "Gravity:"
msgstr "Gravity:"

#: ../glade/gbwidgets/gbwindow.c:321
msgid "The reference point to use when the window coordinates are set"
msgstr "Referenční bod, který používat při nastavení souřadnic okna"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "Focus On Map:"
msgstr "Zaměřit při namapování:"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "If the window should receive the input focus when it is mapped"
msgstr "Jestli má být okno aktivováno, když je namapováno"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "Urgency Hint:"
msgstr "Nápověda naléhavosti:"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "If the window should be brought to the user's attention"
msgstr "Jestli má být na okno upozorněn uživatel"

#: ../glade/gbwidgets/gbwindow.c:1232
msgid "Window"
msgstr "Okno"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:75
msgid "Error"
msgstr "Chyba"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Systémová chyba"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "Chyba při otvírání souboru"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "Chyba při čtení souboru"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "Chyba při zápisu souboru"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "Neplatný adresář"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "Neplatná hodnota"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "Nesprávná XML entita"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "Očekávána zahajovací značka"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "Očekávána ukončovací značka"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "Očekávána znaková data"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "Chybějící class ID"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Neznámá třída"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "Neplatná komponenta"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "Neočekávaný konec souboru"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Neznámý chybový kód"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Ovládán čím"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Ovladač pro"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Nápis pro"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Nadepsán čím"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Člen čeho"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Uzel potomkem čeho"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr "Pokračuje do"

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr "Pokračuje z"

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr "Podpokno okna"

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr "Vkládá"

#: ../glade/glade_atk.c:130
msgid "Embedded By"
msgstr "Vloženo do"

#: ../glade/glade_atk.c:131
msgid "Popup For"
msgstr "Vyskakovací okno pro"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr "Rodičovské okno okna"

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Vztah: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "Widget"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:773
#: ../glade/property.c:776
msgid "Name:"
msgstr "Název:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr "Název widgetu, který se předá pomocným technologiím"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "Popis:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr "Popis widgetu, který bude předán pomocným technologiím"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Titulek tabulky:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "Titulek tabulky, který se předá pomocným technologiím"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Výběr widgetů s tímto vztahem"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Kliknutí"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Stisknutí"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Uvolnění"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr "Zadejte popis akce, který se předá pomocným technologiím"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Schránka"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Musíte vybrat widget, do kterého chcete vkládat"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "Nemohu vkládat do oken či dialogů."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"Nemohu vložit do vybraného widgetu, jelikož\n"
"je vytvářen automaticky svým rodičem."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr "Do menu nebo lišty s menu lze vkládat pouze položky menu."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr "Do prostoru akcí dialogu lze vkládat pouze tlačítka."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "Do widgetu GnomeDock lze vkládat pouze widgety GnomeDockItem."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "Nad widgety GnomeDockItem lze vkládat pouze widgety GnomeDockItem."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr ""
"Promiňte - vkládání nad widgety GnomeDockItem není zatím implementováno."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "Widgety GnomeDockItem lze pouze vložit do widgetu GnomeDock."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:633
msgid "_New"
msgstr "_Nový"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Vytvořit nový soubor"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "Zasta_ralé"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "Zákla_dní GTK+"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "Dod_atečné GTK+"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Vyberte klávesovou zkratku"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Klávesy"

#: ../glade/glade_menu_editor.c:395
msgid "Menu Editor"
msgstr "Editor menu"

#: ../glade/glade_menu_editor.c:412
msgid "Type"
msgstr "Typ"

#: ../glade/glade_menu_editor.c:413
msgid "Accelerator"
msgstr "Akcelerátor"

#: ../glade/glade_menu_editor.c:414
msgid "Name"
msgstr "Název"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:1499
msgid "Handler"
msgstr "Obsluha"

#: ../glade/glade_menu_editor.c:416 ../glade/property.c:102
msgid "Active"
msgstr "Aktivní"

#: ../glade/glade_menu_editor.c:417
msgid "Group"
msgstr "Skupina"

#: ../glade/glade_menu_editor.c:418
msgid "Icon"
msgstr "Ikona"

#: ../glade/glade_menu_editor.c:459
msgid "Move the item and its children up one place in the list"
msgstr "Přesun položky a jejich potomků o jedno místo výše v seznamu"

#: ../glade/glade_menu_editor.c:471
msgid "Move the item and its children down one place in the list"
msgstr "Přesun položky a jejich potomků o jedno místo níže v seznamu"

#: ../glade/glade_menu_editor.c:483
msgid "Move the item and its children up one level"
msgstr "Přesun položky a jejich potomků o úroveň výše"

#: ../glade/glade_menu_editor.c:495
msgid "Move the item and its children down one level"
msgstr "Přesun položky a jejich potomků o úroveň níže"

#: ../glade/glade_menu_editor.c:525
msgid "The stock item to use."
msgstr "Standardní položka k použití."

#: ../glade/glade_menu_editor.c:528 ../glade/glade_menu_editor.c:643
msgid "Stock Item:"
msgstr "Standardní položka:"

#: ../glade/glade_menu_editor.c:641
msgid "The stock Gnome item to use."
msgstr "Standardní položka GNOME k použití."

#: ../glade/glade_menu_editor.c:746
msgid "The text of the menu item, or empty for separators."
msgstr "Text položky menu, prázdné pro oddělovače."

#: ../glade/glade_menu_editor.c:770 ../glade/property.c:777
msgid "The name of the widget"
msgstr "Název widgetu"

#: ../glade/glade_menu_editor.c:791
msgid "The function to be called when the item is selected"
msgstr "Funkce volaná při výběru této položky"

#: ../glade/glade_menu_editor.c:793 ../glade/property.c:1547
msgid "Handler:"
msgstr "Obsluha:"

#: ../glade/glade_menu_editor.c:812
msgid "An optional icon to show on the left of the menu item."
msgstr "Volitelná ikona zobrazovaná po levé straně položky menu."

#: ../glade/glade_menu_editor.c:935
msgid "The tip to show when the mouse is over the item"
msgstr "Tip zobrazovaný při umístění myši nad položkou"

#: ../glade/glade_menu_editor.c:937 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Tip:"

#: ../glade/glade_menu_editor.c:958
msgid "_Add"
msgstr "Přid_at"

#: ../glade/glade_menu_editor.c:963
msgid "Add a new item below the selected item."
msgstr "Přidat novou položku pod vybranou položku."

#: ../glade/glade_menu_editor.c:968
msgid "Add _Child"
msgstr "Přidat poto_mka"

#: ../glade/glade_menu_editor.c:973
msgid "Add a new child item below the selected item."
msgstr "Přidat nového potomka k vybrané položce."

#: ../glade/glade_menu_editor.c:979
msgid "Add _Separator"
msgstr "Přidat _oddělovač"

#: ../glade/glade_menu_editor.c:984
msgid "Add a separator below the selected item."
msgstr "Přidat oddělovač pod vybranou položku."

#: ../glade/glade_menu_editor.c:989 ../glade/glade_project_window.c:242
msgid "_Delete"
msgstr "_Odstranit"

#: ../glade/glade_menu_editor.c:994
msgid "Delete the current item"
msgstr "Odstranit aktuální položku"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:1000
msgid "Item Type:"
msgstr "Typ položky:"

#: ../glade/glade_menu_editor.c:1016
msgid "If the item is initially on."
msgstr "Jestli je položka implicitně zapnuta."

#: ../glade/glade_menu_editor.c:1018
msgid "Active:"
msgstr "Aktivní:"

#: ../glade/glade_menu_editor.c:1023 ../glade/glade_menu_editor.c:1638
#: ../glade/property.c:2216 ../glade/property.c:2226
msgid "No"
msgstr "Ne"

#: ../glade/glade_menu_editor.c:1037
msgid "The radio menu item's group"
msgstr "Skupina položky při výběru"

#: ../glade/glade_menu_editor.c:1054 ../glade/glade_menu_editor.c:2414
#: ../glade/glade_menu_editor.c:2554
msgid "Radio"
msgstr "Radio"

#: ../glade/glade_menu_editor.c:1061 ../glade/glade_menu_editor.c:2412
#: ../glade/glade_menu_editor.c:2552
msgid "Check"
msgstr "Check"

#: ../glade/glade_menu_editor.c:1068 ../glade/property.c:102
msgid "Normal"
msgstr "Normální"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1077
msgid "Accelerator:"
msgstr "Akcelerátor:"

#: ../glade/glade_menu_editor.c:1114 ../glade/property.c:1682
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1119 ../glade/property.c:1685
msgid "Shift"
msgstr "Shift"

#: ../glade/glade_menu_editor.c:1124 ../glade/property.c:1688
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1129 ../glade/property.c:1695
msgid "Key:"
msgstr "Klávesa:"

#: ../glade/glade_menu_editor.c:1135 ../glade/property.c:1674
msgid "Modifiers:"
msgstr "Modifikátory:"

#: ../glade/glade_menu_editor.c:1638 ../glade/glade_menu_editor.c:2419
#: ../glade/glade_menu_editor.c:2562 ../glade/property.c:2216
msgid "Yes"
msgstr "Ano"

#: ../glade/glade_menu_editor.c:2008
msgid "Select icon"
msgstr "Vybrat ikonu"

#: ../glade/glade_menu_editor.c:2353 ../glade/glade_menu_editor.c:2714
msgid "separator"
msgstr "oddělovač"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3638 ../glade/glade_project_window.c:369
#: ../glade/property.c:5115
msgid "New"
msgstr "Nový"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Výběr"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Adresář projektu není nastaven.\n"
"Nastavte jej prosím pomocí dialogu Volby projektu.\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Adresář zdrojů není nastaven.\n"
"Nastavte jej prosím pomocí dialogu Volby projektu.\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"Neplatný adresář zdrojů.\n"
"\n"
"Adresář zdrojů musí být adresář projektu\n"
"nebo jeho podadresář.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Adresář obrázků není nastaven.\n"
"Nastavte jej prosím pomocí dialogu Volby projektu.\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "Promiňte - generování zdrojů pro %s není zatím implementováno"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""
"Váš projekt používá zastaralé widgety, které Gtkmm-2\n"
"nepodporuje. Najděte tyto widgety ve svém projektu\n"
"a použijte jejich náhrady."

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"Chyba při spuštění glade-- pro generování zdrojového kódu C++.\n"
"Zkontrolujte, jestli máte glade-- nainstalován a jestli je ve vaší PATH.\n"
"Pak zkuste spustit 'glade-- <soubor_projektu.glade>' v terminálu."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"Chyba při spuštění gate pro generování zdrojového kódu Ada95.\n"
"Zkontrolujte, jestli máte gate nainstalován a jestli je ve vaší PATH.\n"
"Pak zkuste spustit 'gate <soubor_projektu.glade>' v terminálu."

#: ../glade/glade_project.c:571
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"Chyba při spuštění glade2perl pro generování zdrojového kódu Perl.\n"
"Zkontrolujte, jestli máte glade2perl nainstalován a jestli je ve vaší\n"
"PATH. Pak zkuste spustit 'glade2perl <soubor_projektu.glade>' v terminálu."

#: ../glade/glade_project.c:594
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"Chyba při spuštění eglade pro generování zdrojového kódu Eiffel.\n"
"Zkontrolujte, jestli máte eglade nainstalován a jestli je ve vaší\n"
"PATH. Pak zkuste spustit 'eglade <soubor_projektu.glade>' v terminálu."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Adresář obrázků není nastaven.\n"
"Nastavte jej prosím pomocí dialogu Volby projektu.\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "Chyba při zápisu XML souboru\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:385
#: ../glade/glade_project_window.c:890
msgid "Project Options"
msgstr "Volby projektu"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "Obecné"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Základní volby:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "Adresář projektu"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Adresář projektu:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Výběr..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "Název tohoto projektu"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Název projektu:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "Název tohoto programu"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "Soubor projektu"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Soubor projektu:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Podadresáře:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "Adresář kam uložit vytvořený zdrojový kód"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Zdrojový adresář:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "Adresář kam uložit obrázky"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Adresář obrázků:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "Licence, která je přidána na začátek všech vytvořených souborů"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Jazyk:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "GNOME:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Povolit podporu Gnome"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Jestli se bude vytvářet aplikace pro GNOME"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Povolit podporu GNOME DB"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Jestli se bude vytvářet databázová aplikace pro GNOME"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "Volby C"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr "<b>Upozornění:</b> pro velké aplikace se doporučuje použít libglade."

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Obecné volby:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Podpora gettextu"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "Jestli jsou řetězce označeny pro překlad"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "Nastavit názvy widgetů"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Jestli jsou ve zdrojovém kódu nastaveny názvy widgetů"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Zálohovat zdrojové soubory"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Jestli se vytvářejí kopie starých zdrojových souborů"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Povolit podporu Nápovědy GNOME"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Jestli má být zahrnuta podpora systému nápovědy prostředí GNOME"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Volby výstupu souborů:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "Generovat soubor main.c"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr ""
"Jestli se má generovat soubor main.c obsahující funkci main(), pokud ještě "
"neexistuje"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Generovat podpůrné funkce"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Jestli se mají generovat podpůrné funkce"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Generovat soubory pro sestavení"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Jestli se mají generovat soubory pro sestavení zdrojového kódu, včetně "
"souborů Makefile.am a configure.in, pokud ještě neexistují"

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "Funkce pro vyváření rozhraní:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "Soubor, do kterého jsou zapsány funkce pro tvorbu rozhraní"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Zdrojový soubor:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr ""
"Soubor, do kterého jsou zapisovány deklarace funkcí pro vytvoření rozhraní"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Soubor hlavičky:"

#: ../glade/glade_project_options.c:594
msgid "Source file for interface creation functions"
msgstr "Zdrojový soubor pro funkce pro vyváření rozhraní"

#: ../glade/glade_project_options.c:595
msgid "Header file for interface creation functions"
msgstr "Hlavičkový soubor pro funkce pro vyváření rozhraní"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Obsluhy signálů a funkce zpětného volání:"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr ""
"Soubor, do kterého jsou zapisovány prázdné obsluhy signálů a funkce pro "
"zpětné volání"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"Soubor, do kterého jsou zapisovány deklarace obsluh signálů a funkcí pro "
"zpětné volání"

#: ../glade/glade_project_options.c:640
msgid "Source file for signal handler and callback functions"
msgstr "Zdrojový soubor pro funkce obsluhy signálů a zpětných volání"

#: ../glade/glade_project_options.c:641
msgid "Header file for signal handler and callback functions"
msgstr "Hlavičkový soubor pro funkce obsluhy signálů a zpětných volání"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Podpůrné funkce:"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "Soubor, do kterého jsou zapisovány podpůrné funkce"

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "Soubor, do kterého jsou zapisovány deklarace podpůrných funkcí"

#: ../glade/glade_project_options.c:686
msgid "Source file for support functions"
msgstr "Zdrojový soubor pro podpůrné funkce"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr "Hlavičkový soubor pro podpůrné funkce"

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "Volby LibGlade"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Přeložitelné řetězce:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""
"<b>Upozornění:</b> tento přepínač je zastaralý - použijte místo něj intltool."

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Ukládat přeložitelné řetězce"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Zdali jsou přeložitelné řetězce uloženy ve zvláštním souboru se zdroji C, "
"což umožní překlad rozhraní načítaných knihovnou libglade."

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "Zdrojový soubor C, do kterého se mají ukládat přeložitelné řetězce"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Soubor:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Vyberte projektový adresář"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Musíte nastavit volbu Souboru přeložitelných řetězců"

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Musíte nastavit volbu Adresáře projektu"

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Musíte nastavit volbu Souboru projektu"

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Musíte nastavit volbu Názvu projektu"

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Musíte nastavit volbu Názvu programu"

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Musíte nastavit volbu Adresáře zdrojů"

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Musíte nastavit volbu Adresáře obrázků"

#: ../glade/glade_project_window.c:187
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"Nemohu zobrazit soubor s nápovědou: %s\n"
"\n"
"Chyba: %s"

#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:635
msgid "Create a new project"
msgstr "Vytvořit nový projekt"

#: ../glade/glade_project_window.c:219 ../glade/glade_project_window.c:655
#: ../glade/glade_project_window.c:906
msgid "_Build"
msgstr "_Sestavit"

#: ../glade/glade_project_window.c:220 ../glade/glade_project_window.c:666
msgid "Output the project source code"
msgstr "Generovat zdrojový kód projektu"

#: ../glade/glade_project_window.c:226 ../glade/glade_project_window.c:669
msgid "Op_tions..."
msgstr "_Volby..."

#: ../glade/glade_project_window.c:227 ../glade/glade_project_window.c:678
msgid "Edit the project options"
msgstr "Úprava voleb projektu"

#: ../glade/glade_project_window.c:242 ../glade/glade_project_window.c:717
msgid "Delete the selected widget"
msgstr "Odstranit vybraný widget"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:728
msgid "Show _Palette"
msgstr "Zobrazovat _paletu"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:733
msgid "Show the palette of widgets"
msgstr "Zobrazit paletu widgetů"

#: ../glade/glade_project_window.c:266 ../glade/glade_project_window.c:738
msgid "Show Property _Editor"
msgstr "Zobrazovat _editor vlastností"

#: ../glade/glade_project_window.c:267 ../glade/glade_project_window.c:744
msgid "Show the property editor"
msgstr "Zobrazovat editor vlastností"

#: ../glade/glade_project_window.c:273 ../glade/glade_project_window.c:748
msgid "Show Widget _Tree"
msgstr "Zobrazovat s_trom widgetů"

#: ../glade/glade_project_window.c:274 ../glade/glade_project_window.c:754
#: ../glade/main.c:82 ../glade/main.c:116
msgid "Show the widget tree"
msgstr "Zobrazovat strom widgetů"

#: ../glade/glade_project_window.c:280 ../glade/glade_project_window.c:758
msgid "Show _Clipboard"
msgstr "Zobrazit s_chránku"

#: ../glade/glade_project_window.c:281 ../glade/glade_project_window.c:764
#: ../glade/main.c:86 ../glade/main.c:120
msgid "Show the clipboard"
msgstr "Zobrazit schránku"

#: ../glade/glade_project_window.c:299
msgid "Show _Grid"
msgstr "Zobrazovat _mřížku"

#: ../glade/glade_project_window.c:300 ../glade/glade_project_window.c:800
msgid "Show the grid (in fixed containers only)"
msgstr "Zobrazovat mřížky (pouze ve fixních kontejnerech)"

#: ../glade/glade_project_window.c:306
msgid "_Snap to Grid"
msgstr "Přita_hovat k mřížce"

#: ../glade/glade_project_window.c:307
msgid "Snap widgets to the grid"
msgstr "Přitahovat widgety k mřížce"

#: ../glade/glade_project_window.c:313 ../glade/glade_project_window.c:772
msgid "Show _Widget Tooltips"
msgstr "Zobrazovat tipy pr_vků"

#: ../glade/glade_project_window.c:314 ../glade/glade_project_window.c:780
msgid "Show the tooltips of created widgets"
msgstr "Zobrazení tipů vytvořených widgetů"

#: ../glade/glade_project_window.c:323 ../glade/glade_project_window.c:803
msgid "Set Grid _Options..."
msgstr "Nastavit v_olby mřížky..."

#: ../glade/glade_project_window.c:324
msgid "Set the grid style and spacing"
msgstr "Nastavení stylu a rozestupu mřížky"

#: ../glade/glade_project_window.c:330 ../glade/glade_project_window.c:824
msgid "Set Snap O_ptions..."
msgstr "Nastavit volby _přitahování..."

#: ../glade/glade_project_window.c:331
msgid "Set options for snapping to the grid"
msgstr "Nastavení voleb přitahování k mřížce"

#: ../glade/glade_project_window.c:343
msgid "_FAQ"
msgstr "_FAQ"

#: ../glade/glade_project_window.c:344
msgid "View the Glade FAQ"
msgstr "Zobrazit často kladené otázky o Glade"

#. create File menu
#: ../glade/glade_project_window.c:358 ../glade/glade_project_window.c:626
msgid "_Project"
msgstr "_Projekt"

#: ../glade/glade_project_window.c:369 ../glade/glade_project_window.c:873
#: ../glade/glade_project_window.c:1055
msgid "New Project"
msgstr "Nový projekt"

#: ../glade/glade_project_window.c:374
msgid "Open"
msgstr "Otevřít"

#: ../glade/glade_project_window.c:374 ../glade/glade_project_window.c:878
#: ../glade/glade_project_window.c:1116
msgid "Open Project"
msgstr "Otevřít projekt"

#: ../glade/glade_project_window.c:379
msgid "Save"
msgstr "Uložit"

#: ../glade/glade_project_window.c:379 ../glade/glade_project_window.c:882
#: ../glade/glade_project_window.c:1481
msgid "Save Project"
msgstr "Uložit projekt"

#: ../glade/glade_project_window.c:385
msgid "Options"
msgstr "Volby"

#: ../glade/glade_project_window.c:390
msgid "Build"
msgstr "Sestavit"

#: ../glade/glade_project_window.c:390
msgid "Build the Source Code"
msgstr "Generovat zdrojový kód"

#: ../glade/glade_project_window.c:639
msgid "Open an existing project"
msgstr "Otevřít existující projekt"

#: ../glade/glade_project_window.c:643
msgid "Save project"
msgstr "Uložit projekt"

#: ../glade/glade_project_window.c:688
msgid "Quit Glade"
msgstr "Ukončit Glade"

#: ../glade/glade_project_window.c:702
msgid "Cut the selected widget to the clipboard"
msgstr "Vyjmout vybraný widget do schránky"

#: ../glade/glade_project_window.c:707
msgid "Copy the selected widget to the clipboard"
msgstr "Kopírovat vybraný widget do schránky"

#: ../glade/glade_project_window.c:712
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Vložit widget ze schránky přes vybraný widget"

#: ../glade/glade_project_window.c:784
msgid "_Grid"
msgstr "_Mřížka"

#: ../glade/glade_project_window.c:792
msgid "_Show Grid"
msgstr "_Zobrazovat mřížku"

#: ../glade/glade_project_window.c:809
msgid "Set the spacing between grid lines"
msgstr "Nastavení rozestupu mezi linkami mřížky"

#: ../glade/glade_project_window.c:812
msgid "S_nap to Grid"
msgstr "_Přitahovat k mřížce"

#: ../glade/glade_project_window.c:820
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "Přitahovat widgetů k mřížce (pouze ve fixních kontejnerech)"

#: ../glade/glade_project_window.c:830
msgid "Set which parts of a widget snap to the grid"
msgstr "Nastavení, které části widgetu se mají přitahovat k mřížce"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:855
msgid "_About..."
msgstr "O _aplikaci..."

#: ../glade/glade_project_window.c:896
msgid "Optio_ns"
msgstr "Vol_by"

#: ../glade/glade_project_window.c:900
msgid "Write Source Code"
msgstr "Zapsat zdrojový kód"

#: ../glade/glade_project_window.c:992 ../glade/glade_project_window.c:1697
#: ../glade/glade_project_window.c:1986
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:999
msgid "Are you sure you want to create a new project?"
msgstr "Jste si jisti, že chcete vytvořit nový projekt?"

#: ../glade/glade_project_window.c:1059
msgid "New _GTK+ Project"
msgstr "Nový projekt _GTK+"

#: ../glade/glade_project_window.c:1060
msgid "New G_NOME Project"
msgstr "Nový projekt G_NOME"

#: ../glade/glade_project_window.c:1063
msgid "Which type of project do you want to create?"
msgstr "Jaký typ projektu chcete vytvořit?"

#: ../glade/glade_project_window.c:1097
msgid "New project created."
msgstr "Nový projekt vytvořen."

#: ../glade/glade_project_window.c:1187
msgid "Project opened."
msgstr "Projekt otevřen."

#: ../glade/glade_project_window.c:1201
msgid "Error opening project."
msgstr "Chyba při otevírání projektu."

#: ../glade/glade_project_window.c:1265
msgid "Errors opening project file"
msgstr "Chyby při otevírání souboru projektu"

#: ../glade/glade_project_window.c:1271
msgid " errors opening project file:"
msgstr " chyby při otevírání souboru projektu:"

#: ../glade/glade_project_window.c:1344
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"V této chvíli není otevřený žádný projekt.\n"
"Vytvořte nový projekt pomocí příkazu Projekt/Nový."

#: ../glade/glade_project_window.c:1548
msgid "Error saving project"
msgstr "Chyba při ukládání projektu"

#: ../glade/glade_project_window.c:1550
msgid "Error saving project."
msgstr "Chyba při ukládání projektu."

#: ../glade/glade_project_window.c:1556
msgid "Project saved."
msgstr "Projekt uložen."

#: ../glade/glade_project_window.c:1626
msgid "Errors writing source code"
msgstr "Chyba při zapisování zdrojového kódu"

#: ../glade/glade_project_window.c:1628
msgid "Error writing source."
msgstr "Chyba při zapisování zdrojů."

#: ../glade/glade_project_window.c:1634
msgid "Source code written."
msgstr "Zdrojový kód zapsán."

#: ../glade/glade_project_window.c:1665
msgid "System error message:"
msgstr "Chybová zpráva systému:"

#: ../glade/glade_project_window.c:1704
msgid "Are you sure you want to quit?"
msgstr "Jste si jisti, že chcete skončit?"

#: ../glade/glade_project_window.c:1988 ../glade/glade_project_window.c:2048
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "© 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1989 ../glade/glade_project_window.c:2047
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr "Glade je návrhář uživatelského rozhraní pro GTK+ a prostředí GNOME."

#: ../glade/glade_project_window.c:2018
msgid "About Glade"
msgstr "O aplikaci Glade"

#: ../glade/glade_project_window.c:2103
msgid "<untitled>"
msgstr "<nepojmenován>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Databázový prohlížeč"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Combo svázané s daty"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr "GnomeDbConnectionProperties"

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Výběr připojení"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "Nastavení DSN"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "Průvodce nastavením DSN"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr "Zvýraznit text:"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr "Je-li vybráno, bude text ve widgetu zvýrazněn"

#: ../glade/gnome-db/gnomedbeditor.c:178
msgid "GnomeDbEditor"
msgstr "GnomeDbEditor"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Prohlížeč databázových chyb"

#: ../glade/gnome-db/gnomedberrordlg.c:219
msgid "Database error dialog"
msgstr "Dialog databázových chyb"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Formulář"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr "Text uvnitř šedého pruhu"

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr "Šedý pruh"

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Mřížka svázaná s daty"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Seznam svázaný s daty"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Widget pro přihlášení do databáze"

#: ../glade/gnome-db/gnomedblogindlg.c:78
msgid "Login"
msgstr "Přihlášení"

#: ../glade/gnome-db/gnomedblogindlg.c:221
msgid "Database login dialog"
msgstr "Dialog přihlášení do databáze"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Výběr poskytovatele"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr "GnomeDbQueryBuilder"

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Výběr zdroje dat"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Editor tabulky"

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Povolit plovoucí:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Jestli jsou povoleny plovoucí položky ukotvení"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Přidat kotevní pás nahoru"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Přidat kotevní pás dolu"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Přidat kotevní pás doleva"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Přidat kotevní pás doprava"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Přidat plovoucí položku pro ukotvení"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Ukotvení GNOME"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Uzamčeno:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Jestli je kotvící položka uzamčena na místě"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Exkluzivní:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "Jestli je ukotvená položka vždy jediná ve svém pásu"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Nikdy plovoucí:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "Jestli je ukotvené položce umožněno plavat ve svém vlastním okně"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Nikdy vertikální:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "Jestli je položce ukotvení umožněno vertikální umístění"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Nikdy horizontální:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "Jestli je položce ukotvení umožněno horizontální umístění"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "Typ stínování okolo ukotvených položek"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "Orientace plovoucí položky ukotvení"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Přidat ukotvenou položku před"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Přidat ukotvenou položku za"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Položka ukotvení GNOME"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr ""
"Dodatečné informace, jako třeba popis balíčku a jeho domovská webová stránka"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Dialog GNOME O aplikaci"

#: ../glade/gnome/gnomeapp.c:171
msgid "New File"
msgstr "Nový soubor"

#: ../glade/gnome/gnomeapp.c:173
msgid "Open File"
msgstr "Otevřít soubor"

#: ../glade/gnome/gnomeapp.c:175
msgid "Save File"
msgstr "Uložit soubor"

#: ../glade/gnome/gnomeapp.c:204
msgid "Status Bar:"
msgstr "Stavová lišta:"

#: ../glade/gnome/gnomeapp.c:205
msgid "If the window has a status bar"
msgstr "Jestli má okno stavovou lištu"

#: ../glade/gnome/gnomeapp.c:206
msgid "Store Config:"
msgstr "Ukládat nastavení:"

#: ../glade/gnome/gnomeapp.c:207
msgid "If the layout is saved and restored automatically"
msgstr "Jestli je rozvržení uloženo a obnoveno automaticky"

#: ../glade/gnome/gnomeapp.c:443
msgid "Gnome Application Window"
msgstr "Aplikační okno GNOME"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Stavová zpráva."

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "Průběh:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "Jestli má aplikační lišta indikátor průběhu"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Stav:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr "Jestli má aplikační lišta oblast pro stavové zprávy a vstup uživatele"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Aplikační lišta GNOME"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Vyhlazování:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr ""
"Jestli má plátno povoleno vyhlazování, pro zjemnění hran písma a grafiky"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "Minimální souřadnice X"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "Minimální souřadnice Y"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2:"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "Maximální souřadnice X"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "Maximální souřadnice Y"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Pixelů na jednotku:"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "Počet bodů odpovídajících jedné jednotce"

#: ../glade/gnome/gnomecanvas.c:248
msgid "GnomeCanvas"
msgstr "Plátno GNOME"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Rozptyl:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Jestli má být na vzorek použit rozptyl, aby byl přesnější"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Vyberte barvu"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Výběr barvy GNOME"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "Nemohu vytvořit ovládací prvek Bonobo"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Nový ovládací prvek Bonobo"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Vyberte ovládací prvek Bonobo"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3902
msgid "Description"
msgstr "Popis"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Ovládací prvek Bonobo"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Zobrazovat čas:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Jestli se kromě data zobrazuje i čas"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "24hodinový formát:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Jestli je čas zobrazen ve 24hodinovém formátu"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Nižší hodina:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "Nejnižší hodina, která má být zobrazena ve vyskakovacím okně"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Vyšší hodina:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "Nejvyšší hodina, která má být zobrazena ve vyskakovacím okně"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "GnomeDateEdit"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "Auto Close:"
msgstr "Auto zavření:"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "If the dialog closes when any button is clicked"
msgstr "Jestli se dialog automaticky zavře po stisku jakéhokoli tlačítka"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "Hide on Close:"
msgstr "Skrýt při zavření:"

#: ../glade/gnome/gnomedialog.c:156 ../glade/gnome/gnomemessagebox.c:193
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "Jestli je dialog skryt při zavření, místo zničení"

#: ../glade/gnome/gnomedialog.c:342
msgid "Gnome Dialog Box"
msgstr "Dialogový box GNOME"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Nový průvodce GNOME"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Zobrazovat nápovědu"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Zobrazovat tlačítko s nápovědou"

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Přidat úvodní stranu"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Přidat konečnou stranu"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Průvodce"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "Nadpis stránky"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "Hlavní text stránky, který představí průvodce lidem"

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Barva nadpisu:"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "Barva textu nadpisu"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Barva textu:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "Barva hlavního textu"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "Barva pozadí stránky"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Barva pozadí loga:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "Barva pozadí okolo loga"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Barva textového boxu:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "Barva pozadí okolo oblasti s hlavním textem"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Obrázek loga:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "Logo zobrazené na pravém horním okraji stránky"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Vodotisk na straně:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "Hlavní obrázek zobrazený na straně stránky."

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Vodotisk nahoře:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr "Hlavní obrázek zobrazený nahoře na stránce."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Úvodní nebo konečná strana průvodce"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Barva pozadí obsahu:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "Barva pozadí okolo titulku"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "Obrázek zobrazený nahoře na stránce"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Standardní strana průvodce"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "ID historie:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "ID, pod kterým se mají ukládat položky historie"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Max. uloženo:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "Maximální počet uložených položek historie"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Políčko GNOME"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "Nadpis dialogu pro výběr souboru"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Adresář:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Jestli je požadován adresář místo souboru"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "Jestli by měl být dialog pro výběr souboru modální"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr "Používat FileChooser:"

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr "Používat nový widget GtkFileChooser místo GtkFileSelection"

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Políčko souboru GNOME"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "Text náhledu zobrazený v dialogu pro výběr písma"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Režim:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "Co se má zobrazovat v tlačítko pro výběr písma"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "Velikost písma, která se má použít v tlačítku pro výběr písma"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Výběr písma GNOME"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "URL zobrazené po kliknutí na tlačítko"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "Text zobrazený v tlačítku"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Tlačítko hypertextového odkazu GNOME"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Políčko ikony GNOME"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "Režim výběru"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Šířka ikony:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "Šířka každé ikony"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "Počet bodů mezi každým řádkem ikon"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "Počet bodů mezi sloupci ikon"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Okraj ikon:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "Počet bodů okolo ikon (nepoužívá se?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Rozestup textu:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "Počet bodů mezi textem a ikonou"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Upravitelný text:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Jestli lze text ikony upravit uživatelem"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Statický text:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr ""
"Jestli je text ikony statický, v kterémžto případě text nebude kopírován "
"widgetem GnomeIconList"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Seznam ikon"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Výběr ikony"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "Message Type:"
msgstr "Typ zprávy:"

#: ../glade/gnome/gnomemessagebox.c:176
msgid "The type of the message box"
msgstr "Typ boxu zprávy"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "Message:"
msgstr "Zpráva:"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "The message to display"
msgstr "Zobrazená zpráva"

#: ../glade/gnome/gnomemessagebox.c:499
msgid "Gnome Message Box"
msgstr "Box se zprávou pro GNOME"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "Název souboru obrázku"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Škálovaný:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Jestli je obrázek škálovaný"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Šířka škálování:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "Na jakou šířku se má obrázek zvětšit"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Výška škálování:"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "Na jakou výšku se má obrázek zvětšit"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Obrázek GNOME"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Náhled:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Jestli je zobrazen malý náhled na obrázek"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmapEntry"

#: ../glade/gnome/gnomepropertybox.c:113
msgid "New GnomePropertyBox"
msgstr "Nový widget GnomePropertyBox"

#: ../glade/gnome/gnomepropertybox.c:366
msgid "Property Dialog Box"
msgstr "Dialogové okno vlastností"

#: ../glade/main.c:70 ../glade/main.c:104
msgid "Write the source code and exit"
msgstr "Zapsat zdrojový kód a skončit"

#: ../glade/main.c:74 ../glade/main.c:108
msgid "Start with the palette hidden"
msgstr "Začít se skrytou paletou"

#: ../glade/main.c:78 ../glade/main.c:112
msgid "Start with the property editor hidden"
msgstr "Začít se skrytým editorem vlastností"

#: ../glade/main.c:460
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade: Pro volby '-w' nebo '--write-source' musí být nastaven soubor XML.\n"

#: ../glade/main.c:474
msgid "glade: Error loading XML file.\n"
msgstr "glade: Chyba při čtení souboru XML.\n"

#: ../glade/main.c:481
msgid "glade: Error writing source.\n"
msgstr "glade: Chyba při zápisu souboru.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Paleta"

#: ../glade/property.c:73
msgid "private"
msgstr "soukromá"

#: ../glade/property.c:73
msgid "protected"
msgstr "chráněná"

#: ../glade/property.c:73
msgid "public"
msgstr "veřejná"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "Předsvícená"

#: ../glade/property.c:103
msgid "Selected"
msgstr "Vybraná"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Necitlivá"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Když potřebuje okno překreslit"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Při přesunu myši"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Tipy pro přesun myši"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Přesun myši se stiskem kteréhokoli tlačítka"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Přesun myši se stisknutím tlačítka 1"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Přesun myši se stisknutím tlačítka 2"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Přesun myši se stisknutím tlačítka 3"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "Kterékoli tlačítko stisknuto"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "Kterékoli tlačítko uvolněno"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "Kterákoli klávesa stisknuta"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "Kterákoli klávesa uvolněna"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Při vstupu myši do okna"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Při výstupu myši z okna"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Jakákoli změna v zaměření vstupu"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Jakákoli změna ve struktuře okna"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "Jakákoli změna ve vlastnostech X Windows"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Jakákoli změna ve viditelnosti"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "Pro kurzory v programech znajících XInput"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Vlastnosti"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Zabalení"

#: ../glade/property.c:625
msgid "Common"
msgstr "Společné"

#: ../glade/property.c:631
msgid "Style"
msgstr "Styl"

#: ../glade/property.c:637 ../glade/property.c:4646
msgid "Signals"
msgstr "Signály"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Vlastnosti: "

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Vlastnosti: <žádné>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "Třída:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "Třída widgetu"

#: ../glade/property.c:813
msgid "Width:"
msgstr "Šířka:"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""
"Požadovaná šířka widgetu (obvykle se používá ke stanovení minimální šířky)"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Výška:"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""
"Požadovaná výška widgetu (obvykle se používá ke stanovení minimální výšky)"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Viditelný:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "Jestli je widget zpočátku viditelný"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Citlivý:"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "Jestli widget reaguje na vstup"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "Tip zobrazený při umístění myši nad widgetem"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Může být implicitní:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr "Jestli může být widget implicitní akcí v dialogu"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "Je implicitní"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Jestli je widget implicitní akcí v dialogu"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Lze zaměřit:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "Jestli může widget přijímat zaměření vstupu"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Je zaměřený:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "Jestli widget je zaměřen pro vstup"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Události:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "Události systému X, které widget obdrží"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Rozš. události:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "Režim Rozšířených událostí systému X"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Akcelerátory:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Definuje signály, které jsou vysílány při stisku klávesy"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Upravit..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "Šířit:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "Nastavením hodnoty na True se bude styl předávat potomkům widgetu"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Pojmenovaný styl:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "Název stylu, který lze sdílet mezi více widgety"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Písmo:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "Písmo, které se použije na jakýkoli text ve widgetu"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Kopírovat vše"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Popředí:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Pozadí:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "Základ:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Barva popředí"

#: ../glade/property.c:928
msgid "Background color"
msgstr "Barva pozadí"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Barva textu"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Základní barva"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Pixmapa na pozadí:"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "Grafika použitá jako pozadí widgetu"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "Soubor, do kterého se má zapsat zdrojový kód"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Veřejný:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Jestli je widget přidán do datové struktury komponenty"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Oddělená třída:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Vložit podstromu tohoto widgetu do oddělené třídy"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Oddělený soubor:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Vložit widget do odděleného zdrojového souboru"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Viditelnost:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr ""
"Viditelnost widgetů. Veřejné widgety jsou exportovány do globální mapy."

#: ../glade/property.c:1127
msgid "You need to select a color or background to copy"
msgstr "Musíte vybrat barvu nebo pozadí pro kopírování"

#: ../glade/property.c:1146
msgid "Invalid selection in on_style_copy()"
msgstr "Neplatný výběr v on_style_copy()"

#: ../glade/property.c:1188
msgid "You need to copy a color or background pixmap first"
msgstr "Musíte nejprve zkopírovat barvu nebo pixmapu pozadí"

#: ../glade/property.c:1194
msgid "You need to select a color to paste into"
msgstr "Musíte vybrat barvu, do které chcete vkládat"

#: ../glade/property.c:1204
msgid "You need to select a background pixmap to paste into"
msgstr "Musíte vybrat pixmapu pozadí, do kterého chcete vkládat"

#: ../glade/property.c:1456
msgid "Couldn't create pixmap from file\n"
msgstr "Nemohu vytvořit obrázek ze souboru\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1498
msgid "Signal"
msgstr "Signál"

#: ../glade/property.c:1500
msgid "Data"
msgstr "Data"

#: ../glade/property.c:1501
msgid "After"
msgstr "Potom"

#: ../glade/property.c:1502
msgid "Object"
msgstr "Objekt"

#: ../glade/property.c:1533 ../glade/property.c:1697
msgid "Signal:"
msgstr "Signál:"

#: ../glade/property.c:1534
msgid "The signal to add a handler for"
msgstr "Signál, pro který se přidává obsluha"

#: ../glade/property.c:1548
msgid "The function to handle the signal"
msgstr "Funkce pro zpracování signálu"

#: ../glade/property.c:1551
msgid "Data:"
msgstr "Data:"

#: ../glade/property.c:1552
msgid "The data passed to the handler"
msgstr "Data předávaná obsluze"

#: ../glade/property.c:1553
msgid "Object:"
msgstr "Objekt:"

#: ../glade/property.c:1554
msgid "The object which receives the signal"
msgstr "Objekt, který obdrží signál"

#: ../glade/property.c:1555
msgid "After:"
msgstr "Potom:"

#: ../glade/property.c:1556
msgid "If the handler runs after the class function"
msgstr "Jestli se obsluha vykoná až po funkci třídy"

#: ../glade/property.c:1569
msgid "Add"
msgstr "Přidat"

#: ../glade/property.c:1575
msgid "Update"
msgstr "Obnovit"

#: ../glade/property.c:1587
msgid "Clear"
msgstr "Vymazat"

#: ../glade/property.c:1637
msgid "Accelerators"
msgstr "Akcelerátory"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1650
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1651
msgid "Key"
msgstr "Klávesa"

#: ../glade/property.c:1652
msgid "Signal to emit"
msgstr "Vyslaný signál"

#: ../glade/property.c:1696
msgid "The accelerator key"
msgstr "Klávesová zkratka"

#: ../glade/property.c:1698
msgid "The signal to emit when the accelerator is pressed"
msgstr "Signál, který vyslat při stisku klávesové zkratky"

#: ../glade/property.c:1847
msgid "Edit Text Property"
msgstr "Upravit vlastnost textu"

#: ../glade/property.c:1885
msgid "<b>_Text:</b>"
msgstr "<b>_Text:</b>"

#: ../glade/property.c:1895
msgid "T_ranslatable"
msgstr "_Přeložitelný"

#: ../glade/property.c:1899
msgid "Has Context _Prefix"
msgstr "Má _předponu obsahu"

#: ../glade/property.c:1925
msgid "<b>Co_mments For Translators:</b>"
msgstr "<b>_Poznámky pro překladatele:</b>"

#: ../glade/property.c:3892
msgid "Select X Events"
msgstr "Výběr události X"

#: ../glade/property.c:3901
msgid "Event Mask"
msgstr "Maska událostí"

#: ../glade/property.c:4031 ../glade/property.c:4080
msgid "You need to set the accelerator key"
msgstr "Musíte nastavit klávesovou zkratku"

#: ../glade/property.c:4038 ../glade/property.c:4087
msgid "You need to set the signal to emit"
msgstr "Musíte nastavit signál, který se má vyslat"

#: ../glade/property.c:4314 ../glade/property.c:4370
msgid "You need to set the signal name"
msgstr "Musíte nastavit název signálu"

#: ../glade/property.c:4321 ../glade/property.c:4377
msgid "You need to set the handler for the signal"
msgstr "Musíte nastavit obsluhu signálu"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4580
#, c-format
msgid "%s signals"
msgstr "signály %s"

#: ../glade/property.c:4637
msgid "Select Signal"
msgstr "Výběr signálu"

#: ../glade/property.c:4833
msgid "Value:"
msgstr "Hodnota:"

#: ../glade/property.c:4833
msgid "Min:"
msgstr "Min:"

#: ../glade/property.c:4833
msgid "Step Inc:"
msgstr "Krok:"

#: ../glade/property.c:4834
msgid "Page Inc:"
msgstr "Krok stránky:"

#: ../glade/property.c:4834
msgid "Page Size:"
msgstr "Velikost stránky:"

#: ../glade/property.c:4836
msgid "H Value:"
msgstr "Hodnota H:"

#: ../glade/property.c:4836
msgid "H Min:"
msgstr "Minimální H:"

#: ../glade/property.c:4836
msgid "H Max:"
msgstr "Maximální H:"

#: ../glade/property.c:4836
msgid "H Step Inc:"
msgstr "Krok H:"

#: ../glade/property.c:4837
msgid "H Page Inc:"
msgstr "Stránka H:"

#: ../glade/property.c:4837
msgid "H Page Size:"
msgstr "Velikost stránky H:"

#: ../glade/property.c:4839
msgid "V Value:"
msgstr "Hodnota V:"

#: ../glade/property.c:4839
msgid "V Min:"
msgstr "Minimální V:"

#: ../glade/property.c:4839
msgid "V Max:"
msgstr "Maximální V:"

#: ../glade/property.c:4839
msgid "V Step Inc:"
msgstr "Krok V:"

#: ../glade/property.c:4840
msgid "V Page Inc:"
msgstr "Krok stránky V:"

#: ../glade/property.c:4840
msgid "V Page Size:"
msgstr "Velikost stránky V:"

#: ../glade/property.c:4843
msgid "The initial value"
msgstr "Počáteční hodnota"

#: ../glade/property.c:4844
msgid "The minimum value"
msgstr "Minimální hodnota"

#: ../glade/property.c:4845
msgid "The maximum value"
msgstr "Maximální hodnota"

#: ../glade/property.c:4846
msgid "The step increment"
msgstr "Přírůstek kroku"

#: ../glade/property.c:4847
msgid "The page increment"
msgstr "Přírůstek stránky"

#: ../glade/property.c:4848
msgid "The page size"
msgstr "Velikost stránky"

#: ../glade/property.c:5003
msgid "The requested font is not available."
msgstr "Požadované písmo není k dispozici."

#: ../glade/property.c:5052
msgid "Select Named Style"
msgstr "Výběr pojmenovaného stylu"

#: ../glade/property.c:5063
msgid "Styles"
msgstr "Styly"

#: ../glade/property.c:5122
msgid "Rename"
msgstr "Přejmenovat"

#: ../glade/property.c:5150
msgid "Cancel"
msgstr "Zrušit"

#: ../glade/property.c:5270
msgid "New Style:"
msgstr "Nový styl:"

#: ../glade/property.c:5284 ../glade/property.c:5405
msgid "Invalid style name"
msgstr "Neplatný název stylu"

#: ../glade/property.c:5292 ../glade/property.c:5415
msgid "That style name is already in use"
msgstr "Tento název stylu se již používá"

#: ../glade/property.c:5390
msgid "Rename Style To:"
msgstr "Přejmenovat styl na:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"Nemohu přejmenovat soubor:\n"
"  %s\n"
"na:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"Nemohu vytvořit soubor:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "Chyba při zápisu XML souboru\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Soubor s přeložitelnými řetězci generovaný programem Glade.\n"
" * Přidejte tento soubor do POTFILES.in svého projektu.\n"
" * NEPŘEKLÁDEJTE JEJ jako součást své aplikace.\n"
" */\n"
"\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"Neplatný název souboru se zdroji rozhraní: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"Neplatný název hlavičkového souboru rozhraní: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"Neplatný název zdrojového souboru zpětných volání: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"Neplatný název hlavičkového souboru zpětných volání: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"Neplatný název podpůrného zdrojového souboru: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"Neplatný název podpůrného hlavičkového souboru: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"Nemohu připojovat k souboru:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"Chyba při zápisu do souboru:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr "Název souboru musí být nastaven v dialogu Volby projektu."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"Název souboru musí být jednoduchý relativní název.\n"
"Použijte dialog Volby projektu pro jeho nastavení."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Strom widgetů"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "Widget nebyl v boxu nalezen"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "Widget nebyl v tabulce nalezen"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "Widget nebyl ve fixním kontejneru nalezen"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "Widget nebyl v balíkovači nalezen"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"Nemohu přistupovat k souboru:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"Nemohu otevřít soubor:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"Chyba při čtení ze souboru:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"Nemohu vytvořit adresář:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"Nemohu přistupovat k adresáři:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"Neplatný adresář:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Projekty"

#: ../glade/utils.c:1628
msgid "project"
msgstr "projekt"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"Nemohu otevřít adresář:\n"
"  %s\n"
