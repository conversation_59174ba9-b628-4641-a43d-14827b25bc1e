{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "osx": {
        "tasks": [
            {
                "isBuildCommand": true,
                "taskName": "Build Debug",
                "command": "xcodebuild",
                "args": ["-project", "osx/deadbeef.xcodeproj", "-target", "deadbeef", "-configuration", "Debug"]
            },
            {
                "isBuildCommand": true,
                "taskName": "Build Release",
                "command": "xcodebuild",
                "args": ["-project", "osx/deadbeef.xcodeproj", "-target", "deadbeef", "-configuration", "Release"]
            },
            {
                "isTestCommand": true,
                "taskName": "XCTest Debug",
                "command": "xcodebuild",
                "args": ["test", "-project", "osx/deadbeef.xcodeproj", "-scheme", "deadbeef", "-destination", "platform=OS X,arch=x86_64"]
            }
        ]
    },
    "linux": {
        "tasks": [
            {
                "isBuildCommand": true,
                "taskName": "Build Release",
                "command": "${workspaceRoot}/.vscode/linux-build",
                "args": []
            },
            {
                "isBuildCommand": true,
                "taskName": "Clean",
                "command": "make",
                "args": ["clean"]
            }
        ]
    },
    "tasks": [
        {
            "label": "Build Release",
            "command": "${workspaceRoot}/.vscode/linux-build",
            "problemMatcher": [],
            "group": {
                "_id": "build",
                "isDefault": false
            }
        },
        {
            "label": "Clean",
            "command": "make",
            "args": [
                "clean"
            ],
            "problemMatcher": [],
            "group": {
                "_id": "build",
                "isDefault": false
            }
        }
    ]
}
