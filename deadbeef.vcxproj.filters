﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="external">
      <UniqueIdentifier>{2839F3DC-1406-4EE8-3D1F-3C90294B8376}</UniqueIdentifier>
    </Filter>
    <Filter Include="external\wcwidth">
      <UniqueIdentifier>{F19A8920-DD93-A872-06C6-8DF7F21D887E}</UniqueIdentifier>
    </Filter>
    <Filter Include="icons">
      <UniqueIdentifier>{613DA30F-CD51-CBC0-96D3-6E2002A7C7F1}</UniqueIdentifier>
    </Filter>
    <Filter Include="plugins">
      <UniqueIdentifier>{07D1A6B5-73B0-2551-7CC8-B027E8268CB2}</UniqueIdentifier>
    </Filter>
    <Filter Include="plugins\libparser">
      <UniqueIdentifier>{9A724220-0689-40B6-4F00-CCD7BB55113E}</UniqueIdentifier>
    </Filter>
    <Filter Include="shared">
      <UniqueIdentifier>{9C5CB11B-88FE-DBF1-71B9-EF455DFA6242}</UniqueIdentifier>
    </Filter>
    <Filter Include="shared\filereader">
      <UniqueIdentifier>{DEA7F80F-4ABE-F6A5-9335-82C7FF8AC72D}</UniqueIdentifier>
    </Filter>
    <Filter Include="shared\undo">
      <UniqueIdentifier>{6127641C-CDDC-DC11-56D1-D6E8C285D315}</UniqueIdentifier>
    </Filter>
    <Filter Include="shared\windows">
      <UniqueIdentifier>{F67A7716-E248-6B1C-CBDC-DB42B7494781}</UniqueIdentifier>
    </Filter>
    <Filter Include="src">
      <UniqueIdentifier>{2DAB880B-99B4-887C-2230-9F7C8E38947C}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\md5">
      <UniqueIdentifier>{421C30A9-AEFB-AE44-B713-3A1B237215A6}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\undo">
      <UniqueIdentifier>{923838CF-7E05-93DA-A71E-8182934AC868}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="external\wcwidth\wcwidth.c">
      <Filter>external\wcwidth</Filter>
    </ClCompile>
    <ClCompile Include="plugins\libparser\parser.c">
      <Filter>plugins\libparser</Filter>
    </ClCompile>
    <ClCompile Include="shared\ctmap.c">
      <Filter>shared</Filter>
    </ClCompile>
    <ClCompile Include="shared\filereader\filereader.c">
      <Filter>shared\filereader</Filter>
    </ClCompile>
    <ClCompile Include="shared\undo\undobuffer.c">
      <Filter>shared\undo</Filter>
    </ClCompile>
    <ClCompile Include="shared\undo\undomanager.c">
      <Filter>shared\undo</Filter>
    </ClCompile>
    <ClCompile Include="src\actionhandlers.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\buffered_file_writer.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\conf.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\coreplugin.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\cueutil.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\decodedblock.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\dsp.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\dsppreset.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\escape.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\fft.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\handler.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\junklib.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\logger.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\main.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\md5\md5.c">
      <Filter>src\md5</Filter>
    </ClCompile>
    <ClCompile Include="src\messagepump.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\metacache.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\playlist.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\playmodes.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\playqueue.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\plmeta.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\pltmeta.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\plugins.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\premix.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\replaygain.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\resizable_buffer.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\ringbuf.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\sort.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\streamer.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\streamreader.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\tf.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\threading_pthread.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\undo\undo_playlist.c">
      <Filter>src\undo</Filter>
    </ClCompile>
    <ClCompile Include="src\utf8.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\vfs.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\vfs_stdio.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\viz.c">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\volume.c">
      <Filter>src</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="icons\deadbeef-icon.rc">
      <Filter>icons</Filter>
    </CustomBuild>
    <CustomBuild Include="shared\windows\Resources.rc">
      <Filter>shared\windows</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>