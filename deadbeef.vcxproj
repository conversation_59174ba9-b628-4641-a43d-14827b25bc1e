﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="debug Windows|Win32">
      <Configuration>debug Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="release Windows|Win32">
      <Configuration>release Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A5D9B10D-91A6-0C19-BABF-FAC0A6EB41A7}</ProjectGuid>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>deadbeef</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>bin\debug\</OutDir>
    <IntDir>obj\Windows\debug\deadbeef\</IntDir>
    <TargetName>deadbeef</TargetName>
    <TargetExt>.exe</TargetExt>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>bin\release\</OutDir>
    <IntDir>obj\Windows\release\deadbeef\</IntDir>
    <TargetName>deadbeef</TargetName>
    <TargetExt>.exe</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>DEBUG;VERSION="1.10.0";_GNU_SOURCE;HAVE_LOG2=1;HAVE_ICONV=1;ENABLE_NLS;PACKAGE="deadbeef";USE_STDIO;HAVE_ICONV;_POSIX_C_SOURCE;PORTABLE=1;STATICLINK=1;PREFIX="donotuse";LIBDIR="donotuse";DOCDIR="donotuse";LOCALEDIR="donotuse";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>include;.;static-deps\lib-x86-64\include\x86_64-linux-gnu;static-deps\lib-x86-64\include;shared\windows\include;\include\opus;xdispatch_ddb\include;shared;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <AdditionalOptions>-include shared/windows/mingw32_layer.h -fno-builtin -fblocks %(AdditionalOptions)</AdditionalOptions>
      <ExternalWarningLevel>Level3</ExternalWarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>DEBUG;VERSION=\"1.10.0\";_GNU_SOURCE;HAVE_LOG2=1;HAVE_ICONV=1;ENABLE_NLS;PACKAGE=\"deadbeef\";USE_STDIO;HAVE_ICONV;_POSIX_C_SOURCE;PORTABLE=1;STATICLINK=1;PREFIX=\"donotuse\";LIBDIR=\"donotuse\";DOCDIR=\"donotuse\";LOCALEDIR=\"donotuse\";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>include;.;static-deps\lib-x86-64\include\x86_64-linux-gnu;static-deps\lib-x86-64\include;shared\windows\include;\include\opus;xdispatch_ddb\include;shared;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>intl.lib;ws2_32.lib;psapi.lib;shlwapi.lib;iconv.lib;m.lib;pthread.lib;dl.lib;dispatch.lib;BlocksRuntime.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>static-deps\lib-x86-64\lib\x86_64-linux-gnu;static-deps\lib-x86-64\lib;xdispatch_ddb\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>-Wl,--export-all-symbols %(AdditionalOptions)</AdditionalOptions>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>VERSION="1.10.0";_GNU_SOURCE;HAVE_LOG2=1;HAVE_ICONV=1;ENABLE_NLS;PACKAGE="deadbeef";USE_STDIO;HAVE_ICONV;_POSIX_C_SOURCE;PORTABLE=1;STATICLINK=1;PREFIX="donotuse";LIBDIR="donotuse";DOCDIR="donotuse";LOCALEDIR="donotuse";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>include;.;static-deps\lib-x86-64\include\x86_64-linux-gnu;static-deps\lib-x86-64\include;shared\windows\include;\include\opus;xdispatch_ddb\include;shared;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <Optimization>Disabled</Optimization>
      <AdditionalOptions>-O2 -include shared/windows/mingw32_layer.h -fno-builtin -fblocks %(AdditionalOptions)</AdditionalOptions>
      <ExternalWarningLevel>Level3</ExternalWarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>VERSION=\"1.10.0\";_GNU_SOURCE;HAVE_LOG2=1;HAVE_ICONV=1;ENABLE_NLS;PACKAGE=\"deadbeef\";USE_STDIO;HAVE_ICONV;_POSIX_C_SOURCE;PORTABLE=1;STATICLINK=1;PREFIX=\"donotuse\";LIBDIR=\"donotuse\";DOCDIR=\"donotuse\";LOCALEDIR=\"donotuse\";%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>include;.;static-deps\lib-x86-64\include\x86_64-linux-gnu;static-deps\lib-x86-64\include;shared\windows\include;\include\opus;xdispatch_ddb\include;shared;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <AdditionalDependencies>intl.lib;ws2_32.lib;psapi.lib;shlwapi.lib;iconv.lib;m.lib;pthread.lib;dl.lib;dispatch.lib;BlocksRuntime.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>static-deps\lib-x86-64\lib\x86_64-linux-gnu;static-deps\lib-x86-64\lib;xdispatch_ddb\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>-Wl,--export-all-symbols %(AdditionalOptions)</AdditionalOptions>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="external\wcwidth\wcwidth.c" />
    <ClCompile Include="plugins\libparser\parser.c" />
    <ClCompile Include="shared\ctmap.c" />
    <ClCompile Include="shared\filereader\filereader.c" />
    <ClCompile Include="shared\undo\undobuffer.c" />
    <ClCompile Include="shared\undo\undomanager.c" />
    <ClCompile Include="src\actionhandlers.c" />
    <ClCompile Include="src\buffered_file_writer.c" />
    <ClCompile Include="src\conf.c" />
    <ClCompile Include="src\coreplugin.c" />
    <ClCompile Include="src\cueutil.c" />
    <ClCompile Include="src\decodedblock.c" />
    <ClCompile Include="src\dsp.c" />
    <ClCompile Include="src\dsppreset.c" />
    <ClCompile Include="src\escape.c" />
    <ClCompile Include="src\fft.c" />
    <ClCompile Include="src\handler.c" />
    <ClCompile Include="src\junklib.c" />
    <ClCompile Include="src\logger.c" />
    <ClCompile Include="src\main.c" />
    <ClCompile Include="src\md5\md5.c" />
    <ClCompile Include="src\messagepump.c" />
    <ClCompile Include="src\metacache.c" />
    <ClCompile Include="src\playlist.c" />
    <ClCompile Include="src\playmodes.c" />
    <ClCompile Include="src\playqueue.c" />
    <ClCompile Include="src\plmeta.c" />
    <ClCompile Include="src\pltmeta.c" />
    <ClCompile Include="src\plugins.c" />
    <ClCompile Include="src\premix.c" />
    <ClCompile Include="src\replaygain.c" />
    <ClCompile Include="src\resizable_buffer.c" />
    <ClCompile Include="src\ringbuf.c" />
    <ClCompile Include="src\sort.c" />
    <ClCompile Include="src\streamer.c" />
    <ClCompile Include="src\streamreader.c" />
    <ClCompile Include="src\tf.c" />
    <ClCompile Include="src\threading_pthread.c" />
    <ClCompile Include="src\undo\undo_playlist.c" />
    <ClCompile Include="src\utf8.c" />
    <ClCompile Include="src\vfs.c" />
    <ClCompile Include="src\vfs_stdio.c" />
    <ClCompile Include="src\viz.c" />
    <ClCompile Include="src\volume.c" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="icons\deadbeef-icon.rc">
      <FileType>Document</FileType>
      <Command>windres --define VERSION="1.10.0" -O coff -o "$(IntDir)/%(Filename).o" "%(Identity)"</Command>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">obj/Windows/debug/deadbeef/deadbeef-icon.o</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">obj/Windows/release/deadbeef/deadbeef-icon.o</Outputs>
    </CustomBuild>
    <CustomBuild Include="shared\windows\Resources.rc">
      <FileType>Document</FileType>
      <Command>windres --define VERSION="1.10.0" -O coff -o "$(IntDir)/%(Filename).o" "%(Identity)"</Command>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">obj/Windows/debug/deadbeef/Resources.o</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">obj/Windows/release/deadbeef/Resources.o</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="libwin.vcxproj">
      <Project>{EAFA6F0B-D69C-9AE1-BF57-AE35AB982132}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>