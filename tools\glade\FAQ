
Table of Contents
=================

1. General Information About Glade

  1.1 What documentation is there for Glad<PERSON>?
  1.2 Is there any example code?
  1.3 Do I have to use a particular license for the C code generated by Glade?
  1.4 Which is better - generated C code or libglade?
  1.5 How do I port a GTK+ 1.2 or GNOME 1.4 project to GTK+/GNOME 2.0.

2. Creating A User Interface In Glade

  2.1 When I add a widget to a window, it fills the whole window and I can't
      add any other widgets.
  2.2 How do I change the colors of a widget, e.g. make a label red?
  2.3 How do I add several widgets of the same type in one go?
  2.4 What graphics formats does Glade support?
  2.5 How do I add a signal handler?

3. Building The C Code Generated By Glade

  3.1 How do I build the code generated by Glad<PERSON>?

  3.2 I get this error:
        aclocal: configure.in: 10: macro `AM_PATH_GTK' not found in library

4. Using The C Code Generated By Glade

  4.1  What are all the files output by Glade?
  4.2  What files can the developer edit safely and which are overwritten?
  4.3  How do I add my own source files to the project?
  4.4  How do I add a library to the project?
  4.5  How do I get a pointer to a widget from within a signal handler?
  4.6  How do I get a pointer to a widget in another window?
  4.7  How do I get the value of a GtkOptionMenu?
  4.8  How do I get a GtkOptionMenu to call a function when it changes?
  4.9  How do I connect to GtkAdjustment signals?
  4.10 How do I add rows to a GtkCList before the window is shown?


==============================================================================

1. General Information About Glade
==================================

1.1 What documentation is there for Glade?

In the GNOME version of Glade the Glade Manual and this FAQ should be
available from the Help menu.

There are a few other tutorials and guides available on the Internet,
in several languages. Links to these can be found on Glade's website at
http://glade.gnome.org.


1.2 Is there any example code?

Glade includes a simple text editor example application in the examples/editor
directory. If you have a binary-only package of Glade, these files have
probably not been installed, so you'll need to download the Glade source
code from http://glade.gnome.org.

The website also contains links to quite a few applications created using Glade.
These may contain useful example code. See the 'Applications' page on
http://glade.gnome.org.


1.3 Do I have to use a particular license for the C code generated by Glade?

No. You are free to use whatever license you like for the C code generated
by Glade.

Though in the spirit of free software we do encourage you to use the GPL or
LGPL licenses.


1.4 Which is better - generated C code or libglade?

For large projects libglade is recommended over generated C code.
You can keep each window or dialog in a separate XML file, which makes it
more manageable for large projects with multiple developers.

If you do want to use generated C code for large projects, I'd still suggest
keeping each window or dialog in a separate XML file. You can change the
project options so that the C code is output into separate files for each
window or dialog (e.g. change 'interface.c' to 'main_window.c'), and turn
off all the 'File Output' options.


1.5 How do I port a GTK+ 1.2 or GNOME 1.4 project to GTK+/GNOME 2.0.

libglade includes a script called libglade-convert which will convert an
old GTK+ 1.2 or GNOME 1.4 Glade XML file to the new GTK+/GNOME 2.0 format.

However, for GTK+ applications there is currently a slight problem with this.
You need to:
  (1) Convert the XML file with libglade-convert.
  (2) Open the new file in Glade and save it immediately. This will create
      the project options file with the same name as the XML file but with
      a 'p' on the end.
  (3) Add '<gnome_support>FALSE</gnome_support>'
      to the project options file, so Glade knows it is a GTK+ project.


==============================================================================

2. Creating A User Interface In Glade
=====================================

2.1 When I add a widget to a window, it fills the whole window and I can't
    add any other widgets.

This is not a bug in Glade! In GTK+ you use containers to lay out your
widgets. The commonly-used containers are at the bottom of the main
page on the palette. Try adding a vertical box to a window in Glade.
Now add a table to one of the positions in the vertical box.
Do you get the idea now?

If you really want to position widgets at specific coordinates, try the
Fixed container. However, this isn't recommended since your
windows/dialogs will not look good when resized, and if you translate
the text in the labels and buttons into other languages they may not fit.


2.2 How do I change the colors of a widget, e.g. make a label red?

In GTK+ 2 the label widget allows the use of special markup to change the
text attributes, e.g. in Glade select the label and turn on the "Use Markup"
property, then entrt "<span color="red">Hello World</span>" into the "Label"
property.

For other widgets you can use functions like gtk_widget_modify_fg() to set the
foreground color of a widget, or gtk_widget_modify_style() e.g.

  GdkColor red = { 0, 65535, 0, 0 };
  GtkRcStyle *rc_style = gtk_rc_style_new ();
  rc_style->fg[GTK_STATE_NORMAL] = red;
  rc_style->color_flags[GTK_STATE_NORMAL] |= GTK_RC_FG;
  gtk_widget_modify_style (widget, rc_style);
  gtk_rc_style_unref (rc_style);

It is also possible to use GTK+ rc files to set the colors & fonts of your
widgets. If you turn on the 'Set Widget Names' project option in Glade, that
may make it easier to refer to your widgets, as you can refer to them by name.
See the GTK+ Resource Files documentation at http://developer.gnome.org/doc/API


2.3 How do I add several widgets of the same type in one go?

Hold the 'Control' key when you select the widget in the palette. It will
then stay selected until you select another widget or the selector.


2.4 What graphics formats does Glade support?

GTK+ 2.0 includes the gdk-pixbuf library which provides support for many
graphics formats, including PNG, TIFF, BMP, JPEG and XPM files.

You can convert images between formats using the GIMP graphics
application or the 'convert' utility in the ImageMagick toolkit.


2.5 How do I add a signal handler?

Follow these steps:

 o Select the widget you want to add the handler to.
 o Select the 'Signals' page in the Property Editor window.
 o Click the '...' button to the right of the 'Signal:' field. This should
   show a popup window listing the signals that this widget emits.
 o In the popup window, select which signal you want to connect a handler for
   and hit the 'OK' button.
 o Glade automatically creates a name for the signal handler function in the
   'Handler:' field, but you are free to change this if you desire.
 o Click the 'Add' button to add it to the list of signal handlers for this
   widget.

When you generate the C source code, an empty signal handler function should
be output in the callbacks.c file. You can add your code to this function.


==============================================================================

3. Building The C Code Generated By Glade
=========================================

3.1 How do I build the code generated by Glade?

You need automake >= 1.4 & autoconf >= 2.52 to build the generated C code.
You also need gettext >= 0.10.40 if you enabled gettext support. See the
'Requirements' section in the README file for links to these.

Run './autogen.sh' in the toplevel directory of the project to run
automake, autoconf and the related utilities to build the Makefiles. Pass
it any options you want passed to configure, e.g.

	./autogen.sh --prefix /usr/local/gnome

Then run 'make' to build your application.

You must also do a 'make install' so that the pixmaps are installed
properly. If you don't do that, it will still
run, but you won't see the pixmaps.


3.2 I get this error:

	aclocal: configure.in: 10: macro `AM_PATH_GTK' not found in library

This means that the gtk.m4 file cannot be found. (gtk.m4 is a set of m4
macros which is installed as part of GTK+ and is used to build programs
which use GTK+.) aclocal (part of automake) searches for these macros to
add to aclocal.m4 in your program's root directory.

To find out whereabouts GTK+ is installed, run
'pkg-config --cflags gtk+-2.0'.
The gtk.m4 file should be in the 'share/aclocal' subdirectory rather than
the 'include' subdirectory.
To find out the directory that aclocal is using, run 'aclocal --print-ac-dir'

You should add the directory that the GTK+ m4 file is installed to the
ACLOCAL_FLAGS environment variable, e.g. if the GTK+ m4 files are in
/usr/local/share/aclocal, then add this to your $HOME/.profile:

	export ACLOCAL_FLAGS="-I /usr/local/share/aclocal/"


==============================================================================

4. Using The C Code Generated By Glade
======================================


4.1 What are all the files output by Glade?

These are the default output files, but some of your file names may differ
if you have changed the project options.

autogen.sh	- a script which runs automake, autoconf and the related
		  programs in the correct order, making it very easy to build
		  the application. You pass it any arguments you want to be
		  passed to configure. After running it, you can type 'make'
		  to build the application.
configure.in	- standard script passed to autoconf to generate the configure
		  script.
Makefile.am	- standard make rules passed to automake to generate the
		  Makefile.in, which the configure script turns into Makefile.
stamp-h.in	- used as a timestamp by automake, for rebuilding some
		  generated files.

AUTHORS		- these files are all initially empty, but are created to
ChangeLog	  comply with the GNU conventions.
NEWS
README

src/Makefile.am	- standard automake file.
src/main.c	- contains the main() function which will create one of each
		  window/dialog for you.
src/interface.h - declarations of functions you can call to create the windows
		  and dialogs which were built in Glade.
src/interface.c - the code to create the windows and dialogs and all the
		  widgets.
src/callbacks.h - declarations of the signal handler and callback functions
		  which you will write.
src/callbacks.c - the signal handler and callback functions, 
src/support.h   - declarations of some support functions, including
		  lookup_widget() which you can use to get pointers to widgets.
src/support.c   - the support functions.


If gettext support is enabled, the po directory is created, with POTFILES.in
and a separate ChangeLog. POTFILES.in lists the source files which contain
translatable strings, and you should add any source files you create here.

*NOTE*: if you change the 'GNOME Support' or 'Gettext Support' project
options after building the project, you will need to update some of the
build files such as configure.in, Makefile.am
The best solution may be to change the project directory in the 'Project
Options' dialog, and to rebuild the project from scratch. However, you would
have to copy over any code you have added to signal handlers. An alternative
would be to delete autogen.sh, configure.in, Makefile.am, src/Makefile.am,
and src/main.c and use Glade to recreate them. But if you have changed these
files you will need to add the changes back in.
(Hopefully Glade will deal with this better in future.)



4.2 What files can the developer edit safely and which files are overwritten?

Glade will not overwrite most of the files. It will recreate the build files
if they don't exist (and the corresponding project option is set.)

The files which Glade overwrites are:

  interface.h
  interface.c
  support.h
  support.c

(Though you may have named them differently in your project, if you changed
them in the Project Options dialog.)

These files all have a warning at the top saying 'DO NOT EDIT'.


If you have added or updated any signal handlers, then these are appended to
the callbacks.h and callbacks.c files. So any code you have already added
for callbacks is completely safe! If you have renamed a handler function then
it is up to you to remove the old version and copy any code to the new
function.



4.3 How do I add my own source files to the project?

Add the source file (and any header file) to src/Makefile.am, in the
project1_SOURCES variable (where 'project1' is the name of your project).

If you are using gettext, you may also want to add the source file to
po/POTFILES.in so that the strings can be translated.



4.4 How do I add a library to the project?

You need to add a test for the library in your project's configure.in file,
making sure that the CPPFLAGS and LIBS are updated to account for the library.
(The CPPFLAGS variable should contain any -I flags to pass to the C
preprocessor, and the LIBS variable should contain the -l and -L options to
pass to the linker.)

autoconf provides macros such as AC_CHECK_HEADER and AC_CHECK_LIB which can
be used to check for general headers and libraries.

Many GTK+ and Gnome libraries provide a config script such as gtk-config
which will output the CPPFLAGS and LIBS flags needed.

For example, libxml provides an xml-config script which you can use like this:

  dnl Get libxml flags & libs
  AC_PATH_PROG(xml_config, xml-config)
  if test "x$xml_config" = "x"; then
    AC_MSG_ERROR([*** xml-config not found.])
  fi

  XML_CFLAGS=`$xml_config --cflags 2>/dev/null`
  XML_LIBS=`$xml_config --libs 2>/dev/null`
  CPPFLAGS="$CPPFLAGS $XML_CFLAGS"
  LIBS="$LIBS $XML_LIBS"

NOTE: Make sure you place your configure.in test before the call to AC_OUTPUT.


4.5 How do I get a pointer to a widget from within a signal handler?

If you have a pointer to any widget in a window, you can get a pointer to
any other widget in the window using the lookup_widget() function that Glade
provides (in support.c).

You pass it a pointer to any widget in a window, and the name of the widget
that you want to get. Usually in signal handlers you can use the first argument
to the signal handler as the first parameter to lookup_widget(), e.g.

void
on_button1_clicked                     (GtkButton       *button,
                                        gpointer         user_data)
{
  GtkWidget *entry1;

  entry1 = lookup_widget (GTK_WIDGET (button), "entry1");

  ...
}


Note that this does not work if you are using libglade. The corresponding
code for libglade would be:

void
on_button1_clicked                     (GtkButton       *button,
                                        gpointer         user_data)
{
  GladeXML* xml;
  GtkWidget* entry1;

  xml = glade_get_widget_tree (GTK_WIDGET (button1));
  entry1 = glade_xml_get_widget (xml, "entry1");

  ...
}


4.6 How do I get a pointer to a widget in another window?

You need to keep track of pointers to all your toplevel windows.
For simple applications you can just use global variables to store these
pointers.

For more complicated applications you can use the gtk_object_set_data() and
related functions to store a pointer to one window inside another window.
For example, if you want to create a dialog which needs to access widgets in
the main window you can do this:

  dialog = create_dialog1 ();  /* Call the function generated by Glade. */
  gtk_object_set_data (GTK_OBJECT (dialog), "main_window", main_window);

Then when you need to access the main window from within the dialog code,
you can do this:

  main_window = gtk_object_get_data (GTK_OBJECT (dialog), "main_window");

NOTE: You need to be careful to ensure that the pointer is always valid.
If the window being pointed to is destroyed, make sure you no longer use the
pointer to it or your application may crash.


4.7 How do I get the value of a GtkOptionMenu?

Call gtk_menu_get_active() with the GtkOptionMenu's menu to get the currently
selected menu item. You can use g_list_index() to find its index in the menu:

void
on_button1_clicked                     (GtkButton       *button,
                                        gpointer         user_data)
{
  GtkWidget *option_menu, *menu, *active_item;
  gint active_index;

  option_menu = lookup_widget (GTK_WIDGET (button), "optionmenu1");
  menu = GTK_OPTION_MENU (option_menu)->menu;
  active_item = gtk_menu_get_active (GTK_MENU (menu));
  active_index = g_list_index (GTK_MENU_SHELL (menu)->children, active_item);

  g_print ("Active index: %i\n", active_index);
}


4.8 How do I get a GtkOptionMenu to call a function when it changes?

Glade doesn't support this at present, but you can set it up manually.

When you create the window, get the option menu and connect to the "deactivate"
signal emitted by its menu:

  window1 = create_window1 ();
  option_menu = lookup_widget (window1, "optionmenu1");
  gtk_signal_connect (GTK_OBJECT (GTK_OPTION_MENU (option_menu)->menu),
                      "deactivate", GTK_SIGNAL_FUNC (on_option_selected),
                      NULL);


Then add a handler to callbacks.c. You can get the index of the selected item
just like the previous answer:

static void
on_option_selected (GtkMenuShell *menu_shell,
                    gpointer data)
{
  GtkWidget *active_item;
  gint item_index;
  
  active_item = gtk_menu_get_active (GTK_MENU (menu_shell));
  item_index = g_list_index (menu_shell->children, active_item);

  g_print ("In on_option_selected active: %i\n", item_index);
}


4.9 How do I connect to GtkAdjustment signals?

Glade doesn't support this at present, but you can set it up manually.

When you create the window, get a pointer to the widget containing the
adjustment, and connect to the "changed" or "value_changed" signals:

  window1 = create_window1 ();
  hscale = lookup_widget (window1, "hscale1");
  gtk_signal_connect (GTK_OBJECT (GTK_RANGE (hscale)->adjustment),
                      "changed", GTK_SIGNAL_FUNC (on_adjustment_changed),
                      NULL);


4.10 How do I add rows to a GtkCList before the window is shown?

After you create the window, using the 'create' function generated by Glade,
use lookup_widget() to get a pointer to the GtkCList widget, and add the rows
as required. e.g.

  GtkWidget *window, *clist;
  gchar *row[2];		/* Our GtkCList only has 2 columns. */

  window = create_window1 ();
  clist = lookup_widget (window, "clist1");

  row[0] = "Hello";
  row[1] = "World";
  gtk_clist_append (GTK_CLIST (clist), row);

  row[0] = "Second";
  row[1] = "Row";
  gtk_clist_append (GTK_CLIST (clist), row);

  gtk_widget_show (window1);

