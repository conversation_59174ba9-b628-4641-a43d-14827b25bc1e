# translation of gl.po to Galego
# <PERSON><PERSON> <<EMAIL>>, 1999.
# <PERSON> <<EMAIL>>, 2005, 2006.
# Galician translation of Glade
# Copyright (C) 1999, 2005, 2006 Free Software Foundation, Inc.
msgid ""
msgstr ""
"Project-Id-Version: gl\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2006-11-04 19:23+0100\n"
"PO-Revision-Date: 2006-11-04 19:25+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Galego <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: KBabel 1.11.4\n"

#: ../glade-2.desktop.in.h:1
msgid "Create or open user interface designs for GTK+ or GNOME applications"
msgstr "Crear ou abrir deseños de interface de usuario para GTK+ ou aplicacións de Gnome"

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Deseñador de interfaces Glade "

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Opcións da relliña"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Espaciado horizontal:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Espaciado vertical:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Estilo da relliña:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Puntos"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Liñas"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Opcións de agregación"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Agregado horizontal"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Esquerda"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Dereita"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Agregado vertical:"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Arriba"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Abaixo"

#: ../glade/editor.c:741
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "Os widgets GtkToolItem só se poden engadir nunha barra GtkToolbar."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "Non se pode insertar un compoñente GtkScrolledWindow"

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "Non se puido inserir un widget GtkViewport."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "Non se puido engadir un widget novo."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"Non podes poñse un widget nesa posición.\n"
"\n"
"Axuda: GTK+ usa contenedores para situar os widgets.\n"
"Proba eliminando o widget existente e usando\n"
"un contenedor de tipo caixa ou tabla.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "No se puido borrar o widget."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "Non foi posible borrar o widget"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"O widget esta creado de maneira automatica coma parte do seu widget pai, e "
"nion se pode borrar."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Ancho do bordo:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "O ancho do bordo arredor do contenedor"

#: ../glade/gbwidget.c:1751
msgid "Select"
msgstr "Seleccionar"

#: ../glade/gbwidget.c:1773
msgid "Remove Scrolled Window"
msgstr "Eliminar fiestra de desprazamento"

#: ../glade/gbwidget.c:1782
msgid "Add Scrolled Window"
msgstr "Engadir fiestra de desprazamento"

#: ../glade/gbwidget.c:1803
msgid "Remove Alignment"
msgstr "Borrar Aliñamento"

#: ../glade/gbwidget.c:1811
msgid "Add Alignment"
msgstr "Engadir Aliñamento"

#: ../glade/gbwidget.c:1826
msgid "Remove Event Box"
msgstr "Borrar caixa de eventos"

#: ../glade/gbwidget.c:1834
msgid "Add Event Box"
msgstr "Engadir caixa de eventos"

#: ../glade/gbwidget.c:1844
msgid "Redisplay"
msgstr "Redebuxar"

#: ../glade/gbwidget.c:1859
msgid "Cut"
msgstr "Cortar"

#: ../glade/gbwidget.c:1866 ../glade/property.c:892 ../glade/property.c:5141
msgid "Copy"
msgstr "Copiar"

#: ../glade/gbwidget.c:1875 ../glade/property.c:904
msgid "Paste"
msgstr "Pegar"

#: ../glade/gbwidget.c:1887 ../glade/property.c:1581 ../glade/property.c:5132
msgid "Delete"
msgstr "Borrar"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2414 ../glade/gbwidget.c:2483
msgid "N/A"
msgstr "Non dispoñible"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3213
msgid "replacing child of container - not implemented yet\n"
msgstr "reemplazar fillo do contenedor - non implementado aínda\n"

#: ../glade/gbwidget.c:3441
msgid "Couldn't insert GtkAlignment widget."
msgstr "Non se puido inserir o widget GtkAlignment."

#: ../glade/gbwidget.c:3481
msgid "Couldn't remove GtkAlignment widget."
msgstr "Non foi posible borrar o widget GtkAlignment."

#: ../glade/gbwidget.c:3505
msgid "Couldn't insert GtkEventBox widget."
msgstr "Non se puido inserir o widget GtkEventBox."

#: ../glade/gbwidget.c:3544
msgid "Couldn't remove GtkEventBox widget."
msgstr "Non se puido eliminar o compoñente GtkEventBox."

#: ../glade/gbwidget.c:3579
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "Non se puido insertar un compoñente GtkScrolledWindow."

#: ../glade/gbwidget.c:3618
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "Non se puido eliminar o compoñente GtkScrolledWindow."

#: ../glade/gbwidget.c:3732
msgid "Remove Label"
msgstr "Borrar Etiqueta"

#: ../glade/gbwidgets/gbaboutdialog.c:79
msgid "Application Name"
msgstr "Nome da aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Logo:"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "A imaxe a empregar como logo"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Nome do programa:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
msgid "The name of the application"
msgstr "O nome da aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "Comentarios:"

#: ../glade/gbwidgets/gbaboutdialog.c:106
msgid "Additional information, such as a description of the application"
msgstr "Información adicional, como por exemplo unha descrición da aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Copyright:"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "O aviso dos dereitos de copia"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "Website URL:"
msgstr "URL do sitio Web:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "The URL of the application's website"
msgstr "A URL do sitio Web da aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "Website Label:"
msgstr "Etiqueta do sitio Web:"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "The label to display for the link to the website"
msgstr "A etiqueta para mostrar no ligazón da páxina web"

#: ../glade/gbwidgets/gbaboutdialog.c:112 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Licencia:"

#: ../glade/gbwidgets/gbaboutdialog.c:112
msgid "The license details of the application"
msgstr "Os detalles da licencia da aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "Wrap License:"
msgstr "Licencia cuberta:"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "If the license text should be wrapped"
msgstr "Se o texto de licencia debería ser cuberto"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Autores:"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Os autores do paquete, un por liña"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Documentadores:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Os documentadores do paquete, un por liña"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid "Artists:"
msgstr "Artistas:"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid "The people who have created the artwork for the package, one on each line"
msgstr "A xente que creou o traballo artístico para o paquete, un por liña"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Traductores:"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Os traductores do paquete. Normalmente debería deixarse baleiro para que os "
"traductores poidan engadir os seus nomes nos ficheiros po"

#: ../glade/gbwidgets/gbaboutdialog.c:588
msgid "About Dialog"
msgstr "Diálogo Acerca de"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Etiqueta con Acelerador"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:181 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "Aliñamento X:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "Aliñamento horizontal do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:184 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Aliñamento Y:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "Aliñamento vertical do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "Escala X:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "Escala horizontal do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Escala Y:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "Escala vertical do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:85
msgid "Top Padding:"
msgstr "Separación superior:"

#: ../glade/gbwidgets/gbalignment.c:86
msgid "Space to put above the child widget"
msgstr "Espacio a poñer enriba do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:89
msgid "Bottom Padding:"
msgstr "Separación inferior:"

#: ../glade/gbwidgets/gbalignment.c:90
msgid "Space to put below the child widget"
msgstr "Espacio a poñer debaixo do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:93
msgid "Left Padding:"
msgstr "Separación esquerda:"

#: ../glade/gbwidgets/gbalignment.c:94
msgid "Space to put to the left of the child widget"
msgstr "Espacio a poñer á esquerda do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:97
msgid "Right Padding:"
msgstr "Separación dereita:"

#: ../glade/gbwidgets/gbalignment.c:98
msgid "Space to put to the right of the child widget"
msgstr "Espacio a poñer á dereita do widget fillo"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Aliñamento"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "Dirección"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "Dirección da frecha"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Sombra:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "Tipo de sombra da frecha"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "Aliñamento horizontal da frecha"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "Aliñamento vertical da frecha"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187
msgid "X Pad:"
msgstr "Marxe X:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "Margen horizontal"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189
msgid "Y Pad:"
msgstr "Marxe Y:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "A marxe vertical"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Frecha"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "Aliñ. X da etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "O aliñamento horizontal do widget da etiqueta do marco"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Aliñ. Y da etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "O aliñamento vertical do widget da etiqueta do marco"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "Tipo de sombra do marco (frame)"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "Aliñamento horizontal do fillo do marco"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Proporción:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "A proporción cos fillos do marco"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Obedecer fillos:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Os fillos determinan a proporción"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Aspecto do frame"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Botón de inventario:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "O botón de inventario a usar"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:748
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Etiqueta:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "Texto a amosar"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:297
#: ../glade/glade_menu_editor.c:814
msgid "Icon:"
msgstr "Icono:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "A icona a mostrar"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Relieve do botón"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "O estilo de relieve do botón"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "ID da resposta:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"O código de resposta cando se preme o botón. Seleccione unha das respostas "
"estándares ou introduza un valor enteiro positivo"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr "Enfocar ao premer:"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "If the button grabs focus when it is clicked"
msgstr "Se o botón obtén o foco cando se preme"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "Eliminar o contido dos botóns"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Botón"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Cabeceira:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "Ver o mes e o ano na parte de arriba"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Nomes dos días:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Se se deben amosar os nomes dos días"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Mes fixado:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "Non permitir cambiar o mes ou o ano"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Números da semán:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Se se debe amosar o número da semana"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Luns primeiro:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "A semana comeza en Luns"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Calendario"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Cor de fondo:"

#: ../glade/gbwidgets/gbcellview.c:64
msgid "The background color"
msgstr "A cor do fondo"

#: ../glade/gbwidgets/gbcellview.c:192
msgid "Cell View"
msgstr "Vista de celda"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Premido ó comezo:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Botón premido ó comezo"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Inconsistente:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Indica se o botón se amosa nun estado inconsistente"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "Indicador:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "Debuxar sempre o indicador"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Botón de Chequeo"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Item de checkeo do menú (check menu item) premido ó comezo"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Botón de Chequeo de Menú"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Nova lista con columnas"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Numero de columnas:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:128 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Escoller Modo:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "Modo de selección da lista con columnas"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Amosar Títulos"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Amosar os títulos das columnas"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "Tipo de sombra do bordo da lista con columnas"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Lista con Columnas"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Usar Alfa:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Se se debe empregar a canle alfa"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:244
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Título"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "Título do diálogo de selección de cor"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
msgid "Pick a Color"
msgstr "Escolle unha cor"

#: ../glade/gbwidgets/gbcolorbutton.c:211
msgid "Color Chooser Button"
msgstr "Botón do selector de cor"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Control de opacidade:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Se se amosa o control de opacidade"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Paleta:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Se se amosa a paleta"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Selección de Cor"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:71
msgid "Select Color"
msgstr "Escolla Cor"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:316 ../glade/property.c:1276
msgid "Color Selection Dialog"
msgstr "Diálogo para escoller unha cor"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Valor na lista:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Se o valor debe estar na lista"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "Aceptar se valeiro:"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr "Os valores valeiros van ser aceptables, cando 'Valor na lista' o sexa"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "Sensible á capitalización:"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Busqueda sensible a maiúsculas/minúsculas"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Usar frechas:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "As frechas pódense usar para cambiar o valor"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Usar Sempre:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "As frechas traballan ainda que o valor non esté na lista"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Elementos:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Os elementos na lista combo, un por liña"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Caixa Combo"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr "Engadir desprendibles:"

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr "Indica se os despregables deberían ter un elemento de menú desprendible"

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Indica se a caixa combo obtén o foco cando se preme nela"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Ten marco:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr "Indica se a caixa combo debuxa un marco arredor do fillo"

#: ../glade/gbwidgets/gbcomboboxentry.c:302
msgid "Combo Box Entry"
msgstr "Entrada da caixa combo"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Nova árbore en columna"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "O modo de elixir na árbore en columna"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "Tipo de sombra do bordo da árbore en columna"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Árbore en Columna"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:247
msgid "Type:"
msgstr "Tipo:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "O tipo da curva"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "X Min:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "Valor mínimo horizontal"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "X Max:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "Valor máximo horizontal"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Y Min:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "Valor mínimo vertical"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Y Max:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "Valor máximo vertical"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Curva"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Función de creación"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "A función que crea o widget"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Cadea1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "O primeiro argumento (cadea) que se lle pasa á función"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Cadea2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "O segundo argumento (cadea) que se lle pasa á función"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Enteiro1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "O primeiro argumento enteiro que se lle pasa á función"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Enteiro2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "O segundo argumento enteiro que se lle pasa á función"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Widget a medida"

#: ../glade/gbwidgets/gbdialog.c:293
msgid "New dialog"
msgstr "Novo diálogo"

#: ../glade/gbwidgets/gbdialog.c:305
msgid "Cancel, OK"
msgstr "Cancelar, Aceptar"

#: ../glade/gbwidgets/gbdialog.c:314 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1322 ../glade/property.c:5162
msgid "OK"
msgstr "Aceptar"

#: ../glade/gbwidgets/gbdialog.c:323
msgid "Cancel, Apply, OK"
msgstr "Cancelar, Aplicar, Aceptar"

#: ../glade/gbwidgets/gbdialog.c:332
msgid "Close"
msgstr "Pechar"

#: ../glade/gbwidgets/gbdialog.c:341
msgid "_Standard Button Layout:"
msgstr "Destribución e_stándar dos botóns:"

#: ../glade/gbwidgets/gbdialog.c:350
msgid "_Number of Buttons:"
msgstr "_Número de botóns:"

#: ../glade/gbwidgets/gbdialog.c:367
msgid "Show Help Button"
msgstr "Amosar o botón de axuda"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "Has Separator:"
msgstr "Ten separador:"

#: ../glade/gbwidgets/gbdialog.c:399
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Indica se o diálogo ten un separador horizontal sobre os botóns"

#: ../glade/gbwidgets/gbdialog.c:606
msgid "Dialog"
msgstr "Diálogo"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Zona de Debuxo"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Modificable:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Pódese editar o texto"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Texto Visible:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"O texto que escriba o usuario vai ser visto. Cando se desactiva, o texto "
"escrito amósase coma asteriscos, útil para pedir contrasinais"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Lonxitude Max:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "Lonxitude máxima do texto"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Texto:"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Se a entrada ten un marco arredor"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Carácter invisible:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"O carácter a empregar se o texto non debiera ser visible, ex. cando se "
"introducen contrasinais"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "Activar por defecto:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr "Se o widget por defecto na fiestra actívase cando se preme a tecla Intro"

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Anchura en caracteres:"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "O número de caracteres para deixar espacio na entrada"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Entrada de Texto"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "Visible Window:"
msgstr "Fiestra visible:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr "Se a caixa de eventos usa unha fiestra visible"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "Above Child:"
msgstr "Por enriba do fillo:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr "Se a fiestra da caixa de eventos está por enriba da fiestra do widget fillo"

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Caixa de Eventos"

#: ../glade/gbwidgets/gbexpander.c:54
msgid "Initially Expanded:"
msgstr "Expandido ao inicio:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr "Indica se o expansor está aberto inicialmente para revelar o widget fillo"

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Espaciado:"

#: ../glade/gbwidgets/gbexpander.c:58
msgid "Space to put between the label and the child"
msgstr "Espacio a poñer entre a etiqueta e o fillo"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Engadir Widget Etiqueta"

#: ../glade/gbwidgets/gbexpander.c:228
msgid "Expander"
msgstr "Expansor"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
msgid "The window title of the file chooser dialog"
msgstr "O título da fiestra do diálogo de escoller ficheiro"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:158
#: ../glade/gnome/gnomefileentry.c:109
msgid "Action:"
msgstr "Acción:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:89
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr "O tipo de operación de ficheiro que se está efectuando"

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
msgid "Local Only:"
msgstr "Só local:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether the selected files should be limited to local files"
msgstr ""
"Indica se os ficheiros seleccionados deberían ser limitados aos ficheiros "
"locais"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:165
msgid "Show Hidden:"
msgstr "Amosar ocultos:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:166
msgid "Whether the hidden files and folders should be displayed"
msgstr "Indica se os ficheiros e cartafoles ocultos deben ser mostrados"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#: ../glade/gbwidgets/gbfilechooserdialog.c:167
msgid "Confirm:"
msgstr "Confirmar:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:96
#: ../glade/gbwidgets/gbfilechooserdialog.c:168
msgid "Whether a confirmation dialog will be displayed if a file will be overwritten"
msgstr "Indica un diálogo de confirmación a ser amosado se un ficheiro é sobreescrito"

#: ../glade/gbwidgets/gbfilechooserbutton.c:97
#: ../glade/gbwidgets/gblabel.c:201
msgid "Width in Chars:"
msgstr "Anchura en caracteres:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:98
msgid "The width of the button in characters"
msgstr "O ancho do botón en caracteres"

#: ../glade/gbwidgets/gbfilechooserbutton.c:296
msgid "File Chooser Button"
msgstr "Botón de escoller ficheiro"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
msgid "Select Multiple:"
msgstr "Selección múltiple:"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether to allow multiple files to be selected"
msgstr "Indica se se permite seleccionar múltiples ficheiros"

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
msgid "File Chooser"
msgstr "Escoller ficheiro"

#: ../glade/gbwidgets/gbfilechooserdialog.c:435
msgid "File Chooser Dialog"
msgstr "Diálogo para escoller Ficheiro"

#: ../glade/gbwidgets/gbfileselection.c:72 ../glade/property.c:1366
msgid "Select File"
msgstr "Seleccione Ficheiro"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "File Ops.:"
msgstr "Op. de ficheiros:"

#: ../glade/gbwidgets/gbfileselection.c:115
msgid "If the file operation buttons are shown"
msgstr "Si los botones de operaciones sobre ficheros son mostrados"

#: ../glade/gbwidgets/gbfileselection.c:293
msgid "File Selection Dialog"
msgstr "Diálogo para escoller Ficheiro"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "A coordenada X do widget no GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "A coordenada Y do widget no GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Posicións fixadas"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "O título do diálogo de selección de fonte"

#: ../glade/gbwidgets/gbfontbutton.c:70
msgid "Show Style:"
msgstr "Amosar estilo:"

#: ../glade/gbwidgets/gbfontbutton.c:71
msgid "If the font style is shown as part of the font information"
msgstr "Se o estilo da fonte amósase como parte da información da fonte"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Amosar tamaño:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "Amosase o tamaño da fonte como parte da información"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Usar Fonte:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr "Amosase a fonte elixida cando se amosa da información"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Usar Tamaño:"

#: ../glade/gbwidgets/gbfontbutton.c:77
msgid "if the selected font size is used when displaying the font information"
msgstr ""
"Se o tamaño da fonte seleccionada empregarase cando se amose a información "
"da fonte"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Coller fonte"

#: ../glade/gbwidgets/gbfontbutton.c:268
msgid "Font Chooser Button"
msgstr "Botón de escoller fonte"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Texto de mostra:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "Texto a amosar"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Selección de Fonte"

#: ../glade/gbwidgets/gbfontselectiondialog.c:70
msgid "Select Font"
msgstr "Escolla Fonte"

#: ../glade/gbwidgets/gbfontselectiondialog.c:301
msgid "Font Selection Dialog"
msgstr "Diálogo para escoller Fonte"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Marco"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Tipo Inicial:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "Tipo inicial da curva"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Curva Gamma"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "O tipo de sombra arredor da caixa manipulable"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "Posición do manipulador:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "A posición do manipulador"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Axustar bordo:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "O bordo da caixa aboiante que encaixa na posición"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "Caixa Aboiante"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Nova caixa horizontal"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Tamaño:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "Número de widgets na caixa"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Homoxéneo:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Os fillos deben ter o mesmo tamaño"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "Espacio entre cada fillo"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "Non poido eliminar ningún fillo."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:256
msgid "Position:"
msgstr "Posición"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "A posición do widget relativa aos seus irmáns"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Recheo"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "O recheo que leva o widget (similar á marxe)"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Expandir:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "Deixa que o widget se expanda"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Encher:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "Deixa que o widget encha a área que ten reservada"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Empaquetar ó comezo:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "Pon o widget ó principio da caixa"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Insertar Antes"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Insertar Despóis"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Caixa Horizontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Nova caixa de botóns horizontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "Número de botóns"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Ordenamento:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "Tipo de ordenamento dos botóns"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "Espacio entre botóns"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Caixa de Botóns Horizontal"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "A posición do divisor"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:285
msgid "Shrink:"
msgstr "Diminuir:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "Establecido como Verdadeiro permite que o widget poida encollerse"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Redimensionar:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "Establecido como Verdadeiro permite ao widget redimensionarse"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Panel Horizontal"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Métrica:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "Únidades de medida da regra"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Valor Mínimo:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "Valor mínimo da regra"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Valor Máximo:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "Valor máximo da regra"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "Posición actual da regra"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4833
msgid "Max:"
msgstr "Máximo:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "Valor máximo da regra"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Regra Horizontal"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Amosar Valor:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Amosar o valor da escala"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Díxitos:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "Número de díxitos que se mostran"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Pos. Valor:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "Posición do valor"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Política"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "Política de modificación da escala"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Invertido:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Se os valores do rango están invertidos"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Escala Horizontal"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "Política de modificación da barra de desplazamento"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Barra de Desplazamento Horizontal"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Separador Horizontal"

#: ../glade/gbwidgets/gbiconview.c:107
#, c-format
msgid "Icon %i"
msgstr "Icona %i"

#: ../glade/gbwidgets/gbiconview.c:129
msgid "The selection mode of the icon view"
msgstr "O modo de selección da vista da icona"

#: ../glade/gbwidgets/gbiconview.c:131 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "Orientación:"

#: ../glade/gbwidgets/gbiconview.c:132
msgid "The orientation of the icons"
msgstr "A orientación das iconas"

#: ../glade/gbwidgets/gbiconview.c:134 ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Reordenable:"

#: ../glade/gbwidgets/gbiconview.c:135
msgid "If the view can be reordered using Drag and Drop"
msgstr "Se o visor pode ser reordenado usando Coller e Soltar"

#: ../glade/gbwidgets/gbiconview.c:308
msgid "Icon View"
msgstr "Vista de iconas"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:301
msgid "Named Icon:"
msgstr "Icona nomeada:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:302
msgid "The named icon to use"
msgstr "A icona nomeada a usar"

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Tamaño da icona:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "O tamaño da icona por defecto"

#: ../glade/gbwidgets/gbimage.c:115
msgid "Pixel Size:"
msgstr "Tamaño do píxel:"

#: ../glade/gbwidgets/gbimage.c:116
msgid "The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""
"O tamaño do da icona en píxels, ou -1 para usar a propiedade do tamaño da "
"icona"

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "Aliñamento horizontal"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "Aliñamento vertical"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Imaxe"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "O elemento do menú de inventario non válido"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Elemento de menú con imaxe"

#: ../glade/gbwidgets/gbinputdialog.c:257
msgid "Input Dialog"
msgstr "Diálogo de Entrada"

#: ../glade/gbwidgets/gblabel.c:170
msgid "Use Underline:"
msgstr "Usar subraiado:"

#: ../glade/gbwidgets/gblabel.c:171
msgid "If the text includes an underlined access key"
msgstr "Indica se o texto inclúe unha combinación de teclas subraiada"

#: ../glade/gbwidgets/gblabel.c:172
msgid "Use Markup:"
msgstr "Usar marcado:"

#: ../glade/gbwidgets/gblabel.c:173
msgid "If the text includes pango markup"
msgstr "Indica se o texto inclúe marcado de pango"

#: ../glade/gbwidgets/gblabel.c:174
msgid "Justify:"
msgstr "Xustificación:"

#: ../glade/gbwidgets/gblabel.c:175
msgid "The justification of the lines of the label"
msgstr "Xustificación das liñas da etiqueta"

#: ../glade/gbwidgets/gblabel.c:177
msgid "Wrap Text:"
msgstr "Axuste de texto:"

#: ../glade/gbwidgets/gblabel.c:178
msgid "If the text is wrapped to fit within the width of the label"
msgstr "Cubrir o texto para que caiba no ancho da etiqueta"

#: ../glade/gbwidgets/gblabel.c:179
msgid "Selectable:"
msgstr "Seleccionable:"

#: ../glade/gbwidgets/gblabel.c:180
msgid "If the label text can be selected with the mouse"
msgstr "Se a etiqueta do texto pode seleccionarse co rato"

#: ../glade/gbwidgets/gblabel.c:182
msgid "The horizontal alignment of the entire label"
msgstr "Aliñamento horizontal de toda a etiqueta"

#: ../glade/gbwidgets/gblabel.c:185
msgid "The vertical alignment of the entire label"
msgstr "Aliñamento vertical de toda a etiqueta"

#: ../glade/gbwidgets/gblabel.c:191
msgid "Focus Target:"
msgstr "Enfocar Destino:"

#: ../glade/gbwidgets/gblabel.c:192
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"O widget ao que se establecerá o foco do teclado cando a combinación de "
"teclas sexa usada"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:146
msgid "Ellipsize:"
msgstr "Tamaño da elipse:"

#: ../glade/gbwidgets/gblabel.c:199 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr "Cómo facer a elipse dunha cadea"

#: ../glade/gbwidgets/gblabel.c:202
msgid "The width of the label in characters"
msgstr "O ancho da etiqueta en caracteres"

#: ../glade/gbwidgets/gblabel.c:204
msgid "Single Line Mode:"
msgstr "Modo de liña único:"

#: ../glade/gbwidgets/gblabel.c:205
msgid "If the label is only given enough height for a single line"
msgstr "Se á etiqueta dáselle suficiente altura para unha soa liña"

#: ../glade/gbwidgets/gblabel.c:206
msgid "Angle:"
msgstr "Ángulo:"

#: ../glade/gbwidgets/gblabel.c:207
msgid "The angle of the label text"
msgstr "O ángulo do texto da etiqueta"

#: ../glade/gbwidgets/gblabel.c:333 ../glade/gbwidgets/gblabel.c:348
#: ../glade/gbwidgets/gblabel.c:616
msgid "Auto"
msgstr "Automático"

#: ../glade/gbwidgets/gblabel.c:872 ../glade/glade_menu_editor.c:411
msgid "Label"
msgstr "Etiqueta"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Ancho do área:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "O ancho do área de colocación"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Altura do área:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "A altura do área de colocación"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "A coordenada X do widget no GtkLayout"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "A coordenada Y do widget no GtkLayout"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Colocación"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "Maneira de escoller na lista"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Lista"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Opción da Lista"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Menú Popup"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:215
msgid "_File"
msgstr "_Ficheiro"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:223 ../glade/glade_project_window.c:692
msgid "_Edit"
msgstr "_Editar"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:229 ../glade/glade_project_window.c:721
msgid "_View"
msgstr "_Ver"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:231 ../glade/glade_project_window.c:834
msgid "_Help"
msgstr "A_xuda"

#: ../glade/gbwidgets/gbmenubar.c:232
msgid "_About"
msgstr "_Acerca de"

#: ../glade/gbwidgets/gbmenubar.c:291
msgid "Pack Direction:"
msgstr "Dirección de paquete:"

#: ../glade/gbwidgets/gbmenubar.c:292
msgid "The pack direction of the menubar"
msgstr "Dirección do paquete da barra de menú"

#: ../glade/gbwidgets/gbmenubar.c:294
msgid "Child Direction:"
msgstr "Dirección filla:"

#: ../glade/gbwidgets/gbmenubar.c:295
msgid "The child pack direction of the menubar"
msgstr "A dirección do paquete da barra de menú"

#: ../glade/gbwidgets/gbmenubar.c:300 ../glade/gbwidgets/gbmenubar.c:418
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Editar Menús..."

#: ../glade/gbwidgets/gbmenubar.c:541
msgid "Menu Bar"
msgstr "Barra de Menú"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Elemento do Menú"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
msgid "Show Horizontal:"
msgstr "Amosar Horizontal:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
msgid "If the item is visible when the toolbar is horizontal"
msgstr "Se o elemento é visible cando a barra de ferramentas é horizontal"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
msgid "Show Vertical:"
msgstr "Amosar Vertical:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
msgid "If the item is visible when the toolbar is vertical"
msgstr "Se o elemento é visible cando a barra de ferramentas é vertical"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr "É importante:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""
"Se o texto do elemento debería mostrarse cando o modo da barra de "
"ferramentas é GTK_TOOLBAR_BOTH_HORIZ"

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
msgid "Toolbar Button with Menu"
msgstr "Botón de barra de ferramentas con menú"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Novo notebook"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:125
msgid "Number of pages:"
msgstr "Número de páxinas:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "Mostrar Pestanas:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Amosar as pestanas do notebook"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Amosar Bordo:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "Se se mostra o bordo do caderno, cando non se mostren as solapas"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Pos. Pestañas:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "Posicion das pestanas do notebook"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Scrollable:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Pódese facer scroll nas pestanas do notebook"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Bordo solapa Horz.:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "Tamaño do bordo horizontal das solapas do caderno"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Bordo solapa Vert.:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "Tamaño do bordo vertical das solapas do caderno"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Amosar Popup:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "O menú contextual (popup) está activo"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Número de Páxinas:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "Número de páxinas do notebook"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Páxina Anterior"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "Páxina Seguinte"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Borrar Páxina"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "Cambiar á Siguiente"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Cambiar á Anterior"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Inserir páxina despois"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Inserir páxina antes"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "A posición da páxina na lista de páxinas"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Establecer a verdadeiro para permitir á solapa expandirse"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr "Establecer a verdadeiro para permitir á solapa recher o área ocupada"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr "Establecer a verdadeiro para empaquetar as solapas ao comezo dun caderno"

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Etiqueta de menú:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "O texto a amosar no menú emerxente"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Caderno"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "Non se pode engadir %s a un GtkOptionMenu."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Menú de Opcións"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Cor:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "A previsualización é en cor ou en escala de gris"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "A previsualización expándese para encher a área reservada"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Previsualización"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "Orientación dos contidos da barra de progreso"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Fracción:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "A fracción do traballo que se completou"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Paso de pulsación:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""
"A fracción da lonxitude da barra de progreso para mover o bloque saínte ao "
"premerse"

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "O texto a amosar na barra de progreso"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Amosar Texto:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "Amosar o texto na barra de progreso"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Modo de actividad:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "A barra de progreso ten que ser coma o fronte do coche fantástico"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "A aliñación horizontal do texto"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "A aliñación vertical do texto"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "Barra de Progreso"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "O botón de radio está aceso ó comezo"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1039
msgid "Group:"
msgstr "Grupo:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr "Grupo de botóns de radio (por defecto todos os botons co mesmo pai)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Novo Grupo"

#: ../glade/gbwidgets/gbradiobutton.c:465
msgid "Radio Button"
msgstr "Botón de Radio"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "O elemento radio do menu está aceso ó comezo"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"O grupo de elementos de radio do menú (por defecto todos eles teñen omesmo "
"pai"

#: ../glade/gbwidgets/gbradiomenuitem.c:388
msgid "Radio Menu Item"
msgstr "Elemento de Menú Radio"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr ""
"Grupo de botóns de radio (por defecto todos os botons de radio na barra de "
"ferramentas)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:530
msgid "Toolbar Radio Button"
msgstr "Botón de radio de barra de ferramentas"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "Política H:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Cando se vai amosar a barra de desprazamento horizontal"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "Política V:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Cando se vai amosar a barra de desprazamento vertical"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Posición da fiestra:"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr "Onde se ubica a fiestra filla respecto ás barras de desprazamento"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Tipo de sombra:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "A política de actualizamento da barra de desprazamento vertical"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Ventana con desprazamiento"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Separador de menús"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr "Debuxar:"

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr "Se se debuxa o separador, ou só un branco"

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
msgid "Toolbar Separator Item"
msgstr "Elemento separador de barra de ferramentas"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Incremento:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid "The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr ""
"A relación de incremento do boton spin, que se usa xunto co incremento "
"depáxina"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "Número de decimáis a amosar"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Numeración:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr "Se só se permite unha entrada numérica"

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Política de Actualizado:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Cando se emite o sinal value_changed"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Redondeo:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "Redondear o valor a múltiplos do incremento de paso"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Recomezar:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "O valor recomeza no mínimo cando chega ó máximo"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Botón de Incremento"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Pulsador de redimensionado:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr ""
"Se a barra de estado ten un asa de redimensionado para redimensionar a "
"fiestra"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Barra de Estado"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Nova táboa"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Número de filas:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Filas:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "Número de filas da táboa"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Columnas:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "Número de columnas da táboa"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Os fillos teñen que ter o mesmo tamaño"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Esp. Filas:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "Espacio entre cada fila"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Esp. Col:"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "Espacio entre cada columna"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Celda X:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "O bordo esquerdo do widget na táboa"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Celda Y:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "O bordo superior do widget na táboa"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Cols. ocupadas:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "Cantas columnas ocupa o widget dentro da táboa"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Filas ocupadas:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "Cantas filas ocupa o widget dentro da táboa"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "Marxe Horiz.:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "Marxe Vert.:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "Expand. en X:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "Actívao para que o widget se expanda horizontalmente"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Expand. en Y:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "Actívao para que o widget se expanda verticalmente"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "Diminuír en X:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "Deixar que o widget diminúa horizontalmente"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Diminuír en Y:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "Deixar que o widget diminúa en verticalmente"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "Encher en X:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr "Para que o widget encha o seu espacio reservado en horizontal"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Recheo en Y:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "Para que o widget encha o seu espacio reservado en vertical"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Insertar Fila Antes"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Insertar Fila Despóis"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Insertar Columna Antes"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Insertar Columna Despóis"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Borrar Fila"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Borrar Columna"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Táboa"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Centro"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Encher"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:543 ../glade/glade_menu_editor.c:830
#: ../glade/glade_menu_editor.c:1345 ../glade/glade_menu_editor.c:2255
#: ../glade/property.c:2432
msgid "None"
msgstr "Ningún"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Carácter"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Palabra"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Cursor visible:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Se o cursor é visible"

#: ../glade/gbwidgets/gbtextview.c:119
msgid "Overwrite:"
msgstr "Sobrescribir:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr "Se o texto introducido sobrescribe o texto existente"

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr "Acepta tabulación:"

#: ../glade/gbwidgets/gbtextview.c:122
msgid "If tab characters can be entered"
msgstr "Se poden introducirse caracteres de tabulación"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Xustificación:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "A xustificación do texto"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Axuste de liñas:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "Lonxitude máxima do texto"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Espacio superior:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Píxels de espacio en branco diante dos parágrafos"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Espacio inferior:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Píxels de espacio en branco despois dos parágrafos"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Espacio interior:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Píxels de espacio en branco entre liñas rotas nun parágrafo"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Marxe esquerda:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Ancho da marxe esquerda en píxels"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Marxe dereita:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Ancho da marxe dereita en píxels"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Sangrado:"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Cantidade de píxels para sangrar os parágrafos"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Vista do texto"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "O botón de dous estados está aceso ó comezo"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Botón de Dous Estados"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
msgid "Toolbar Toggle Button"
msgstr "Botón de conmutación de barra de ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Nova barra de ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Número de elementos:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "Cantos elementos ten a barra de ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "Orientación da barra de ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Estilo:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "Estilo da barra de ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Texto da axuda:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Activar os textos de axuda"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "Show Arrow:"
msgstr "Amosar Frecha:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""
"Se se debe mostrar unha frecha para emerxer un menú se non cabe toda a barra "
"de ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:427
msgid "If the item should be the same size as other homogeneous items"
msgstr "Se o elemento debería ser do mesmo tamaño que outros elementos homoxéneos"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Insertar Antes"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Insertar Despóis"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Barra de Ferramentas"

#: ../glade/gbwidgets/gbtoolbutton.c:586
msgid "Toolbar Button"
msgstr "Botón de barra de ferramentas"

#: ../glade/gbwidgets/gbtoolitem.c:201
msgid "Toolbar Item"
msgstr "Elemento da barra de ferramentas"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Columna 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Columna 2"

#: ../glade/gbwidgets/gbtreeview.c:87
msgid "Column 3"
msgstr "Columna 3"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr "Ringleira %i"

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Cabeceiras visibles:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Se se deben amosar os botóns de cabeceira de columna"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Indicación de regras:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid "If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""
"Se se establece unha indicación de forma que o motor de temas deba debuxar "
"ringleiras en cores alternas."

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Se a vista é reordenable"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Activar busca:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr "Se o usuario pode buscar interactivamente a través das columnas"

#: ../glade/gbwidgets/gbtreeview.c:123
msgid "Fixed Height Mode:"
msgstr "Modo de altura fixa:"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr "Pon todas as ringleiras á mesma altura para mellorar o rendemento"

#: ../glade/gbwidgets/gbtreeview.c:125
msgid "Hover Selection:"
msgstr "Selección ao pasar por enriba:"

#: ../glade/gbwidgets/gbtreeview.c:126
msgid "Whether the selection should follow the pointer"
msgstr "Indica se a selección debe seguir o punteiro"

#: ../glade/gbwidgets/gbtreeview.c:127
msgid "Hover Expand:"
msgstr "Expandir ao pasar por enriba:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid "Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""
"Indica se as ringleiras deben expandirse ou contraerse cando o punteiro "
"móvase sobre elas"

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Lista ou vista de árbore"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Nova caixa vertical"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Caixa Vertical"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Nova caixa de botóns vertical"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Caixa de Botóns Vertical"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "Tipo da sombra da vista"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Vista"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Panel Vertical"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Regla Vertical"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Escala Vertical"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Barra de Desprazamiento Vertical"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Separador Vertical"

#: ../glade/gbwidgets/gbwindow.c:244
msgid "The title of the window"
msgstr "Título da fiestra"

#: ../glade/gbwidgets/gbwindow.c:247
msgid "The type of the window"
msgstr "Tipo da fiestra"

#: ../glade/gbwidgets/gbwindow.c:251
msgid "Type Hint:"
msgstr "Indicación de consellos:"

#: ../glade/gbwidgets/gbwindow.c:252
msgid "Tells the window manager how to treat the window"
msgstr "Dille ao xestor de fiestras cómo tratar a fiestras"

#: ../glade/gbwidgets/gbwindow.c:257
msgid "The initial position of the window"
msgstr "Posición inicial da fiestra"

#: ../glade/gbwidgets/gbwindow.c:261 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Modal:"

#: ../glade/gbwidgets/gbwindow.c:261
msgid "If the window is modal"
msgstr "Se a fiestra é modal"

#: ../glade/gbwidgets/gbwindow.c:266
msgid "Default Width:"
msgstr "Ancho por defecto:"

#: ../glade/gbwidgets/gbwindow.c:267
msgid "The default width of the window"
msgstr "O ancho predeterminado da fiestra"

#: ../glade/gbwidgets/gbwindow.c:271
msgid "Default Height:"
msgstr "Altura por defecto:"

#: ../glade/gbwidgets/gbwindow.c:272
msgid "The default height of the window"
msgstr "A altura predeterminada da fiestra"

#: ../glade/gbwidgets/gbwindow.c:278
msgid "Resizable:"
msgstr "Redimensionable:"

#: ../glade/gbwidgets/gbwindow.c:279
msgid "If the window can be resized"
msgstr "Se a fiestra pode ser redimensionable"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be shrunk"
msgstr "Se a fiestra pódese diminuir"

#: ../glade/gbwidgets/gbwindow.c:287
msgid "Grow:"
msgstr "Crecer:"

#: ../glade/gbwidgets/gbwindow.c:288
msgid "If the window can be enlarged"
msgstr "Se a fiestra pódese agrandar"

#: ../glade/gbwidgets/gbwindow.c:293
msgid "Auto-Destroy:"
msgstr "Auto destruir:"

#: ../glade/gbwidgets/gbwindow.c:294
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Se a fiestra destrúese cando a súa fiestra nai é destruida"

#: ../glade/gbwidgets/gbwindow.c:298
msgid "The icon for this window"
msgstr "A icona para esta fiestra"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "Role:"
msgstr "Rol:"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""
"Un identificador único para a fiestra que será usado cando se restableza "
"unha sesión"

#: ../glade/gbwidgets/gbwindow.c:308
msgid "Decorated:"
msgstr "Decorado:"

#: ../glade/gbwidgets/gbwindow.c:309
msgid "If the window should be decorated by the window manager"
msgstr "Se a fiestra debe ser decorada polo xestor de fiestras"

#: ../glade/gbwidgets/gbwindow.c:312
msgid "Skip Taskbar:"
msgstr "Saltar barra de tarefas:"

#: ../glade/gbwidgets/gbwindow.c:313
msgid "If the window should not appear in the task bar"
msgstr "Se a fiestra non debe aparecer na barra de tarefas"

#: ../glade/gbwidgets/gbwindow.c:316
msgid "Skip Pager:"
msgstr "Saltar paxinador:"

#: ../glade/gbwidgets/gbwindow.c:317
msgid "If the window should not appear in the pager"
msgstr "Se a fiestra non debe aparecer no paxinador"

#: ../glade/gbwidgets/gbwindow.c:320
msgid "Gravity:"
msgstr "Gravidade:"

#: ../glade/gbwidgets/gbwindow.c:321
msgid "The reference point to use when the window coordinates are set"
msgstr "O punto de referencia a usar cando se establezan as coordenadas da fiestra"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "Focus On Map:"
msgstr "Enfocar ao Mapa:"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "If the window should receive the input focus when it is mapped"
msgstr "Se a fiestra debería recibir o foco cando é mapeada"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "Urgency Hint:"
msgstr "Golpe urxente:"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "If the window should be brought to the user's attention"
msgstr "Se a fiestra debería ser contruido pola tentativa do usuario"

#: ../glade/gbwidgets/gbwindow.c:1232
msgid "Window"
msgstr "Fiestra"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:75
msgid "Error"
msgstr "Error"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Error do Sistema"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "Error abrindo ficheiro"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "Error lendo ficheiro"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "Error escribindo ficheiro"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "Directorio inválido"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "Valor non válido"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "Entidade XML non válida"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "Esperábase inicio de elemento"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "Esperábase final de elemento"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "Esperábase dato tipo caracter"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "Non se atopou o identificador de clase"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Clase descoñecida"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "Compoñente non válida"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "Fin de ficheiro inesperado"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Código de error descoñecido"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Controlado por"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Controlador para"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Etiqueta para"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Etiquetado por"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Membro de"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Nodo fillo de"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr "Flúe hacia"

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr "Flúe desde"

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr "Subfiestra de"

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr "Empotra"

#: ../glade/glade_atk.c:130
msgid "Embedded By"
msgstr "Empotrado por"

#: ../glade/glade_atk.c:131
msgid "Popup For"
msgstr "Emerxente para"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr "Fiestra nai de"

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Relación: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "Widget"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:773
#: ../glade/property.c:776
msgid "Name:"
msgstr "Nome:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr "O nome do widget que pasar ás tecnoloxías de accesibilidade"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "Descrición:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr "A descrición do widget a pasar ás tecnoloxías de accesibilidade"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Descrición da táboa:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "A descrición da táboa a pasar ás tecnoloxías de accesibilidade"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Selecciona os widgets con esta interrelación"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Pulsación"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Premer"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Liberación"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr "Introduza a descrición da acción a pasar ás tecnoloxías de accesibilidade"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Portapapéis"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Necesita seleccionar un widget sobre o cal pegar"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "Non podes pegar sobre fiestras ou diálogos."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"Non podes pegar no widget seleccionado, porque\n"
"foi creado automáticamente polo seu pai."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr "So podes pegar elementos de menú nun menú ou unha barra de menú."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr "So podes pegar botóns na área de acción dun diálogo."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "So podes pegar compoñentes GnomeDockItem nun compoñente GnomeDock."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "So podes pegar compoñentes GnomeDockItem nun compoñente GnomeDockItem."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "Sintoo - pegar nun compoñente GnomeDockItem ainda non está implementado."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "Os compoñentes GnomeDockItem so se poden pegar nun compoñente GnomeDock."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:633
msgid "_New"
msgstr "_Novo"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Crea un novo ficheiro"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "Ob_soleto"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "GTK+ _Básico"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "GTK+ _Adicional"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Seleccionar tecla aceleradora"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Teclas"

#: ../glade/glade_menu_editor.c:395
msgid "Menu Editor"
msgstr "Editor de menú"

#: ../glade/glade_menu_editor.c:412
msgid "Type"
msgstr "Tipo"

#: ../glade/glade_menu_editor.c:413
msgid "Accelerator"
msgstr "Acelerador"

#: ../glade/glade_menu_editor.c:414
msgid "Name"
msgstr "Nome"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:1499
msgid "Handler"
msgstr "Manexador"

#: ../glade/glade_menu_editor.c:416 ../glade/property.c:102
msgid "Active"
msgstr "Activo"

#: ../glade/glade_menu_editor.c:417
msgid "Group"
msgstr "Grupo"

#: ../glade/glade_menu_editor.c:418
msgid "Icon"
msgstr "Icono"

#: ../glade/glade_menu_editor.c:459
msgid "Move the item and its children up one place in the list"
msgstr "Move o elemento e os fillos una posición arriba na lista"

#: ../glade/glade_menu_editor.c:471
msgid "Move the item and its children down one place in the list"
msgstr "Move o elemento e os fillos unha posición abaixo na lista"

#: ../glade/glade_menu_editor.c:483
msgid "Move the item and its children up one level"
msgstr "Move o elemento e os fillos un nivel cara arriba"

#: ../glade/glade_menu_editor.c:495
msgid "Move the item and its children down one level"
msgstr "Move o elemento e os fillos un nivel cara abaixo"

#: ../glade/glade_menu_editor.c:525
msgid "The stock item to use."
msgstr "O elemento de inventario a usar."

#: ../glade/glade_menu_editor.c:528 ../glade/glade_menu_editor.c:643
msgid "Stock Item:"
msgstr "Elemento de inventario:"

#: ../glade/glade_menu_editor.c:641
msgid "The stock Gnome item to use."
msgstr "O elemento do grupo Gnome a usar"

#: ../glade/glade_menu_editor.c:746
msgid "The text of the menu item, or empty for separators."
msgstr "O texto do elemento de menú, ou baleiro para separadores."

#: ../glade/glade_menu_editor.c:770 ../glade/property.c:777
msgid "The name of the widget"
msgstr "O nome do widget"

#: ../glade/glade_menu_editor.c:791
msgid "The function to be called when the item is selected"
msgstr "A función que vai ser chamada cando se seleccióne o elemento"

#: ../glade/glade_menu_editor.c:793 ../glade/property.c:1547
msgid "Handler:"
msgstr "Manexador:"

#: ../glade/glade_menu_editor.c:812
msgid "An optional icon to show on the left of the menu item."
msgstr "Icono opcional para mostrar á esquerda do elemento do menú."

#: ../glade/glade_menu_editor.c:935
msgid "The tip to show when the mouse is over the item"
msgstr "Axuda a mostrar cando o rato está sobre o elemento"

#: ../glade/glade_menu_editor.c:937 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Texto de axuda:"

#: ../glade/glade_menu_editor.c:958
msgid "_Add"
msgstr "_Engadir"

#: ../glade/glade_menu_editor.c:963
msgid "Add a new item below the selected item."
msgstr "Engadir un novo item debaixo do elixido"

#: ../glade/glade_menu_editor.c:968
msgid "Add _Child"
msgstr "Engadir _fillo"

#: ../glade/glade_menu_editor.c:973
msgid "Add a new child item below the selected item."
msgstr "Engadir un novo elemento fillo debaixo do elemento seleccionado."

#: ../glade/glade_menu_editor.c:979
msgid "Add _Separator"
msgstr "Engadir _separador"

#: ../glade/glade_menu_editor.c:984
msgid "Add a separator below the selected item."
msgstr "Engade un separador debaixo do elemento seleccionado."

#: ../glade/glade_menu_editor.c:989 ../glade/glade_project_window.c:242
msgid "_Delete"
msgstr "_Borrar"

#: ../glade/glade_menu_editor.c:994
msgid "Delete the current item"
msgstr "Borrar o elemento actual"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:1000
msgid "Item Type:"
msgstr "Tipo de elmento:"

#: ../glade/glade_menu_editor.c:1016
msgid "If the item is initially on."
msgstr "Se o elemento está inicialmente activo."

#: ../glade/glade_menu_editor.c:1018
msgid "Active:"
msgstr "Activo:"

#: ../glade/glade_menu_editor.c:1023 ../glade/glade_menu_editor.c:1638
#: ../glade/property.c:2216 ../glade/property.c:2226
msgid "No"
msgstr "Non"

#: ../glade/glade_menu_editor.c:1037
msgid "The radio menu item's group"
msgstr "O grupo de elementos de menú radio"

#: ../glade/glade_menu_editor.c:1054 ../glade/glade_menu_editor.c:2414
#: ../glade/glade_menu_editor.c:2554
msgid "Radio"
msgstr "Radio"

#: ../glade/glade_menu_editor.c:1061 ../glade/glade_menu_editor.c:2412
#: ../glade/glade_menu_editor.c:2552
msgid "Check"
msgstr "Comprobar"

#: ../glade/glade_menu_editor.c:1068 ../glade/property.c:102
msgid "Normal"
msgstr "Normal"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1077
msgid "Accelerator:"
msgstr "Acelerador:"

#: ../glade/glade_menu_editor.c:1114 ../glade/property.c:1682
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1119 ../glade/property.c:1685
msgid "Shift"
msgstr "Shift"

#: ../glade/glade_menu_editor.c:1124 ../glade/property.c:1688
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1129 ../glade/property.c:1695
msgid "Key:"
msgstr "Tecla:"

#: ../glade/glade_menu_editor.c:1135 ../glade/property.c:1674
msgid "Modifiers:"
msgstr "Modificadores:"

#: ../glade/glade_menu_editor.c:1638 ../glade/glade_menu_editor.c:2419
#: ../glade/glade_menu_editor.c:2562 ../glade/property.c:2216
msgid "Yes"
msgstr "Sí"

#: ../glade/glade_menu_editor.c:2008
msgid "Select icon"
msgstr "Seleccionar icona"

#: ../glade/glade_menu_editor.c:2353 ../glade/glade_menu_editor.c:2714
msgid "separator"
msgstr "separador"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3638 ../glade/glade_project_window.c:369
#: ../glade/property.c:5115
msgid "New"
msgstr "Novo"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Selector"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directorio do proxecto non está elixido.\n"
"Por favor, elíxao co diálogo de opcións de proxecto.\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directorio do código fonte non está elixido.\n"
"Por favor, elíxao co diálogo de opcións de proxecto.\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"Directorio non válido para o código fonte:\n"
"\n"
"O directorio do código fonte debe ser o do proxecto\n"
"ou ben un subdirectorio deste.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directorio dos pixmaps non está elixido.\n"
"Por favor, elíxao co diálogo de opcións de proxecto.\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "Sintoo - xerar código para %s ainda non está implementado"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""
"Seu proxecto usa widgets obsoletos que Gtkmm-2\n"
"non soporta. Comprobe no seu proxecto estos\n"
"widgets, e use os seus remprazos."

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"Error executando glade-- para xerar o código C++.\n"
"Comprobe que ten instalado glade-- e que está incluido no PATH.\n"
"Entón probe executando 'glade-- <proxecto_ficheiro.glade>' no terminal."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"Erro ao executar gate para xerar o código fonte Ada95.\n"
"Comprobe que ten instalado gate e que se atopa na súa variable PATH.\n"
"Despois tente executar 'gate <ficheiro_proxecto.glade>' nunha terminal."

#: ../glade/glade_project.c:571
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"Erro ao executar glade2perl para xerar o código fonte Perl.\n"
"Comprobe que ten instalado glade2perl e que se atopa na súa variable PATH.\n"
"Despois tente executar 'glade2perl <ficheiro_proxecto.glade>' nunha terminal."

#: ../glade/glade_project.c:594
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"Erro ao executar eglade para xerar o código fonte Eiffel.\n"
"Comprobe que ten instalado eglade e que se atopa na súa variable PATH.\n"
"Despois tente executar 'eglade <ficheiro_proxecto.glade>' nunha terminal."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directorio de Pixmaps non está elixido.\n"
"Por favor, elíxao co dialogo de opcións de proxecto.\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "Error escribindo o ficheiro de proxecto XML\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:385
#: ../glade/glade_project_window.c:890
msgid "Project Options"
msgstr "Opcións do proxecto"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "Xeral"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Opcións Básicas:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "O directorio do proxecto"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Directorio do proxecto:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Facer un Browse..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "O nome do proxecto actual"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Nome do proxecto:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "O Nome do programa"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "O Ficheiro do proxecto"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Ficheiro do proxecto:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Subdirectorios:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "O directorio onde gardar o código fonte xerado"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Directorio do código fonte:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "Directorio para gardar os pixmaps (imaxes)"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Directorio dos pixmaps:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "Licencia que se vai engadir ó principio dos ficheiros xerados"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Linguaxe:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Activar o soporte de Gnome"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Se imos a construir unha aplicación de Gnome"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Activar o soporte de Gnome DB"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Se se vai construir unha aplicación Gnome DB"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "Opcións de C"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr "<b>Nota:</b> para aplicacións grandes recoméndase o uso de libglade."

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Opcións xerais:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Soporte de Gettext"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr ""
"Se as cadeas de texto van ser marcadas para a sua traducción poloprograma "
"Gettext"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "Fixar o nome dos Widgets"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Se o nome dos widgets vai posto no código fonte"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Copias de seguridade dos ficheiros fonte"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Facer copias de seguridade dos fontes antigos"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Soporte de Axuda de Gnome"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Se se debe incluir soporte para o sistema de axuda de Gnome"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Opcións de ficheiros de saída:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "Xerar ficheiro main.c"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr ""
"Se se vai xerar un ficheiro main.c coa función main(), no caso de que "
"nonexista xa"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Xerar funcións de soporte"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Se van ser xeradas funcións de soporte"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Xerar ficheiros compilación"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Para que sexan xerados ficheiros para compilar o programa, como Makefile.am "
"e configure.in, se non existisen xa."

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "Funcións de construcción da interface:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "Ficheiro onde se van escribir as funcións para crear a interface"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Ficheiro fonte:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr "O ficheiro onde van as declaracións das funcións que crean a interface"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Ficheiro de cabeceira:"

#: ../glade/glade_project_options.c:594
msgid "Source file for interface creation functions"
msgstr "Ficheiro fonte para as funcións de creación da interface"

#: ../glade/glade_project_options.c:595
msgid "Header file for interface creation functions"
msgstr "Ficheiro cabeceira para as funcións de creación da interface"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Manexadores de sinais e funcións callback"

#: ../glade/glade_project_options.c:610
msgid "The file in which the empty signal handler and callback functions are written"
msgstr ""
"O ficheiro onde se escribirán as funcións dos manexadores de sinais e de "
"callback baleiros"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"O ficheiro onde van as declaracións dos manexadores de sinais e as funcións "
"callback (de retrochamada)"

#: ../glade/glade_project_options.c:640
msgid "Source file for signal handler and callback functions"
msgstr "Ficheiro fonte para as funcións manexadoras de sinais e de callback"

#: ../glade/glade_project_options.c:641
msgid "Header file for signal handler and callback functions"
msgstr "O ficheiro cabeceira para as funcións dos manexadores de sinais e de callback"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Funcións de soporte"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "O ficheiro onde van ser escritas as funcións de soporte"

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "O ficheiro onde van ser escritas as declaracións das funcións de soporte"

#: ../glade/glade_project_options.c:686
msgid "Source file for support functions"
msgstr "Ficheiro fonte para as funcións de apoio"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr "Ficheiro cabeceira para as funcións de soporte"

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "Opcións de LibGlade"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Cadeas traducibles:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr "<b>Nota:</b> esta opción é obsoleta - use intltool no seu lugar."

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Gardar as cadeas traducibles"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Para gardar as cadeas traducibles nun ficheiro C separado e permitir que "
"LibGlade faga a traducción das interfaces"

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "O ficheiro de código C onde se gardan as cadeas traducibles"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Ficheiro:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Seleccione o directorio do proxecto"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Necesita indicar un ficheiro para almacenar as cadeas traducibles"

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Necesita indicar o directorio do proxecto"

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Necesita indicar un ficheiro para almacenar o proxecto"

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Necesita indicar un nome para o proxecto"

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Necesita indicar un nome para o programa"

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Necesita indicar o directorio para o código fonte"

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Necesita indicar o directorio para as imaxes"

#: ../glade/glade_project_window.c:187
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"Non se puido mostrar o ficheiro de axuda: %s.\n"
"\n"
"Erro: %s"

#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:635
msgid "Create a new project"
msgstr "Crear un novo proxecto"

#: ../glade/glade_project_window.c:219 ../glade/glade_project_window.c:655
#: ../glade/glade_project_window.c:906
msgid "_Build"
msgstr "_Compilar"

#: ../glade/glade_project_window.c:220 ../glade/glade_project_window.c:666
msgid "Output the project source code"
msgstr "Escribe o código fonte do proxecto"

#: ../glade/glade_project_window.c:226 ../glade/glade_project_window.c:669
msgid "Op_tions..."
msgstr "Op_cións..."

#: ../glade/glade_project_window.c:227 ../glade/glade_project_window.c:678
msgid "Edit the project options"
msgstr "Elixir as opcións do proxecto"

#: ../glade/glade_project_window.c:242 ../glade/glade_project_window.c:717
msgid "Delete the selected widget"
msgstr "Borra o widget seleccionado"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:728
msgid "Show _Palette"
msgstr "Mostrar _Paleta"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:733
msgid "Show the palette of widgets"
msgstr "Mostra a paleta de widgets"

#: ../glade/glade_project_window.c:266 ../glade/glade_project_window.c:738
msgid "Show Property _Editor"
msgstr "Mostrar _Editor de propiedades"

#: ../glade/glade_project_window.c:267 ../glade/glade_project_window.c:744
msgid "Show the property editor"
msgstr "Amosa o editor de propiedades"

#: ../glade/glade_project_window.c:273 ../glade/glade_project_window.c:748
msgid "Show Widget _Tree"
msgstr "Mostrar a _Arbore de Widgets"

#: ../glade/glade_project_window.c:274 ../glade/glade_project_window.c:754
#: ../glade/main.c:82 ../glade/main.c:116
msgid "Show the widget tree"
msgstr "Mostrar a árbore de widgets"

#: ../glade/glade_project_window.c:280 ../glade/glade_project_window.c:758
msgid "Show _Clipboard"
msgstr "Mostrar Po_rtapapeis"

#: ../glade/glade_project_window.c:281 ../glade/glade_project_window.c:764
#: ../glade/main.c:86 ../glade/main.c:120
msgid "Show the clipboard"
msgstr "Mostra o contido do portapapeis"

#: ../glade/glade_project_window.c:299
msgid "Show _Grid"
msgstr "Mostrar Re_lliña"

#: ../glade/glade_project_window.c:300 ../glade/glade_project_window.c:800
msgid "Show the grid (in fixed containers only)"
msgstr "Mostra la relliña (nos contenedores estáticos soamente)"

#: ../glade/glade_project_window.c:306
msgid "_Snap to Grid"
msgstr "A_xustar á Relliña"

#: ../glade/glade_project_window.c:307
msgid "Snap widgets to the grid"
msgstr "Aliña os widgets á relliña"

#: ../glade/glade_project_window.c:313 ../glade/glade_project_window.c:772
msgid "Show _Widget Tooltips"
msgstr "Mostrar consello dos _Widgets"

#: ../glade/glade_project_window.c:314 ../glade/glade_project_window.c:780
msgid "Show the tooltips of created widgets"
msgstr "Mostra os consellos dos widgets creados"

#: ../glade/glade_project_window.c:323 ../glade/glade_project_window.c:803
msgid "Set Grid _Options..."
msgstr "_Opcions da relliña..."

#: ../glade/glade_project_window.c:324
msgid "Set the grid style and spacing"
msgstr "Elixir o estilo e separación para a relliña"

#: ../glade/glade_project_window.c:330 ../glade/glade_project_window.c:824
msgid "Set Snap O_ptions..."
msgstr "O_pcións de aliñación..."

#: ../glade/glade_project_window.c:331
msgid "Set options for snapping to the grid"
msgstr "Establece as opcións da aliñación á relliña"

#: ../glade/glade_project_window.c:343
msgid "_FAQ"
msgstr "_FAQ"

#: ../glade/glade_project_window.c:344
msgid "View the Glade FAQ"
msgstr "Ver as preguntas frecuentes de Glade"

#. create File menu
#: ../glade/glade_project_window.c:358 ../glade/glade_project_window.c:626
msgid "_Project"
msgstr "_Proxecto"

#: ../glade/glade_project_window.c:369 ../glade/glade_project_window.c:873
#: ../glade/glade_project_window.c:1055
msgid "New Project"
msgstr "Novo proxecto"

#: ../glade/glade_project_window.c:374
msgid "Open"
msgstr "Abrir"

#: ../glade/glade_project_window.c:374 ../glade/glade_project_window.c:878
#: ../glade/glade_project_window.c:1116
msgid "Open Project"
msgstr "Abrir proxecto"

#: ../glade/glade_project_window.c:379
msgid "Save"
msgstr "Gardar"

#: ../glade/glade_project_window.c:379 ../glade/glade_project_window.c:882
#: ../glade/glade_project_window.c:1481
msgid "Save Project"
msgstr "Gardar proxecto"

#: ../glade/glade_project_window.c:385
msgid "Options"
msgstr "Opcións"

#: ../glade/glade_project_window.c:390
msgid "Build"
msgstr "Compilar"

#: ../glade/glade_project_window.c:390
msgid "Build the Source Code"
msgstr "Xerar o código fonte"

#: ../glade/glade_project_window.c:639
msgid "Open an existing project"
msgstr "Abre un proxecto xa existente"

#: ../glade/glade_project_window.c:643
msgid "Save project"
msgstr "Gardar proxecto"

#: ../glade/glade_project_window.c:688
msgid "Quit Glade"
msgstr "Saír de Glade"

#: ../glade/glade_project_window.c:702
msgid "Cut the selected widget to the clipboard"
msgstr "Corta o widget seleccionado ao portapapeis"

#: ../glade/glade_project_window.c:707
msgid "Copy the selected widget to the clipboard"
msgstr "Copiar o widget seleccionado ao portapapeis"

#: ../glade/glade_project_window.c:712
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Pega o widget do portapapeis sobre o widget seleccionado"

#: ../glade/glade_project_window.c:784
msgid "_Grid"
msgstr "_Relliña"

#: ../glade/glade_project_window.c:792
msgid "_Show Grid"
msgstr "_Mostrar Relliña"

#: ../glade/glade_project_window.c:809
msgid "Set the spacing between grid lines"
msgstr "Axustar o espaciado entre as liñas da relliña"

#: ../glade/glade_project_window.c:812
msgid "S_nap to Grid"
msgstr "Aliñar _coa relliña"

#: ../glade/glade_project_window.c:820
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr ""
"Fai que os widgets se axusten á relliña (somentes en contenedores de tipo "
"fixed (estáticos)"

#: ../glade/glade_project_window.c:830
msgid "Set which parts of a widget snap to the grid"
msgstr "Elixe que partes de un widget ten que axustarse ós puntos da relliña"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:855
msgid "_About..."
msgstr "_Acerca de..."

#: ../glade/glade_project_window.c:896
msgid "Optio_ns"
msgstr "Opció_ns"

#: ../glade/glade_project_window.c:900
msgid "Write Source Code"
msgstr "Escribir código fonte..."

#: ../glade/glade_project_window.c:992 ../glade/glade_project_window.c:1697
#: ../glade/glade_project_window.c:1986
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:999
msgid "Are you sure you want to create a new project?"
msgstr "¿Está seguro de que quere comezar un proxecto novo?"

#: ../glade/glade_project_window.c:1059
msgid "New _GTK+ Project"
msgstr "Novo proxecto _GTK+"

#: ../glade/glade_project_window.c:1060
msgid "New G_NOME Project"
msgstr "Novo proxecto G_nome"

#: ../glade/glade_project_window.c:1063
msgid "Which type of project do you want to create?"
msgstr "Que tipo de proxecto quere crear?"

#: ../glade/glade_project_window.c:1097
msgid "New project created."
msgstr "Novo proxecto creado."

#: ../glade/glade_project_window.c:1187
msgid "Project opened."
msgstr "Proxecto aberto."

#: ../glade/glade_project_window.c:1201
msgid "Error opening project."
msgstr "Error abrindo proxecto."

#: ../glade/glade_project_window.c:1265
msgid "Errors opening project file"
msgstr "Error abrindo o ficheiro do proxecto"

#: ../glade/glade_project_window.c:1271
msgid " errors opening project file:"
msgstr " errores abrindo o ficheiro do proxecto:"

#: ../glade/glade_project_window.c:1344
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"Non hai ningún proxecto aberto.\n"
"Crear un novo proxecto co comando Proxecto/Novo."

#: ../glade/glade_project_window.c:1548
msgid "Error saving project"
msgstr "Error gardando o proxecto."

#: ../glade/glade_project_window.c:1550
msgid "Error saving project."
msgstr "Error gardando proxecto."

#: ../glade/glade_project_window.c:1556
msgid "Project saved."
msgstr "Proxecto gardado."

#: ../glade/glade_project_window.c:1626
msgid "Errors writing source code"
msgstr "Error ó escribir o código fuente"

#: ../glade/glade_project_window.c:1628
msgid "Error writing source."
msgstr "Error ó escribir o código fuente"

#: ../glade/glade_project_window.c:1634
msgid "Source code written."
msgstr "Código fonte escrito"

#: ../glade/glade_project_window.c:1665
msgid "System error message:"
msgstr "Mensaxe de error do sistema:"

#: ../glade/glade_project_window.c:1704
msgid "Are you sure you want to quit?"
msgstr "Está seguro de que quere saír?"

#: ../glade/glade_project_window.c:1988 ../glade/glade_project_window.c:2048
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "(C) 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1989 ../glade/glade_project_window.c:2047
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr "Glade é un constructor de interfaces de usuario para GTK+ e Gnome."

#: ../glade/glade_project_window.c:2018
msgid "About Glade"
msgstr "Acerca de Glade"

#: ../glade/glade_project_window.c:2103
msgid "<untitled>"
msgstr "<sen titulo>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Navegador da base de datos"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Cadro combinado vinculado a datos"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr "GnomeDbConnectionProperties"

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Selector de conexión"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "Configurador de DSN"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "Druida de configuración de DSN"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr "Resaltar texto:"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr "Se se selecciona, o texto será resaltado dentro do widget"

#: ../glade/gnome-db/gnomedbeditor.c:178
msgid "GnomeDbEditor"
msgstr "GnomeDbEditor"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Visor de erros da base de datos"

#: ../glade/gnome-db/gnomedberrordlg.c:219
msgid "Database error dialog"
msgstr "Diálogo de erros da base de datos"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Formulario"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr "Texto dentro da barra gris"

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr "Barra gris"

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Relliña vinculada a datos"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Lista vinculada a datos"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Widget de inicio de sesión na base de datos"

#: ../glade/gnome-db/gnomedblogindlg.c:78
msgid "Login"
msgstr "Inicio de sesión"

#: ../glade/gnome-db/gnomedblogindlg.c:221
msgid "Database login dialog"
msgstr "Diálogo de inicio de sesión na base de datos"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Selector do proveedor"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr "GnomeDbQueryBuilder"

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Selector da fonte de datos"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Editor da táboa"

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Permitir Aboiar:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Permítense elementos dock aboiantes"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Por a banda do elemento dock arriba"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Por a banda do elemento dock abaixo"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Por a banda do elemento dock á esquerda"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Por a banda do elemento dock á dereita"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Engadir elemendo dock aboiante"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Dock Gnome"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Bloqueado:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Se o elemento de anclaxe está anclado nunha posición"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Exclusivo:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "O elemento dock e sempre o único na sua banda"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Nunca aboiar:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "O elemento dock nunca pode aboiar na súa fiestra"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Xamais Vertical:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "O elemento dock xamais pode ser vertical"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Xamais Horizontal:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "O elemento dock xamais pode ser horizontal"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "Tipo de sombra arredor do elemento de anclaxe"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "Orientación do elemento de anclaxe aboiante"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Engadir elemento aboiante antes"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Engadir elemento aboiante despois"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Elemento de anclaxe de Gnome"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr "Información a maiores, como descripción do paquete e a páxina web do mesmo"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Diálogo 'Acerca de' de Gnome"

#: ../glade/gnome/gnomeapp.c:171
msgid "New File"
msgstr "Novo Ficheiro"

#: ../glade/gnome/gnomeapp.c:173
msgid "Open File"
msgstr "Abrir ficheiro"

#: ../glade/gnome/gnomeapp.c:175
msgid "Save File"
msgstr "Gardar ficheiro"

#: ../glade/gnome/gnomeapp.c:204
msgid "Status Bar:"
msgstr "Barra de Estado:"

#: ../glade/gnome/gnomeapp.c:205
msgid "If the window has a status bar"
msgstr "Se a fiestra ten barra de estado"

#: ../glade/gnome/gnomeapp.c:206
msgid "Store Config:"
msgstr "Gardar configuración"

#: ../glade/gnome/gnomeapp.c:207
msgid "If the layout is saved and restored automatically"
msgstr "Se a distribución gárdase e restáurase automaticamente"

#: ../glade/gnome/gnomeapp.c:443
msgid "Gnome Application Window"
msgstr "Fiestra de aplicación de GNOME"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Mensaxe de estado."

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "Progreso:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "A barra da aplicación ten indicador de progreso"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Estado:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr ""
"A barra da aplicación ten una zona para mensaxes de estado e entradade datos "
"polo usuario"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Barra de aplicación de GNOME"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Anti-Alias:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr "Faise un efecto anti-alias, para suavizar os bordes do texto e dosgráficos"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "A coordenada x inferior"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "A coordenada y inferior"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2:"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "A coordenada x superior"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "A coordenada y superior"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Píxels por unidade:"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "O número de píxels que contén a unha unidade"

#: ../glade/gnome/gnomecanvas.c:248
msgid "GnomeCanvas"
msgstr "Canvas de Gnome"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Degradado:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Se o exemplo ten que usar entramado de cor para ser máis exacto"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Escolle unha cor"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Selector de cor de GNOME"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "Non se puido crear o control Bonobo"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Control Bonobo novo"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Seleccionar un control Bonobo"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3902
msgid "Description"
msgstr "Descrición"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Control Bonobo"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Amosar hora:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Se ademáis da data móstrase a hora"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "Formato 24 Horas:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Se a hora móstrase en formato de 24 horas"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Hora de inicio:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "A primeira hora a mostrar no menú emerxente"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Hora de fin:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "A hora máxima que se amosa no menú contextual (popup)"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "GnomeDateEdit"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "Auto Close:"
msgstr "Auto-Pechar:"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "If the dialog closes when any button is clicked"
msgstr "O diálogo péchase cando se pulsa un botón"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "Hide on Close:"
msgstr "Esconder ó pechar:"

#: ../glade/gnome/gnomedialog.c:156 ../glade/gnome/gnomemessagebox.c:193
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "O diálogo escóndese ó pecharse en vez de destruirse"

#: ../glade/gnome/gnomedialog.c:342
msgid "Gnome Dialog Box"
msgstr "Caixa de diálogo GNOME"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Novo Druida Gnome"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Amosar axuda"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Amosar o botón de axuda."

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Engadir páxina de inicio"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Engadir páxina de remate"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Druida"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "O título da páxina"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "O texto principal da páxina, introducindo a xente ao druida."

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Cor do título:"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "A cor do texto do título"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Cor do texto:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "A cor do texto principal"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "A cor do fondo da páxina"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Cor de fondo do logo:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "A cor de fondo arredor do logo"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Cor da caixa de texto:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "A cor de fonfo da área de texto principal"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Imaxe de logotipo:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "O logo a mostrar na parte superior dereita da páxina"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Marca de auga lateral:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "A imaxe principal a mostrar ao lado da páxina."

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Marca de auga superior:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr "A marca de auga a mostrar ao principio da páxina."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Inicio do asistente ou fin de páxina"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Contidos do fondo. Cor:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "A cor de fondo arredor do título"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "A imaxe a mostrar xunto ao inicio da páxina"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Páxina estándar do druida"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "Id. de Historial:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "O identificador no que se graban as entradas do historial"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Max Grabados:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "O número máximo de entradas de historial que se gardan"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Entrada de Gnome"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "Título do dialogo para seleccionar ficheiro"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Directorio:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Se precisas un directorio en lugar dunha carpeta"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "O diálogo para elixir ficheiro ten que ser modal"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr "Usar FileChooser:"

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr "Usar o novo widget GtkFileChooser en vez de GtkFileSelection"

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Entrada de ficheiro GNOME"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "O texto mostrado como vista previa no diálogo de selección de tipo de letra"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Modo:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "Qué amosar no botón para escoller fonte"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "O tamaño da fonte que se usa no botón para elixir fonte"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Selector de fonte GNOME"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "A URL a mostrar cando se preme o botón"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "Texto que se amosa no botón"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Botón de enlace GNOME"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Icono de entrada GNOME"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "O modo de selección"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Ancho da icona:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "O ancho de cada icona"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "Número de pixels entre ringleiras e iconas"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "Número de pixels entre columnas e iconas"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Bodo da icona:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "O número de pixels arredor das iconas (sen uso?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Espaciado do texto:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "O número de pixels entre o texto e a icona"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Texto editable:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Se o texto da icona pode ser editable polo usuario"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Texto estático:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr "Se o texto da icona é estático, nese caso non copiará o GnomeIconList"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Lista de iconas"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Selección de iconas"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "Message Type:"
msgstr "Tipo da Mensaxe:"

#: ../glade/gnome/gnomemessagebox.c:176
msgid "The type of the message box"
msgstr "O tipo de caixa de mensaxes"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "Message:"
msgstr "Mensaxe:"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "The message to display"
msgstr "A mensaxe a mostrar"

#: ../glade/gnome/gnomemessagebox.c:499
msgid "Gnome Message Box"
msgstr "Caixa de mensaxe GNOME"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "Ficheiro da imaxe"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Escalado:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Se a imaxe é escalada"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Ancho escalado:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "A anchura á cal escalar a imaxe"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Alto escalado:"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "A altura á cal escalar a imaxe"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Imaxe de Gnome"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Previsualización:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Ver unha previsualización en pequeno da imaxe (pixmap)"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "Entrada de imaxe de Gnome"

#: ../glade/gnome/gnomepropertybox.c:113
msgid "New GnomePropertyBox"
msgstr "GnomePropertyBox novo"

#: ../glade/gnome/gnomepropertybox.c:366
msgid "Property Dialog Box"
msgstr "Caixa de diálogo de propiedades"

#: ../glade/main.c:70 ../glade/main.c:104
msgid "Write the source code and exit"
msgstr "Escribir o código fonte e saír"

#: ../glade/main.c:74 ../glade/main.c:108
msgid "Start with the palette hidden"
msgstr "Iniciar coa paleta de widgets oculta"

#: ../glade/main.c:78 ../glade/main.c:112
msgid "Start with the property editor hidden"
msgstr "Iniciar co editor de propiedades oculto"

#: ../glade/main.c:460
msgid "glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade: Debe especificar o ficheiro XML coa opción  '-w' ou '--write-"
"source'.\n"

#: ../glade/main.c:474
msgid "glade: Error loading XML file.\n"
msgstr "glade: Erro cargando o ficheiro XML.\n"

#: ../glade/main.c:481
msgid "glade: Error writing source.\n"
msgstr "glade: Erro escribindo o código fonte.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Paleta"

#: ../glade/property.c:73
msgid "private"
msgstr "privado"

#: ../glade/property.c:73
msgid "protected"
msgstr "protexido"

#: ../glade/property.c:73
msgid "public"
msgstr "público"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "Iluminado"

#: ../glade/property.c:103
msgid "Selected"
msgstr "Seleccionado"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Insensible"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Cando a fiestra precisa ser redibuxada"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Cando o rato se move"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Indicacións de movemento do rato"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Movemento do rato con calquera botón pulsado"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Movemento do rato co boton 1 pulsado"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Movemento do rato co boton 2 pulsado"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Movemento do rato co boton 3 pulsado"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "Cualquera botón do rato pulsado"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "Cualquera botón do rato soltado"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "Cualquera tecla pulsada"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "Cualquera tecla soltada"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Cando o rato entra na fiestra"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Cando o rato sale da fiestra"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Calquera cambio do foco de entrada"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Calquera cambio na estructura da fiestra"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "Cualquera cambio nas propiedades de X-Window"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Cualquera cambio na visibilidade"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "Para cursores en programas que usan XInput"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Propiedades"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Empaquetado"

#: ../glade/property.c:625
msgid "Common"
msgstr "Comúns"

#: ../glade/property.c:631
msgid "Style"
msgstr "Estilo"

#: ../glade/property.c:637 ../glade/property.c:4646
msgid "Signals"
msgstr "Sinais"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Propiedades:"

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Propiedades: <ningunha>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "Clase:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "A clase do widget"

#: ../glade/property.c:813
msgid "Width:"
msgstr "Ancho:"

#: ../glade/property.c:814
msgid "The requested width of the widget (usually used to set the minimum width)"
msgstr ""
"A anchura requerida do widget (usualmente empregado para definir o ancho "
"mínimo)"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Altura:"

#: ../glade/property.c:817
msgid "The requested height of the widget (usually used to set the minimum height)"
msgstr ""
"A altura requerida do widget (usualmente empregado para definir a altura "
"mínima)"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Visible:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "O widget está visible ó comezo"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Sensible:"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "Se o widget responde ás entradas"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "A mensaxe de axuda a mostrar se o rato pasa encima do widget"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Pode ser por defecto:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr "Se o widget pode ser a acción predeterminada do diálogo"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "É por defecto:"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Se o widget ten a acción predeterminada no diálogo"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Pode ter o foco:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "Se o widget pode ter o foco de entrada"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Ten o foco:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "Se o widget ten o foco de entrada"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Eventos:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "Os eventos X que o widget recibe"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Eventos Ext.:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "Modo de eventos das extensións X"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Aceleradores:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Define as sinais a emitir cando se premen teclas"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Editar..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "Propagar:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "Actívao para propaga-lo estilo ós widgets fillos"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Estilo con nome:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "Nome do estilo, que pode ser compartido por varios widgets"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Fonte:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "A fonte a usar para calqueira texto no widget"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Copiar Todo"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Primeiro Plano:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Fondo:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "Base:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Cor do primeiro plano "

#: ../glade/property.c:928
msgid "Background color"
msgstr "Cor do fondo"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Cor do texto"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Cor base"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Imaxe do fondo:"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "Imaxe para por no fondo do widget"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "Ficheiro onde se vai escribir o código fonte"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Público:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Indica se hai que engadir o widget á estructura de datos da compoñente"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Clase separada:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Poñer a subárbore deste widget nunha clase separada"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Ficheiro separado:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Pon o widget nun ficheiro fonte separado"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Visibilidade:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr "Visibilidade dos widgets. Os públicos expórtanse a un mapa global."

#: ../glade/property.c:1127
msgid "You need to select a color or background to copy"
msgstr "Tes que escoller unha cor ou un fondo para copiar"

#: ../glade/property.c:1146
msgid "Invalid selection in on_style_copy()"
msgstr "Selección inválida en on_style_copy()"

#: ../glade/property.c:1188
msgid "You need to copy a color or background pixmap first"
msgstr "Tes que copiar primeiro unha cor ou un fondo"

#: ../glade/property.c:1194
msgid "You need to select a color to paste into"
msgstr "Tes que escoller unha cor para pegar nel"

#: ../glade/property.c:1204
msgid "You need to select a background pixmap to paste into"
msgstr "Tes que escoller un pixmap de fondo para pegar nel"

#: ../glade/property.c:1456
msgid "Couldn't create pixmap from file\n"
msgstr "Non poido crear un pixmap dende ese ficheiro\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1498
msgid "Signal"
msgstr "Sinal"

#: ../glade/property.c:1500
msgid "Data"
msgstr "Datos"

#: ../glade/property.c:1501
msgid "After"
msgstr "Despóis"

#: ../glade/property.c:1502
msgid "Object"
msgstr "Obxeto"

#: ../glade/property.c:1533 ../glade/property.c:1697
msgid "Signal:"
msgstr "Sinal:"

#: ../glade/property.c:1534
msgid "The signal to add a handler for"
msgstr "A sinal para a que engades o manexador"

#: ../glade/property.c:1548
msgid "The function to handle the signal"
msgstr "Función para manexar o sinal"

#: ../glade/property.c:1551
msgid "Data:"
msgstr "Datos:"

#: ../glade/property.c:1552
msgid "The data passed to the handler"
msgstr "Datos que queres pasarlle ó manexador"

#: ../glade/property.c:1553
msgid "Object:"
msgstr "Obxeto:"

#: ../glade/property.c:1554
msgid "The object which receives the signal"
msgstr "O obxeto que vai recibir o sinal"

#: ../glade/property.c:1555
msgid "After:"
msgstr "Despóis:"

#: ../glade/property.c:1556
msgid "If the handler runs after the class function"
msgstr "Actívao se o manexador debe executarse despois da función da clase"

#: ../glade/property.c:1569
msgid "Add"
msgstr "Engadir"

#: ../glade/property.c:1575
msgid "Update"
msgstr "Actualizar"

#: ../glade/property.c:1587
msgid "Clear"
msgstr "Limpar"

#: ../glade/property.c:1637
msgid "Accelerators"
msgstr "Aceleradores"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1650
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1651
msgid "Key"
msgstr "Tecla"

#: ../glade/property.c:1652
msgid "Signal to emit"
msgstr "Sinal a emitir"

#: ../glade/property.c:1696
msgid "The accelerator key"
msgstr "A tecla aceleradora"

#: ../glade/property.c:1698
msgid "The signal to emit when the accelerator is pressed"
msgstr "Sinal a emitir cando premen o acelerador"

#: ../glade/property.c:1847
msgid "Edit Text Property"
msgstr "Editar propiedade do texto"

#: ../glade/property.c:1885
msgid "<b>_Text:</b>"
msgstr "<b>_Texto:</b>"

#: ../glade/property.c:1895
msgid "T_ranslatable"
msgstr "T_raducible"

#: ../glade/property.c:1899
msgid "Has Context _Prefix"
msgstr "Ten _prefixo de contexto"

#: ../glade/property.c:1925
msgid "<b>Co_mments For Translators:</b>"
msgstr "<b>Co_mentarios para os traductores:</b>"

#: ../glade/property.c:3892
msgid "Select X Events"
msgstr "Escoller eventos de X"

#: ../glade/property.c:3901
msgid "Event Mask"
msgstr "Máscara de eventos"

#: ../glade/property.c:4031 ../glade/property.c:4080
msgid "You need to set the accelerator key"
msgstr "Tes que escoller unha tecla aceleradora"

#: ../glade/property.c:4038 ../glade/property.c:4087
msgid "You need to set the signal to emit"
msgstr "Tes que escoller un sinal para emitir"

#: ../glade/property.c:4314 ../glade/property.c:4370
msgid "You need to set the signal name"
msgstr "Tes que escoller un nome para o sinal"

#: ../glade/property.c:4321 ../glade/property.c:4377
msgid "You need to set the handler for the signal"
msgstr "Tes que escoller o manexador para o sinal"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4580
#, c-format
msgid "%s signals"
msgstr "Sinais de %s"

#: ../glade/property.c:4637
msgid "Select Signal"
msgstr "Elixa sinal"

#: ../glade/property.c:4833
msgid "Value:"
msgstr "Valor:"

#: ../glade/property.c:4833
msgid "Min:"
msgstr "Mínimo:"

#: ../glade/property.c:4833
msgid "Step Inc:"
msgstr "Inc. Paso:"

#: ../glade/property.c:4834
msgid "Page Inc:"
msgstr "Inc. Páxina"

#: ../glade/property.c:4834
msgid "Page Size:"
msgstr "Tamaño de Páxina:"

#: ../glade/property.c:4836
msgid "H Value:"
msgstr "Valor H:"

#: ../glade/property.c:4836
msgid "H Min:"
msgstr "Min H:"

#: ../glade/property.c:4836
msgid "H Max:"
msgstr "Max H:"

#: ../glade/property.c:4836
msgid "H Step Inc:"
msgstr "Inc. Paso H:"

#: ../glade/property.c:4837
msgid "H Page Inc:"
msgstr "Inc. Páxina H:"

#: ../glade/property.c:4837
msgid "H Page Size:"
msgstr "Tamaño Páxina H:"

#: ../glade/property.c:4839
msgid "V Value:"
msgstr "Valor V:"

#: ../glade/property.c:4839
msgid "V Min:"
msgstr "Min V:"

#: ../glade/property.c:4839
msgid "V Max:"
msgstr "Max V:"

#: ../glade/property.c:4839
msgid "V Step Inc:"
msgstr "Inc. Paso V:"

#: ../glade/property.c:4840
msgid "V Page Inc:"
msgstr "Inc. Páxina V:"

#: ../glade/property.c:4840
msgid "V Page Size:"
msgstr "Tamaño Páxina V:"

#: ../glade/property.c:4843
msgid "The initial value"
msgstr "Valor inicial"

#: ../glade/property.c:4844
msgid "The minimum value"
msgstr "Valor mínimo"

#: ../glade/property.c:4845
msgid "The maximum value"
msgstr "Valor máximo"

#: ../glade/property.c:4846
msgid "The step increment"
msgstr "Aumento de paso"

#: ../glade/property.c:4847
msgid "The page increment"
msgstr "Aumnento de páxina"

#: ../glade/property.c:4848
msgid "The page size"
msgstr "Tamaño de páxina"

#: ../glade/property.c:5003
msgid "The requested font is not available."
msgstr "A fonte pedida non está dispoñible."

#: ../glade/property.c:5052
msgid "Select Named Style"
msgstr "Seleccione Nome de Estilo"

#: ../glade/property.c:5063
msgid "Styles"
msgstr "Estilos"

#: ../glade/property.c:5122
msgid "Rename"
msgstr "Cambiar nome"

#: ../glade/property.c:5150
msgid "Cancel"
msgstr "Cancelar"

#: ../glade/property.c:5270
msgid "New Style:"
msgstr "Novo Estilo:"

#: ../glade/property.c:5284 ../glade/property.c:5405
msgid "Invalid style name"
msgstr "Nome de estilo non válido"

#: ../glade/property.c:5292 ../glade/property.c:5415
msgid "That style name is already in use"
msgstr "Ese nome de estilo xa está sendo usado"

#: ../glade/property.c:5390
msgid "Rename Style To:"
msgstr "Cambiar nome a:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"Non se puido renomear o ficheiro:\n"
"  %s\n"
"a:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"Non se puido crear o ficheiro:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "Error escribindo o ficheiro XML\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Cadeas de texto traducibles xeradas por Glade.\n"
" * Engáde este ficheiro ó ficheiro POTFILES.in do teu proxecto.\n"
" * NON o compiles coma parte da túa aplicación. */\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"O ficheiro para o codigo fonte non é válido: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"O ficheiro para as cabeceiras non é válido: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"O ficheiro para as retrochamadas non é válido: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"O ficheiro para as cabeceiras das retrochamadas non é válido: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"O ficheiro para o soporte non é válido: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"O ficheiro para as cabeceiras do soporte non é válido: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"Non se puido engadir ó ficheiro:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"Erro escribindo ao ficheiro:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr "O nome do ficheiro debe de introducirse no diálogo de Opcións do Proxecto."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"O nome do ficheiro ten que ser simple e relativo.\n"
"Usa as opcións de proxecto para cambialo."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Árbore de Widgets"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "O widget non se atopa na caixa"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "O widget non se atopa na táboa"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "O widget non se atopa no colector fixo"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "O widget non se atopa no empaquetador (packer)"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"Non se puido acceder ó ficheiro:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"Non se puido abrir o ficheiro:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"Error lendo do ficheiro:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"Non se puido crear o directorio:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"Non se puido acceder ó directorio:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"Directorio non válido:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Proxectos"

#: ../glade/utils.c:1628
msgid "project"
msgstr "proxecto"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"Non se puido abrir o directorio:\n"
"  %s\n"

