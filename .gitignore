*.o
*.bak
*~
*.tar.gz
*.swp
.exec
aclocal.m4
configure
plugins/*/Makefile
intl
intltool-*
icons/Makefile
pixmaps/Makefile
Makefile.in
/config.h
config.h.in
config.status
config.sub
config.rpath
depcomp
stamp-h*
*.zip
*.log
*.guess
autom4te.cache
tags
todo.txt
test
shots
.deps
install-sh
missing
libtool
ltmain.sh
m4
.libs
*.la
*.lo
bin/*
obj/*
deadbeef.desktop
.vimrc
ddbcellrenderertextmultiline.vapi
ddbequalizer.vapi
ddbseekbar.vapi
vala.stamp
*.tar.bz2
*.tar.gz
*.gmo
stamp-it
*.so
tools/pluginfo/pluginfo
cscope.out
tools/apbuild/apsymbols.h
po/Makefile
po/Makefile.in.in
po/Makevars.template
po/POTFILES
po/Rules-quot
po/boldquot.sed
po/<EMAIL>
po/<EMAIL>
po/insert-header.sin
po/quot.sed
po/remove-potcdate.sin
po/deadbeef.pot
compile
src/deadbeef
valgrind.out
*.diff
portable
package_out
package_temp
messages.mo
.install
.config
plugins/libmp4ff/libmp4ff.a
plugins/libparser/libparser.a
xcuserdata
plugins/liboggedit/liboggedit.a
testrun.sh
tools/pluginfo/i686
tools/pluginfo/x86_64
portable_out
static
INSTALL
/static-deps
osx/build
.dirstamp
.vscode/c_cpp_properties.json
*.make
Makefile
*.a
docker-artifacts
compile_commands.json
plugins/gtkui/gtkui-gresources.c
build/
dist/
testbuild/
.DS_Store
