# Norwegian translation og glade (bokmål dialect).
# Copyright (C) 1999-2003 Free Software Foundation, Inc.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 1999-2007.
#
msgid ""
msgstr ""
"Project-Id-Version: glade 2.6.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2005-08-26 13:38+0200\n"
"PO-Revision-Date: 2005-02-15 01:29+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Norwegian Bokmål <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../glade-2.desktop.in.h:1
msgid "Design user interfaces"
msgstr "Utforming av brukergrensesnitt"

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Glade grensesnittutforming"

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Alternativer for rutenett"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Horisontalt mellomrom:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Vertikalt mellomrom:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Stil for rutenett:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Prikker"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Linjer"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Alternativer for feste"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Horisontal festing:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Venstre"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Høyre"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Vertikal festing:"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Øverst"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Nederst"

#: ../glade/editor.c:741
#, fuzzy
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "GnomeDockItem widgeter kan kun limes inn i en GnomeDock."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "Kunne ikke sette inn et GtkScrolledWindow widget."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "Kunne ikke sette inn et GtkViewport widget."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "Kunne ikke legge til nytt widget."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"Du kan ikke legge til et widget i den valgte posisjonen.\n"
"\n"
"Tips: GTK+ bruker kontainere for å plassere et widget.\n"
"Prøv å slette eksisterende widget og bruk en\n"
"boks eller tabell kontainer i stedet.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "Kunne ikke slette widgetet."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "Widgetet kan ikke slettes"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"Det valgte widgetet opprettes automatisk som en del av forelder-widgetet, og "
"det kan ikke slettes."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Rammebredde:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "Bredden på kanten rundt kontaineren"

#: ../glade/gbwidget.c:1745
msgid "Select"
msgstr "Velg"

#: ../glade/gbwidget.c:1767
msgid "Remove Scrolled Window"
msgstr "Fjern rullet vindu"

#: ../glade/gbwidget.c:1776
msgid "Add Scrolled Window"
msgstr "Legg til rullet vindu"

#: ../glade/gbwidget.c:1797
msgid "Remove Alignment"
msgstr "Fjern justering"

#: ../glade/gbwidget.c:1805
msgid "Add Alignment"
msgstr "Legg til justering"

#: ../glade/gbwidget.c:1820
msgid "Remove Event Box"
msgstr "Fjern hendelsesboks"

#: ../glade/gbwidget.c:1828
msgid "Add Event Box"
msgstr "Legg til hendelsesboks"

#: ../glade/gbwidget.c:1838
msgid "Redisplay"
msgstr "Vis på nytt"

#: ../glade/gbwidget.c:1849
msgid "Cut"
msgstr "Klipp ut"

#: ../glade/gbwidget.c:1856 ../glade/property.c:892 ../glade/property.c:5135
msgid "Copy"
msgstr "Kopier"

#: ../glade/gbwidget.c:1865 ../glade/property.c:904
msgid "Paste"
msgstr "Lim inn"

#: ../glade/gbwidget.c:1877 ../glade/property.c:1580 ../glade/property.c:5126
msgid "Delete"
msgstr "Slett"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2403 ../glade/gbwidget.c:2472
msgid "N/A"
msgstr "N/A"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3202
msgid "replacing child of container - not implemented yet\n"
msgstr "erstatter barnet til kontainer - ikke implementert ennå\n"

#: ../glade/gbwidget.c:3430
msgid "Couldn't insert GtkAlignment widget."
msgstr "Kunne ikke sette inn GtkAlignment widget."

#: ../glade/gbwidget.c:3470
msgid "Couldn't remove GtkAlignment widget."
msgstr "Kunne ikke fjerne GtkAlignment widget."

#: ../glade/gbwidget.c:3494
msgid "Couldn't insert GtkEventBox widget."
msgstr "Kunne ikke sette inn GtkEventBox widget."

#: ../glade/gbwidget.c:3533
msgid "Couldn't remove GtkEventBox widget."
msgstr "Kunne ikke fjerne GtkEventBox widget."

#: ../glade/gbwidget.c:3568
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "Kunne ikke sette inn GtkScrolledWindow widget."

#: ../glade/gbwidget.c:3607
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "Kunne ikke fjerne GtkScrolledWindow widget."

#: ../glade/gbwidget.c:3721
msgid "Remove Label"
msgstr "Fjern etikett"

#: ../glade/gbwidgets/gbaboutdialog.c:78
msgid "Application Name"
msgstr "Navn på programmet"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Logo:"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "Bildet som skal brukes som logo"

#: ../glade/gbwidgets/gbaboutdialog.c:104 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Programnavn:"

#: ../glade/gbwidgets/gbaboutdialog.c:104
msgid "The name of the application"
msgstr "Navnet på programmet"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "Kommentarer:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
msgid "Additional information, such as a description of the application"
msgstr "Tilleggsinformasjon. For eksempel en beskrivelse av programmet"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Opphavsrett:"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "Lisensteksten"

#: ../glade/gbwidgets/gbaboutdialog.c:108
msgid "Website URL:"
msgstr "URL til nettside:"

#: ../glade/gbwidgets/gbaboutdialog.c:108
msgid "The URL of the application's website"
msgstr "URL for programmets nettside"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "Website Label:"
msgstr "Nettside-etikett:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "The label to display for the link to the website"
msgstr "Bildet som skal vises langs toppen av siden"

#: ../glade/gbwidgets/gbaboutdialog.c:111 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Lisens:"

#: ../glade/gbwidgets/gbaboutdialog.c:111
#, fuzzy
msgid "The license details of the application"
msgstr "Relief-stilen for knappen"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Forfattere:"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Forfatterene av pakken, en for hver linje"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Dokumentasjonsforfattere:"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Forfatterene av dokumentasjon for pakken, en for hver linje"

#: ../glade/gbwidgets/gbaboutdialog.c:115
msgid "Artists:"
msgstr "Grafikk:"

#: ../glade/gbwidgets/gbaboutdialog.c:115
#, fuzzy
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr "Forfatterene av pakken, en for hver linje"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Oversettere:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Oversettere av denne pakken. Dette bør vanligvis stå tomt slik at "
"oversetterene kan legge inn sine navn i po-filene"

#: ../glade/gbwidgets/gbaboutdialog.c:559
msgid "About Dialog"
msgstr "Om-dialog"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Etikett med tastatursnarvei"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:180 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "X justering:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "Horisontal justering av barnwidgetet"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:183 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Y justering:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "Vertikal justering av barnwidgetet"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "X-skalering:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "Horisontal skalering av barnwidget"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Y-skalering:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "Vertikal skalering av barnwidget"

#: ../glade/gbwidgets/gbalignment.c:85
msgid "Top Padding:"
msgstr "Toppfyll:"

#: ../glade/gbwidgets/gbalignment.c:86
msgid "Space to put above the child widget"
msgstr "Mellomrom som plasseres over barn-widget"

#: ../glade/gbwidgets/gbalignment.c:89
msgid "Bottom Padding:"
msgstr "Bunnfyll:"

#: ../glade/gbwidgets/gbalignment.c:90
msgid "Space to put below the child widget"
msgstr "Mellomrom som plasseres under barnwidget"

#: ../glade/gbwidgets/gbalignment.c:93
msgid "Left Padding:"
msgstr "Venstre fyll:"

#: ../glade/gbwidgets/gbalignment.c:94
msgid "Space to put to the left of the child widget"
msgstr "Mellomrom som plasseres til venstre for barnwidget"

#: ../glade/gbwidgets/gbalignment.c:97
msgid "Right Padding:"
msgstr "Høyre fyll:"

#: ../glade/gbwidgets/gbalignment.c:98
msgid "Space to put to the right of the child widget"
msgstr "Mellomrom som plasseres til høyre for barnwidget"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Justering"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "Retning:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "Retningen på pilen"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Skygge:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "Skyggetypen for pilen"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "Horisontal justering av pilen"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "Vertikal justering av pilen"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186
msgid "X Pad:"
msgstr "X-fyll:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "Horisontalt fyll"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188
msgid "Y Pad:"
msgstr "Y-fyll:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "Vertikalt fyll"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Pil"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "Etikettens X-justering:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "Horisontal justering av rammens etikettwidget"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Etikettens Y-justering:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "Vertikal justering av rammens etikettwidget"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "Rammens skyggetype"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "Horisontal justering av rammens barn"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Rate:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "Aspektraten til rammens barn"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Adlyd barn:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Om aspektraten skal bestemmes av barnet"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Aspektramme"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Standard knapp:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "Standardknapp som skal brukes"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:747
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Etikett:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "Teksten som skal vises"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:295
#: ../glade/glade_menu_editor.c:813
msgid "Icon:"
msgstr "Ikon:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "Ikon som skal vises"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Knapperelief:"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "Relief-stilen for knappen"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "Respons-ID:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"Svarkoden som returneres når knappen klikkes. Velg et av de forvalgte "
"svarene eller skriv inn en positiv heltallsverdi"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr "Fokus ved klikk:"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "If the button grabs focus when it is clicked"
msgstr "Om knappen tar fokus når den klikkes"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "Fjern innhold i knapp"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Knapp"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Tittel:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "Om måned og år skal vises øverst"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Dagnavn:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Om dagnavn skal vises"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Fast måned:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "Om måned og år skal kunne endres"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Ukenummer:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Om antall uker skal vises"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Mandag først:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "Om uken skal starte på Mandag"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Kalender"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Bakgr. farge:"

#: ../glade/gbwidgets/gbcellview.c:64
msgid "The background color"
msgstr "Bakgrunnsfarge"

#: ../glade/gbwidgets/gbcellview.c:192
msgid "Cell View"
msgstr "Cellevisning"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Initielt på:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Om avkrysningsboksen er på initielt"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Inkonsistent:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Om knappen skal vises i inkonsistent tilstand"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "Indikator:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "Om indikatoren alltid skal tegnes"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Avkrysningsboks"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Om avkrysningsmenyen er slått på i initielt"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Menyoppføring for avkrysning"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Ny kolonneliste"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Antall kolonner:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:127 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Velg modus:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "Utvalgsmodus for kolonnelisten"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Vis titler:"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Om kolonnetitler skal vises"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "Type skyggelegging av kolonnelistens kanter"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Kolonneliste"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Bruk alpha:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Om alpha-kanalen skal brukes"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:85
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:242
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Tittel:"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "Tittelen på fargevalgsdialogen"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
msgid "Pick a Color"
msgstr "Velg en farge"

#: ../glade/gbwidgets/gbcolorbutton.c:211
msgid "Color Chooser Button"
msgstr "Fargevelgerknapp"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Ugjennomsiktighetskontroll:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Om kontroll for ugjennomsiktighet skal vises"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Palett:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Om paletten skal vises"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Fargevalg"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:70
msgid "Select Color"
msgstr "Velg farge"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:315 ../glade/property.c:1275
msgid "Color Selection Dialog"
msgstr "Dialog for fargevalg"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Verdi i listen:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Om verdien må være i listen"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "OK hvis tom:"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr "Om en tom verdi er akseptabelt, nå 'Verdi i listen' er satt"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "Skill mello små/store bokstaver:"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Om det skal skilles mellom små/store bokstaver i søk"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Bruk piler:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "Om piler kan brukes til å endre verdien"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Bruk alltid:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "Pilene virker selv om verdien ikke er i listen"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Oppføringer:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Oppføringene på kombolisten, en pr. linje"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Komboboks"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
#, fuzzy
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Om knappen tar fokus når den klikkes"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Har ramme:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr ""

#: ../glade/gbwidgets/gbcomboboxentry.c:302
msgid "Combo Box Entry"
msgstr "Komboboksoppføring"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Nytt kolonnetre"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "Utvalgsmodus for kolonnetreet"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "Skyggetype for kolonnetreets kant"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Kolonnetre"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:245
msgid "Type:"
msgstr "Type:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "Typen på kurven"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "X min:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "Minimum horisontal verdi"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "X maks:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "Maksimal horisontal verdi"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Y min:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "Minimum vertikal verdi"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Y maks:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "Maksimal vertikal verdi"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Kurve"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Funksjon for oppretting:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "Funksjonen som oppretter widgetet"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Streng1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "Det første streng-argumentet som skal sendes til funksjonen"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Streng2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "Det andre streng-argumentet som skal sendes til funksjonen"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Int1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "Første heltallsargument som skal gis til funksjonen"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Int2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "Andre heltallsargument som skal gis til funksjonen"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Egendefinert widget"

#: ../glade/gbwidgets/gbdialog.c:292
msgid "New dialog"
msgstr "Ny dialog"

#: ../glade/gbwidgets/gbdialog.c:304
msgid "Cancel, OK"
msgstr "Avbryt, OK"

#: ../glade/gbwidgets/gbdialog.c:313 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1316 ../glade/property.c:5156
msgid "OK"
msgstr "OK"

#: ../glade/gbwidgets/gbdialog.c:322
msgid "Cancel, Apply, OK"
msgstr "Avbryt, Bruk, OK"

#: ../glade/gbwidgets/gbdialog.c:331
msgid "Close"
msgstr "Lukk"

#: ../glade/gbwidgets/gbdialog.c:340
msgid "_Standard Button Layout:"
msgstr "_Forvalgt plassering av knapper:"

#: ../glade/gbwidgets/gbdialog.c:349
msgid "_Number of Buttons:"
msgstr "A_ntall knapper:"

#: ../glade/gbwidgets/gbdialog.c:366
msgid "Show Help Button"
msgstr "Vis hjelpknapp"

#: ../glade/gbwidgets/gbdialog.c:397
msgid "Has Separator:"
msgstr "Har separator:"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Om dialogen har en horisontal separator over knappene"

#: ../glade/gbwidgets/gbdialog.c:605
msgid "Dialog"
msgstr "Dialog"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Tegneområde"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Redigerbar:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Om teksten kan redigeres"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Teksten synlig:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"Om teksten brukeren skriver inn skal vises. Når dette er slått av, vises "
"teksten som skrives inn som stjerner, noe som er nyttig for inntasting av "
"passord"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Maks lengde:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "Maksimal lengde på teksten"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Tekst:"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Om inntastingsboksen har en ramme rundt seg"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Usynlig tegn:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"Tegn som skal brukes om teksten skal være usynlig, f.eks når du skriver inn "
"passord"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "Aktiverer forvalg:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr "Om forvalgt widget i vinduet er aktivert når man trykker linjeskift"

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Bredde i tegn:"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "Antall tegn det skal lages plass for i feltet"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Tekstoppføring"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "Visible Window:"
msgstr "Synlig vindu:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr "Om hendelsesboksen bruker et synlig vindu"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "Above Child:"
msgstr "Over barn:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr ""

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Hendelsesboks"

#: ../glade/gbwidgets/gbexpander.c:54
msgid "Initially Expanded:"
msgstr "Utvidet initielt:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr ""

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Mellomrom:"

#: ../glade/gbwidgets/gbexpander.c:58
msgid "Space to put between the label and the child"
msgstr "Mellomrom som plasseres mellom etikett og barn"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Legg til etikettwidget"

#: ../glade/gbwidgets/gbexpander.c:228
msgid "Expander"
msgstr "Utvider"

#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#, fuzzy
msgid "The window title of the file chooser dialog"
msgstr "Tittelen på dialogen for filvalg"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:156
#: ../glade/gnome/gnomefileentry.c:109
msgid "Action:"
msgstr "Handling:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:157
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:90
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
msgid "Local Only:"
msgstr "Kun lokal:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:160
msgid "Whether the selected files should be limited to local files"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
msgid "Show Hidden:"
msgstr "Vis skjulte:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether the hidden files and folders should be displayed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gblabel.c:200
msgid "Width in Chars:"
msgstr "Bredde i tegn:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#, fuzzy
msgid "The width of the button in characters"
msgstr "Bredden av utformingsområdet"

#: ../glade/gbwidgets/gbfilechooserbutton.c:283
msgid "File Chooser Button"
msgstr "Filvelgerknapp"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
msgid "Select Multiple:"
msgstr "Velg flere:"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether to allow multiple files to be selected"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
msgid "File Chooser"
msgstr "Filvelger:"

#: ../glade/gbwidgets/gbfilechooserdialog.c:421
msgid "File Chooser Dialog"
msgstr "Filvelgerdialog"

#: ../glade/gbwidgets/gbfileselection.c:71 ../glade/property.c:1365
msgid "Select File"
msgstr "Velg fil:"

#: ../glade/gbwidgets/gbfileselection.c:113
msgid "File Ops.:"
msgstr "Filoperasjoner:"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "If the file operation buttons are shown"
msgstr "Om knappene for filoperasjoner skal vises"

#: ../glade/gbwidgets/gbfileselection.c:292
msgid "File Selection Dialog"
msgstr "Dialog for filvalg"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "X-koordinaten for widget i GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "Y-koordinat for widget i GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Faste posisjoner"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "Tittelen på dialogen for valg av skrifttype"

#: ../glade/gbwidgets/gbfontbutton.c:70
msgid "Show Style:"
msgstr "Vis stil:"

#: ../glade/gbwidgets/gbfontbutton.c:71
msgid "If the font style is shown as part of the font information"
msgstr "Om skriftstilen skal vises som en del a informasjonen om skriften"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Vis størrelse:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "Om størrelsen på skrifttypen skal vises som en del a informasjonen"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Bruk skrifttype:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr "Om den merkede skrifttypen skal brukes ved visning av informasjonen"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Bruk størrelse:"

#: ../glade/gbwidgets/gbfontbutton.c:77
msgid "if the selected font size is used when displaying the font information"
msgstr ""
"Om den merkede skriftstørrelsen skal brukes ved visning av informasjonen om "
"skriften"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Plukk en skrifttype"

#: ../glade/gbwidgets/gbfontbutton.c:268
msgid "Font Chooser Button"
msgstr "Skriftvelgerknapp"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Forhåndsvisningstekst:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "Forhåndsvisningsteksten som skal vises"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Valg av skrifttype"

#: ../glade/gbwidgets/gbfontselectiondialog.c:69
msgid "Select Font"
msgstr "Velg skrifttype"

#: ../glade/gbwidgets/gbfontselectiondialog.c:300
msgid "Font Selection Dialog"
msgstr "Dialog for valg av skrifttype"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Ramme"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Opprinnelig type:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "Den opprinnelige typen for kurven"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Gamma kurve"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "Typen skygge rundt hånderingsboksen"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "Håndtak posisjon:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "Håndtakets posisjon"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Fest til kant:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "Kanten av håndtakboksen som festes i posisjon"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "Håndtakboks"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Ny horisontal boks"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Størrelse:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "Antall widgeter i boksen"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Homogen:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Om barna skal være av samme størrelse"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "Mellomrommet mellom hvert barn"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "Kunne ikke slette barn."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:254
msgid "Position:"
msgstr "Posisjon:"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "Widgetets posisjon i forhold til etterkommerene"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Fyll:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "Widgetets fyll"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Utvid:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "Sett til sann for å la widgetet utvides"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Fyll:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "Sett til sann for å la widgetet fylle sitt allokerte område"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Start pakking:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "Sett til sann for å pakke widgetet ved begynnelsen av boksen"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Sett inn før"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Sett inn etter"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Horisontal boks"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Ny horisontal knappeboks"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "Antall knapper"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Utseende:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "Utseende på knappene"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "Mellomrommet mellom knappene"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Horisontal knappeboks"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "Separatorens posisjon"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:283
msgid "Shrink:"
msgstr "Krymp:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "Sett til sann for å la widgetet krympe"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Endre størrelse:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "Sett til sann for å la widgetet endre størrelse"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Horisontale felt"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Måleenhet:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "Enhetene på linjalen"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Nedre verdi:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "Nedre verdi på linjalen"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Øvre verdi:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "Øvre verdi på linjalen"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "Nåværende posisjon på linjalen"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4827
msgid "Max:"
msgstr "Maks:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "Maksimal verdi på linjalen"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Horisontal linjal"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Vis verdi:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Om skalaens verdi skal vises"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Siffer:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "Antall siffer som skal vises"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Verdi posisjon:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "Verdiens posisjon"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Policy:"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "Oppdateringskriteriet for målet"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Invertert:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Om verdiene for området er invertert"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Horisontal måler"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "Oppdateringskriteriet for runllefeltet"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Horisontalt rullefelt"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Horisontal separator"

#: ../glade/gbwidgets/gbiconview.c:106
#, c-format
msgid "Icon %i"
msgstr "Ikon %i"

#: ../glade/gbwidgets/gbiconview.c:128
#, fuzzy
msgid "The selection mode of the icon view"
msgstr "Utvalgsmodus for kolonnetreet"

#: ../glade/gbwidgets/gbiconview.c:130 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "Orientering:"

#: ../glade/gbwidgets/gbiconview.c:131
#, fuzzy
msgid "The orientation of the icons"
msgstr "Orienteringen av fremgangsmålerens innhold"

#: ../glade/gbwidgets/gbiconview.c:287
msgid "Icon View"
msgstr "Ikonvisning"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:299
msgid "Named Icon:"
msgstr "Ikon med navn:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:300
#, fuzzy
msgid "The named icon to use"
msgstr "Standard Gnome objekt som skal brukes."

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Ikonstørrelse:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "Størrelse på standard ikon"

#: ../glade/gbwidgets/gbimage.c:115
msgid "Pixel Size:"
msgstr "Pikselstørrelse:"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "Horisontal justering"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "Vertikal justering"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Bilde"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "Ugyldig standard menyoppføring"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Menyoppføring med bilde"

#: ../glade/gbwidgets/gbinputdialog.c:256
msgid "Input Dialog"
msgstr "Dialog for input"

#: ../glade/gbwidgets/gblabel.c:169
msgid "Use Underline:"
msgstr "Understreket:"

#: ../glade/gbwidgets/gblabel.c:170
msgid "If the text includes an underlined access key"
msgstr "Om teksten includerer et understreket hurtigtast"

#: ../glade/gbwidgets/gblabel.c:171
msgid "Use Markup:"
msgstr "Bruk merking:"

#: ../glade/gbwidgets/gblabel.c:172
msgid "If the text includes pango markup"
msgstr "Om teksten inneholder pango-merking"

#: ../glade/gbwidgets/gblabel.c:173
msgid "Justify:"
msgstr "Juster:"

#: ../glade/gbwidgets/gblabel.c:174
msgid "The justification of the lines of the label"
msgstr "Plasseringen av linjene i etiketten"

#: ../glade/gbwidgets/gblabel.c:176
msgid "Wrap Text:"
msgstr "Bryt teksten:"

#: ../glade/gbwidgets/gblabel.c:177
msgid "If the text is wrapped to fit within the width of the label"
msgstr "Om teksten skal brytes til å passe innenfor bredden av etiketten"

#: ../glade/gbwidgets/gblabel.c:178
msgid "Selectable:"
msgstr "Valgbar:"

#: ../glade/gbwidgets/gblabel.c:179
msgid "If the label text can be selected with the mouse"
msgstr "Om teksten i etiketten kan markeres med musen"

#: ../glade/gbwidgets/gblabel.c:181
msgid "The horizontal alignment of the entire label"
msgstr "Den horisontale justeringen av hele etiketten"

#: ../glade/gbwidgets/gblabel.c:184
msgid "The vertical alignment of the entire label"
msgstr "Den vertikale justeringen av hele etiketten"

#: ../glade/gbwidgets/gblabel.c:190
msgid "Focus Target:"
msgstr "Mål for fokus:"

#: ../glade/gbwidgets/gblabel.c:191
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"Widgetet som tastaturfokus skal settes til når den understrekede "
"hurtigtasten brukes"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:197 ../glade/gbwidgets/gbprogressbar.c:146
#, fuzzy
msgid "Ellipsize:"
msgstr "Eksklusiv:"

#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:201
#, fuzzy
msgid "The width of the label in characters"
msgstr "Bredden av utformingsområdet"

#: ../glade/gbwidgets/gblabel.c:203
msgid "Single Line Mode:"
msgstr "Enkeltlinjemodus:"

#: ../glade/gbwidgets/gblabel.c:204
msgid "If the label is only given enough height for a single line"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:205
msgid "Angle:"
msgstr "Vinkel:"

#: ../glade/gbwidgets/gblabel.c:206
#, fuzzy
msgid "The angle of the label text"
msgstr "Brytning av teksten"

#: ../glade/gbwidgets/gblabel.c:332 ../glade/gbwidgets/gblabel.c:347
#: ../glade/gbwidgets/gblabel.c:614
msgid "Auto"
msgstr "Auto"

#: ../glade/gbwidgets/gblabel.c:870 ../glade/glade_menu_editor.c:410
msgid "Label"
msgstr "Etikett"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Områdebredde:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "Bredden av utformingsområdet"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Områdehøyde:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "Høyden på utformingsområdet"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "X-koordinat for widget i GtkLayout"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "Y-koordinat for widget i GtkLayout"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Utforming"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "Listens utvalgsmodus"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Liste"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Listeoppføring"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Oppsprettmeny"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:190
msgid "_File"
msgstr "_Fil"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:198 ../glade/glade_project_window.c:691
msgid "_Edit"
msgstr "R_ediger"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:204 ../glade/glade_project_window.c:720
msgid "_View"
msgstr "_Vis"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:206 ../glade/glade_project_window.c:833
msgid "_Help"
msgstr "_Hjelp"

#: ../glade/gbwidgets/gbmenubar.c:207
msgid "_About"
msgstr "_Om"

#: ../glade/gbwidgets/gbmenubar.c:268 ../glade/gbwidgets/gbmenubar.c:346
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Rediger menyer..."

#: ../glade/gbwidgets/gbmenubar.c:442
msgid "Menu Bar"
msgstr "Menylinje"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Menyoppføring"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
msgid "Show Horizontal:"
msgstr "Vis horisontalt:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
#, fuzzy
msgid "If the item is visible when the toolbar is horizontal"
msgstr "Om dokkoppføringen aldri kan være horisontal"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
msgid "Show Vertical:"
msgstr "Vis vertikalt:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
#, fuzzy
msgid "If the item is visible when the toolbar is vertical"
msgstr "Om dokkoppføringen aldri kan være vertikal"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr "Er viktig:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
#, fuzzy
msgid "Toolbar Button with Menu"
msgstr "Av/på knapp"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Ny notisblokk"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:124
msgid "Number of pages:"
msgstr "Antall sider:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "Vis faner:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Om notisblokkens faner skal vises"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Vis kant:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "Om notisblokkens kant skal vises, når flikene ikke vises"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Faneposisjon:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "Posisjon for notisblokkens faner"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Kan rulles:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Om notisblokkens faner kan rulles"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Fane Horis. kanr:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "Størrelsen på notisblokkfanens horisontale kant"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Fane vert. kant:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "Størrelsen på notisblokkfanens vertikale kant"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Vis oppsprett:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "Om oppsprettmenyer er slått på"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Antall sider:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "Antall sider i notisblokken"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Forrige side"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "Neste side"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Slett side"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "Bytt til neste"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Bytt til forrige"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Sett inn side etter"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Sett inn side før"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "Sidens posisjon i listen over sider"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Sett til sann for å la fanen utvides"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr "Sett til sann for å la fanen fylle sitt allokerte område"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr "Sett til sann for å pakke fanen ved begynnelsen av notisblokken"

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Menyetikett:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "Teksten som skal vises i oppsprettmenyen"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Notisblokk"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "Kan ikke legge til %s i en GtkOptionMenu."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Alternativer meny"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Farge:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "Om forhåndsvisningen er i farger eller gråtoner"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "Om forhåndsvisningen utvides til å fylle sitt allokerte område"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Forhåndsvisning"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "Orienteringen av fremgangsmålerens innhold"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Andel:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "Andel arbeid som er fullført"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Pulssteg:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr "Hvor mye den sprettende blokken skal flyttes for hver puls"

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "Teksten som skal vises over fremgangsmåleren"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Vis tekst:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "Om teksten skal vises på fremgangsmåleren"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Aktivitetsmodus:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "Om fremgangsmåleren skal oppføre seg som fronten på Kit's bil"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "Horisontal justering av teksten"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "Vertikal justering av teksten"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "Fremgangsmåler"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "Om radioknappen opprinnelig er på"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1038
msgid "Group:"
msgstr "Gruppe:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr "Radioknapp-gruppen (alle radioknapper med samme forelder er forvalgt)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Ny gruppe"

#: ../glade/gbwidgets/gbradiobutton.c:463
msgid "Radio Button"
msgstr "Radioknapp"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "Om radiomenyoppføringen opprinnelig er på"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"Radiomenyoppføring gruppen (forvalg er alle radiomenyoppføringer med samme "
"forelder)"

#: ../glade/gbwidgets/gbradiomenuitem.c:386
msgid "Radio Menu Item"
msgstr "Radiomenyoppføring"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
#, fuzzy
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr "Radioknapp-gruppen (alle radioknapper med samme forelder er forvalgt)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:528
#, fuzzy
msgid "Toolbar Radio Button"
msgstr "Radioknapp"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "H policy:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Når det horisontale rullefeltet vises"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "V policy:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Når det vertikale rullefeltet vises"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Vindusposisjon:"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr "Posisjonen av undervinduet i forhold til rullefeltene"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Skyggetype:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "Oppdateringspolicy for vertikalt rullefelt"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Rullet vindu"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Separator for menyer"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr "Tegn:"

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
#, fuzzy
msgid "Toolbar Separator Item"
msgstr "Horisontal separator"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Klatrerate:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr "Klatreraten til snurreknappen, brukes sammen med sideøkning"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "Antall desimaltall som skal vises"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Numerisk:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr ""

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Oppdateringskriterie:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Når value_changed signaler sendes ut"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Fest:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "Skal verdien festes til multipler av steg-verdien"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Bryt:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "Skal verdien begynne på nytt ved grensen"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Snurreknapp"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Håndtak for å endre størrelse:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr "Om statuslinjen har et håndtak for å endre størrelse på vinduet"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Statuslinje"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Ny tabell"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Antall rader:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Rader:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "Antall rader i tabellen"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Kolonner:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "Antall kolonner i tabellen"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Om barna skal være av samme størrelse"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Mellomrom mellom rader:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "Mellomrommet mellom hver rad"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Mellomrom mellom kolonner:"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "Mellomrommet mellom hver kolonne"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Celle X:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "Venstre kant av widgetet i tabellen"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Celle Y:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "Øverste kant av widgetet i tabellen"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Antall kolonner:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "Antall kolonner widgetet spenner over i tabellen"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Antall rader:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "Antall rader widgetet spenner over i tabellen"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "H fyll:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "V fyll:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "X utvidelse:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "Sett til sann for å la widgetet utvide seg horisontalt"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Y utvidelse:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "Sett til sann for å la widgetet utvide seg vertikalt"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "X krymp:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "Sett til sann for å la widgetet krympes horisontalt"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Y krymping:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "Sett til sann for å la widgetet krympe vertikalt"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "X-fyll:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr ""
"Sett til sann for å la widgetet fylle dets allokerte horisontale område"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Y-fyll:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "Sett til sann for å la widgetet fylle dets allokerte vertikale område"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Sett inn rad før"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Sett inn rad etter"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Sett inn kolonne før"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Sett inn kolonne etter"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Slett rad"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Slett kolonne"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Tabell"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Senter"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Fyll"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:542 ../glade/glade_menu_editor.c:829
#: ../glade/glade_menu_editor.c:1344 ../glade/glade_menu_editor.c:2251
#: ../glade/property.c:2431
msgid "None"
msgstr "Ingen"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Tegn"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Ord"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Markør synlig:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Om markøren er synlig"

#: ../glade/gbwidgets/gbtextview.c:119
msgid "Overwrite:"
msgstr "Overskriv:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:122
msgid "If tab characters can be entered"
msgstr "Om tabulatortegn kan settes inn"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Plassering:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "Plasseringen av teksten"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Brytning:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "Brytning av teksten"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Plass over:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Piksler med tomrom over avsnittene"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Plass under:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Piksler med tomrom under avsnittene"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Plass inni:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Piksler med tomrom mellom brutte linjer i et avsnitt"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Venstre marg:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Bredde på venstre marg i piksler"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Høyre marg:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Bredde på høyre marg i piksler"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Innrykk:"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Antall piksler avsnittene skal rykkes inn"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Tekstvisning"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "Om vendingsknapp opprinnelig er på"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Av/på knapp"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
#, fuzzy
msgid "Toolbar Toggle Button"
msgstr "Av/på knapp"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Ny verktøylinje"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Antall oppføringer:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "Antall oppføringer på verktøylinjen"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "Verktøylinjens orientering"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Stil:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "Verktøylinjens stil"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Verktøytips:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Om verktøytips er slått på"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "Show Arrow:"
msgstr "Vis pil:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""

#: ../glade/gbwidgets/gbtoolbar.c:427
#, fuzzy
msgid "If the item should be the same size as other homogeneous items"
msgstr "Om barna skal være av samme størrelse"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Sett inn oppføring før"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Sett inn oppføring etter"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Verktøylinje"

#: ../glade/gbwidgets/gbtoolbutton.c:586
msgid "Toolbar Button"
msgstr "Verktøylinjeknapp"

#: ../glade/gbwidgets/gbtoolitem.c:201
msgid "Toolbar Item"
msgstr "Verktøylinjeoppføring"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Kolonne 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Kolonne 2"

#: ../glade/gbwidgets/gbtreeview.c:87
msgid "Column 3"
msgstr "Kolonne 3"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr "Rad %i"

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Headere synlige:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Om knapper for kolonnetitler skal vises"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Regelhint:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""
"Om det skal settes et hint slik at temamotoren tegner radene i forskjellige "
"farger"

#: ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Omorganiserbar:"

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Om visningen kan omorganiseres"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Aktiver søk:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr "Om brukeren kan søke gjennom kolonnene interaktivt"

#: ../glade/gbwidgets/gbtreeview.c:123
#, fuzzy
msgid "Fixed Height Mode:"
msgstr "Skalert høyde:"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:125
#, fuzzy
msgid "Hover Selection:"
msgstr "Fargevalg"

#: ../glade/gbwidgets/gbtreeview.c:126
#, fuzzy
msgid "Whether the selection should follow the pointer"
msgstr "Listens utvalgsmodus"

#: ../glade/gbwidgets/gbtreeview.c:127
#, fuzzy
msgid "Hover Expand:"
msgstr "X utvidelse:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Liste- eller trevisning"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Ny vertikal boks"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Vertikal boks"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Ny vertikal knappeboks"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Vertikal knappeboks"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "Skyggetype for visningsområdet"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Visningsområde"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Vertikale faner"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Vertikal linjal"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Vertikal skala"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Vertikalt rullefelt"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Vertikalt skille"

#: ../glade/gbwidgets/gbwindow.c:242
msgid "The title of the window"
msgstr "Tittelen på vinduet"

#: ../glade/gbwidgets/gbwindow.c:245
msgid "The type of the window"
msgstr "Type vindu"

#: ../glade/gbwidgets/gbwindow.c:249
msgid "Type Hint:"
msgstr "Type hint:"

#: ../glade/gbwidgets/gbwindow.c:250
msgid "Tells the window manager how to treat the window"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:255
msgid "The initial position of the window"
msgstr "Startposisjon for vinduet"

#: ../glade/gbwidgets/gbwindow.c:259 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Modalt:"

#: ../glade/gbwidgets/gbwindow.c:259
msgid "If the window is modal"
msgstr "Om vinduet er modalt"

#: ../glade/gbwidgets/gbwindow.c:264
msgid "Default Width:"
msgstr "Standard bredde:"

#: ../glade/gbwidgets/gbwindow.c:265
msgid "The default width of the window"
msgstr "Vinduets standard bredde"

#: ../glade/gbwidgets/gbwindow.c:269
msgid "Default Height:"
msgstr "Standard høyde:"

#: ../glade/gbwidgets/gbwindow.c:270
msgid "The default height of the window"
msgstr "Vinduets standard høyde"

#: ../glade/gbwidgets/gbwindow.c:276
msgid "Resizable:"
msgstr "Kan endre størrelse:"

#: ../glade/gbwidgets/gbwindow.c:277
msgid "If the window can be resized"
msgstr "Om vinduet kan endre størrelse"

#: ../glade/gbwidgets/gbwindow.c:284
msgid "If the window can be shrunk"
msgstr "Om vinduet kan forminskes"

#: ../glade/gbwidgets/gbwindow.c:285
msgid "Grow:"
msgstr "Voks:"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be enlarged"
msgstr "Om vinduet kan forstørres"

#: ../glade/gbwidgets/gbwindow.c:291
msgid "Auto-Destroy:"
msgstr "Auto-ødelegg:"

#: ../glade/gbwidgets/gbwindow.c:292
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Om vinduet blir ødelagt når dets transiente opphav blir ødelagt"

#: ../glade/gbwidgets/gbwindow.c:296
msgid "The icon for this window"
msgstr "Ikonet for dette vinduet"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "Role:"
msgstr "Rolle:"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:306
msgid "Decorated:"
msgstr "Dekorert:"

#: ../glade/gbwidgets/gbwindow.c:307
msgid "If the window should be decorated by the window manager"
msgstr "Om vinduet skal dekoreres av vindushåndtereren"

#: ../glade/gbwidgets/gbwindow.c:310
msgid "Skip Taskbar:"
msgstr "Hopp over oppgavelisten:"

#: ../glade/gbwidgets/gbwindow.c:311
msgid "If the window should not appear in the task bar"
msgstr "Om vinduet skal vises i oppgavelisten"

#: ../glade/gbwidgets/gbwindow.c:314
msgid "Skip Pager:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:315
msgid "If the window should not appear in the pager"
msgstr "Om vinduet ikke skal vises i skrivebordsvelgeren"

#: ../glade/gbwidgets/gbwindow.c:318
msgid "Gravity:"
msgstr "Gravitasjon:"

#: ../glade/gbwidgets/gbwindow.c:319
msgid "The reference point to use when the window coordinates are set"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "Focus On Map:"
msgstr "Mål for fokus:"

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "If the window should receive the input focus when it is mapped"
msgstr "Om vinduet skal dekoreres av vindushåndtereren"

#: ../glade/gbwidgets/gbwindow.c:1198
msgid "Window"
msgstr "Vindu"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:74
msgid "Error"
msgstr "Feil"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Systemfeil"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "Feil under åpning av fil"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "Feil under lesing av fil"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "Feil under skriving til fil"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "Ugyldig katalog"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "Ugyldig verdi"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "Ugyldig XML-enhet"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "Start-tag forventet"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "Slutt-tag forventet"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "Karakterdata forventet"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "Klasse id mangler"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Ukjent klasse"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "Ugyldig komponent"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "Uventet slutt på fil"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Ukjent feilkode"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Kontrollert av"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Kontroller for"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Etikett for"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Etikett laget av"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Medlem av"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Nodebarn av"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr "Flyter til"

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr "Flyter fra"

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr "Undervindu av"

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr ""

#: ../glade/glade_atk.c:130
#, fuzzy
msgid "Embedded By"
msgstr "Etikett laget av"

#: ../glade/glade_atk.c:131
msgid "Popup For"
msgstr "Oppsprettmeny for"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr ""

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Forhold: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "Widget"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:772
#: ../glade/property.c:776
msgid "Name:"
msgstr "Navn:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr "Navn på widget som skal sendes til assisterende teknologi"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "Beskrivelse:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr "Beskrivelse av widget som skal sendes til assisterende teknologi"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Topptekst for tabell:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "Topptekst for tabell som skal sendes til assisterende teknologi"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Velg widget med dette forhold"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Klikk"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Trykk"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Slipp"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr ""
"Skriv inn beskrivelse av handlingen som skal sendes til assisterende "
"teknologi"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Utklippstavle"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Du må velge et widget å lime inn i"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "Du kan ikke lime inn i vinduer eller dialoger."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"Du kan ikke lime inn i valgt widget, siden det \n"
"opprettes automatisk av sin forelder."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr "Kun menyoppføringer kan limes inn i en meny eller en menylinje."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr "Kun knapper kan limes inn i en dialogs hendelsesområde."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "Kun GnomeDockItem widgeter kan limes inn i en GnomeDock."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "Kun GnomeDockItem widgeter kan limes inn over et GnomeDockItem."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "Beklager - innliming over et GnomeDockItem er ikke implementert ennå."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "GnomeDockItem widgeter kan kun limes inn i en GnomeDock."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:632
msgid "_New"
msgstr "_Ny"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Opprett en ny fil"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "Uøn_sket"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "GTK+ _grunnleggende"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "GTK+ _tillegg"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Velg snarveitast"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Taster"

#: ../glade/glade_menu_editor.c:394
msgid "Menu Editor"
msgstr "Menyredigering"

#: ../glade/glade_menu_editor.c:411
msgid "Type"
msgstr "Type"

#: ../glade/glade_menu_editor.c:412
msgid "Accelerator"
msgstr "Snarveitast"

#: ../glade/glade_menu_editor.c:413
msgid "Name"
msgstr "Navn"

#: ../glade/glade_menu_editor.c:414 ../glade/property.c:1498
msgid "Handler"
msgstr "Håndterer"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:102
msgid "Active"
msgstr "Aktiv"

#: ../glade/glade_menu_editor.c:416
msgid "Group"
msgstr "Gruppe"

#: ../glade/glade_menu_editor.c:417
msgid "Icon"
msgstr "Ikon"

#: ../glade/glade_menu_editor.c:458
msgid "Move the item and its children up one place in the list"
msgstr "Flytt objektet og dets barn opp en plass på listen"

#: ../glade/glade_menu_editor.c:470
msgid "Move the item and its children down one place in the list"
msgstr "Flytt objektet og dets barn ned en plass på listen"

#: ../glade/glade_menu_editor.c:482
msgid "Move the item and its children up one level"
msgstr "Flytt objektet og dets barn opp et nivå"

#: ../glade/glade_menu_editor.c:494
msgid "Move the item and its children down one level"
msgstr "Flytt objektet og dets barn ned et nivå"

#: ../glade/glade_menu_editor.c:524
msgid "The stock item to use."
msgstr "Standardobjekt som skal brukes."

#: ../glade/glade_menu_editor.c:527 ../glade/glade_menu_editor.c:642
msgid "Stock Item:"
msgstr "Standard objekt:"

#: ../glade/glade_menu_editor.c:640
msgid "The stock Gnome item to use."
msgstr "Standard Gnome objekt som skal brukes."

#: ../glade/glade_menu_editor.c:745
msgid "The text of the menu item, or empty for separators."
msgstr ""

#: ../glade/glade_menu_editor.c:769 ../glade/property.c:777
msgid "The name of the widget"
msgstr "Navnet på widgetet"

#: ../glade/glade_menu_editor.c:790
msgid "The function to be called when the item is selected"
msgstr "Funksjonen som skal kalles når oppføringen velges"

#: ../glade/glade_menu_editor.c:792 ../glade/property.c:1546
msgid "Handler:"
msgstr "Håndterer:"

#: ../glade/glade_menu_editor.c:811
msgid "An optional icon to show on the left of the menu item."
msgstr "Et alternativt ikon som skal vises på venstresiden av menyoppføringen."

#: ../glade/glade_menu_editor.c:934
msgid "The tip to show when the mouse is over the item"
msgstr "Tipset som skal vises når musen holdes over oppføringen"

#: ../glade/glade_menu_editor.c:936 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Verktøytips:"

#: ../glade/glade_menu_editor.c:957
msgid "_Add"
msgstr "_Legg til"

#: ../glade/glade_menu_editor.c:962
msgid "Add a new item below the selected item."
msgstr "Legg til en ny oppføring under den valgte."

#: ../glade/glade_menu_editor.c:967
msgid "Add _Child"
msgstr "Legg til _barn"

#: ../glade/glade_menu_editor.c:972
msgid "Add a new child item below the selected item."
msgstr "Legg til et nytt barn under den valgte oppføringen."

#: ../glade/glade_menu_editor.c:978
msgid "Add _Separator"
msgstr "Legg til _separator"

#: ../glade/glade_menu_editor.c:983
msgid "Add a separator below the selected item."
msgstr "Legg til separator under den valgte oppføringen."

#: ../glade/glade_menu_editor.c:988 ../glade/glade_project_window.c:239
msgid "_Delete"
msgstr "_Slett"

#: ../glade/glade_menu_editor.c:993
msgid "Delete the current item"
msgstr "Slett aktiv oppføring"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:999
msgid "Item Type:"
msgstr "Type oppføring:"

#: ../glade/glade_menu_editor.c:1015
msgid "If the item is initially on."
msgstr "Om oppføringen er på fra begynnelsen."

#: ../glade/glade_menu_editor.c:1017
msgid "Active:"
msgstr "Aktiv:"

#: ../glade/glade_menu_editor.c:1022 ../glade/glade_menu_editor.c:1632
#: ../glade/property.c:2215 ../glade/property.c:2225
msgid "No"
msgstr "Nei"

#: ../glade/glade_menu_editor.c:1036
msgid "The radio menu item's group"
msgstr "Radiomenyoppføringens gruppe"

#: ../glade/glade_menu_editor.c:1053 ../glade/glade_menu_editor.c:2406
#: ../glade/glade_menu_editor.c:2546
msgid "Radio"
msgstr "Radio"

#: ../glade/glade_menu_editor.c:1060 ../glade/glade_menu_editor.c:2404
#: ../glade/glade_menu_editor.c:2544
msgid "Check"
msgstr "Avkryssning"

#: ../glade/glade_menu_editor.c:1067 ../glade/property.c:102
msgid "Normal"
msgstr "Normal"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1076
msgid "Accelerator:"
msgstr "Snarveitast:"

#: ../glade/glade_menu_editor.c:1113 ../glade/property.c:1681
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1118 ../glade/property.c:1684
msgid "Shift"
msgstr "Shift"

#: ../glade/glade_menu_editor.c:1123 ../glade/property.c:1687
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1128 ../glade/property.c:1694
msgid "Key:"
msgstr "Tast:"

#: ../glade/glade_menu_editor.c:1134 ../glade/property.c:1673
msgid "Modifiers:"
msgstr "Modifikatorer:"

#: ../glade/glade_menu_editor.c:1632 ../glade/glade_menu_editor.c:2411
#: ../glade/glade_menu_editor.c:2554 ../glade/property.c:2215
msgid "Yes"
msgstr "Ja"

#: ../glade/glade_menu_editor.c:2002
msgid "Select icon"
msgstr "Velg ikon"

#: ../glade/glade_menu_editor.c:2345 ../glade/glade_menu_editor.c:2706
msgid "separator"
msgstr "separator"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3624 ../glade/glade_project_window.c:366
#: ../glade/property.c:5109
msgid "New"
msgstr "Ny"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Utvelger"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Prosjektkatalogen er ikke satt.\n"
"Vennligst sett den ved å bruke prosjektalternativer-dialogen.\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Kildekatalogen er ikke satt.\n"
"Vennligst sett den ved å bruke prosjektalternativer-dialogen.\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"Ugyldig kildekatalog:\n"
"\n"
"Kildekatalogen må være lik prosjektkatalogen\n"
"eller en underkatalog av prosjektkatalogen.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Bildekatalogen er ikke satt.\n"
"Vennligst sett den ved å bruke prosjektalternativer-dialogen.\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "Beklager - generering av kilde for %s er ikke implementert ennå"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""
"Prosjektet ditt bruker gamle programkomponenter som\n"
"ikke støttes av Gtkmm-2. Prøv å bruke erstatningene\n"
"for disse komponentene i ditt prosjekt."

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"Feil under kjøring av glade-- for å generere C++ kildekode.\n"
"Sjekk at du har glade-- installert og at det er i stien.\n"
"Prøv deretter å kjøre «glade-- <prosjekt_fil.glade>» i en terminal."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"Feil under kjøring av gate for å generere ADA-kildekode.\n"
"Sjekk at du har gate installert og at det er i stien.\n"
"Prøv deretter å kjøre «gate <prosjekt_fil.glade>» i en terminal."

#: ../glade/glade_project.c:571
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"Feil under kjøring av glade2perl for å generere Perl-kildekode.\n"
"Sjekk at du har glade2perl installert og at det er i stien.\n"
"Prøv deretter å kjøre «glade2perl <prosjekt_fil.glade>» i en terminal."

#: ../glade/glade_project.c:594
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"Feil under kjøring av eglade for å generere Eiffel-kildekode.\n"
"Sjekk at du har eglade installert og at det er i stien.\n"
"Prøv deretter å kjøre «eglade <prosjekt_fil.glade>» i en terminal."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Bildekatalogen er ikke satt.\n"
"Vennligst sett den ved å bruke prosjektalternativ-dialogen.\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "Feil under skriving av prosjektets XML-fil\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:382
#: ../glade/glade_project_window.c:889
msgid "Project Options"
msgstr "Alternativer for prosjekt"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "Generelt"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Basisalternativer:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "Katalog for prosjektet"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Prosjektkatalog:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Bla gjennom..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "Navnet på aktivt prosjekt"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Prosjektnavn:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "Navnet på programmet"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "Prosjektfilen"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Prosjektfil:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Underkataloger:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "Katalogen hvor generert kildekode skal lagres"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Kildekatalog:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "Katalogen hvor bilder skal lagres"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Bildekatalog:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "Lisensen som legges til øverst i genererte filer"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Språk:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Slå på Gnome støtte"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Om en skal bygge en Gnome applikasjon"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Slå på Gnome DB støtte"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Om en skal bygge en Gnome DB applikasjon"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "C-alternativer"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Generelle alternativer:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Gettext støtte"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "Om strenger skal merkes for oversettelse av gettext"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "Sett widget navn"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Om navn på widgeter skal settes i kildekoden"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Sikkerhetskopi av kildekode"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Om kopier av gamle kildekodefiler lages"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Gnome hjelp støtte"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Om støtte for GNOME hjelp systemet skal inkluderes"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Alternativer for filutskrift:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "Skriv ut main.c fil"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr ""
"Om en main.c fil skrives ut og inneholder en main() funksjon, om denne ikke "
"eksisterer"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Skriv ut støttefunksjoner"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Om støttefunksjonene skrives ut"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Skriv ut build-filer"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Om filer for bygging av kildekoden skal skrives ut, dette inkluderer "
"Makefile.am og configure.in, hvis disse ikke allerede eksisterer"

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "Funksjoner for oppretting av grensesnitt:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "Filen hvor funksjonene for oppretting av grensesnittet blir plassert"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Kildefil:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr ""
"Filen hvor deklarasjonene av funksjonene for å opprette grenesnittet blir "
"skrevet."

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Header-fil:"

#: ../glade/glade_project_options.c:594
msgid "Source file for interface creation functions"
msgstr "Kildefil for funksjoner for oppretting av grensesnitt"

#: ../glade/glade_project_options.c:595
msgid "Header file for interface creation functions"
msgstr "Header-fil for fuksjoner for oppretting av grensesnitt"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Signalhåndterer & Callback funksjoner:"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr ""
"Filen hvor den tomme signalhåndtereren og callback funksjonene blir skrevet"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"Filen hvor deklarasjonene av signalhåndtereren og callback funksjonene blir "
"skrevet"

#: ../glade/glade_project_options.c:640
msgid "Source file for signal handler and callback functions"
msgstr "Kildefil for signalhåndterer og funksjoner for tilbakekall"

#: ../glade/glade_project_options.c:641
msgid "Header file for signal handler and callback functions"
msgstr "Headerfil for signalhåndterer og funksjoner for tilbakekall"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Støttefunksjoner:"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "Filen hvor støttefunksjonene blir skrevet"

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "Filen hvor deklarasjonene av støttefunksjonene blir skrevet"

#: ../glade/glade_project_options.c:686
msgid "Source file for support functions"
msgstr "Kildefil for støttefunksjoner"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr ""

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "LibGlade alternativer"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Oversettbare strenger:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Lagre oversettbare strenger"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Om oversettbare strenger skal lagres i separate C kildefiler, for å gjøre "
"mulig oversetting av grensesnitt lastet av libglade"

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "C kildefilen hvor alle oversettbare strenger lagres"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Fil:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Velg prosjektkatalogen"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Du må sette alternativet for fil med oversettbare strenger"

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Du må sette prosjektkatalog alternativet"

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Du må sette prosjektfil alternativet"

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Du må sette prosjektnavn alternativet"

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Du må sette programnavn alternativet"

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Du må sette kildekatalog alternativet"

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Du må sette bildekatalog alternativet"

#: ../glade/glade_project_window.c:184
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"Kunne ikke vise hjelpfil: %s.\n"
"Feil: %s"

#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:634
msgid "Create a new project"
msgstr "Opprett et nytt prosjekt"

#: ../glade/glade_project_window.c:216 ../glade/glade_project_window.c:654
#: ../glade/glade_project_window.c:905
msgid "_Build"
msgstr "_Bygg"

#: ../glade/glade_project_window.c:217 ../glade/glade_project_window.c:665
msgid "Output the project source code"
msgstr "Skriv ut prosjektets kildekode"

#: ../glade/glade_project_window.c:223 ../glade/glade_project_window.c:668
msgid "Op_tions..."
msgstr "Al_ternativer..."

#: ../glade/glade_project_window.c:224 ../glade/glade_project_window.c:677
msgid "Edit the project options"
msgstr "Rediger alternativene for prosjektet"

#: ../glade/glade_project_window.c:239 ../glade/glade_project_window.c:716
msgid "Delete the selected widget"
msgstr "Slett valgt widget"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:727
msgid "Show _Palette"
msgstr "Vis _palett"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:732
msgid "Show the palette of widgets"
msgstr "Vis widget-paletten"

#: ../glade/glade_project_window.c:263 ../glade/glade_project_window.c:737
msgid "Show Property _Editor"
msgstr "Vis redigering av _egenskaper"

#: ../glade/glade_project_window.c:264 ../glade/glade_project_window.c:743
msgid "Show the property editor"
msgstr "Vis redigering av egenskaper"

#: ../glade/glade_project_window.c:270 ../glade/glade_project_window.c:747
msgid "Show Widget _Tree"
msgstr "Vis widget _tre"

#: ../glade/glade_project_window.c:271 ../glade/glade_project_window.c:753
#: ../glade/main.c:82
msgid "Show the widget tree"
msgstr "Trevisning av widgeter"

#: ../glade/glade_project_window.c:277 ../glade/glade_project_window.c:757
msgid "Show _Clipboard"
msgstr "Vis ut_klippstavlen"

#: ../glade/glade_project_window.c:278 ../glade/glade_project_window.c:763
#: ../glade/main.c:86
msgid "Show the clipboard"
msgstr "Vis utklippstavlen"

#: ../glade/glade_project_window.c:296
msgid "Show _Grid"
msgstr "Vis r_utenett"

#: ../glade/glade_project_window.c:297 ../glade/glade_project_window.c:799
msgid "Show the grid (in fixed containers only)"
msgstr "Vis rutenettet (kun i faste kontainere)"

#: ../glade/glade_project_window.c:303
msgid "_Snap to Grid"
msgstr "_Fest til rutenettet"

#: ../glade/glade_project_window.c:304
msgid "Snap widgets to the grid"
msgstr "Fest widgeter til rutenettet"

#: ../glade/glade_project_window.c:310 ../glade/glade_project_window.c:771
msgid "Show _Widget Tooltips"
msgstr "Vis _widget vektøytips"

#: ../glade/glade_project_window.c:311 ../glade/glade_project_window.c:779
msgid "Show the tooltips of created widgets"
msgstr "Vis verktøytips for opprettede widgeter"

#: ../glade/glade_project_window.c:320 ../glade/glade_project_window.c:802
msgid "Set Grid _Options..."
msgstr "Sett _alternativer for rutenett..."

#: ../glade/glade_project_window.c:321
msgid "Set the grid style and spacing"
msgstr "Sett stil og mellomrom for rutenett"

#: ../glade/glade_project_window.c:327 ../glade/glade_project_window.c:823
msgid "Set Snap O_ptions..."
msgstr "Sett a_lternativer for feste..."

#: ../glade/glade_project_window.c:328
msgid "Set options for snapping to the grid"
msgstr "Sett alternativer for feste til rutenettet"

#: ../glade/glade_project_window.c:340
msgid "_FAQ"
msgstr "_OBS"

#: ../glade/glade_project_window.c:341
msgid "View the Glade FAQ"
msgstr "Vis OBS for Glade"

#. create File menu
#: ../glade/glade_project_window.c:355 ../glade/glade_project_window.c:625
msgid "_Project"
msgstr "_Prosjekt"

#: ../glade/glade_project_window.c:366 ../glade/glade_project_window.c:872
#: ../glade/glade_project_window.c:1049
msgid "New Project"
msgstr "Nytt prosjekt"

#: ../glade/glade_project_window.c:371
msgid "Open"
msgstr "Åpne"

#: ../glade/glade_project_window.c:371 ../glade/glade_project_window.c:877
#: ../glade/glade_project_window.c:1110
msgid "Open Project"
msgstr "Åpne prosjekt"

#: ../glade/glade_project_window.c:376
msgid "Save"
msgstr "Lagre"

#: ../glade/glade_project_window.c:376 ../glade/glade_project_window.c:881
#: ../glade/glade_project_window.c:1475
msgid "Save Project"
msgstr "Lagre prosjekt"

#: ../glade/glade_project_window.c:382
msgid "Options"
msgstr "Alternativer"

#: ../glade/glade_project_window.c:387
msgid "Build"
msgstr "Bygg"

#: ../glade/glade_project_window.c:387
msgid "Build the Source Code"
msgstr "Bygg kildekoden"

#: ../glade/glade_project_window.c:638
msgid "Open an existing project"
msgstr "Åpne et eksisterende prosjekt"

#: ../glade/glade_project_window.c:642
msgid "Save project"
msgstr "Lagre prosjekt"

#: ../glade/glade_project_window.c:687
msgid "Quit Glade"
msgstr "Avslutt Glade"

#: ../glade/glade_project_window.c:701
msgid "Cut the selected widget to the clipboard"
msgstr "Klipp ut valgt widget til utklippstavlen"

#: ../glade/glade_project_window.c:706
msgid "Copy the selected widget to the clipboard"
msgstr "Kopier valgt widget til utklippstavlen"

#: ../glade/glade_project_window.c:711
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Lim inn widget fra utklippstavlen over valgt widget"

#: ../glade/glade_project_window.c:783
msgid "_Grid"
msgstr "_Rutenett"

#: ../glade/glade_project_window.c:791
msgid "_Show Grid"
msgstr "Vi_s rutenett"

#: ../glade/glade_project_window.c:808
msgid "Set the spacing between grid lines"
msgstr "Sett mellomrom mellom linjene i rutenettet"

#: ../glade/glade_project_window.c:811
msgid "S_nap to Grid"
msgstr "Fest til rute_nett"

#: ../glade/glade_project_window.c:819
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "Fest widgeter til rutenettet (kun i faste kontainere)"

#: ../glade/glade_project_window.c:829
msgid "Set which parts of a widget snap to the grid"
msgstr "Sett hvilke deler av et widget som skal festes til rutenettet"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:854
msgid "_About..."
msgstr "_Om..."

#: ../glade/glade_project_window.c:895
msgid "Optio_ns"
msgstr "Alter_nativer"

#: ../glade/glade_project_window.c:899
msgid "Write Source Code"
msgstr "Skriv kildekode"

#: ../glade/glade_project_window.c:986 ../glade/glade_project_window.c:1691
#: ../glade/glade_project_window.c:1980
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:993
msgid "Are you sure you want to create a new project?"
msgstr "Er du sikker på at du vil opprette et nytt prosjekt?"

#: ../glade/glade_project_window.c:1053
msgid "New _GTK+ Project"
msgstr "Nytt _GTK+-prosjekt"

#: ../glade/glade_project_window.c:1054
msgid "New G_NOME Project"
msgstr "Nytt G_NOME-prosjekt"

#: ../glade/glade_project_window.c:1057
msgid "Which type of project do you want to create?"
msgstr "Hvilken type prosjekt ønsker du å opprette?"

#: ../glade/glade_project_window.c:1091
msgid "New project created."
msgstr "Nytt prosjekt opprettet."

#: ../glade/glade_project_window.c:1181
msgid "Project opened."
msgstr "Prosjekt åpnet."

#: ../glade/glade_project_window.c:1195
msgid "Error opening project."
msgstr "Feil under åpning av prosjekt."

#: ../glade/glade_project_window.c:1259
msgid "Errors opening project file"
msgstr "Feil under åpning av prosjektfilen"

#: ../glade/glade_project_window.c:1265
msgid " errors opening project file:"
msgstr " feil under åpning av prosjektfil:"

#: ../glade/glade_project_window.c:1338
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"Det finnes ingen åpne prosjekter.\n"
"Opprett et nytt prosjekt med kommandoen «Prosjekt/Nytt»."

#: ../glade/glade_project_window.c:1542
msgid "Error saving project"
msgstr "Feil under lagring av prosjekt"

#: ../glade/glade_project_window.c:1544
msgid "Error saving project."
msgstr "Feil under lagring av prosjekt."

#: ../glade/glade_project_window.c:1550
msgid "Project saved."
msgstr "Prosjekt lagret."

#: ../glade/glade_project_window.c:1620
msgid "Errors writing source code"
msgstr "Feil under utskrift av kildekode"

#: ../glade/glade_project_window.c:1622
msgid "Error writing source."
msgstr "Feil ved skriving av kildekode."

#: ../glade/glade_project_window.c:1628
msgid "Source code written."
msgstr "Kildekode skrevet."

#: ../glade/glade_project_window.c:1659
msgid "System error message:"
msgstr "Systemfeilmelding:"

#: ../glade/glade_project_window.c:1698
msgid "Are you sure you want to quit?"
msgstr "Er du sikker på at du vil avslutte?"

#: ../glade/glade_project_window.c:1982 ../glade/glade_project_window.c:2042
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "© 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1983 ../glade/glade_project_window.c:2041
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr "Glade er et program for å bygge grensesnitt for GTK+ og GNOME."

#: ../glade/glade_project_window.c:2012
msgid "About Glade"
msgstr "Om Glade"

#: ../glade/glade_project_window.c:2097
msgid "<untitled>"
msgstr "<uten navn>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Databaseleser"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Databundet kombo"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr ""

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Tilkoblingsvelger"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "DSN-konfigurator"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "Konfigurasjonsveiviser for DSN"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:178
msgid "GnomeDbEditor"
msgstr "GnomeDbEditor"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Visning av databasefeil"

#: ../glade/gnome-db/gnomedberrordlg.c:218
msgid "Database error dialog"
msgstr "Dialog for databasefeil"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Form"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr "Tekst i den grå linjen"

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr "Grå linje"

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Databundet rutenett"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Databundet liste"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Widget for pålogging til database"

#: ../glade/gnome-db/gnomedblogindlg.c:76
msgid "Login"
msgstr "Pålogging"

#: ../glade/gnome-db/gnomedblogindlg.c:219
msgid "Database login dialog"
msgstr "Dialog for pålogging til database"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Valg av tilbyder"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr "GnomeDbQueryBuilder"

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Valg av datakilde"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Tabellredigering "

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Tillat flytende:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Om flytende dokkoppføringer tillates"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Legg til dokkbånd øverst"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Legg til dokkbånd nederst"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Legg til dokkbånd til venstre"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Legg til dokkbånd til høyre"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Legg til flytende dokkoppføring"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Gnome dokk"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Låst:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Om dokkoppføringen låses på plass"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Eksklusiv:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "Om dokkoppføringen alltid er den eneste oppføringen på sitt bånd"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Aldri flytende:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "Om dokkoppførinen aldri kan flyte i sitt eget vindu"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Aldri vertikal:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "Om dokkoppføringen aldri kan være vertikal"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Aldri horisontal:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "Om dokkoppføringen aldri kan være horisontal"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "Type skygge rundt dokkoppføringen"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "Orienteringen til en flytende dokkoppføring"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Legg til dokkoppføring før"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Legg til dokkoppføring etter"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Gnome dokkoppføring"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr ""
"Tilleggsinformasjon, slik som beskrivelse av pakken og dens hjemmeside på web"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Om GNOME dialog"

#: ../glade/gnome/gnomeapp.c:170
msgid "New File"
msgstr "Ny fil"

#: ../glade/gnome/gnomeapp.c:172
msgid "Open File"
msgstr "Åpne fil"

#: ../glade/gnome/gnomeapp.c:174
msgid "Save File"
msgstr "Lagre fil"

#: ../glade/gnome/gnomeapp.c:203
msgid "Status Bar:"
msgstr "Statuslinje:"

#: ../glade/gnome/gnomeapp.c:204
msgid "If the window has a status bar"
msgstr "Om vinduet har en statuslinje"

#: ../glade/gnome/gnomeapp.c:205
msgid "Store Config:"
msgstr "Lagre konfigurasjon:"

#: ../glade/gnome/gnomeapp.c:206
msgid "If the layout is saved and restored automatically"
msgstr "Om utformingen skal lagres og gjenoprettes automatisk"

#: ../glade/gnome/gnomeapp.c:442
msgid "Gnome Application Window"
msgstr "GNOME applikasjonsvindu"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Statusmelding."

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "Fremgang:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "Om applikasjonslinjen har en fremgangsindikator"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Status:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr ""
"Om applikasjonslinjen har et område for statusmeldinger og inndata fra bruker"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "GNOME Applikasjonslinje"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Anti-aliased:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr "Om kanvaset er anti-aliased, gjør kantene på tekst og grafikk mykere"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "Minimum x koordinat"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "Minimum y koordinat"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2:"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "Maksimum x koordinat"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "Maksimum y koordinat"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Piksler per enhet:"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "Antall piksler som tilsvarer en enhet"

#: ../glade/gnome/gnomecanvas.c:239
msgid "GnomeCanvas"
msgstr "GnomeCanvas"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Dither:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Om prøven skal bruke dithering for å bli mer nøyaktig"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Velg en farge"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Gnome fargevelger"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "Kunne ikke opprette en Bonobo-kontroll"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Ny Bonobo-kontroll"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Velg en Bonobo-kontroll"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3896
msgid "Description"
msgstr "Beskrivelse"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Bonobo-kontroll"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Vis tid:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Om tiden skal vises i tillegg til datoen"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "24 timers format:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Om tiden skal vises i 24 timers format"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Laveste time:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "Laveste time som skal vises i oppsprettmenyen"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Øverste time:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "Øverste time som vises i oppsprettmenyen"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "GnomeDateEdit"

#: ../glade/gnome/gnomedialog.c:152 ../glade/gnome/gnomemessagebox.c:189
msgid "Auto Close:"
msgstr "Autolukk:"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "If the dialog closes when any button is clicked"
msgstr "Om dialogen lukkes når en hvilken som helst knapp trykkes"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "Hide on Close:"
msgstr "Skjul ved lukking:"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "Om dialogen er skjult når den er lukket, i stedet for å bli ødelagt"

#: ../glade/gnome/gnomedialog.c:341
msgid "Gnome Dialog Box"
msgstr "Gnome dialogboks"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Ny Gnome druide"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Vis hjelp"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Vis hjelpknappen."

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Legg til startside"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Legg til sluttside"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Druide"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "Tittelen på siden"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "Hovedteksten på siden som presenterer druiden for folk."

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Tittelfarge:"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "Fargen på tittelteksten"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Tekstfarge:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "Fargen på hovedteksten"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "Bakgrunnsfarge for siden"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Logo bakgr. farge:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "Bakgrunnsfargen rundt logoen"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Tekstboksfarge:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "Bakgrunnsfargen for hovedtekstområdet"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Logobilde:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "Logoen som skal vises i øverste høyre hjørne av siden"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Vannmerke til siden:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "Hovedbildet som skal vises på kanten av siden."

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Vannmerke øverst:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr "Vannmerket som skal vises øverst på siden."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Druid startside eller sluttside"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Bakgrunnsfarge for innhold:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "Bakgrunnsfargen rundt tittelen"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "Bildet som skal vises langs toppen av siden"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Druid standardside"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "Historikk ID:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "ID som historikkoppføringer skal lagres under"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Maks lagrede:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "Maksimalt antall historikkoppføringer som skal lagres"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Gnome Entry"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "Tittelen på dialogen for filvalg"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Katalog:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Om en katalog kreves i stedet for en fil"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "Om filutvalgsdialogen skal være modal"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr "Bruk filvelger:"

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Gnome File Entry"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "Den forhåndsviste teksten i dialogen for valg av skrifttype"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Modus:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "Hva som skal vises på skrifttypeplukker knappen"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "Størrelsen på skrifttypen som skal brukes på skrifttypeplukker knappen"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Gnome skrifftypeplukker"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "URL'en som skal vises når knappen klikkes"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "Teksten som skal vises på knappen"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Gnome HRef link knapp"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Gnome ikonoppføring"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "Utvalgsmodus"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Ikonbredde:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "Bredden for hvert ikon"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "Antall piksler mellom rader med ikoner"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "Antall piksler mellom kolonner med ikoner"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Ikonkant:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "Antall piksler rundt ikoner (ubrukt?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Tekstmellomrom:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "Antall piksler mellom tekst og ikon"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Redigerbar tekst:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Tekst kan redigeres av brukeren"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Statisk tekst:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr "Bruk statisk ikontekst, vil ikke bli kopiert av GnomeIconList"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Ikonliste"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Ikonvalg"

#: ../glade/gnome/gnomemessagebox.c:174
msgid "Message Type:"
msgstr "Meldingstype:"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "The type of the message box"
msgstr "Type meldingsboks"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "Message:"
msgstr "Melding:"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "The message to display"
msgstr "Meldingen som skal vises"

#: ../glade/gnome/gnomemessagebox.c:498
msgid "Gnome Message Box"
msgstr "Gnome Message Box"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "Filnavn for bilde"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Skalert:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Om bildet er skalert"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Skalert bredde:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "Bredden bildet skal skaleres til"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Skalert høyde:"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "Høyden bildet skal skaleres til"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Gnome bilde"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Forhåndsvisning:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Om en liten forhåndsvisning av bildet skal vises "

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmapEntry"

#: ../glade/gnome/gnomepropertybox.c:112
msgid "New GnomePropertyBox"
msgstr "Ny GnomePropertyBox"

#: ../glade/gnome/gnomepropertybox.c:365
msgid "Property Dialog Box"
msgstr "Egenskaper-dialogboks"

#: ../glade/main.c:70
msgid "Write the source code and exit"
msgstr "Skriv kildekoden og avslutt"

#: ../glade/main.c:74
msgid "Start with the palette hidden"
msgstr "Start med paletten skjult"

#: ../glade/main.c:78
msgid "Start with the property editor hidden"
msgstr "Start med redigering av egenskaper skjult"

#: ../glade/main.c:436
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade: XML-filen må være satt for '-w' eller '--write-source' flagget.\n"

#: ../glade/main.c:450
msgid "glade: Error loading XML file.\n"
msgstr "glade: Feil under lasting av XML-fil.\n"

#: ../glade/main.c:457
msgid "glade: Error writing source.\n"
msgstr "glade: Feil ved skriving av kildekode.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Palett"

#: ../glade/property.c:73
msgid "private"
msgstr "privat"

#: ../glade/property.c:73
msgid "protected"
msgstr "beskyttet"

#: ../glade/property.c:73
msgid "public"
msgstr "offentlig"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "Forhåndsbelys"

#: ../glade/property.c:103
msgid "Selected"
msgstr "Valgt"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Insens"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Når vinduet trenger gjenopptegning"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Når musen beveger seg"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Hint for musbevegelse"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Musbevegelse med en knapp nedtrykket"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Musbevegelse med knapp 1 nedtrykket"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Musbevegelse med knapp 2 nedtrykket"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Musbevegelse med knapp 3 nedtrykket"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "Hvilken som helst musknapp nedtrykket"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "Hvilken som helst musknapp sluppet"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "Enhver knapp sluppet"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "Hvilken som helst knapp sluppet opp"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Når muspekeren kommer inn i vinduet"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Når muspekeren forlater vinduet"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Enhver endring i input fokus"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Enhver endring i vindustruktur"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "Enhver endring av X-vindu egenskaper"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Enhver endring av synlighet"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "For markører i XInput bevisste programmet"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Egenskaper"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Pakking"

#: ../glade/property.c:625
msgid "Common"
msgstr "Felles"

#: ../glade/property.c:631
msgid "Style"
msgstr "Stil"

#: ../glade/property.c:637 ../glade/property.c:4640
msgid "Signals"
msgstr "Signaler"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Egenskaper: "

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Egenskaper: <ingen>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "Klasse:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "Widgetets klasse"

#: ../glade/property.c:813
msgid "Width:"
msgstr "Bredde:"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""
"Forespurt bredde på widget (brukes vanligvis for å sette minimumsbredden)"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Høyde:"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""
"Forespurt høyde på widget (brukes vanligvis for å sette minimumshøyden)"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Synlig:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "Om widgetet er synlig initielt"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Følsomt:"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "Om widgetet svarer på input"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "Verktøytipset som skal vises hvis muspekeren holdes over widgetet"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Kan forvelges:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr "Om widgetet kan være forvalgt handling i en dialog"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "Har forvalg:"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Om widgetet er forvalgt handling i en dialog"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Kan fokuserer:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "Om widgetet kan ta imot input fokus"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Har fokus:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "Om widgetet har input fokus"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Hendelser:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "X-hendelsene som widgetet mottar"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Utv. hendelser:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "X-utvidelse modus"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Snarveitaster:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Definerer signalene som skal sendes når taster trykkes"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Rediger..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "Videresend:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "Sett til sann for å videresende widgetets stil til dets barn"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Navngitt stil:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "Navnet på stilen, som kan brukes av flere widgeter"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Skrifttype:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "Skrifttypen som skal brukes for tekst i widgetet"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Kopier alt"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Forgrunn:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Bakgrunn:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "Basis:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Forgrunnsfarge"

#: ../glade/property.c:928
msgid "Background color"
msgstr "Bakgrunnsfarge"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Tekstfarge"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Basisfarge"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Bakgrunnsbilde:"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "Grafikken som skal brukes som bakgrunn for widgetet"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "Filen som kildekoden skal skrives til"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Offentlig:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Om widgetet skal legges til i komponentens datastruktur"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Egen klasse:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Legg dette widgetets undertre i en egen klasse"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Egen fil:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Legg dette widgetet i en egen kildefil"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Synlighet:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr ""
"Synlighet for widgets. Offentlige widgets eksporteres til et globalt kart."

#: ../glade/property.c:1126
msgid "You need to select a color or background to copy"
msgstr "Du må velge en farge eller bakgrunn som skal kopieres"

#: ../glade/property.c:1145
msgid "Invalid selection in on_style_copy()"
msgstr "Ugyldig valg i on_style_copy()"

#: ../glade/property.c:1187
msgid "You need to copy a color or background pixmap first"
msgstr "Du må kopiere en farge eller et bakgrunnsbilde først"

#: ../glade/property.c:1193
msgid "You need to select a color to paste into"
msgstr "Du må velge en farge som det skal limes inn i"

#: ../glade/property.c:1203
msgid "You need to select a background pixmap to paste into"
msgstr "Du må velge et bakgrunnsbilde å lime inn i"

#: ../glade/property.c:1455
msgid "Couldn't create pixmap from file\n"
msgstr "Kunne ikke opprette bilde fra fil\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1497
msgid "Signal"
msgstr "Signal"

#: ../glade/property.c:1499
msgid "Data"
msgstr "Data"

#: ../glade/property.c:1500
msgid "After"
msgstr "Etter"

#: ../glade/property.c:1501
msgid "Object"
msgstr "Objekt"

#: ../glade/property.c:1532 ../glade/property.c:1696
msgid "Signal:"
msgstr "Signal:"

#: ../glade/property.c:1533
msgid "The signal to add a handler for"
msgstr "Signalet det skal legges til en håndterer for"

#: ../glade/property.c:1547
msgid "The function to handle the signal"
msgstr "Funksjonen som skal håndtere signalet"

#: ../glade/property.c:1550
msgid "Data:"
msgstr "Data:"

#: ../glade/property.c:1551
msgid "The data passed to the handler"
msgstr "Data som sendes til håndtereren"

#: ../glade/property.c:1552
msgid "Object:"
msgstr "Objekt:"

#: ../glade/property.c:1553
msgid "The object which receives the signal"
msgstr "Objektet som mottar signalet"

#: ../glade/property.c:1554
msgid "After:"
msgstr "Etter:"

#: ../glade/property.c:1555
msgid "If the handler runs after the class function"
msgstr "Om håndtereren kjører etter klassefunksjonen"

#: ../glade/property.c:1568
msgid "Add"
msgstr "Legg til"

#: ../glade/property.c:1574
msgid "Update"
msgstr "Oppdater"

#: ../glade/property.c:1586
msgid "Clear"
msgstr "Tøm"

#: ../glade/property.c:1636
msgid "Accelerators"
msgstr "Snarveitaster"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1649
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1650
msgid "Key"
msgstr "Tast"

#: ../glade/property.c:1651
msgid "Signal to emit"
msgstr "Signal som skal sendes"

#: ../glade/property.c:1695
msgid "The accelerator key"
msgstr "Snarveitast"

#: ../glade/property.c:1697
msgid "The signal to emit when the accelerator is pressed"
msgstr "Signal som skal sendes når snarveitasten trykkes"

#: ../glade/property.c:1846
msgid "Edit Text Property"
msgstr "Rediger tekstegenskap"

#: ../glade/property.c:1884
msgid "<b>_Text:</b>"
msgstr "<b>_Tekst:</b>"

#: ../glade/property.c:1894
msgid "T_ranslatable"
msgstr "Ove_rsettbare"

#: ../glade/property.c:1898
msgid "Has Context _Prefix"
msgstr ""

#: ../glade/property.c:1924
msgid "<b>Co_mments For Translators:</b>"
msgstr "<b>Ko_mmentarer for oversettere:</b>"

#: ../glade/property.c:3886
msgid "Select X Events"
msgstr "Velg X-hendelser"

#: ../glade/property.c:3895
msgid "Event Mask"
msgstr "Hendelsesmaske"

#: ../glade/property.c:4025 ../glade/property.c:4074
msgid "You need to set the accelerator key"
msgstr "Du må sette tastatursnarveien"

#: ../glade/property.c:4032 ../glade/property.c:4081
msgid "You need to set the signal to emit"
msgstr "Du må sette signalet som skal sendes ut"

#: ../glade/property.c:4308 ../glade/property.c:4364
msgid "You need to set the signal name"
msgstr "Du må sette navnet på signalet"

#: ../glade/property.c:4315 ../glade/property.c:4371
msgid "You need to set the handler for the signal"
msgstr "Du må sette håndtereren for signalet"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4574
#, c-format
msgid "%s signals"
msgstr "%s signaler"

#: ../glade/property.c:4631
msgid "Select Signal"
msgstr "Velg signal"

#: ../glade/property.c:4827
msgid "Value:"
msgstr "Verdi:"

#: ../glade/property.c:4827
msgid "Min:"
msgstr "Min:"

#: ../glade/property.c:4827
msgid "Step Inc:"
msgstr "Øk stegvis:"

#: ../glade/property.c:4828
msgid "Page Inc:"
msgstr "Øk sidevis:"

#: ../glade/property.c:4828
msgid "Page Size:"
msgstr "Sidestørrelse:"

#: ../glade/property.c:4830
msgid "H Value:"
msgstr "H verdi:"

#: ../glade/property.c:4830
msgid "H Min:"
msgstr "H min:"

#: ../glade/property.c:4830
msgid "H Max:"
msgstr "H maks:"

#: ../glade/property.c:4830
msgid "H Step Inc:"
msgstr "Øk stegvis H:"

#: ../glade/property.c:4831
msgid "H Page Inc:"
msgstr "Øk sidevis H:"

#: ../glade/property.c:4831
msgid "H Page Size:"
msgstr "H sidestørrelse:"

#: ../glade/property.c:4833
msgid "V Value:"
msgstr "V verdi:"

#: ../glade/property.c:4833
msgid "V Min:"
msgstr "V min:"

#: ../glade/property.c:4833
msgid "V Max:"
msgstr "V maks:"

#: ../glade/property.c:4833
msgid "V Step Inc:"
msgstr "Øk stegvis V:"

#: ../glade/property.c:4834
msgid "V Page Inc:"
msgstr "Øk sidevis V:"

#: ../glade/property.c:4834
msgid "V Page Size:"
msgstr "V sidestørrelse:"

#: ../glade/property.c:4837
msgid "The initial value"
msgstr "Opprinnelig verdi"

#: ../glade/property.c:4838
msgid "The minimum value"
msgstr "Minimumsverdi"

#: ../glade/property.c:4839
msgid "The maximum value"
msgstr "Maksimalverdi"

#: ../glade/property.c:4840
msgid "The step increment"
msgstr "Stegvis økning"

#: ../glade/property.c:4841
msgid "The page increment"
msgstr "Sidevis økning"

#: ../glade/property.c:4842
msgid "The page size"
msgstr "Sidestørrelsen"

#: ../glade/property.c:4997
msgid "The requested font is not available."
msgstr "Den forespurte skrifttypen er ikke tilgjengelig."

#: ../glade/property.c:5046
msgid "Select Named Style"
msgstr "Velg navngitt stil"

#: ../glade/property.c:5057
msgid "Styles"
msgstr "Stiler"

#: ../glade/property.c:5116
msgid "Rename"
msgstr "Omdøp"

#: ../glade/property.c:5144
msgid "Cancel"
msgstr "Avbryt"

#: ../glade/property.c:5264
msgid "New Style:"
msgstr "Ny stil:"

#: ../glade/property.c:5278 ../glade/property.c:5399
msgid "Invalid style name"
msgstr "Ugyldig stilnavn"

#: ../glade/property.c:5286 ../glade/property.c:5409
msgid "That style name is already in use"
msgstr "Dette stilnavnet er allerede i bruk"

#: ../glade/property.c:5384
msgid "Rename Style To:"
msgstr "Omdøp stilen til:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"Kunne ikke døpe om filen:\n"
"  %s\n"
"til:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"Kunne ikke opprette fil:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "Feil under skriving av XML-fil\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*n * Fil med oversettbare strenger generert av Glade.\n"
" * Legg till denne filen i ditt prosjekts POTFILES.in.\n"
" * IKKE kompiler den som en del av applikasjonen din.\n"
" */\n"
"\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"Ugyldig filnavn for grensesnittfil: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"Ugyldig filnavn for grensesnitt header: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"Ugyldig filnavn for kildefil for tilbakekall: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"Ugyldig filnavn for headerfil for tilbakekall: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"Ugyldig filnavn for støttekilde: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"Ugyldig filnavn for støtteheader: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"Kunne ikke legge til i fil:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"Feil under skriving til fil:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr "Filnavnet må settes i dialogen alternativer for prosjekt."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"Filnavnet må være et enkelt relativt filnavn.\n"
"Bruk dialogen alternativer for prosjekt for å sette det."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Widget-tre"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "Widget ikke funnet i boksen"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "Widget ikke funnet i tabellen"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "Widget ikke funnet i fast kontainer"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "Widget ikke funnet i pakker"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"Kunne ikke aksessere filen:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"Kunne ikke åpne fil:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"Feil under lesing fra fil:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"Kunne ikke opprette katalogen:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"Kunne ikke aksessere katalogen:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"Ugyldig katalog:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Prosjekter"

#: ../glade/utils.c:1628
msgid "project"
msgstr "prosjekt"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"Kunne ikke åpne katalogen:\n"
"  %s\n"
