AdPlug - A free AdLib sound player library
Copyright (C) 1999 - 2017 <PERSON>, et al.

Website: http://adplug.github.io/

Description:
------------
AdPlug is a free, multi-platform, hardware independent AdLib sound
player library, mainly written in C++. Ad<PERSON>lug plays sound data,
originally created for the AdLib (OPL2/3) audio board, on top of an
OPL2/3 emulator or by using the real hardware. No OPL2/3 chips are
required for playback.

Supported platforms:
--------------------
AdPlug currently is tested to compile and run well on the following
platforms and compilers:

Platform                Operating System        Compiler
--------                ----------------        --------
IA32 - x86              Windows XP              MinGW 3.4
                        MS-DOS 6.22             DJGPP 4.0
                        Linux 2.6               GCC 4.1
x86_64                  Linux 2.6               GCC 4.4.2

This list only incorporates platforms on which this release of AdPlug has
been tested successfully. This and/or earlier versions of AdPlug may and did
run on a variety of other platforms as well. It just means they haven't been
explicitly tested for this release.

The library builds as dynamic and/or static link library, depending on
the facilities provided by the host <PERSON>. These are autodetected.

Prerequisites:
--------------
Ad<PERSON>lug depends upon the following libraries:

Library:	Version:
--------	--------
libbinio	>= 1.4

libbinio can be obtained from http://adplug.github.io/libbinio/.

Installation:
-------------
Please refer to the INSTALL file for installation instructions.

Linux/OSX git:
--------------
If you checked out from git, please first run:

autoreconf --install

to generate the build cruft and get the configure script. Oh, and did i
mention you need recent versions of autoconf, automake and libtool?

Windows git:
------------
If you checked out from git, you will need to copy src\version.h.in to
src\version.h and replace @VERSION@ with a proper version number.

A Visual Studio 2015 solution you can open and build is located in
contrib\vs2015\vs2015.sln.

Debugging AdPlug:
-----------------
If you feel you have to debug AdPlug, you can compile it with debug logging
support. This is disabled by default. To enable it, you have to define the
DEBUG preprocessor macro. This is done by passing the '--enable-debug'
commandline option to the 'configure' script to enable debugging.

AdPlug logs to stderr by default. The output can be redirected anytime to a
user specified logfile. This is done by using the 'CAdPlug::debug_output'
method of the 'CAdPlug' class.

If you have changed a format and the tests now fail (run "make check"), once
you have confirmed the output is correct, there will be a *.test file in the
test/ folder.  Rename this from .test to .ref to have it used as the new
reference data.

The emulator license issue:
---------------------------
AdPlug now uses WoodyOPL, the LGPL-licenced OPL emulator from the DOSBox
team.

Previously, AdPlug used MAME's fmopl OPL2 emulator. While this distribution
includes an old LGPL'd version of the emulator, which was developed by
Tatsuyuki Satoh alone, who relicensed this version under the LGPL, there is
a patch available in the `mame` branch of the GitHub repository
that replaces the old version by the latest version from the MAME source
distribution. The new version features improved emulation quality, but is
licensed under the MAME license, which restricts commercial redistribution.

The same goes for the OPL3 emulator, which requires the new version of
the OPL2 emulator to build.

Later versions of MAME are released under the GPL, so this patch could be
updated to use that instead, however this is probably not necessary given
the high quality of the WoodyOPL emulator.

	- Simon Peter <<EMAIL>>
