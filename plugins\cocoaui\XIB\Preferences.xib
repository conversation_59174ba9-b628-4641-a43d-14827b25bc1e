<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="23727" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="23727"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="PreferencesWindowController">
            <connections>
                <outlet property="appearancePaneContainerView" destination="wuF-q5-DhL" id="TOh-cv-QSo"/>
                <outlet property="dspViewController" destination="lFU-Xs-9Xm" id="uoG-jh-Sr8"/>
                <outlet property="guiViewController" destination="jtP-FH-nkX" id="GQ4-wY-XrK"/>
                <outlet property="keyboardShortcutsPreferencesViewController" destination="XoS-qb-O3F" id="M6F-1o-smB"/>
                <outlet property="mediaLibraryPreferencesViewController" destination="dfZ-70-v7J" id="ylm-9k-cnY"/>
                <outlet property="networkViewController" destination="4Qv-4Y-koA" id="Sws-hE-eaY"/>
                <outlet property="playbackViewController" destination="YqN-ZP-2eU" id="DKY-gJ-i4q"/>
                <outlet property="pluginsViewController" destination="AAs-to-EGG" id="gfD-Ut-jGg"/>
                <outlet property="soundViewController" destination="rug-kD-oVn" id="cEh-Za-gUT"/>
                <outlet property="toolbar" destination="b4X-86-Cex" id="1AX-xy-fdb"/>
                <outlet property="window" destination="KeT-Jx-1Jt" id="tQF-UN-TAd"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <window identifier="Preferences" title="Preferences" allowsToolTipsWhenApplicationIsInactive="NO" releasedWhenClosed="NO" animationBehavior="default" toolbarStyle="preference" id="KeT-Jx-1Jt" userLabel="Preferences Window">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="410" y="584" width="520" height="172"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2048" height="1127"/>
            <view key="contentView" id="rQA-9t-ob7">
                <rect key="frame" x="0.0" y="0.0" width="520" height="172"/>
                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            </view>
            <toolbar key="toolbar" implicitIdentifier="AE2F03B5-C725-441F-9238-4A4685409E1F" autosavesConfiguration="NO" allowsUserCustomization="NO" showsBaselineSeparator="NO" displayMode="iconAndLabel" sizeMode="regular" id="b4X-86-Cex">
                <allowedToolbarItems>
                    <toolbarItem implicitItemIdentifier="75E57758-BBE5-4279-935F-752C4EA66F19" explicitItemIdentifier="DSP" label="DSP" paletteLabel="DSP" tag="-1" image="iconDSPTemplate" selectable="YES" id="7pd-zb-0Gw">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                        <connections>
                            <action selector="dspAction:" target="-2" id="qWC-dG-UIf"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="46FAA4BA-56B9-485A-9E34-49E7C3E6B82F" explicitItemIdentifier="GUI" label="GUI/Misc" paletteLabel="GUI/Misc" tag="-1" image="iconGUISettingsTemplate" selectable="YES" id="wT9-7a-ELU">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                        <connections>
                            <action selector="guiAction:" target="-2" id="E54-wU-8Jr"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="96A9B13A-F7A0-46B5-808C-C81A24A1A94E" explicitItemIdentifier="Appearance" label="Appearance" paletteLabel="Appearance" tag="-1" image="iconAppearanceTemplate" selectable="YES" id="uEs-n6-F9h">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="2116A5C0-2F18-48E0-AE6B-E9C22EB0AFC5" explicitItemIdentifier="Network" label="Network" paletteLabel="Network" tag="-1" image="iconNetworkTemplate" selectable="YES" id="m7u-Kv-WdW">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                        <connections>
                            <action selector="networkAction:" target="-2" id="cCh-Ss-9OS"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="B7A7111D-0590-491A-837D-81E36D82982F" explicitItemIdentifier="Plugins" label="Plugins" paletteLabel="Plugins" tag="-1" image="iconPluginsTemplate" selectable="YES" id="mxa-vX-uKR">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                        <connections>
                            <action selector="pluginsAction:" target="-2" id="1B0-xl-6qU"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="3AB77CD1-2F67-424D-9A3B-71851BD2803B" explicitItemIdentifier="Playback" label="Playback" paletteLabel="Playback" tag="-1" image="iconPlaybackTemplate" selectable="YES" id="SxW-Uj-tLS">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                        <connections>
                            <action selector="playbackAction:" target="-2" id="0Mk-dD-GyA"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="A86E1DAF-08F0-4BE4-AFBC-9C662848DBA1" explicitItemIdentifier="Sound" label="Sound" paletteLabel="Sound" tag="-1" image="iconSoundTemplate" selectable="YES" id="Ae6-BH-0s5">
                        <size key="minSize" width="17" height="22"/>
                        <size key="maxSize" width="17" height="22"/>
                        <connections>
                            <action selector="soundAction:" target="-2" id="qlL-bv-tG4"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="11BE9398-88E5-4357-AE50-5E8B174DEBCB" explicitItemIdentifier="Medialib" label="Media Library" paletteLabel="Media Library" tag="-1" image="iconLibraryTemplate" selectable="YES" id="9px-Af-jjm">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                        <connections>
                            <action selector="medialibAction:" target="-2" id="2ex-Cv-Gbb"/>
                        </connections>
                    </toolbarItem>
                    <toolbarItem implicitItemIdentifier="0AA84E59-D1BD-43E5-A275-C4069DB30E18" explicitItemIdentifier="Hotkeys" label="Hotkeys" paletteLabel="Hotkeys" tag="-1" image="btnKeyboardTemplate" selectable="YES" id="Y78-3q-73e">
                        <size key="minSize" width="22" height="22"/>
                        <size key="maxSize" width="22" height="22"/>
                        <connections>
                            <action selector="hotkeysAction:" target="-2" id="Me0-XQ-ahA"/>
                        </connections>
                    </toolbarItem>
                </allowedToolbarItems>
                <defaultToolbarItems>
                    <toolbarItem reference="Ae6-BH-0s5"/>
                    <toolbarItem reference="SxW-Uj-tLS"/>
                    <toolbarItem reference="7pd-zb-0Gw"/>
                    <toolbarItem reference="wT9-7a-ELU"/>
                    <toolbarItem reference="9px-Af-jjm"/>
                    <toolbarItem reference="m7u-Kv-WdW"/>
                    <toolbarItem reference="Y78-3q-73e"/>
                    <toolbarItem reference="mxa-vX-uKR"/>
                </defaultToolbarItems>
            </toolbar>
            <point key="canvasLocation" x="303.5" y="-67"/>
        </window>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView identifier="GUI_Misc" id="MUz-8x-xuE" userLabel="Gui View">
            <rect key="frame" x="0.0" y="0.0" width="520" height="255"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tabView translatesAutoresizingMaskIntoConstraints="NO" id="Kew-j7-aa9">
                    <rect key="frame" x="13" y="10" width="494" height="231"/>
                    <font key="font" metaFont="system"/>
                    <tabViewItems>
                        <tabViewItem label="Player" identifier="1" id="0mG-vg-fx3">
                            <view key="view" id="oqj-Ho-G5W">
                                <rect key="frame" x="10" y="33" width="474" height="185"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="zMC-Ss-j3s">
                                        <rect key="frame" x="18" y="164" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Recode Japanese SHIFT-JIS" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="j3e-m3-cZP">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.enableShiftJISDetection" id="mql-sx-XxI"/>
                                        </connections>
                                    </button>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="nXp-DV-5XR">
                                        <rect key="frame" x="18" y="144" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Recode Cyrillic CP1251" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="FIl-Oa-w27">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.enableCP1251Detection" id="Nj2-0P-QQZ"/>
                                        </connections>
                                    </button>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="0RD-fs-B31">
                                        <rect key="frame" x="18" y="124" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Recode Chinese CP936" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="xgh-X2-f08">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.enableCP936Detection" id="qIm-PO-fgA"/>
                                        </connections>
                                    </button>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Eg9-UN-hyh">
                                        <rect key="frame" x="18" y="99" width="169" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Titlebar text while playing:" id="z67-mO-2Ni">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="3Ja-ME-qNM">
                                        <rect key="frame" x="18" y="75" width="169" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Titlebar text while stopped:" id="AcN-7j-3DA">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="pSK-4V-QiQ">
                                        <rect key="frame" x="193" y="96" width="261" height="21"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="6KN-Vg-1d6">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.titlebarPlaying" id="fCu-P1-8Fw"/>
                                        </connections>
                                    </textField>
                                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="usM-X5-aNf">
                                        <rect key="frame" x="193" y="72" width="261" height="21"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="PeB-76-5XW">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.titlebarStopped" id="FZM-rT-URR"/>
                                        </connections>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="usM-X5-aNf" secondAttribute="bottom" constant="20" id="7fz-wl-9F6"/>
                                    <constraint firstItem="nXp-DV-5XR" firstAttribute="firstBaseline" secondItem="zMC-Ss-j3s" secondAttribute="baseline" constant="20" id="Fku-BC-yem"/>
                                    <constraint firstItem="3Ja-ME-qNM" firstAttribute="firstBaseline" secondItem="usM-X5-aNf" secondAttribute="firstBaseline" id="Lu8-tG-ZZr"/>
                                    <constraint firstItem="zMC-Ss-j3s" firstAttribute="leading" secondItem="oqj-Ho-G5W" secondAttribute="leading" constant="20" id="NN7-Lh-eQ1"/>
                                    <constraint firstItem="usM-X5-aNf" firstAttribute="trailing" secondItem="pSK-4V-QiQ" secondAttribute="trailing" id="Obp-H9-vxa"/>
                                    <constraint firstItem="0RD-fs-B31" firstAttribute="trailing" secondItem="nXp-DV-5XR" secondAttribute="trailing" id="QL4-44-HBD"/>
                                    <constraint firstItem="pSK-4V-QiQ" firstAttribute="leading" secondItem="Eg9-UN-hyh" secondAttribute="trailing" constant="8" id="Sub-gV-FUH"/>
                                    <constraint firstItem="pSK-4V-QiQ" firstAttribute="trailing" secondItem="0RD-fs-B31" secondAttribute="trailing" id="TaW-s8-BZc"/>
                                    <constraint firstItem="0RD-fs-B31" firstAttribute="leading" secondItem="nXp-DV-5XR" secondAttribute="leading" id="X6c-Qs-plh"/>
                                    <constraint firstItem="0RD-fs-B31" firstAttribute="firstBaseline" secondItem="nXp-DV-5XR" secondAttribute="baseline" constant="20" id="ZRL-4P-exD"/>
                                    <constraint firstItem="usM-X5-aNf" firstAttribute="leading" secondItem="pSK-4V-QiQ" secondAttribute="leading" id="ZwI-qK-qeW"/>
                                    <constraint firstItem="3Ja-ME-qNM" firstAttribute="firstBaseline" secondItem="Eg9-UN-hyh" secondAttribute="baseline" constant="24" id="adp-qb-oxP"/>
                                    <constraint firstItem="Eg9-UN-hyh" firstAttribute="firstBaseline" secondItem="pSK-4V-QiQ" secondAttribute="firstBaseline" id="bP4-Vj-DSj"/>
                                    <constraint firstItem="nXp-DV-5XR" firstAttribute="leading" secondItem="zMC-Ss-j3s" secondAttribute="leading" id="cMl-rh-zw2"/>
                                    <constraint firstItem="zMC-Ss-j3s" firstAttribute="top" secondItem="oqj-Ho-G5W" secondAttribute="top" constant="4" id="dGz-U1-ioJ"/>
                                    <constraint firstItem="nXp-DV-5XR" firstAttribute="trailing" secondItem="zMC-Ss-j3s" secondAttribute="trailing" id="exb-Gr-xmE"/>
                                    <constraint firstItem="pSK-4V-QiQ" firstAttribute="top" secondItem="0RD-fs-B31" secondAttribute="bottom" constant="8" id="o5f-vS-zL4"/>
                                    <constraint firstAttribute="trailing" secondItem="zMC-Ss-j3s" secondAttribute="trailing" constant="20" id="spf-oD-oPq"/>
                                    <constraint firstItem="3Ja-ME-qNM" firstAttribute="trailing" secondItem="Eg9-UN-hyh" secondAttribute="trailing" id="v3z-Iq-pUV"/>
                                    <constraint firstItem="3Ja-ME-qNM" firstAttribute="leading" secondItem="Eg9-UN-hyh" secondAttribute="leading" id="wdC-4N-joI"/>
                                    <constraint firstItem="Eg9-UN-hyh" firstAttribute="leading" secondItem="0RD-fs-B31" secondAttribute="leading" id="zU6-rL-21j"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="Playlist" identifier="2" id="Bas-Mn-ob1">
                            <view key="view" id="tbJ-9l-ZFL">
                                <rect key="frame" x="10" y="33" width="474" height="185"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="mQR-r2-hgB">
                                        <rect key="frame" x="18" y="164" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Close playlists with middle mouse button" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="vbQ-dU-f2J">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.mmbDeletePlaylist" id="ekb-cu-8mz"/>
                                        </connections>
                                    </button>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="5ti-KG-6jS">
                                        <rect key="frame" x="18" y="142" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Hide &quot;Delete from Disk&quot; context menu item" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="3fF-la-mG4">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.hideRemoveFromDisk" id="8ED-V6-PlK"/>
                                        </connections>
                                    </button>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="va0-Vc-fXe">
                                        <rect key="frame" x="18" y="120" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Automatically name playlists when adding a single folder" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="Ejz-uG-nxa">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="self.namePlaylistFromFolder" id="uJE-i4-0xv"/>
                                        </connections>
                                    </button>
                                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="p2Y-PO-L5O">
                                        <rect key="frame" x="18" y="63" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Use Recycle Bin" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="cjY-a5-Gbj">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="deleteFromDiskUseBin" id="wrP-J4-LgU"/>
                                        </connections>
                                    </button>
                                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="hyY-IL-s4f">
                                        <rect key="frame" x="18" y="41" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Display confirmation dialog" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="klO-kO-Na9">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="deleteFromDiskEnableConfirmationDialog" id="3Z2-ga-0sb"/>
                                        </connections>
                                    </button>
                                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="PBl-HV-gCa">
                                        <rect key="frame" x="18" y="19" width="436" height="18"/>
                                        <buttonCell key="cell" type="check" title="Skip the currently playing track if it's deleted" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="l1L-sL-vfs">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="jtP-FH-nkX" name="value" keyPath="deleteFromDiskSkipDeletedTracks" id="q5x-QZ-0et"/>
                                        </connections>
                                    </button>
                                    <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="gbe-10-ftY">
                                        <rect key="frame" x="20" y="110" width="434" height="5"/>
                                    </box>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="FW6-jn-RQA">
                                        <rect key="frame" x="18" y="88" width="112" height="16"/>
                                        <textFieldCell key="cell" lineBreakMode="clipping" title="Delete from Disk" id="mo3-CR-le4">
                                            <font key="font" metaFont="systemBold"/>
                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="p2Y-PO-L5O" firstAttribute="trailing" secondItem="va0-Vc-fXe" secondAttribute="trailing" id="0Gs-Is-n36"/>
                                    <constraint firstAttribute="bottom" secondItem="PBl-HV-gCa" secondAttribute="bottom" constant="20" symbolic="YES" id="1tA-Ct-c3s"/>
                                    <constraint firstItem="FW6-jn-RQA" firstAttribute="top" secondItem="gbe-10-ftY" secondAttribute="bottom" constant="8" symbolic="YES" id="4kA-hn-z8a"/>
                                    <constraint firstItem="mQR-r2-hgB" firstAttribute="leading" secondItem="tbJ-9l-ZFL" secondAttribute="leading" constant="20" id="A9D-Tu-IOA"/>
                                    <constraint firstItem="p2Y-PO-L5O" firstAttribute="top" secondItem="FW6-jn-RQA" secondAttribute="bottom" constant="8" symbolic="YES" id="Aj8-gS-5TR"/>
                                    <constraint firstItem="5ti-KG-6jS" firstAttribute="top" secondItem="mQR-r2-hgB" secondAttribute="bottom" constant="6" symbolic="YES" id="Amk-IG-zT7"/>
                                    <constraint firstItem="p2Y-PO-L5O" firstAttribute="leading" secondItem="va0-Vc-fXe" secondAttribute="leading" id="BeL-av-5kB"/>
                                    <constraint firstAttribute="trailing" secondItem="mQR-r2-hgB" secondAttribute="trailing" constant="20" id="C8H-d8-WTv"/>
                                    <constraint firstItem="hyY-IL-s4f" firstAttribute="trailing" secondItem="p2Y-PO-L5O" secondAttribute="trailing" id="FSR-sW-1Nh"/>
                                    <constraint firstItem="FW6-jn-RQA" firstAttribute="leading" secondItem="gbe-10-ftY" secondAttribute="leading" id="JdW-pI-pvQ"/>
                                    <constraint firstItem="hyY-IL-s4f" firstAttribute="leading" secondItem="p2Y-PO-L5O" secondAttribute="leading" id="N3b-mO-ZXU"/>
                                    <constraint firstItem="gbe-10-ftY" firstAttribute="leading" secondItem="va0-Vc-fXe" secondAttribute="leading" id="Nb0-K9-N80"/>
                                    <constraint firstItem="va0-Vc-fXe" firstAttribute="trailing" secondItem="5ti-KG-6jS" secondAttribute="trailing" id="Tzs-Z1-GIn"/>
                                    <constraint firstItem="gbe-10-ftY" firstAttribute="trailing" secondItem="va0-Vc-fXe" secondAttribute="trailing" id="UH8-4p-KAK"/>
                                    <constraint firstItem="5ti-KG-6jS" firstAttribute="leading" secondItem="mQR-r2-hgB" secondAttribute="leading" id="ZI7-80-xEx"/>
                                    <constraint firstItem="va0-Vc-fXe" firstAttribute="leading" secondItem="5ti-KG-6jS" secondAttribute="leading" id="ZWl-Ja-yku"/>
                                    <constraint firstItem="5ti-KG-6jS" firstAttribute="trailing" secondItem="mQR-r2-hgB" secondAttribute="trailing" id="amj-1W-bwZ"/>
                                    <constraint firstItem="mQR-r2-hgB" firstAttribute="top" secondItem="tbJ-9l-ZFL" secondAttribute="top" constant="4" id="fkV-kw-IOy"/>
                                    <constraint firstItem="hyY-IL-s4f" firstAttribute="top" secondItem="p2Y-PO-L5O" secondAttribute="bottom" constant="6" symbolic="YES" id="gLm-uL-r8D"/>
                                    <constraint firstItem="PBl-HV-gCa" firstAttribute="trailing" secondItem="hyY-IL-s4f" secondAttribute="trailing" id="gtU-k8-6cE"/>
                                    <constraint firstItem="PBl-HV-gCa" firstAttribute="top" secondItem="hyY-IL-s4f" secondAttribute="bottom" constant="6" symbolic="YES" id="pKE-b6-tDg"/>
                                    <constraint firstItem="gbe-10-ftY" firstAttribute="top" secondItem="va0-Vc-fXe" secondAttribute="bottom" constant="8" symbolic="YES" id="tvk-eh-kie"/>
                                    <constraint firstItem="va0-Vc-fXe" firstAttribute="top" secondItem="5ti-KG-6jS" secondAttribute="bottom" constant="6" symbolic="YES" id="xwf-Hi-szR"/>
                                    <constraint firstItem="PBl-HV-gCa" firstAttribute="leading" secondItem="hyY-IL-s4f" secondAttribute="leading" id="ybH-jS-odD"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="Appearance" identifier="" id="o97-3F-d9E">
                            <view key="view" id="wuF-q5-DhL">
                                <rect key="frame" x="10" y="33" width="474" height="185"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            </view>
                        </tabViewItem>
                    </tabViewItems>
                </tabView>
            </subviews>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="Kew-j7-aa9" secondAttribute="bottom" constant="20" id="6j6-N2-6iR"/>
                <constraint firstItem="Kew-j7-aa9" firstAttribute="top" secondItem="MUz-8x-xuE" secondAttribute="top" constant="20" id="B3f-ar-Uz7"/>
                <constraint firstAttribute="trailing" secondItem="Kew-j7-aa9" secondAttribute="trailing" constant="20" id="DLF-2x-zOT"/>
                <constraint firstItem="Kew-j7-aa9" firstAttribute="leading" secondItem="MUz-8x-xuE" secondAttribute="leading" constant="20" id="Gez-Eo-mY3"/>
            </constraints>
            <point key="canvasLocation" x="145" y="862"/>
        </customView>
        <customView identifier="Playback" id="Zmc-ua-enY" userLabel="Playback View">
            <rect key="frame" x="0.0" y="0.0" width="520" height="352"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ySk-s8-tM7">
                    <rect key="frame" x="183" y="167" width="317" height="21"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="YV9-Zx-yEt">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.cliSpecificPlaylist" id="lLR-Iy-Ucz"/>
                    </connections>
                </textField>
                <button verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="elV-fL-38G">
                    <rect key="frame" x="18" y="142" width="482" height="18"/>
                    <buttonCell key="cell" type="check" title="Resume last session on startup" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="uyW-tY-gTy">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.resumeLastSession" id="QX5-V8-Aeh"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="Rqu-qW-OA7">
                    <rect key="frame" x="18" y="169" width="157" height="18"/>
                    <buttonCell key="cell" type="check" title="Default playlist name:" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="cZj-Ac-CB1">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.cliAddToSpecificPlaylist" id="5ux-xH-mT9"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="zCE-Xa-7cL">
                    <rect key="frame" x="18" y="115" width="482" height="18"/>
                    <buttonCell key="cell" type="check" title="Don't traverse archives when adding folders" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="3mB-gl-NjR">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.ignoreArchives" id="nrL-Ot-eKE"/>
                    </connections>
                </button>
                <button verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Ypp-Lr-7tH">
                    <rect key="frame" x="18" y="61" width="482" height="18"/>
                    <buttonCell key="cell" type="check" title="Disable the &quot;Stop after current album&quot; option after triggering" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="Pfx-qP-Nzg">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.stopAfterCurrentAlbumReset" id="eab-qC-eAm"/>
                    </connections>
                </button>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="jCD-pm-VFD">
                    <rect key="frame" x="34" y="291" width="89" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Source mode:" id="gnG-qq-c4U">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="OHh-mq-dNT">
                    <rect key="frame" x="126" y="284" width="378" height="25"/>
                    <popUpButtonCell key="cell" type="push" title="By Playback Order" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="TkV-9B-QR1" id="E92-Zt-t0u">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message"/>
                        <menu key="menu" id="SAg-rN-x4O">
                            <items>
                                <menuItem title="By Playback Order" state="on" id="TkV-9B-QR1">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                                <menuItem title="Track" id="htq-z1-cmL"/>
                                <menuItem title="Album" id="nRc-Rp-bMy"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="selectedIndex" keyPath="self.rgSourceMode" id="xkg-Ac-J0c"/>
                    </connections>
                </popUpButton>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="WcI-yT-RjC">
                    <rect key="frame" x="34" y="264" width="75" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Processing:" id="eON-pt-Eg0">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="78i-nx-GgZ">
                    <rect key="frame" x="112" y="257" width="392" height="25"/>
                    <popUpButtonCell key="cell" type="push" title="None" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="VPT-9h-S6N" id="kHx-5R-lmz">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message"/>
                        <menu key="menu" id="X1h-2D-y3m">
                            <items>
                                <menuItem title="None" state="on" id="VPT-9h-S6N"/>
                                <menuItem title="Apply Gain" id="q04-jl-zF0"/>
                                <menuItem title="Apply Gain And Prevent Clipping According To Peak" id="pf4-jz-JG9"/>
                                <menuItem title="Only Prevent Clipping" id="WL4-Ar-4Cx">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="selectedIndex" keyPath="self.rgProcessingIdx" id="qsz-oy-zQX"/>
                    </connections>
                </popUpButton>
                <slider verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="arn-tx-1Aq">
                    <rect key="frame" x="189" y="227" width="282" height="28"/>
                    <sliderCell key="cell" continuous="YES" state="on" alignment="left" minValue="-12" maxValue="12" tickMarkPosition="below" numberOfTickMarks="25" sliderType="linear" id="Evm-9c-G7T"/>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.rgPreampWithRg" id="FKU-Do-7dT"/>
                    </connections>
                </slider>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="50R-70-80x">
                    <rect key="frame" x="469" y="236" width="33" height="14"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="+0dB" id="JHv-qd-TUt">
                        <font key="font" metaFont="label" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.rgPreampWithRgLabel" id="gab-Zm-Y3c"/>
                    </connections>
                </textField>
                <slider verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="zKw-Aq-JuH">
                    <rect key="frame" x="189" y="199" width="282" height="28"/>
                    <sliderCell key="cell" continuous="YES" state="on" alignment="left" minValue="-12" maxValue="12" tickMarkPosition="below" numberOfTickMarks="25" sliderType="linear" id="fjS-kS-bqN"/>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.rgPreampWithoutRg" id="eyP-P2-uhb"/>
                    </connections>
                </slider>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="UVS-UD-Ivp">
                    <rect key="frame" x="34" y="236" width="151" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Preamp with RG info:" id="ht1-sb-ufq">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="pUH-bn-6Sk">
                    <rect key="frame" x="34" y="208" width="151" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Preamp without RG info:" id="aGB-Jo-lpu">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="dBd-wH-v4A">
                    <rect key="frame" x="469" y="208" width="33" height="14"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="+0dB" id="cCa-sk-iJw">
                        <font key="font" metaFont="label" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.rgPreampWithoutRgLabel" id="k7I-fG-RIT"/>
                    </connections>
                </textField>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="2WW-3v-yct">
                    <rect key="frame" x="18" y="316" width="77" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="ReplayGain" id="iy0-pC-6J0">
                        <font key="font" metaFont="systemBold"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="o57-76-gIf">
                    <rect key="frame" x="8" y="194" width="504" height="5"/>
                </box>
                <button verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="vaP-mG-6h4">
                    <rect key="frame" x="18" y="88" width="482" height="18"/>
                    <buttonCell key="cell" type="check" title="Disable the &quot;Stop after current track&quot; option after triggering" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="MU5-Ea-QsQ">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="YqN-ZP-2eU" name="value" keyPath="self.stopAfterCurrentReset" id="Ck3-5A-0zm"/>
                    </connections>
                </button>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="yPy-ce-QKl">
                    <rect key="frame" x="18" y="23" width="153" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="Visualization buffer size:" id="V3k-Ug-wSf">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <slider verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="gEg-C3-cea">
                    <rect key="frame" x="175" y="14" width="249" height="28"/>
                    <sliderCell key="cell" continuous="YES" state="on" alignment="left" minValue="100" maxValue="1000" doubleValue="100" tickMarkPosition="above" numberOfTickMarks="10" sliderType="linear" id="lkE-UT-m1p"/>
                    <connections>
                        <action selector="visBufferSliderAction:" target="YqN-ZP-2eU" id="9hA-E8-Uly"/>
                    </connections>
                </slider>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="9H9-w2-zUI">
                    <rect key="frame" x="428" y="23" width="74" height="16"/>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="70" id="63h-2b-Ki4"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" placeholderString="100ms" id="z3C-ge-R68">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="elh-dZ-TAr">
                    <rect key="frame" x="8" y="46" width="504" height="5"/>
                </box>
            </subviews>
            <constraints>
                <constraint firstItem="2WW-3v-yct" firstAttribute="top" secondItem="Zmc-ua-enY" secondAttribute="top" constant="20" id="08r-pT-hKm"/>
                <constraint firstItem="elh-dZ-TAr" firstAttribute="trailing" secondItem="o57-76-gIf" secondAttribute="trailing" id="0ov-Mb-FTo"/>
                <constraint firstItem="Rqu-qW-OA7" firstAttribute="firstBaseline" secondItem="ySk-s8-tM7" secondAttribute="firstBaseline" id="3gD-Ea-QXD"/>
                <constraint firstItem="Ypp-Lr-7tH" firstAttribute="trailing" secondItem="vaP-mG-6h4" secondAttribute="trailing" id="4rJ-8A-Rhd"/>
                <constraint firstItem="zKw-Aq-JuH" firstAttribute="firstBaseline" secondItem="dBd-wH-v4A" secondAttribute="firstBaseline" id="7te-eC-FdA"/>
                <constraint firstItem="78i-nx-GgZ" firstAttribute="trailing" secondItem="OHh-mq-dNT" secondAttribute="trailing" id="8ey-VL-Vas"/>
                <constraint firstItem="ySk-s8-tM7" firstAttribute="trailing" secondItem="OHh-mq-dNT" secondAttribute="trailing" id="8u0-7K-CKf"/>
                <constraint firstItem="WcI-yT-RjC" firstAttribute="leading" secondItem="jCD-pm-VFD" secondAttribute="leading" id="94v-vg-trd"/>
                <constraint firstItem="UVS-UD-Ivp" firstAttribute="trailing" secondItem="pUH-bn-6Sk" secondAttribute="trailing" id="9XT-Z3-sU1"/>
                <constraint firstItem="OHh-mq-dNT" firstAttribute="leading" secondItem="jCD-pm-VFD" secondAttribute="trailing" constant="8" id="BZK-Rv-MWK"/>
                <constraint firstItem="UVS-UD-Ivp" firstAttribute="firstBaseline" secondItem="arn-tx-1Aq" secondAttribute="firstBaseline" id="Bhn-G0-yRv"/>
                <constraint firstItem="zKw-Aq-JuH" firstAttribute="leading" secondItem="pUH-bn-6Sk" secondAttribute="trailing" constant="8" id="Bjq-e7-U4l"/>
                <constraint firstItem="gEg-C3-cea" firstAttribute="leading" secondItem="yPy-ce-QKl" secondAttribute="trailing" constant="8" id="Bjv-Ke-rFd"/>
                <constraint firstItem="yPy-ce-QKl" firstAttribute="firstBaseline" secondItem="gEg-C3-cea" secondAttribute="firstBaseline" id="DUf-q2-xgz"/>
                <constraint firstItem="78i-nx-GgZ" firstAttribute="leading" secondItem="WcI-yT-RjC" secondAttribute="trailing" constant="8" id="F1O-Pq-UPL"/>
                <constraint firstItem="dBd-wH-v4A" firstAttribute="leading" secondItem="zKw-Aq-JuH" secondAttribute="trailing" constant="2" id="FQl-n8-p2J"/>
                <constraint firstItem="elV-fL-38G" firstAttribute="trailing" secondItem="ySk-s8-tM7" secondAttribute="trailing" id="FuH-Ro-crc"/>
                <constraint firstItem="50R-70-80x" firstAttribute="trailing" secondItem="OHh-mq-dNT" secondAttribute="trailing" id="GYB-m1-osB"/>
                <constraint firstItem="zCE-Xa-7cL" firstAttribute="top" secondItem="elV-fL-38G" secondAttribute="bottom" constant="11" id="HHG-x9-dya"/>
                <constraint firstItem="OHh-mq-dNT" firstAttribute="top" secondItem="2WW-3v-yct" secondAttribute="bottom" constant="8" id="HNI-Li-CX7"/>
                <constraint firstItem="zKw-Aq-JuH" firstAttribute="leading" secondItem="arn-tx-1Aq" secondAttribute="leading" id="Jep-LR-lrE"/>
                <constraint firstAttribute="trailing" secondItem="o57-76-gIf" secondAttribute="trailing" constant="8" id="LZD-2O-wsV"/>
                <constraint firstItem="2WW-3v-yct" firstAttribute="leading" secondItem="Zmc-ua-enY" secondAttribute="leading" constant="20" id="M04-2S-OWz"/>
                <constraint firstItem="elV-fL-38G" firstAttribute="leading" secondItem="2WW-3v-yct" secondAttribute="leading" id="McV-B1-AjR"/>
                <constraint firstItem="elh-dZ-TAr" firstAttribute="top" relation="greaterThanOrEqual" secondItem="Ypp-Lr-7tH" secondAttribute="bottom" constant="8" id="T2Y-dg-eWX"/>
                <constraint firstItem="yPy-ce-QKl" firstAttribute="leading" secondItem="Ypp-Lr-7tH" secondAttribute="leading" id="Uap-My-pRN"/>
                <constraint firstItem="ySk-s8-tM7" firstAttribute="baseline" secondItem="Rqu-qW-OA7" secondAttribute="firstBaseline" id="VEw-uA-zoZ"/>
                <constraint firstItem="gEg-C3-cea" firstAttribute="top" secondItem="elh-dZ-TAr" secondAttribute="bottom" constant="8" id="VMN-xA-fIK"/>
                <constraint firstItem="jCD-pm-VFD" firstAttribute="leading" secondItem="2WW-3v-yct" secondAttribute="leading" constant="16" id="XLp-Wc-AcB"/>
                <constraint firstItem="o57-76-gIf" firstAttribute="top" secondItem="zKw-Aq-JuH" secondAttribute="bottom" constant="8" id="ZKY-tm-pjj"/>
                <constraint firstItem="9H9-w2-zUI" firstAttribute="leading" secondItem="gEg-C3-cea" secondAttribute="trailing" constant="8" id="ZRb-jR-Kms"/>
                <constraint firstItem="Rqu-qW-OA7" firstAttribute="leading" secondItem="2WW-3v-yct" secondAttribute="leading" id="aqb-I7-ooo"/>
                <constraint firstItem="Ypp-Lr-7tH" firstAttribute="top" secondItem="vaP-mG-6h4" secondAttribute="bottom" constant="11" id="blW-wF-Zx6"/>
                <constraint firstItem="zCE-Xa-7cL" firstAttribute="leading" secondItem="2WW-3v-yct" secondAttribute="leading" id="c2h-dG-MmO"/>
                <constraint firstItem="elh-dZ-TAr" firstAttribute="leading" secondItem="o57-76-gIf" secondAttribute="leading" id="cGq-fZ-CHn"/>
                <constraint firstItem="zCE-Xa-7cL" firstAttribute="trailing" secondItem="elV-fL-38G" secondAttribute="trailing" id="e0y-ax-wN2"/>
                <constraint firstItem="50R-70-80x" firstAttribute="leading" secondItem="arn-tx-1Aq" secondAttribute="trailing" constant="2" id="f9o-0i-0Ud"/>
                <constraint firstItem="vaP-mG-6h4" firstAttribute="top" secondItem="zCE-Xa-7cL" secondAttribute="bottom" constant="11" id="fFw-oh-zne"/>
                <constraint firstItem="pUH-bn-6Sk" firstAttribute="leading" secondItem="UVS-UD-Ivp" secondAttribute="leading" id="fIb-Bf-rS2"/>
                <constraint firstItem="arn-tx-1Aq" firstAttribute="top" secondItem="78i-nx-GgZ" secondAttribute="bottom" constant="8" id="ffg-CV-k0U"/>
                <constraint firstItem="Ypp-Lr-7tH" firstAttribute="leading" secondItem="2WW-3v-yct" secondAttribute="leading" id="gzp-ze-uWD"/>
                <constraint firstItem="zCE-Xa-7cL" firstAttribute="leading" secondItem="2WW-3v-yct" secondAttribute="leading" id="h4z-wx-O00"/>
                <constraint firstItem="78i-nx-GgZ" firstAttribute="top" secondItem="OHh-mq-dNT" secondAttribute="bottom" constant="7" id="hHb-92-tZa"/>
                <constraint firstItem="ySk-s8-tM7" firstAttribute="baseline" secondItem="Rqu-qW-OA7" secondAttribute="baseline" id="hPk-Id-tyt"/>
                <constraint firstItem="vaP-mG-6h4" firstAttribute="trailing" secondItem="zCE-Xa-7cL" secondAttribute="trailing" id="iXX-I6-eVh"/>
                <constraint firstItem="o57-76-gIf" firstAttribute="leading" secondItem="Zmc-ua-enY" secondAttribute="leading" constant="8" id="l2x-8I-eYC"/>
                <constraint firstItem="UVS-UD-Ivp" firstAttribute="leading" secondItem="WcI-yT-RjC" secondAttribute="leading" id="lP4-bo-VSj"/>
                <constraint firstItem="ySk-s8-tM7" firstAttribute="top" secondItem="o57-76-gIf" secondAttribute="bottom" constant="8" id="nY4-QT-QUH"/>
                <constraint firstItem="vaP-mG-6h4" firstAttribute="leading" secondItem="2WW-3v-yct" secondAttribute="leading" id="oj7-pg-2xN"/>
                <constraint firstItem="ySk-s8-tM7" firstAttribute="leading" secondItem="Rqu-qW-OA7" secondAttribute="trailing" constant="8" symbolic="YES" id="p9U-95-d02"/>
                <constraint firstAttribute="trailing" secondItem="OHh-mq-dNT" secondAttribute="trailing" constant="20" id="q3e-KC-kgM"/>
                <constraint firstItem="arn-tx-1Aq" firstAttribute="firstBaseline" secondItem="50R-70-80x" secondAttribute="firstBaseline" id="qTT-Xa-GhM"/>
                <constraint firstItem="OHh-mq-dNT" firstAttribute="firstBaseline" secondItem="jCD-pm-VFD" secondAttribute="baseline" id="rW2-6i-Rj6"/>
                <constraint firstItem="9H9-w2-zUI" firstAttribute="trailing" secondItem="Ypp-Lr-7tH" secondAttribute="trailing" id="sdA-0R-cHc"/>
                <constraint firstItem="elV-fL-38G" firstAttribute="top" secondItem="ySk-s8-tM7" secondAttribute="bottom" constant="8" id="spV-4L-z5P"/>
                <constraint firstItem="zKw-Aq-JuH" firstAttribute="top" secondItem="arn-tx-1Aq" secondAttribute="bottom" constant="8" id="u9y-kZ-b5G"/>
                <constraint firstItem="pUH-bn-6Sk" firstAttribute="firstBaseline" secondItem="zKw-Aq-JuH" secondAttribute="firstBaseline" id="vXP-Gx-oug"/>
                <constraint firstItem="WcI-yT-RjC" firstAttribute="firstBaseline" secondItem="78i-nx-GgZ" secondAttribute="firstBaseline" id="vgV-GT-Qp6"/>
                <constraint firstAttribute="bottom" secondItem="gEg-C3-cea" secondAttribute="bottom" constant="20" id="y8S-B3-7Vf"/>
                <constraint firstItem="gEg-C3-cea" firstAttribute="firstBaseline" secondItem="9H9-w2-zUI" secondAttribute="firstBaseline" id="yRb-yR-b9Z"/>
                <constraint firstItem="dBd-wH-v4A" firstAttribute="trailing" secondItem="OHh-mq-dNT" secondAttribute="trailing" id="yxW-Sj-qxv"/>
            </constraints>
            <point key="canvasLocation" x="303" y="248"/>
        </customView>
        <customView identifier="DSP" id="FgU-f9-bru" userLabel="Dsp View">
            <rect key="frame" x="0.0" y="0.0" width="520" height="222"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="zZe-aS-edz">
                    <rect key="frame" x="18" y="24" width="77" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="DSP Preset:" id="80n-mJ-LRw">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <customView translatesAutoresizingMaskIntoConstraints="NO" id="7EN-x3-aU8">
                    <rect key="frame" x="101" y="20" width="399" height="24"/>
                </customView>
                <customView translatesAutoresizingMaskIntoConstraints="NO" id="wB2-nl-rwo">
                    <rect key="frame" x="20" y="52" width="480" height="150"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="150" id="rA4-8s-pdL"/>
                    </constraints>
                </customView>
            </subviews>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="wB2-nl-rwo" secondAttribute="trailing" constant="20" id="233-mp-hyG"/>
                <constraint firstItem="7EN-x3-aU8" firstAttribute="top" secondItem="wB2-nl-rwo" secondAttribute="bottom" constant="8" id="8Zr-Cu-SA6"/>
                <constraint firstItem="7EN-x3-aU8" firstAttribute="leading" secondItem="zZe-aS-edz" secondAttribute="trailing" constant="8" id="DPs-6w-4aX"/>
                <constraint firstItem="wB2-nl-rwo" firstAttribute="top" secondItem="FgU-f9-bru" secondAttribute="top" constant="20" id="JBS-xI-3xO"/>
                <constraint firstItem="7EN-x3-aU8" firstAttribute="trailing" secondItem="wB2-nl-rwo" secondAttribute="trailing" id="dOt-5P-IeB"/>
                <constraint firstItem="wB2-nl-rwo" firstAttribute="leading" secondItem="FgU-f9-bru" secondAttribute="leading" constant="20" id="exl-ha-FUe"/>
                <constraint firstItem="zZe-aS-edz" firstAttribute="leading" secondItem="FgU-f9-bru" secondAttribute="leading" constant="20" id="kK6-Zg-dGb"/>
                <constraint firstAttribute="bottom" secondItem="7EN-x3-aU8" secondAttribute="bottom" constant="20" id="qSq-Ep-8Lu"/>
                <constraint firstItem="zZe-aS-edz" firstAttribute="centerY" secondItem="7EN-x3-aU8" secondAttribute="centerY" id="rrU-wg-biq"/>
            </constraints>
            <point key="canvasLocation" x="355" y="597"/>
        </customView>
        <customView identifier="Network" id="JAw-ar-I3a" userLabel="Network View">
            <rect key="frame" x="0.0" y="0.0" width="520" height="334"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tabView translatesAutoresizingMaskIntoConstraints="NO" id="oKC-A2-rMr">
                    <rect key="frame" x="13" y="10" width="494" height="310"/>
                    <font key="font" metaFont="system"/>
                    <tabViewItems>
                        <tabViewItem label="General" identifier="" id="e83-u9-txO">
                            <view key="view" id="sg6-kF-sPZ">
                                <rect key="frame" x="10" y="33" width="474" height="264"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Syy-0M-GXu">
                                        <rect key="frame" x="6" y="157" width="73" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Proxy type:" id="fn2-4B-frd">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="PWA-i7-bgb">
                                        <rect key="frame" x="6" y="185" width="113" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Proxy server port:" id="l3T-JE-lcR">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="kg8-9z-h8H">
                                        <rect key="frame" x="158" y="182" width="308" height="21"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="n9I-nG-sPd">
                                            <numberFormatter key="formatter" formatterBehavior="default10_4" localizesFormat="NO" usesGroupingSeparator="NO" formatWidth="-1" groupingSize="0" minimumIntegerDigits="1" maximumIntegerDigits="42" id="RnU-re-G7C">
                                                <real key="minimum" value="0.0"/>
                                                <real key="maximum" value="65535"/>
                                            </numberFormatter>
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="4Qv-4Y-koA" name="value" keyPath="self.networkProxyPort" id="ykr-f6-M1n"/>
                                        </connections>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="6kD-YR-t66">
                                        <rect key="frame" x="6" y="128" width="106" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Proxy username:" id="GLd-IT-6w5">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="YLc-o3-bbc">
                                        <rect key="frame" x="6" y="214" width="136" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Proxy server address:" id="U3o-0m-QVt">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <secureTextField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="df2-Ec-ZTs">
                                        <rect key="frame" x="158" y="96" width="308" height="21"/>
                                        <secureTextFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="sKl-07-c8I">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <allowedInputSourceLocales>
                                                <string>NSAllRomanInputSourcesLocaleIdentifier</string>
                                            </allowedInputSourceLocales>
                                        </secureTextFieldCell>
                                        <connections>
                                            <binding destination="4Qv-4Y-koA" name="value" keyPath="self.networkProxyPassword" id="rhM-gn-Lbp"/>
                                        </connections>
                                    </secureTextField>
                                    <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="vYg-2y-G9G">
                                        <rect key="frame" x="8" y="85" width="458" height="5"/>
                                    </box>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="IeZ-TL-qL4">
                                        <rect key="frame" x="6" y="99" width="104" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Proxy password:" id="TMd-HT-8no">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="tI7-xT-470">
                                        <rect key="frame" x="6" y="61" width="110" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="HTTP user agent:" id="9J9-Sf-c6S">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="HWj-Ba-h3x">
                                        <rect key="frame" x="158" y="211" width="308" height="21"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="mrT-53-F7y">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="4Qv-4Y-koA" name="value" keyPath="self.networkProxyAddress" id="OvK-de-q4t"/>
                                        </connections>
                                    </textField>
                                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Otk-ep-eDt">
                                        <rect key="frame" x="158" y="125" width="308" height="21"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="624-jB-EhB">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="4Qv-4Y-koA" name="value" keyPath="self.networkProxyUserName" id="szY-DQ-dJq"/>
                                        </connections>
                                    </textField>
                                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="xdl-oE-Ddr">
                                        <rect key="frame" x="158" y="58" width="308" height="21"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="51P-0P-dh5">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="4Qv-4Y-koA" name="value" keyPath="self.networkProxyUserAgent" id="3CM-Qp-jB9"/>
                                        </connections>
                                    </textField>
                                    <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="6ZQ-2R-J1F">
                                        <rect key="frame" x="155" y="150" width="315" height="25"/>
                                        <popUpButtonCell key="cell" type="push" title="HTTP" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="itC-J5-evW" id="4rP-C2-giL">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message"/>
                                            <menu key="menu" id="byS-Kp-Hsi">
                                                <items>
                                                    <menuItem title="HTTP" state="on" id="itC-J5-evW">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem title="HTTP_1_0" id="mJg-we-Hep">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem title="SOCKS4" id="0mJ-FN-pwp">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem title="SOCKS5" id="b5W-OK-efk">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem title="SOCKS4A" id="NDf-7O-7C4">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem title="SOCKS5_HOSTNAME" id="JjD-8G-NKS">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                        <connections>
                                            <binding destination="4Qv-4Y-koA" name="selectedIndex" keyPath="self.networkProxyType" id="ioA-Xd-3zH"/>
                                        </connections>
                                    </popUpButton>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="hob-ds-asX">
                                        <rect key="frame" x="156" y="239" width="310" height="18"/>
                                        <buttonCell key="cell" type="check" title="Enable proxy server" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="o6T-DG-Hxz">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="4Qv-4Y-koA" name="value" keyPath="self.enableNetworkProxy" id="5gy-WU-zJk"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="HWj-Ba-h3x" firstAttribute="trailing" secondItem="hob-ds-asX" secondAttribute="trailing" id="0If-AH-9wu"/>
                                    <constraint firstItem="df2-Ec-ZTs" firstAttribute="trailing" secondItem="Otk-ep-eDt" secondAttribute="trailing" id="1V7-DR-UiG"/>
                                    <constraint firstItem="kg8-9z-h8H" firstAttribute="top" secondItem="HWj-Ba-h3x" secondAttribute="bottom" constant="8" id="4NB-er-Jpl"/>
                                    <constraint firstItem="HWj-Ba-h3x" firstAttribute="leading" secondItem="hob-ds-asX" secondAttribute="leading" id="5kD-2e-Uso"/>
                                    <constraint firstItem="Syy-0M-GXu" firstAttribute="firstBaseline" secondItem="6ZQ-2R-J1F" secondAttribute="firstBaseline" id="7JJ-xk-YDh"/>
                                    <constraint firstItem="tI7-xT-470" firstAttribute="leading" secondItem="vYg-2y-G9G" secondAttribute="leading" id="83H-3k-PUH"/>
                                    <constraint firstItem="df2-Ec-ZTs" firstAttribute="leading" secondItem="IeZ-TL-qL4" secondAttribute="trailing" constant="50" id="8K7-Pv-m6b"/>
                                    <constraint firstItem="vYg-2y-G9G" firstAttribute="top" secondItem="df2-Ec-ZTs" secondAttribute="bottom" constant="8" id="8xq-bG-S7A"/>
                                    <constraint firstItem="IeZ-TL-qL4" firstAttribute="firstBaseline" secondItem="df2-Ec-ZTs" secondAttribute="firstBaseline" id="EGr-hS-M3Q"/>
                                    <constraint firstItem="kg8-9z-h8H" firstAttribute="trailing" secondItem="HWj-Ba-h3x" secondAttribute="trailing" id="EYD-gP-pdg"/>
                                    <constraint firstItem="6ZQ-2R-J1F" firstAttribute="trailing" secondItem="kg8-9z-h8H" secondAttribute="trailing" id="Iqn-ap-d6g"/>
                                    <constraint firstItem="6kD-YR-t66" firstAttribute="firstBaseline" secondItem="Otk-ep-eDt" secondAttribute="firstBaseline" id="KTy-qk-6BI"/>
                                    <constraint firstItem="hob-ds-asX" firstAttribute="top" secondItem="sg6-kF-sPZ" secondAttribute="top" constant="8" id="M2a-Li-qQ6"/>
                                    <constraint firstItem="Otk-ep-eDt" firstAttribute="leading" secondItem="6kD-YR-t66" secondAttribute="trailing" constant="48" id="OtV-na-9xh"/>
                                    <constraint firstItem="IeZ-TL-qL4" firstAttribute="leading" secondItem="6kD-YR-t66" secondAttribute="leading" id="Oth-MC-bpV"/>
                                    <constraint firstItem="df2-Ec-ZTs" firstAttribute="top" secondItem="Otk-ep-eDt" secondAttribute="bottom" constant="8" id="QDb-JA-xVD"/>
                                    <constraint firstItem="kg8-9z-h8H" firstAttribute="leading" secondItem="PWA-i7-bgb" secondAttribute="trailing" constant="41" id="Sn1-Va-Syk"/>
                                    <constraint firstItem="vYg-2y-G9G" firstAttribute="trailing" secondItem="df2-Ec-ZTs" secondAttribute="trailing" id="TCU-Sd-HGf"/>
                                    <constraint firstItem="HWj-Ba-h3x" firstAttribute="top" secondItem="hob-ds-asX" secondAttribute="bottom" constant="8" id="V7A-TT-07L"/>
                                    <constraint firstAttribute="trailing" secondItem="hob-ds-asX" secondAttribute="trailing" constant="8" id="V8w-Y0-AEF"/>
                                    <constraint firstItem="tI7-xT-470" firstAttribute="firstBaseline" secondItem="xdl-oE-Ddr" secondAttribute="firstBaseline" id="XKw-xg-KMC"/>
                                    <constraint firstItem="6ZQ-2R-J1F" firstAttribute="leading" secondItem="Syy-0M-GXu" secondAttribute="trailing" constant="81" id="cb3-QJ-G7k"/>
                                    <constraint firstItem="PWA-i7-bgb" firstAttribute="firstBaseline" secondItem="kg8-9z-h8H" secondAttribute="firstBaseline" id="d1s-c2-bfL"/>
                                    <constraint firstItem="YLc-o3-bbc" firstAttribute="leading" secondItem="sg6-kF-sPZ" secondAttribute="leading" constant="8" id="edG-ih-VJ9"/>
                                    <constraint firstItem="Otk-ep-eDt" firstAttribute="leading" secondItem="6ZQ-2R-J1F" secondAttribute="leading" id="gZ5-xi-Va9"/>
                                    <constraint firstItem="xdl-oE-Ddr" firstAttribute="leading" secondItem="df2-Ec-ZTs" secondAttribute="leading" id="hTl-hv-fba"/>
                                    <constraint firstItem="YLc-o3-bbc" firstAttribute="firstBaseline" secondItem="HWj-Ba-h3x" secondAttribute="firstBaseline" id="hbw-Hh-9dm"/>
                                    <constraint firstItem="kg8-9z-h8H" firstAttribute="leading" secondItem="HWj-Ba-h3x" secondAttribute="leading" id="iEK-1K-GTG"/>
                                    <constraint firstItem="vYg-2y-G9G" firstAttribute="leading" secondItem="IeZ-TL-qL4" secondAttribute="leading" id="ilh-BH-N1h"/>
                                    <constraint firstItem="xdl-oE-Ddr" firstAttribute="top" secondItem="vYg-2y-G9G" secondAttribute="bottom" constant="8" id="l0w-BZ-YNG"/>
                                    <constraint firstItem="6ZQ-2R-J1F" firstAttribute="top" secondItem="kg8-9z-h8H" secondAttribute="bottom" constant="8" id="lut-Nh-hqP"/>
                                    <constraint firstItem="6ZQ-2R-J1F" firstAttribute="leading" secondItem="kg8-9z-h8H" secondAttribute="leading" id="mXf-3X-9g8"/>
                                    <constraint firstItem="df2-Ec-ZTs" firstAttribute="leading" secondItem="Otk-ep-eDt" secondAttribute="leading" id="mjV-zt-YzW"/>
                                    <constraint firstItem="xdl-oE-Ddr" firstAttribute="trailing" secondItem="vYg-2y-G9G" secondAttribute="trailing" id="mr5-o4-eZQ"/>
                                    <constraint firstItem="Syy-0M-GXu" firstAttribute="leading" secondItem="PWA-i7-bgb" secondAttribute="leading" id="n0K-sL-ErD"/>
                                    <constraint firstItem="Otk-ep-eDt" firstAttribute="top" secondItem="6ZQ-2R-J1F" secondAttribute="bottom" constant="8" id="nAE-MD-YEn"/>
                                    <constraint firstItem="xdl-oE-Ddr" firstAttribute="leading" secondItem="tI7-xT-470" secondAttribute="trailing" constant="44" id="pN3-6I-oyR"/>
                                    <constraint firstItem="6kD-YR-t66" firstAttribute="leading" secondItem="Syy-0M-GXu" secondAttribute="leading" id="sbL-mK-0Yt"/>
                                    <constraint firstItem="PWA-i7-bgb" firstAttribute="leading" secondItem="YLc-o3-bbc" secondAttribute="leading" id="seb-MR-naI"/>
                                    <constraint firstItem="HWj-Ba-h3x" firstAttribute="leading" secondItem="YLc-o3-bbc" secondAttribute="trailing" constant="18" id="y0r-Bg-kQd"/>
                                    <constraint firstItem="Otk-ep-eDt" firstAttribute="trailing" secondItem="6ZQ-2R-J1F" secondAttribute="trailing" id="ys2-5F-Yv2"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="Content Types" identifier="" id="bj4-V7-oVj">
                            <view key="view" id="mx7-S2-itp">
                                <rect key="frame" x="10" y="33" width="474" height="264"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="Xot-m8-ggx">
                                        <rect key="frame" x="15" y="205" width="444" height="56"/>
                                        <textFieldCell key="cell" id="USw-iz-pYK">
                                            <font key="font" metaFont="label" size="11"/>
                                            <string key="title">This table defines the mapping between network stream content types and DeaDBeeF decoder plugins. For example, mp3 files can have content type "audio/x-mpeg", and need to be decoded by DeaDBeeF's own "stdmpg" plugin, or "ffmpeg" plugin.</string>
                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <scrollView borderType="line" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kfO-QJ-hCv">
                                        <rect key="frame" x="17" y="40" width="440" height="157"/>
                                        <clipView key="contentView" id="JU5-t2-anJ">
                                            <rect key="frame" x="1" y="1" width="438" height="155"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnSelection="YES" autosaveColumns="NO" rowSizeStyle="automatic" headerView="dQI-1a-EJU" viewBased="YES" id="Bgl-g5-g9B">
                                                    <rect key="frame" x="0.0" y="0.0" width="438" height="130"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <size key="intercellSpacing" width="3" height="2"/>
                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                    <tableColumns>
                                                        <tableColumn width="116" minWidth="40" maxWidth="1000" id="b7a-VK-IwI">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" title="Content-Type">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" title="Text Cell" id="wJl-97-xBq">
                                                                <font key="font" metaFont="system"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            <prototypeCellViews>
                                                                <tableCellView id="oPR-lT-aW9">
                                                                    <rect key="frame" x="1" y="1" width="121" height="17"/>
                                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                    <subviews>
                                                                        <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="f01-xW-vbE">
                                                                            <rect key="frame" x="0.0" y="0.0" width="121" height="17"/>
                                                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                            <textFieldCell key="cell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" title="Table View Cell" id="sHv-1y-Wt9">
                                                                                <font key="font" metaFont="system"/>
                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                            </textFieldCell>
                                                                            <connections>
                                                                                <binding destination="oPR-lT-aW9" name="value" keyPath="objectValue.contentType" id="Axx-Rb-UBl"/>
                                                                            </connections>
                                                                        </textField>
                                                                    </subviews>
                                                                    <connections>
                                                                        <outlet property="textField" destination="f01-xW-vbE" id="b6O-SE-lxu"/>
                                                                    </connections>
                                                                </tableCellView>
                                                            </prototypeCellViews>
                                                        </tableColumn>
                                                        <tableColumn width="278" minWidth="40" maxWidth="1000" id="x9C-oO-vV2">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" title="Plugins">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" title="Text Cell" id="lCb-bt-roY">
                                                                <font key="font" metaFont="system"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            <prototypeCellViews>
                                                                <tableCellView id="qMd-hB-qUt">
                                                                    <rect key="frame" x="125" y="1" width="282" height="17"/>
                                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                    <subviews>
                                                                        <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7wV-L3-U1l">
                                                                            <rect key="frame" x="0.0" y="0.0" width="282" height="17"/>
                                                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                            <textFieldCell key="cell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" title="Table View Cell" id="7IO-Mv-m0c">
                                                                                <font key="font" metaFont="system"/>
                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                            </textFieldCell>
                                                                            <connections>
                                                                                <binding destination="qMd-hB-qUt" name="value" keyPath="objectValue.plugins" id="wtU-Gz-xH4"/>
                                                                            </connections>
                                                                        </textField>
                                                                    </subviews>
                                                                    <connections>
                                                                        <outlet property="textField" destination="7wV-L3-U1l" id="tml-8Z-B3B"/>
                                                                    </connections>
                                                                </tableCellView>
                                                            </prototypeCellViews>
                                                        </tableColumn>
                                                    </tableColumns>
                                                    <connections>
                                                        <binding destination="H3C-fa-Rey" name="content" keyPath="arrangedObjects" id="Czz-qA-mHo"/>
                                                        <binding destination="H3C-fa-Rey" name="selectionIndexes" keyPath="selectionIndexes" previousBinding="Czz-qA-mHo" id="DJ0-dI-Bwd"/>
                                                    </connections>
                                                </tableView>
                                            </subviews>
                                        </clipView>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="440" id="OMT-cc-c8H"/>
                                        </constraints>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="9aJ-43-c52">
                                            <rect key="frame" x="1" y="150" width="498" height="15"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="8Qd-uo-q88">
                                            <rect key="frame" x="224" y="17" width="15" height="102"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <tableHeaderView key="headerView" wantsLayer="YES" id="dQI-1a-EJU">
                                            <rect key="frame" x="0.0" y="0.0" width="438" height="25"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableHeaderView>
                                    </scrollView>
                                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ucg-CG-ZQ2">
                                        <rect key="frame" x="17" y="19" width="440" height="23"/>
                                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="uXQ-G3-n4A">
                                            <behavior key="behavior" lightByContents="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                    </button>
                                    <segmentedControl verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="vVB-D3-QUd">
                                        <rect key="frame" x="17" y="19" width="65" height="23"/>
                                        <segmentedCell key="cell" borderStyle="border" alignment="left" style="smallSquare" trackingMode="momentary" id="BeG-CV-BcL">
                                            <font key="font" metaFont="system"/>
                                            <segments>
                                                <segment toolTip="Add" image="NSAddTemplate" width="30"/>
                                                <segment toolTip="Remove" image="NSRemoveTemplate" width="30" tag="1"/>
                                            </segments>
                                        </segmentedCell>
                                        <connections>
                                            <action selector="segmentedControlAction:" target="4Qv-4Y-koA" id="4T1-hB-Boa"/>
                                        </connections>
                                    </segmentedControl>
                                    <segmentedControl verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Yan-dH-Iaq">
                                        <rect key="frame" x="403" y="19" width="54" height="23"/>
                                        <segmentedCell key="cell" borderStyle="border" alignment="left" style="smallSquare" trackingMode="momentary" id="rcm-Yt-d3Y">
                                            <font key="font" metaFont="system"/>
                                            <segments>
                                                <segment label="Reset" toolTip="Add" width="52"/>
                                            </segments>
                                        </segmentedCell>
                                        <connections>
                                            <action selector="resetContentTypeMapping:" target="4Qv-4Y-koA" id="dff-Cu-Ix6"/>
                                        </connections>
                                    </segmentedControl>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Xot-m8-ggx" firstAttribute="top" secondItem="mx7-S2-itp" secondAttribute="top" constant="3" id="4vp-Nj-kcS"/>
                                    <constraint firstItem="kfO-QJ-hCv" firstAttribute="top" secondItem="Xot-m8-ggx" secondAttribute="bottom" constant="8" id="8sj-Oo-sXY"/>
                                    <constraint firstAttribute="bottom" secondItem="ucg-CG-ZQ2" secondAttribute="bottom" constant="20" id="AzD-jl-8ZV"/>
                                    <constraint firstItem="Yan-dH-Iaq" firstAttribute="trailing" secondItem="ucg-CG-ZQ2" secondAttribute="trailing" id="DaF-Dx-MxV"/>
                                    <constraint firstItem="vVB-D3-QUd" firstAttribute="leading" secondItem="ucg-CG-ZQ2" secondAttribute="leading" id="O8L-wj-BUu"/>
                                    <constraint firstItem="vVB-D3-QUd" firstAttribute="bottom" secondItem="ucg-CG-ZQ2" secondAttribute="bottom" id="QFR-Wl-Jca"/>
                                    <constraint firstItem="Xot-m8-ggx" firstAttribute="trailing" secondItem="kfO-QJ-hCv" secondAttribute="trailing" id="csX-iW-b5h"/>
                                    <constraint firstAttribute="trailing" secondItem="kfO-QJ-hCv" secondAttribute="trailing" constant="17" id="e0G-cA-hs1"/>
                                    <constraint firstItem="Xot-m8-ggx" firstAttribute="leading" secondItem="mx7-S2-itp" secondAttribute="leading" constant="17" id="ecM-sW-YWy"/>
                                    <constraint firstItem="kfO-QJ-hCv" firstAttribute="leading" secondItem="Xot-m8-ggx" secondAttribute="leading" id="gn0-VI-hia"/>
                                    <constraint firstItem="ucg-CG-ZQ2" firstAttribute="leading" secondItem="kfO-QJ-hCv" secondAttribute="leading" id="hIt-rt-zC9"/>
                                    <constraint firstItem="ucg-CG-ZQ2" firstAttribute="top" secondItem="kfO-QJ-hCv" secondAttribute="bottom" constant="-1" id="iJJ-Uv-9uZ"/>
                                    <constraint firstItem="Yan-dH-Iaq" firstAttribute="bottom" secondItem="ucg-CG-ZQ2" secondAttribute="bottom" id="lca-hE-3k1"/>
                                    <constraint firstItem="Yan-dH-Iaq" firstAttribute="top" secondItem="ucg-CG-ZQ2" secondAttribute="top" id="qVo-m5-Fzc"/>
                                    <constraint firstItem="vVB-D3-QUd" firstAttribute="top" secondItem="ucg-CG-ZQ2" secondAttribute="top" id="sqq-7M-SDr"/>
                                    <constraint firstItem="ucg-CG-ZQ2" firstAttribute="trailing" secondItem="kfO-QJ-hCv" secondAttribute="trailing" id="ulS-eL-iTF"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                    </tabViewItems>
                </tabView>
            </subviews>
            <constraints>
                <constraint firstItem="oKC-A2-rMr" firstAttribute="top" secondItem="JAw-ar-I3a" secondAttribute="top" constant="20" id="ZaH-bP-VLY"/>
                <constraint firstItem="oKC-A2-rMr" firstAttribute="leading" secondItem="JAw-ar-I3a" secondAttribute="leading" constant="20" id="hf6-X7-cao"/>
                <constraint firstAttribute="bottom" secondItem="oKC-A2-rMr" secondAttribute="bottom" constant="20" id="s0G-i0-mfD"/>
                <constraint firstAttribute="trailing" secondItem="oKC-A2-rMr" secondAttribute="trailing" constant="20" id="t6c-ah-d7g"/>
            </constraints>
            <point key="canvasLocation" x="303" y="1585"/>
        </customView>
        <customView identifier="Plugins" id="ZNh-3b-s1L" userLabel="Plugins View">
            <rect key="frame" x="0.0" y="0.0" width="703" height="349"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <scrollView autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oPE-t8-hhR">
                    <rect key="frame" x="20" y="20" width="200" height="309"/>
                    <clipView key="contentView" id="Qwb-Aj-8az">
                        <rect key="frame" x="1" y="1" width="198" height="307"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="none" alternatingRowBackgroundColors="YES" columnReordering="NO" columnResizing="NO" multipleSelection="NO" autosaveColumns="NO" autosaveName="" rowSizeStyle="automatic" viewBased="YES" id="oDJ-0h-H53">
                                <rect key="frame" x="0.0" y="0.0" width="198" height="307"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <size key="intercellSpacing" width="3" height="2"/>
                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                <tableColumns>
                                    <tableColumn identifier="PluginName" editable="NO" width="157" minWidth="40" maxWidth="1000" id="dEs-DP-gGS">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Title">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="wE0-Ne-iAj">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        <prototypeCellViews>
                                            <tableCellView identifier="PluginName" id="9HD-NU-Iig">
                                                <rect key="frame" x="1" y="1" width="166" height="17"/>
                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                <subviews>
                                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="8Kn-6X-dr9">
                                                        <rect key="frame" x="0.0" y="1" width="166" height="16"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES" flexibleMaxY="YES"/>
                                                        <textFieldCell key="cell" lineBreakMode="truncatingTail" sendsActionOnEndEditing="YES" title="Table View Cell" id="xEi-wX-KlI">
                                                            <font key="font" usesAppearanceFont="YES"/>
                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                </subviews>
                                                <connections>
                                                    <outlet property="textField" destination="8Kn-6X-dr9" id="59A-BU-Vxn"/>
                                                </connections>
                                            </tableCellView>
                                        </prototypeCellViews>
                                    </tableColumn>
                                </tableColumns>
                            </tableView>
                        </subviews>
                    </clipView>
                    <constraints>
                        <constraint firstAttribute="width" constant="200" id="2GJ-lW-HZT"/>
                    </constraints>
                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="7JL-Cd-jgz">
                        <rect key="frame" x="1" y="295" width="198" height="15"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="rFc-DW-Td0">
                        <rect key="frame" x="224" y="17" width="15" height="102"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                </scrollView>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Oe9-ps-NvQ">
                    <rect key="frame" x="362" y="166" width="185" height="17"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMinY="YES" flexibleMaxY="YES"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Select a plugin from sidebar." id="2QE-d3-bZ4">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <tabView translatesAutoresizingMaskIntoConstraints="NO" id="bES-gi-vpR">
                    <rect key="frame" x="221" y="10" width="469" height="325"/>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="455" id="dew-8A-h4b"/>
                    </constraints>
                    <font key="font" metaFont="system"/>
                    <tabViewItems>
                        <tabViewItem label="Configuration" identifier="2" id="dZm-hO-siC">
                            <view key="view" id="rqC-ar-oIf">
                                <rect key="frame" x="10" y="33" width="449" height="279"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="WQ4-SD-wFX">
                                        <rect key="frame" x="307" y="1" width="141" height="32"/>
                                        <buttonCell key="cell" type="push" title="Reset To Defaults" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="hx7-KI-xg9">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="pluginConfResetDefaults:" target="AAs-to-EGG" id="XHT-qt-wJE"/>
                                        </connections>
                                    </button>
                                    <scrollView borderType="line" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Oef-pD-hfw">
                                        <rect key="frame" x="8" y="36" width="433" height="235"/>
                                        <clipView key="contentView" drawsBackground="NO" copiesOnScroll="NO" id="c6E-sw-DGw" customClass="FlippedClipView">
                                            <rect key="frame" x="1" y="1" width="431" height="233"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <view fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9eO-pn-WbB" customClass="PropertySheetContentView">
                                                    <rect key="frame" x="0.0" y="0.0" width="431" height="233"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                </view>
                                            </subviews>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="JjP-Kq-V4s">
                                            <rect key="frame" x="1" y="80" width="166" height="15"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="Ywy-t2-JeC">
                                            <rect key="frame" x="393" y="1" width="15" height="219"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                    </scrollView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="WQ4-SD-wFX" secondAttribute="bottom" constant="8" id="4eE-Wq-aN3"/>
                                    <constraint firstAttribute="trailing" secondItem="WQ4-SD-wFX" secondAttribute="trailing" constant="8" id="IYZ-Tt-jAh"/>
                                    <constraint firstItem="WQ4-SD-wFX" firstAttribute="top" secondItem="Oef-pD-hfw" secondAttribute="bottom" constant="8" id="gF2-Sf-9if"/>
                                    <constraint firstItem="Oef-pD-hfw" firstAttribute="trailing" secondItem="WQ4-SD-wFX" secondAttribute="trailing" id="kBH-AF-IJd"/>
                                    <constraint firstItem="Oef-pD-hfw" firstAttribute="leading" secondItem="rqC-ar-oIf" secondAttribute="leading" constant="8" id="mUV-Ef-3cd"/>
                                    <constraint firstItem="Oef-pD-hfw" firstAttribute="top" secondItem="rqC-ar-oIf" secondAttribute="top" constant="8" id="wSb-3f-GAz"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="Info" identifier="1" id="nFO-cb-yUJ">
                            <view key="view" id="XwR-5F-hVu">
                                <rect key="frame" x="10" y="33" width="449" height="279"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <scrollView horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GXS-T2-rdA">
                                        <rect key="frame" x="8" y="8" width="433" height="231"/>
                                        <clipView key="contentView" drawsBackground="NO" id="Lle-Gh-L94">
                                            <rect key="frame" x="1" y="1" width="431" height="229"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textView editable="NO" importsGraphics="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" continuousSpellChecking="YES" allowsUndo="YES" usesRuler="YES" allowsNonContiguousLayout="YES" quoteSubstitution="YES" dashSubstitution="YES" spellingCorrection="YES" smartInsertDelete="YES" id="RAU-kc-ZWb">
                                                    <rect key="frame" x="0.0" y="0.0" width="431" height="229"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <size key="minSize" width="431" height="229"/>
                                                    <size key="maxSize" width="463" height="10000000"/>
                                                    <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                </textView>
                                            </subviews>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="x02-aW-8R2">
                                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="VjM-Ek-iC2">
                                            <rect key="frame" x="417" y="1" width="15" height="229"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                    </scrollView>
                                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="UT2-B7-f8U">
                                        <rect key="frame" x="6" y="253" width="53" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Version:" id="BKS-4W-sKb">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="wDm-op-PV2">
                                        <rect key="frame" x="65" y="250" width="310" height="21"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="wgi-a1-xpy">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <button horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="cbN-IE-HyD">
                                        <rect key="frame" x="376" y="244" width="72" height="32"/>
                                        <buttonCell key="cell" type="push" title="WWW" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="aSj-5z-BnR">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="pluginOpenWebsite:" target="AAs-to-EGG" id="CEx-ry-pOn"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="GXS-T2-rdA" secondAttribute="bottom" constant="8" id="63O-Ee-SaU"/>
                                    <constraint firstAttribute="trailing" secondItem="cbN-IE-HyD" secondAttribute="trailing" constant="8" id="Ftd-4S-fne"/>
                                    <constraint firstItem="wDm-op-PV2" firstAttribute="leading" secondItem="UT2-B7-f8U" secondAttribute="trailing" constant="8" id="K1c-MW-2uk"/>
                                    <constraint firstItem="cbN-IE-HyD" firstAttribute="centerY" secondItem="wDm-op-PV2" secondAttribute="centerY" id="L6S-E9-Xul"/>
                                    <constraint firstItem="GXS-T2-rdA" firstAttribute="leading" secondItem="UT2-B7-f8U" secondAttribute="leading" id="R58-Yy-Nag"/>
                                    <constraint firstItem="GXS-T2-rdA" firstAttribute="trailing" secondItem="cbN-IE-HyD" secondAttribute="trailing" id="WAW-4b-9zY"/>
                                    <constraint firstItem="cbN-IE-HyD" firstAttribute="leading" secondItem="wDm-op-PV2" secondAttribute="trailing" constant="8" id="XYa-gO-EQ2"/>
                                    <constraint firstItem="GXS-T2-rdA" firstAttribute="top" secondItem="wDm-op-PV2" secondAttribute="bottom" constant="11" id="bdf-Yc-67x"/>
                                    <constraint firstItem="UT2-B7-f8U" firstAttribute="leading" secondItem="XwR-5F-hVu" secondAttribute="leading" constant="8" id="liy-hf-Nfa"/>
                                    <constraint firstItem="UT2-B7-f8U" firstAttribute="firstBaseline" secondItem="wDm-op-PV2" secondAttribute="firstBaseline" id="mX6-rp-OpP"/>
                                    <constraint firstItem="wDm-op-PV2" firstAttribute="top" secondItem="XwR-5F-hVu" secondAttribute="top" constant="8" id="qNf-WJ-G9G"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="License" identifier="" id="9au-DZ-wqP">
                            <view key="view" id="LWj-6o-vOR">
                                <rect key="frame" x="10" y="33" width="449" height="279"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <scrollView horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jGd-YI-nuw">
                                        <rect key="frame" x="8" y="8" width="433" height="263"/>
                                        <clipView key="contentView" drawsBackground="NO" id="kGi-v7-aYF">
                                            <rect key="frame" x="1" y="1" width="431" height="261"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textView editable="NO" importsGraphics="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" continuousSpellChecking="YES" allowsUndo="YES" usesRuler="YES" allowsNonContiguousLayout="YES" quoteSubstitution="YES" dashSubstitution="YES" spellingCorrection="YES" smartInsertDelete="YES" id="8p1-kz-NZ1">
                                                    <rect key="frame" x="0.0" y="0.0" width="431" height="261"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <size key="minSize" width="431" height="261"/>
                                                    <size key="maxSize" width="463" height="10000000"/>
                                                    <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                </textView>
                                            </subviews>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="ACq-4K-UWO">
                                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="1Oa-sp-wEf">
                                            <rect key="frame" x="417" y="1" width="15" height="261"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                    </scrollView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="jGd-YI-nuw" firstAttribute="leading" secondItem="LWj-6o-vOR" secondAttribute="leading" constant="8" id="8hC-ni-UGF"/>
                                    <constraint firstItem="jGd-YI-nuw" firstAttribute="top" secondItem="LWj-6o-vOR" secondAttribute="top" constant="8" id="9dp-Qq-0OW"/>
                                    <constraint firstAttribute="trailing" secondItem="jGd-YI-nuw" secondAttribute="trailing" constant="8" id="Fkx-wj-KfM"/>
                                    <constraint firstAttribute="bottom" secondItem="jGd-YI-nuw" secondAttribute="bottom" constant="8" id="t62-VW-WvO"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                    </tabViewItems>
                </tabView>
            </subviews>
            <constraints>
                <constraint firstItem="oPE-t8-hhR" firstAttribute="top" secondItem="ZNh-3b-s1L" secondAttribute="top" constant="20" id="1Jy-Tj-ZP2"/>
                <constraint firstItem="bES-gi-vpR" firstAttribute="top" secondItem="ZNh-3b-s1L" secondAttribute="top" constant="20" id="9zJ-te-fSb"/>
                <constraint firstAttribute="trailing" secondItem="bES-gi-vpR" secondAttribute="trailing" constant="20" id="CRi-f1-Wmd"/>
                <constraint firstItem="oPE-t8-hhR" firstAttribute="leading" secondItem="ZNh-3b-s1L" secondAttribute="leading" constant="20" id="HZp-91-yum"/>
                <constraint firstItem="bES-gi-vpR" firstAttribute="bottom" secondItem="oPE-t8-hhR" secondAttribute="bottom" id="KpB-sh-fs4"/>
                <constraint firstItem="bES-gi-vpR" firstAttribute="leading" secondItem="oPE-t8-hhR" secondAttribute="trailing" constant="8" id="Tw8-VZ-Hai"/>
                <constraint firstAttribute="bottom" secondItem="oPE-t8-hhR" secondAttribute="bottom" constant="20" id="Zif-gB-q3h"/>
            </constraints>
            <point key="canvasLocation" x="396" y="1896.5"/>
        </customView>
        <viewController id="MIg-N1-ie7" customClass="PropertySheetViewController">
            <connections>
                <outlet property="contentView" destination="9eO-pn-WbB" id="7Iy-wk-ius"/>
                <outlet property="view" destination="Oef-pD-hfw" id="0Uc-7j-d5f"/>
            </connections>
        </viewController>
        <customView id="UtU-5A-sPy" userLabel="Sound View">
            <rect key="frame" x="0.0" y="0.0" width="520" height="232"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="389-ic-PQa">
                    <rect key="frame" x="18" y="196" width="88" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="Output Plugin" id="0Ww-cL-OsE">
                        <font key="font" usesAppearanceFont="YES"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="WGI-Tr-J4c">
                    <rect key="frame" x="109" y="189" width="395" height="25"/>
                    <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="fJ1-UJ-dTa">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message"/>
                        <menu key="menu" id="soW-Kc-Bpg"/>
                    </popUpButtonCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="selectedIndex" keyPath="self.outputPluginsIndex" id="H1v-1I-47Q"/>
                    </connections>
                </popUpButton>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="l0R-EK-reQ">
                    <rect key="frame" x="18" y="169" width="91" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="Output Device" id="9hn-Ic-Cwd">
                        <font key="font" usesAppearanceFont="YES"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="0ET-qW-1ss">
                    <rect key="frame" x="112" y="162" width="392" height="25"/>
                    <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="Y8g-eD-4BE">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message"/>
                        <menu key="menu" id="IBa-jO-ZD7"/>
                    </popUpButtonCell>
                    <connections>
                        <action selector="outputDeviceAction:" target="rug-kD-oVn" id="1vA-pe-4Gr"/>
                    </connections>
                </popUpButton>
                <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="dAy-8r-6bC">
                    <rect key="frame" x="8" y="155" width="504" height="5"/>
                </box>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Lkp-Xx-fTd">
                    <rect key="frame" x="18" y="132" width="482" height="18"/>
                    <buttonCell key="cell" type="check" title="Override Samplerate" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="zuf-lv-2OS">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="value" keyPath="self.overrideSamplerate" id="kw3-LA-UrK"/>
                    </connections>
                </button>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="OHq-bQ-a2N">
                    <rect key="frame" x="34" y="77" width="466" height="18"/>
                    <buttonCell key="cell" type="check" title="Based on input samplerate" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="3g2-4L-t6H">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="value" keyPath="self.basedOnInputSamplerate" id="j1B-z4-jmo"/>
                        <binding destination="rug-kD-oVn" name="enabled" keyPath="self.overrideSamplerate" id="oTC-0A-efm"/>
                    </connections>
                </button>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="CiA-bx-OlM">
                    <rect key="frame" x="52" y="50" width="222" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="For multiples of 48 kHz (96, 192, …)" drawsBackground="YES" id="tA5-pH-SS3">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="enabled2" keyPath="self.basedOnInputSamplerate" previousBinding="K3P-jL-vA9" id="ktb-gI-Bka">
                            <dictionary key="options">
                                <integer key="NSMultipleValuesPlaceholder" value="-1"/>
                                <integer key="NSNoSelectionPlaceholder" value="-1"/>
                                <integer key="NSNotApplicablePlaceholder" value="-1"/>
                                <integer key="NSNullPlaceholder" value="-1"/>
                            </dictionary>
                        </binding>
                        <binding destination="rug-kD-oVn" name="enabled" keyPath="self.overrideSamplerate" id="K3P-jL-vA9"/>
                    </connections>
                </textField>
                <comboBox focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ePT-oo-DxM">
                    <rect key="frame" x="158" y="101" width="345" height="23"/>
                    <comboBoxCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" completes="NO" numberOfVisibleItems="5" id="vBl-I0-kUV">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        <objectValues>
                            <string>44100</string>
                            <string>48000</string>
                            <string>88200</string>
                            <string>96000</string>
                            <string>176400</string>
                            <string>192000</string>
                        </objectValues>
                    </comboBoxCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="enabled" keyPath="self.overrideSamplerate" id="08t-kd-081"/>
                        <binding destination="rug-kD-oVn" name="value" keyPath="self.targetSamplerate" id="g1R-w5-5YO"/>
                    </connections>
                </comboBox>
                <comboBox focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="xIv-0C-D3P">
                    <rect key="frame" x="281" y="46" width="222" height="23"/>
                    <comboBoxCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" completes="NO" numberOfVisibleItems="5" id="QJM-v7-gmh">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        <objectValues>
                            <string>44100</string>
                            <string>48000</string>
                            <string>88200</string>
                            <string>96000</string>
                            <string>176400</string>
                            <string>192000</string>
                        </objectValues>
                        <connections>
                            <binding destination="rug-kD-oVn" name="value" keyPath="self.samplerateForMultiplesOf48" id="N5b-ef-4nr"/>
                        </connections>
                    </comboBoxCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="enabled2" keyPath="self.basedOnInputSamplerate" previousBinding="uXo-PR-ek7" id="lL3-Nq-B9f">
                            <dictionary key="options">
                                <integer key="NSMultipleValuesPlaceholder" value="-1"/>
                                <integer key="NSNoSelectionPlaceholder" value="-1"/>
                                <integer key="NSNotApplicablePlaceholder" value="-1"/>
                                <integer key="NSNullPlaceholder" value="-1"/>
                            </dictionary>
                        </binding>
                        <binding destination="rug-kD-oVn" name="enabled" keyPath="self.overrideSamplerate" id="uXo-PR-ek7"/>
                        <binding destination="rug-kD-oVn" name="value" keyPath="self.samplerateForMultiplesOf48" id="uIE-ct-FVd"/>
                    </connections>
                </comboBox>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="kev-Ar-D1H">
                    <rect key="frame" x="36" y="105" width="115" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="Target Samplerate" drawsBackground="YES" id="ULG-Yu-8aS">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="enabled" keyPath="self.overrideSamplerate" id="ra9-im-VaU"/>
                    </connections>
                </textField>
                <comboBox focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="DZJ-up-vkC">
                    <rect key="frame" x="311" y="18" width="192" height="23"/>
                    <comboBoxCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" completes="NO" numberOfVisibleItems="5" id="Qzu-T6-MU2">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        <objectValues>
                            <string>44100</string>
                            <string>48000</string>
                            <string>88200</string>
                            <string>96000</string>
                            <string>176400</string>
                            <string>192000</string>
                        </objectValues>
                    </comboBoxCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="enabled2" keyPath="self.basedOnInputSamplerate" previousBinding="951-WQ-MXm" id="g0S-l1-6cZ">
                            <dictionary key="options">
                                <integer key="NSMultipleValuesPlaceholder" value="-1"/>
                                <integer key="NSNoSelectionPlaceholder" value="-1"/>
                                <integer key="NSNotApplicablePlaceholder" value="-1"/>
                                <integer key="NSNullPlaceholder" value="-1"/>
                            </dictionary>
                        </binding>
                        <binding destination="rug-kD-oVn" name="enabled" keyPath="self.overrideSamplerate" id="951-WQ-MXm"/>
                        <binding destination="rug-kD-oVn" name="value" keyPath="self.samplerateForMultiplesOf44" id="MvL-7c-g46"/>
                    </connections>
                </comboBox>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="VdO-R5-ZVn">
                    <rect key="frame" x="52" y="22" width="252" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="For multiples of 44.1 kHz (88.2, 176.4, …)" drawsBackground="YES" id="nwz-Pi-GKV">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="rug-kD-oVn" name="enabled2" keyPath="self.basedOnInputSamplerate" previousBinding="AiK-A8-JPB" id="PbR-Fe-W3n">
                            <dictionary key="options">
                                <integer key="NSMultipleValuesPlaceholder" value="-1"/>
                                <integer key="NSNoSelectionPlaceholder" value="-1"/>
                                <integer key="NSNotApplicablePlaceholder" value="-1"/>
                                <integer key="NSNullPlaceholder" value="-1"/>
                            </dictionary>
                        </binding>
                        <binding destination="rug-kD-oVn" name="enabled" keyPath="self.overrideSamplerate" id="AiK-A8-JPB"/>
                    </connections>
                </textField>
            </subviews>
            <constraints>
                <constraint firstItem="l0R-EK-reQ" firstAttribute="leading" secondItem="389-ic-PQa" secondAttribute="leading" id="2dg-UG-E6F"/>
                <constraint firstItem="0ET-qW-1ss" firstAttribute="leading" secondItem="l0R-EK-reQ" secondAttribute="trailing" constant="8" id="2gG-W0-CKl"/>
                <constraint firstItem="xIv-0C-D3P" firstAttribute="leading" secondItem="CiA-bx-OlM" secondAttribute="trailing" constant="8" id="4Dz-Ai-nqn"/>
                <constraint firstItem="389-ic-PQa" firstAttribute="top" secondItem="UtU-5A-sPy" secondAttribute="top" constant="20" id="4mC-Gw-mQS"/>
                <constraint firstItem="CiA-bx-OlM" firstAttribute="leading" secondItem="OHq-bQ-a2N" secondAttribute="leading" constant="16" id="8UV-IX-OIm"/>
                <constraint firstItem="WGI-Tr-J4c" firstAttribute="leading" secondItem="389-ic-PQa" secondAttribute="trailing" constant="8" id="9ZE-9w-3i2"/>
                <constraint firstItem="DZJ-up-vkC" firstAttribute="trailing" secondItem="xIv-0C-D3P" secondAttribute="trailing" id="Cyz-tZ-Rj6"/>
                <constraint firstItem="DZJ-up-vkC" firstAttribute="leading" secondItem="VdO-R5-ZVn" secondAttribute="trailing" constant="8" id="D44-h3-b97"/>
                <constraint firstItem="OHq-bQ-a2N" firstAttribute="leading" secondItem="kev-Ar-D1H" secondAttribute="leading" id="DDz-Vw-dGY"/>
                <constraint firstItem="xIv-0C-D3P" firstAttribute="top" secondItem="OHq-bQ-a2N" secondAttribute="bottom" constant="10" id="Dcx-Di-MJi"/>
                <constraint firstItem="ePT-oo-DxM" firstAttribute="trailing" secondItem="Lkp-Xx-fTd" secondAttribute="trailing" id="GX5-9d-K1U"/>
                <constraint firstAttribute="trailing" secondItem="dAy-8r-6bC" secondAttribute="trailing" constant="8" id="H01-ch-Qxs"/>
                <constraint firstItem="OHq-bQ-a2N" firstAttribute="top" secondItem="ePT-oo-DxM" secondAttribute="bottom" constant="9" id="JJ6-we-yBO"/>
                <constraint firstItem="0ET-qW-1ss" firstAttribute="top" secondItem="WGI-Tr-J4c" secondAttribute="bottom" constant="7" id="JNM-qR-bbn"/>
                <constraint firstItem="xIv-0C-D3P" firstAttribute="trailing" secondItem="OHq-bQ-a2N" secondAttribute="trailing" id="OoM-n4-uTS"/>
                <constraint firstItem="OHq-bQ-a2N" firstAttribute="trailing" secondItem="ePT-oo-DxM" secondAttribute="trailing" id="Q2O-fj-zSE"/>
                <constraint firstItem="ePT-oo-DxM" firstAttribute="leading" secondItem="kev-Ar-D1H" secondAttribute="trailing" constant="8" id="Ugm-qX-JVZ"/>
                <constraint firstItem="389-ic-PQa" firstAttribute="leading" secondItem="UtU-5A-sPy" secondAttribute="leading" constant="20" id="Wrp-aY-EKU"/>
                <constraint firstItem="CiA-bx-OlM" firstAttribute="firstBaseline" secondItem="xIv-0C-D3P" secondAttribute="firstBaseline" id="YQD-gJ-5R0"/>
                <constraint firstItem="0ET-qW-1ss" firstAttribute="trailing" secondItem="WGI-Tr-J4c" secondAttribute="trailing" id="Yfn-ie-iYb"/>
                <constraint firstItem="dAy-8r-6bC" firstAttribute="top" secondItem="0ET-qW-1ss" secondAttribute="bottom" constant="8" id="Zt4-1i-YTm"/>
                <constraint firstItem="kev-Ar-D1H" firstAttribute="firstBaseline" secondItem="ePT-oo-DxM" secondAttribute="firstBaseline" id="eTe-uL-NIg"/>
                <constraint firstItem="VdO-R5-ZVn" firstAttribute="firstBaseline" secondItem="DZJ-up-vkC" secondAttribute="firstBaseline" id="jUZ-xG-SD2"/>
                <constraint firstAttribute="bottom" secondItem="DZJ-up-vkC" secondAttribute="bottom" constant="20" id="jbY-nG-Nj6"/>
                <constraint firstItem="dAy-8r-6bC" firstAttribute="leading" secondItem="UtU-5A-sPy" secondAttribute="leading" constant="8" id="jjP-ei-Lzx"/>
                <constraint firstItem="Lkp-Xx-fTd" firstAttribute="trailing" secondItem="0ET-qW-1ss" secondAttribute="trailing" id="mwk-9D-dYI"/>
                <constraint firstItem="l0R-EK-reQ" firstAttribute="firstBaseline" secondItem="0ET-qW-1ss" secondAttribute="firstBaseline" id="oIn-51-NTJ"/>
                <constraint firstItem="kev-Ar-D1H" firstAttribute="leading" secondItem="Lkp-Xx-fTd" secondAttribute="leading" constant="16" id="qbX-fj-Jw1"/>
                <constraint firstItem="DZJ-up-vkC" firstAttribute="top" secondItem="xIv-0C-D3P" secondAttribute="bottom" constant="8" id="rUT-g3-TZ5"/>
                <constraint firstItem="Lkp-Xx-fTd" firstAttribute="top" secondItem="dAy-8r-6bC" secondAttribute="bottom" constant="8" symbolic="YES" id="rvm-oU-VJR"/>
                <constraint firstItem="Lkp-Xx-fTd" firstAttribute="leading" secondItem="l0R-EK-reQ" secondAttribute="leading" id="uab-zr-scB"/>
                <constraint firstItem="389-ic-PQa" firstAttribute="firstBaseline" secondItem="WGI-Tr-J4c" secondAttribute="firstBaseline" id="vt5-dO-NBC"/>
                <constraint firstItem="VdO-R5-ZVn" firstAttribute="leading" secondItem="CiA-bx-OlM" secondAttribute="leading" id="xZs-zX-Lli"/>
                <constraint firstItem="ePT-oo-DxM" firstAttribute="top" secondItem="Lkp-Xx-fTd" secondAttribute="bottom" constant="10" id="xwO-AZ-ZgS"/>
                <constraint firstAttribute="trailing" secondItem="WGI-Tr-J4c" secondAttribute="trailing" constant="20" id="zbM-Hr-Ki3"/>
            </constraints>
            <point key="canvasLocation" x="-470" y="65"/>
        </customView>
        <arrayController objectClassName="ContentTypeMap" avoidsEmptySelection="NO" id="H3C-fa-Rey">
            <declaredKeys>
                <string>contentType</string>
                <string>plugins</string>
            </declaredKeys>
            <classReference key="objectClass" className="ContentTypeMap"/>
        </arrayController>
        <userDefaultsController representsSharedInstance="YES" id="4c2-YY-bzC"/>
        <viewController id="jtP-FH-nkX" customClass="GuiPreferencesWindowController">
            <connections>
                <outlet property="view" destination="MUz-8x-xuE" id="s6B-qV-Pgq"/>
            </connections>
        </viewController>
        <viewController id="lFU-Xs-9Xm" customClass="DspPreferencesViewController">
            <connections>
                <outlet property="dspNodeEditorContainer" destination="wB2-nl-rwo" id="qfi-i3-PGQ"/>
                <outlet property="dspPresetNamePanel" destination="hwK-wK-v7Z" id="c5k-81-JtR"/>
                <outlet property="dspPresetNameTextField" destination="w5H-TF-NxP" id="dbN-PW-sin"/>
                <outlet property="dspPresetSelectorContainer" destination="7EN-x3-aU8" id="Z5z-ay-O5c"/>
                <outlet property="view" destination="FgU-f9-bru" id="nq9-P1-CvP"/>
            </connections>
        </viewController>
        <viewController id="4Qv-4Y-koA" customClass="NetworkPreferencesViewController">
            <connections>
                <outlet property="contentTypeMappingArrayController" destination="H3C-fa-Rey" id="Uc3-r0-hNE"/>
                <outlet property="contentTypeMappingTableView" destination="Bgl-g5-g9B" id="riG-qm-lGi"/>
                <outlet property="view" destination="JAw-ar-I3a" id="WNs-fS-3Gu"/>
            </connections>
        </viewController>
        <viewController id="AAs-to-EGG" customClass="PluginsPreferencesViewController">
            <connections>
                <outlet property="pluginConfViewController" destination="MIg-N1-ie7" id="UGa-2A-e4X"/>
                <outlet property="pluginDescription" destination="RAU-kc-ZWb" id="6Iv-QP-yfo"/>
                <outlet property="pluginLicense" destination="8p1-kz-NZ1" id="c1y-vH-fLV"/>
                <outlet property="pluginTabView" destination="bES-gi-vpR" id="CbE-ET-MR2"/>
                <outlet property="pluginUnselectedText" destination="Oe9-ps-NvQ" id="rG6-Ck-voZ"/>
                <outlet property="pluginVersion" destination="wDm-op-PV2" id="yiW-hS-MMz"/>
                <outlet property="pluginsTableView" destination="oDJ-0h-H53" id="lwW-gU-heT"/>
                <outlet property="view" destination="ZNh-3b-s1L" id="Aag-Jo-0i9"/>
                <outlet property="wwwButton" destination="cbN-IE-HyD" id="y8M-AM-q7I"/>
            </connections>
        </viewController>
        <viewController id="YqN-ZP-2eU" customClass="PlaybackPreferencesViewController">
            <connections>
                <outlet property="view" destination="UtU-5A-sPy" id="taT-ru-Hbc"/>
                <outlet property="visBufferSlider" destination="gEg-C3-cea" id="8BE-1e-HZh"/>
                <outlet property="visBufferValue" destination="9H9-w2-zUI" id="5bW-Kt-Dti"/>
            </connections>
        </viewController>
        <viewController id="rug-kD-oVn" customClass="SoundPreferencesViewController">
            <connections>
                <outlet property="audioDevicesPopupButton" destination="0ET-qW-1ss" id="i76-UM-UzV"/>
                <outlet property="outputPluginsPopupButton" destination="WGI-Tr-J4c" id="HFj-0L-GdY"/>
                <outlet property="view" destination="Zmc-ua-enY" id="JNj-c9-K90"/>
            </connections>
        </viewController>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" id="hwK-wK-v7Z" userLabel="Name new preset" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="167" y="89" width="453" height="71"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2048" height="1127"/>
            <view key="contentView" misplaced="YES" id="da6-wn-fR8">
                <rect key="frame" x="0.0" y="0.0" width="453" height="71"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="w5H-TF-NxP">
                        <rect key="frame" x="107" y="38" width="326" height="21"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="O2D-7M-eqP">
                            <font key="font" usesAppearanceFont="YES"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="YZb-RO-0JH">
                        <rect key="frame" x="27" y="40" width="74" height="16"/>
                        <textFieldCell key="cell" lineBreakMode="clipping" title="New preset" id="bcD-X7-wEZ">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="8Wb-4u-mqJ">
                        <rect key="frame" x="387" y="3" width="53" height="32"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="0bf-rH-0TH">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="presetNameOK:" target="lFU-Xs-9Xm" id="Scb-kP-nGx"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="d3t-1i-4Vk">
                        <rect key="frame" x="303" y="3" width="76" height="32"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="BK7-aG-D8y">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="presetNameCancel:" target="lFU-Xs-9Xm" id="Lc7-Hl-iO6"/>
                        </connections>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstItem="w5H-TF-NxP" firstAttribute="top" secondItem="da6-wn-fR8" secondAttribute="top" constant="20" id="3pO-xx-HZm"/>
                    <constraint firstItem="YZb-RO-0JH" firstAttribute="leading" secondItem="da6-wn-fR8" secondAttribute="leading" constant="29" id="6eO-ft-58G"/>
                    <constraint firstItem="8Wb-4u-mqJ" firstAttribute="top" secondItem="w5H-TF-NxP" secondAttribute="bottom" constant="8" id="8q2-QT-mjT"/>
                    <constraint firstAttribute="trailing" secondItem="8Wb-4u-mqJ" secondAttribute="trailing" constant="20" id="A6B-VR-zcs"/>
                    <constraint firstAttribute="bottom" secondItem="8Wb-4u-mqJ" secondAttribute="bottom" constant="10" id="Yj2-XC-mtp"/>
                    <constraint firstItem="d3t-1i-4Vk" firstAttribute="top" secondItem="8Wb-4u-mqJ" secondAttribute="top" id="e9b-i4-mYJ"/>
                    <constraint firstItem="YZb-RO-0JH" firstAttribute="firstBaseline" secondItem="w5H-TF-NxP" secondAttribute="firstBaseline" id="ebb-qw-SBH"/>
                    <constraint firstItem="8Wb-4u-mqJ" firstAttribute="trailing" secondItem="w5H-TF-NxP" secondAttribute="trailing" id="gM4-Vg-0BD"/>
                    <constraint firstItem="w5H-TF-NxP" firstAttribute="leading" secondItem="YZb-RO-0JH" secondAttribute="trailing" constant="8" id="vQl-F8-Ux0"/>
                    <constraint firstItem="8Wb-4u-mqJ" firstAttribute="leading" secondItem="d3t-1i-4Vk" secondAttribute="trailing" constant="22" id="zXK-Bg-gF2"/>
                </constraints>
            </view>
            <point key="canvasLocation" x="-241" y="612"/>
        </window>
        <customView id="hCs-rl-gm1" userLabel="Media Library Preferences">
            <rect key="frame" x="0.0" y="0.0" width="520" height="330"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <scrollView autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JYX-K5-GGP">
                    <rect key="frame" x="20" y="40" width="480" height="213"/>
                    <clipView key="contentView" id="do0-5z-Q8M">
                        <rect key="frame" x="1" y="1" width="478" height="211"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="none" multipleSelection="NO" autosaveColumns="NO" typeSelect="NO" rowSizeStyle="automatic" viewBased="YES" id="0Ov-kf-8Ci">
                                <rect key="frame" x="0.0" y="0.0" width="478" height="211"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <size key="intercellSpacing" width="3" height="2"/>
                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                <tableColumns>
                                    <tableColumn identifier="Folder" editable="NO" width="397" minWidth="40" maxWidth="1000" id="zYn-uq-36q">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" usesSingleLineMode="YES" id="x6X-FC-bYb">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                        <prototypeCellViews>
                                            <tableCellView identifier="Folder" id="rYj-wb-T8g">
                                                <rect key="frame" x="1" y="1" width="406" height="17"/>
                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                <subviews>
                                                    <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" allowsExpansionToolTips="YES" translatesAutoresizingMaskIntoConstraints="NO" id="o06-ej-KEK">
                                                        <rect key="frame" x="0.0" y="0.0" width="406" height="17"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                        <textFieldCell key="cell" lineBreakMode="truncatingMiddle" selectable="YES" sendsActionOnEndEditing="YES" title="Table View Cell" usesSingleLineMode="YES" id="y6G-l0-45c">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                        </textFieldCell>
                                                    </textField>
                                                </subviews>
                                                <connections>
                                                    <outlet property="textField" destination="o06-ej-KEK" id="fkw-ep-d8W"/>
                                                </connections>
                                            </tableCellView>
                                        </prototypeCellViews>
                                    </tableColumn>
                                </tableColumns>
                                <connections>
                                    <outlet property="dataSource" destination="dfZ-70-v7J" id="rJX-0m-CfK"/>
                                    <outlet property="delegate" destination="dfZ-70-v7J" id="cH8-5T-ydz"/>
                                </connections>
                            </tableView>
                        </subviews>
                    </clipView>
                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="U89-3t-2a6">
                        <rect key="frame" x="1" y="197" width="438" height="15"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="ccM-dg-PJS">
                        <rect key="frame" x="224" y="17" width="15" height="102"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                </scrollView>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="O56-1y-jCg">
                    <rect key="frame" x="20" y="19" width="480" height="23"/>
                    <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" imagePosition="overlaps" alignment="center" lineBreakMode="truncatingTail" refusesFirstResponder="YES" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="i4b-3X-s3X">
                        <behavior key="behavior" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
                <segmentedControl verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="H95-hH-kPe">
                    <rect key="frame" x="20" y="19" width="480" height="23"/>
                    <segmentedCell key="cell" borderStyle="border" alignment="left" style="smallSquare" trackingMode="momentary" id="OU5-HW-8cC">
                        <font key="font" metaFont="system"/>
                        <segments>
                            <segment toolTip="Add" image="NSAddTemplate" width="30"/>
                            <segment toolTip="Remove" image="NSRemoveTemplate" width="30" tag="1"/>
                        </segments>
                    </segmentedCell>
                    <connections>
                        <action selector="addRemoveAction:" target="dfZ-70-v7J" id="Ed3-su-kGe"/>
                    </connections>
                </segmentedControl>
                <segmentedControl hidden="YES" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="zBg-Ib-ca5">
                    <rect key="frame" x="484" y="19" width="16" height="23"/>
                    <segmentedCell key="cell" borderStyle="border" alignment="left" style="smallSquare" trackingMode="momentary" id="7Lp-Jf-YZg">
                        <font key="font" metaFont="system"/>
                        <segments>
                            <segment>
                                <nil key="label"/>
                            </segment>
                        </segments>
                    </segmentedCell>
                </segmentedControl>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="XbD-B5-Ml6">
                    <rect key="frame" x="18" y="261" width="484" height="16"/>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="Add / remove music folders" id="2lU-Ux-u17">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="NNO-zr-q0H">
                    <rect key="frame" x="18" y="293" width="149" height="18"/>
                    <buttonCell key="cell" type="check" title="Enable media library" bezelStyle="regularSquare" imagePosition="left" inset="2" id="J4g-sz-Y82">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                        <connections>
                            <binding destination="dfZ-70-v7J" name="value" keyPath="enabled" id="8kH-MB-2DN"/>
                        </connections>
                    </buttonCell>
                </button>
                <box verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="DTh-93-rjQ">
                    <rect key="frame" x="10" y="283" width="500" height="5"/>
                </box>
            </subviews>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="XbD-B5-Ml6" secondAttribute="trailing" constant="20" id="1Qj-u9-mx7"/>
                <constraint firstItem="JYX-K5-GGP" firstAttribute="leading" secondItem="XbD-B5-Ml6" secondAttribute="leading" id="AJZ-rS-SYT"/>
                <constraint firstItem="JYX-K5-GGP" firstAttribute="top" secondItem="XbD-B5-Ml6" secondAttribute="bottom" constant="8" id="BOP-3V-dgl"/>
                <constraint firstAttribute="trailing" secondItem="DTh-93-rjQ" secondAttribute="trailing" constant="10" id="CxA-uT-k0H"/>
                <constraint firstItem="NNO-zr-q0H" firstAttribute="top" secondItem="hCs-rl-gm1" secondAttribute="top" constant="20" symbolic="YES" id="KMd-Qn-2AH"/>
                <constraint firstItem="zBg-Ib-ca5" firstAttribute="trailing" secondItem="0Ov-kf-8Ci" secondAttribute="trailing" constant="1" id="NSk-1v-F0Q"/>
                <constraint firstItem="DTh-93-rjQ" firstAttribute="top" secondItem="NNO-zr-q0H" secondAttribute="bottom" constant="8" symbolic="YES" id="OXs-Cq-Vvg"/>
                <constraint firstItem="XbD-B5-Ml6" firstAttribute="top" secondItem="DTh-93-rjQ" secondAttribute="bottom" constant="8" symbolic="YES" id="SIj-bd-vQ3"/>
                <constraint firstItem="DTh-93-rjQ" firstAttribute="leading" secondItem="hCs-rl-gm1" secondAttribute="leading" constant="10" id="YYO-Wm-Vph"/>
                <constraint firstAttribute="bottom" secondItem="O56-1y-jCg" secondAttribute="bottom" constant="20" id="akN-Q3-HHQ"/>
                <constraint firstItem="H95-hH-kPe" firstAttribute="trailing" secondItem="O56-1y-jCg" secondAttribute="trailing" id="bTi-3T-A7W"/>
                <constraint firstItem="O56-1y-jCg" firstAttribute="top" secondItem="JYX-K5-GGP" secondAttribute="bottom" constant="-1" id="bel-9b-ESz"/>
                <constraint firstItem="H95-hH-kPe" firstAttribute="leading" secondItem="O56-1y-jCg" secondAttribute="leading" id="bx9-VC-UaM"/>
                <constraint firstItem="NNO-zr-q0H" firstAttribute="leading" secondItem="hCs-rl-gm1" secondAttribute="leading" constant="20" symbolic="YES" id="caS-k3-Zxd"/>
                <constraint firstItem="XbD-B5-Ml6" firstAttribute="leading" secondItem="hCs-rl-gm1" secondAttribute="leading" constant="20" id="iH8-8b-Xis"/>
                <constraint firstItem="zBg-Ib-ca5" firstAttribute="bottom" secondItem="H95-hH-kPe" secondAttribute="bottom" id="izI-d7-gza"/>
                <constraint firstItem="JYX-K5-GGP" firstAttribute="trailing" secondItem="XbD-B5-Ml6" secondAttribute="trailing" id="nXE-45-0Bd"/>
                <constraint firstItem="H95-hH-kPe" firstAttribute="top" secondItem="JYX-K5-GGP" secondAttribute="bottom" constant="-1" id="oWv-YP-0R6"/>
                <constraint firstItem="O56-1y-jCg" firstAttribute="leading" secondItem="JYX-K5-GGP" secondAttribute="leading" id="s0U-Nx-3XK"/>
                <constraint firstItem="O56-1y-jCg" firstAttribute="trailing" secondItem="JYX-K5-GGP" secondAttribute="trailing" id="y7F-mf-082"/>
                <constraint firstItem="H95-hH-kPe" firstAttribute="bottom" secondItem="O56-1y-jCg" secondAttribute="bottom" id="yan-Dw-ZrR"/>
                <constraint firstItem="zBg-Ib-ca5" firstAttribute="top" secondItem="H95-hH-kPe" secondAttribute="top" id="yyT-cM-wcf"/>
            </constraints>
            <point key="canvasLocation" x="-327" y="217"/>
        </customView>
        <viewController id="dfZ-70-v7J" customClass="MediaLibraryPreferencesViewController">
            <connections>
                <outlet property="tableView" destination="0Ov-kf-8Ci" id="Okr-5s-Yne"/>
                <outlet property="view" destination="hCs-rl-gm1" id="aEs-vH-GWi"/>
            </connections>
        </viewController>
        <viewController id="XoS-qb-O3F" userLabel="Hotkeys Preferences View Controller" customClass="KeyboardShortcutEditorViewController"/>
    </objects>
    <resources>
        <image name="NSAddTemplate" width="18" height="17"/>
        <image name="NSRemoveTemplate" width="18" height="5"/>
        <image name="btnKeyboardTemplate" width="25" height="16"/>
        <image name="iconAppearanceTemplate" width="72" height="73"/>
        <image name="iconDSPTemplate" width="72" height="72"/>
        <image name="iconGUISettingsTemplate" width="72" height="72"/>
        <image name="iconLibraryTemplate" width="72" height="72"/>
        <image name="iconNetworkTemplate" width="72" height="72"/>
        <image name="iconPlaybackTemplate" width="72" height="72"/>
        <image name="iconPluginsTemplate" width="72" height="72"/>
        <image name="iconSoundTemplate" width="50" height="51"/>
    </resources>
</document>
