# traducción de es.po al Spanish
# translation of es.po to Spanish
# Spanish Translation for Glade.
# Copyright © 1999-2003, 2006 Free Software Foundation, Inc.
# This file is distributed under the same license as the glade package.
#
# <PERSON><PERSON> <<EMAIL>>, 1999.
# <AUTHOR> <EMAIL>, 2002.
# <PERSON> <<EMAIL>>, 2000-2002.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2003.
# <PERSON> <franciscojavier.fernandez.serra<PERSON>@hispalinux.es>, 2003.
# <PERSON> <<EMAIL>>, 2004, 2005, 2006.
msgid ""
msgstr ""
"Project-Id-Version: glade\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2006-12-05 16:04+0100\n"
"PO-Revision-Date: 2006-12-05 16:04+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Spanish <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"<EMAIL>>\n"
"X-Generator: KBabel 1.11.1\n"
"Plural-Forms:  nplurals=2; plural=(n != 1);\n"

#: ../glade-2.desktop.in.h:1
msgid "Create or open user interface designs for GTK+ or GNOME applications"
msgstr "Cree o abra diseños de interfaces para aplicaciones GTK+ o GNOME"

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Diseñador de interfaces Glade"

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Opciones de la rejilla"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Espaciado horizontal:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Espaciado vertical:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Estilo de la rejilla:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Puntos"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Líneas"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Opciones de ajuste"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Ajuste horizontal:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Izquierda"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Derecha"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Ajuste vertical:"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Superior"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Inferior"

#: ../glade/editor.c:741
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "Los widgets GtkToolItem sólo se pueden añadir a una barra GTKToolbar."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "No ha sido posible insertar un widget GtkScrolledWindow."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "No ha sido posible insertar un widget GtkViewport."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "No se ha podido añadir un widget nuevo."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"No puede añadir un widget en la posición seleccionada.\n"
"\n"
"Sugerencia: GTK+ emplea contenedores para colocar widgets.\n"
"Pruebe a borrar el widget actual y en su lugar emplee\n"
"una caja o tabla contenedora.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "No ha sido posible borrar el widget."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "El widget no puede ser borrado"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"El widget ha sido creado automáticamente como parte del widget padre, y no "
"puede ser borrado."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Anchura del borde:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "La anchura del borde alrededor del contenedor"

#: ../glade/gbwidget.c:1751
msgid "Select"
msgstr "Seleccionar"

#: ../glade/gbwidget.c:1773
msgid "Remove Scrolled Window"
msgstr "Quitar ventana de desplazamiento"

#: ../glade/gbwidget.c:1782
msgid "Add Scrolled Window"
msgstr "Añadir ventana de desplazamiento"

#: ../glade/gbwidget.c:1803
msgid "Remove Alignment"
msgstr "Quitar alineación"

#: ../glade/gbwidget.c:1811
msgid "Add Alignment"
msgstr "Añadir alineación"

#: ../glade/gbwidget.c:1826
msgid "Remove Event Box"
msgstr "Quitar caja de eventos"

#: ../glade/gbwidget.c:1834
msgid "Add Event Box"
msgstr "Añadir caja de eventos"

#: ../glade/gbwidget.c:1844
msgid "Redisplay"
msgstr "Redibujar"

#: ../glade/gbwidget.c:1859
msgid "Cut"
msgstr "Cortar"

#: ../glade/gbwidget.c:1866 ../glade/property.c:892 ../glade/property.c:5141
msgid "Copy"
msgstr "Copiar"

#: ../glade/gbwidget.c:1875 ../glade/property.c:904
msgid "Paste"
msgstr "Pegar"

#: ../glade/gbwidget.c:1887 ../glade/property.c:1581 ../glade/property.c:5132
msgid "Delete"
msgstr "Borrar"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2414 ../glade/gbwidget.c:2483
msgid "N/A"
msgstr "N/D"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3213
msgid "replacing child of container - not implemented yet\n"
msgstr "reemplazar hijo del contenedor - no implementado todavía\n"

#: ../glade/gbwidget.c:3441
msgid "Couldn't insert GtkAlignment widget."
msgstr "No ha sido posible insertar el widget GtkAlignment."

#: ../glade/gbwidget.c:3481
msgid "Couldn't remove GtkAlignment widget."
msgstr "No ha sido posible quitar el widget GtkAlignment."

#: ../glade/gbwidget.c:3505
msgid "Couldn't insert GtkEventBox widget."
msgstr "No ha sido posible insertar el widget GtkEventBox."

#: ../glade/gbwidget.c:3544
msgid "Couldn't remove GtkEventBox widget."
msgstr "No ha sido posible quitar el widget GtkEventBox."

#: ../glade/gbwidget.c:3579
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "No ha sido posible insertar el widget GtkScrolledWindow."

#: ../glade/gbwidget.c:3618
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "No ha sido posible quitar el widget GtkScrolledWindow."

#: ../glade/gbwidget.c:3732
msgid "Remove Label"
msgstr "Quitar etiqueta"

#: ../glade/gbwidgets/gbaboutdialog.c:79
msgid "Application Name"
msgstr "Nombre de la aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Logotipo:"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "La imagen a emplear como logotipo"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Nombre del programa:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
msgid "The name of the application"
msgstr "El nombre de la aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "Comentarios:"

#: ../glade/gbwidgets/gbaboutdialog.c:106
msgid "Additional information, such as a description of the application"
msgstr ""
"Información adicional, como por ejemplo una descripción de la aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Copyright:"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "El aviso de Copyright"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "Website URL:"
msgstr "URL del sitio web:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "The URL of the application's website"
msgstr "El URL del sitio web de la aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "Website Label:"
msgstr "Etiqueta del sitio web:"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "The label to display for the link to the website"
msgstr "La etiqueta para mostrar en el enlace a la página web"

#: ../glade/gbwidgets/gbaboutdialog.c:112 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Licencia:"

#: ../glade/gbwidgets/gbaboutdialog.c:112
msgid "The license details of the application"
msgstr "Los detalles de la licencia de la aplicación"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "Wrap License:"
msgstr "Autoajuste de la licencia:"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "If the license text should be wrapped"
msgstr "Si el texto de la licencia debe tener ajuste de líneas"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Autores:"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Los autores del programa, uno por línea"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Documentadores:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Los documentadores del paquete, uno en cada línea"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid "Artists:"
msgstr "Artistas:"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr ""
"La gente que ha creado el trabajo artístico para el paquete, uno por cada "
"línea"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Traductores:"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Los traductores del paquete.  Normalmente debería dejarse vacío para que los "
"traductores puedan añadir sus nombres en los archivos po"

#: ../glade/gbwidgets/gbaboutdialog.c:588
msgid "About Dialog"
msgstr "Diálogo «Acerca de»"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Etiqueta con acelerador"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:181 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "Alineación X:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "La alineación horizontal del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:184 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Alineación Y:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "La alineación vertical del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "Escala X:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "La escala horizontal del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Escala Y:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "La escala vertical del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:85
msgid "Top Padding:"
msgstr "Separación superior:"

#: ../glade/gbwidgets/gbalignment.c:86
msgid "Space to put above the child widget"
msgstr "El espacio a poner encima del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:89
msgid "Bottom Padding:"
msgstr "Separación inferior:"

#: ../glade/gbwidgets/gbalignment.c:90
msgid "Space to put below the child widget"
msgstr "El espacio a poner debajo del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:93
msgid "Left Padding:"
msgstr "Separación izquierda:"

#: ../glade/gbwidgets/gbalignment.c:94
msgid "Space to put to the left of the child widget"
msgstr "El espacio a poner a la izquierda del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:97
msgid "Right Padding:"
msgstr "Separación derecha:"

#: ../glade/gbwidgets/gbalignment.c:98
msgid "Space to put to the right of the child widget"
msgstr "El espacio a poner a la derecha del widget hijo"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Alineación"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "Dirección:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "La dirección de la flecha"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Sombra:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "El tipo de sombra de la flecha"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "La alineación horizontal de la flecha"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "La alineación vertical de la flecha"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187
msgid "X Pad:"
msgstr "Separación X:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "La separación horizontal"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189
msgid "Y Pad:"
msgstr "Separación Y:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "Separación vertical"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Flecha"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "Alin. X de la etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "La alineación horizontal del widget de la etiqueta del marco"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Alin. Y de la etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "La alineación vertical del widget de la etiqueta del marco"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "El tipo de sombra del marco"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "La alineación horizontal del hijo del marco"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Proporción:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "La proporción de los hijos del marco"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Obedecer al hijo:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Si la proporción debe ser determinada por el hijo"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Aspecto del marco"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Botón de inventario:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "El botón de inventario a usar"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:748
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Etiqueta:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "El texto a mostrar"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:297
#: ../glade/glade_menu_editor.c:814
msgid "Icon:"
msgstr "Icono:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "El icono a mostrar"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Relieve del botón:"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "El estilo del relieve del botón"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "ID de la respuesta:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"El código de respuesta cuando se presiona el botón.  Seleccione una de las "
"respuestas estándares o introduzca un valor entero positivo"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr "Enfocar al pulsar:"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "If the button grabs focus when it is clicked"
msgstr "Si el botón obtiene el foco cuando se pulsa"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "Quitar el contenido de los botones"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Botón"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Cabecera:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "Si el mes y el año deben de mostrarse encima"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Nombre de los días:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Si se deben mostrar los nombres de los días"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Mes fijo:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "Si el mes y el año no deben ser modificables"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Números de las semanas:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Si se debe mostrar el número de la semana"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Lunes primero:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "Si la semana debe empezar en lunes"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Calendario"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Color de fondo:"

#: ../glade/gbwidgets/gbcellview.c:64
msgid "The background color"
msgstr "El color del fondo"

#: ../glade/gbwidgets/gbcellview.c:192
msgid "Cell View"
msgstr "Vista de celda"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Activado inicialmente:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Si el botón de comprobación está marcado"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Inconsistente:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Indica si el botón se muestra en un estado inconsistente"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "Indicador:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "Si el indicador se dibuja siempre"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Botón de verificación"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Si el elemento de verificación del menú está inicialmente marcado"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Elemento de verificación en menú"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Nueva lista en columna"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Número de columnas:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:128 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Modo de selección:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "El modo de selección de la lista en columnas"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Mostrar títulos:"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Si se muestran los títulos de las columnas"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "El tipo de sombra del borde la lista de columna"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Lista en columnas"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Usar alfa:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Si se debe emplear el canal alfa"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:244
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Título:"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "El título del diálogo de selección de color"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
msgid "Pick a Color"
msgstr "Escoja un color"

#: ../glade/gbwidgets/gbcolorbutton.c:211
msgid "Color Chooser Button"
msgstr "Botón del selector de color"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Control de opacidad:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Indica si se muestra el control de opacidad"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Paleta:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Indica si la paleta debe ser mostrada"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Selección de color"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:71
msgid "Select Color"
msgstr "Seleccione el color"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:316 ../glade/property.c:1276
msgid "Color Selection Dialog"
msgstr "Diálogo de selección de color"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Valor en la lista:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Si el valor debe estar en la lista"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "Aceptar si está vacío"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr ""
"Indica si un valor vacío es válido, cuando se ha establecido «Valor en la "
"lista»"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "Sensible a capitalización:"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Si la búsqueda distingue entre mayúsculas y minúsculas"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Usar flechas:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "Si las flechas pueden ser usadas para cambiar de valor"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Usar siempre:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "Si las flechas funcionan a pesar de que el valor no esté en la lista"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Elementos:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Los elementos de la lista combo, una por línea"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Caja combo"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr "Añadir desprendibles:"

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr ""
"Indica si los desplegables deberían tener un elemento de menú desprendible"

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Indica si la caja combo obtiene el foco cuando se pulsa en ella"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Tiene marco:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr "Indica si la caja combo dibuja un marco alrededor del hijo"

#: ../glade/gbwidgets/gbcomboboxentry.c:302
msgid "Combo Box Entry"
msgstr "Entrada de la caja combo"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Árbol en columna nuevo"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "El modo de selección del árbol en columna"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "El tipo de sombra del borde del árbol en columna"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Árbol en columna"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:247
msgid "Type:"
msgstr "Tipo:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "El tipo de curva"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "X Mín:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "Valor mínimo horizontal"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "X Máx:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "Valor máximo horizontal"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Y Mín:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "Valor mínimo vertical"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Y Máx:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "Valor máximo vertical"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Curva"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Función de creación:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "La función que crea el widget"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Cadena1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "El primer parámetro (cadena de caracteres) a pasar a la función"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Cadena2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "El segundo parámetro (cadena de caracteres) a pasar a la función"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Entero1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "El primer parámetro entero a pasar a la función"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Entero2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "El segundo parámetro entero a pasar a la función"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Widget personalizado"

#: ../glade/gbwidgets/gbdialog.c:293
msgid "New dialog"
msgstr "Diálogo nuevo"

#: ../glade/gbwidgets/gbdialog.c:305
msgid "Cancel, OK"
msgstr "Cancelar, Aceptar"

#: ../glade/gbwidgets/gbdialog.c:314 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1322 ../glade/property.c:5162
msgid "OK"
msgstr "Aceptar"

#: ../glade/gbwidgets/gbdialog.c:323
msgid "Cancel, Apply, OK"
msgstr "Cancelar, Aplicar, Aceptar"

#: ../glade/gbwidgets/gbdialog.c:332
msgid "Close"
msgstr "Cerrar"

#: ../glade/gbwidgets/gbdialog.c:341
msgid "_Standard Button Layout:"
msgstr "Colocación e_stándar de los botones:"

#: ../glade/gbwidgets/gbdialog.c:350
msgid "_Number of Buttons:"
msgstr "_Número de botones:"

#: ../glade/gbwidgets/gbdialog.c:367
msgid "Show Help Button"
msgstr "Mostrar el botón de ayuda"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "Has Separator:"
msgstr "Tiene separador:"

#: ../glade/gbwidgets/gbdialog.c:399
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Indica si el diálogo tiene un separador horizontal sobre los botones"

#: ../glade/gbwidgets/gbdialog.c:606
msgid "Dialog"
msgstr "Diálogo"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Zona de dibujo"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Modificable:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Si el texto puede ser editado"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Texto visible:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"Si el texto que introduzca el usuario se mostrará. Cuando está desactivado, "
"el texto tecleado se muestra como caracteres asterisco, que es bastante útil "
"para introducir contraseñas"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Longitud máx:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "Longitud máxima del texto"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Texto:"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Indica si la entrada tiene un marco alrededor"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Carácter invisible:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"El carácter a emplear si el texto no debiera ser visible, p. e. cuando se "
"introducen contraseñas"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "Activar por omisión:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr ""
"Indica si el widget predeterminado en la ventana se activa cuando se "
"presiona tecla Intro"

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Anchura en caracteres:"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "El número de caracteres para dejar espacio en la entrada"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Entrada de texto"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "Visible Window:"
msgstr "Ventana visible:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr "Si la caja de eventos usa una ventana visible"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "Above Child:"
msgstr "Por encima del hijo:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr ""
"Si la ventana de la caja de eventos está por encima de la ventana del widget "
"hijo"

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Caja de eventos"

#: ../glade/gbwidgets/gbexpander.c:54
msgid "Initially Expanded:"
msgstr "Expandido al inicio:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr ""
"Indica si el expansor está abierto inicialmente para revelar el widget hijo"

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Espaciado:"

#: ../glade/gbwidgets/gbexpander.c:58
msgid "Space to put between the label and the child"
msgstr "El espacio a poner entre la etiqueta y el hijo"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Añadir widget etiqueta"

#: ../glade/gbwidgets/gbexpander.c:228
msgid "Expander"
msgstr "Expansor"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
msgid "The window title of the file chooser dialog"
msgstr "El título de la ventana del diálogo de selección de archivos"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:158
#: ../glade/gnome/gnomefileentry.c:109
msgid "Action:"
msgstr "Acción:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:89
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr "El tipo de operación de archivo que se está efectuando"

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
msgid "Local Only:"
msgstr "Sólo local:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether the selected files should be limited to local files"
msgstr ""
"Indica si los archivos seleccionados deberían ser limitados a los archivos "
"locales"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:165
msgid "Show Hidden:"
msgstr "Mostrar ocultos:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:166
msgid "Whether the hidden files and folders should be displayed"
msgstr "Indica si los archivos y carpetas ocultos deben ser mostrados"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#: ../glade/gbwidgets/gbfilechooserdialog.c:167
msgid "Confirm:"
msgstr "Confirmar:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:96
#: ../glade/gbwidgets/gbfilechooserdialog.c:168
msgid ""
"Whether a confirmation dialog will be displayed if a file will be overwritten"
msgstr ""
"Indica si se debe mostrar un diálogo de confirmación si un archivo se va a "
"sobreescribir"

#: ../glade/gbwidgets/gbfilechooserbutton.c:97
#: ../glade/gbwidgets/gblabel.c:201
msgid "Width in Chars:"
msgstr "Anchura en caracteres:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:98
msgid "The width of the button in characters"
msgstr "La anchura del botón en caracteres"

#: ../glade/gbwidgets/gbfilechooserbutton.c:296
msgid "File Chooser Button"
msgstr "Botón del «Selector de archivos»"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
msgid "Select Multiple:"
msgstr "Selección múltiple:"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether to allow multiple files to be selected"
msgstr "Indica si se permite seleccionar múltiples archivos"

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
msgid "File Chooser"
msgstr "Selector de archivos"

#: ../glade/gbwidgets/gbfilechooserdialog.c:435
msgid "File Chooser Dialog"
msgstr "Diálogo de selección de archivo"

#: ../glade/gbwidgets/gbfileselection.c:72 ../glade/property.c:1366
msgid "Select File"
msgstr "Seleccionar archivo"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "File Ops.:"
msgstr "Ops. de archivos:"

#: ../glade/gbwidgets/gbfileselection.c:115
msgid "If the file operation buttons are shown"
msgstr "Indica si se muestran los botones de operaciones sobre archivos"

#: ../glade/gbwidgets/gbfileselection.c:293
msgid "File Selection Dialog"
msgstr "Diálogo de selección de archivo"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "La coordenada X del widget en GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "La coordenada Y del widget en GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Posiciones estáticas"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "El título del diálogo de selección de tipografía"

#: ../glade/gbwidgets/gbfontbutton.c:70
msgid "Show Style:"
msgstr "Estilo de representación:"

#: ../glade/gbwidgets/gbfontbutton.c:71
msgid "If the font style is shown as part of the font information"
msgstr ""
"Si el estilo tipográfico se muestra como parte de la información de la "
"tipografía"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Mostrar tamaño:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr ""
"Si el tamaño tipográfico se muestra como parte de la información de la "
"tipografía"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Emplear tipografía:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr ""
"Si la tipografía seleccionada se empleará cuando se muestre la información "
"de la tipografía"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Usar tamaño:"

#: ../glade/gbwidgets/gbfontbutton.c:77
msgid "if the selected font size is used when displaying the font information"
msgstr ""
"Si el tamaño de la tipografía seleccionada se empleará cuando se muestre la "
"información de la tipografía"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Seleccione una tipografía"

#: ../glade/gbwidgets/gbfontbutton.c:268
msgid "Font Chooser Button"
msgstr "Botón de selección de tipografía"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Texto de muestra:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "El texto de muestra a ver"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Selección de la tipografía"

#: ../glade/gbwidgets/gbfontselectiondialog.c:70
msgid "Select Font"
msgstr "Seleccione una tipografía"

#: ../glade/gbwidgets/gbfontselectiondialog.c:301
msgid "Font Selection Dialog"
msgstr "Diálogo de selección de la tipografía"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Marco"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Tipo inicial:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "El tipo inicial de la curva"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Curva gamma"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "El tipo de sombra alrededor de la caja manipulable"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "Posición del manipulador:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "La posición del manipulador"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Ajustar borde:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "Borde de la caja manipulable que se ajusta en la posición"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "Caja manipulable"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Nueva caja horizontal"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Tamaño:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "El número de widgets en la caja"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Homogéneo:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Si los hijos deben tener el mismo tamaño"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "El espacio entre cada hijo"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "No es posible borrar ningún hijo."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:256
msgid "Position:"
msgstr "Posición:"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "La posición del widget relativa a sus hermanos"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Separación:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "La separación del widget"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Expandir:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "Permitir al widget expandirse"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Rellenar:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "Permitir al widget que rellene el área ocupada"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Empaquetar al comienzo:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "Si está activado empaqueta el widget al comienzo de la caja"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Insertar antes"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Insertar después"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Caja horizontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Nueva caja de botones horizontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "El número de botones"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Colocación:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "El estilo en que se colocan los botones"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "El espacio entre los botones"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Caja de botones horizontal"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "La posición del divisor"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:285
msgid "Shrink:"
msgstr "Encoger:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "Si está establecido a «true» permite que el widget pueda encogerse"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Redimensionar:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "Si está activado le permite al widget redimensionarse"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Panel horizontal"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Métrica:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "Las unidades de la regla"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Valor mínimo:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "El valor mínimo de la regla"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Valor máximo:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "El valor máximo de la regla"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "La posición actual de la regla"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4833
msgid "Max:"
msgstr "Máx:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "El valor máximo de la regla"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Regla horizontal"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Mostrar valor:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Si el valor de la escala es mostrado"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Dígitos:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "El número de dígitos a mostrar"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Pos. valor:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "La posición del valor"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Directiva:"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "Directiva de modificación de la escala"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Invertido:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Indica si el valores del rango están invertidos"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Escala horizontal"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "La directiva de actualización de la barra de desplazamiento"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Barra de desplazamiento horizontal"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Separador horizontal"

#: ../glade/gbwidgets/gbiconview.c:107
#, c-format
msgid "Icon %i"
msgstr "Icono %i"

#: ../glade/gbwidgets/gbiconview.c:129
msgid "The selection mode of the icon view"
msgstr "El modo de selección de la vista de iconos"

#: ../glade/gbwidgets/gbiconview.c:131 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "Orientación:"

#: ../glade/gbwidgets/gbiconview.c:132
msgid "The orientation of the icons"
msgstr "La orientación de los iconos"

#: ../glade/gbwidgets/gbiconview.c:134 ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Reordenable:"

#: ../glade/gbwidgets/gbiconview.c:135
msgid "If the view can be reordered using Drag and Drop"
msgstr "Si la vista puede ser reordenada usando arrastrar y soltar"

#: ../glade/gbwidgets/gbiconview.c:308
msgid "Icon View"
msgstr "Vista de iconos"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:301
msgid "Named Icon:"
msgstr "Icono con nombre:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:302
msgid "The named icon to use"
msgstr "El icono con nombre a usar"

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Tamaño del icono:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "El tamaño del icono de inventario"

#: ../glade/gbwidgets/gbimage.c:115
msgid "Pixel Size:"
msgstr "Tamaño de píxel:"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""
"El tamaño del icono nombrado en píxeles, o -1 para usar la propiedad «Tamaño "
"del icono»"

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "La alineación horizontal"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "La alineación vertical"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Imagen"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "El elemento del menú de inventario no es válido"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Elemento de menú con una imagen"

#: ../glade/gbwidgets/gbinputdialog.c:257
msgid "Input Dialog"
msgstr "Diálogo de entrada"

#: ../glade/gbwidgets/gblabel.c:170
msgid "Use Underline:"
msgstr "Usar subrayado:"

#: ../glade/gbwidgets/gblabel.c:171
msgid "If the text includes an underlined access key"
msgstr "Indica si el texto incluye una combinación de teclas subrayada"

#: ../glade/gbwidgets/gblabel.c:172
msgid "Use Markup:"
msgstr "Usar marcado:"

#: ../glade/gbwidgets/gblabel.c:173
msgid "If the text includes pango markup"
msgstr "Indica si el texto incluye marcado de Pango"

#: ../glade/gbwidgets/gblabel.c:174
msgid "Justify:"
msgstr "Justificación:"

#: ../glade/gbwidgets/gblabel.c:175
msgid "The justification of the lines of the label"
msgstr "La justificación de las líneas de la etiqueta"

#: ../glade/gbwidgets/gblabel.c:177
msgid "Wrap Text:"
msgstr "Ajuste de texto:"

#: ../glade/gbwidgets/gblabel.c:178
msgid "If the text is wrapped to fit within the width of the label"
msgstr ""
"Si el texto es ajustado para que quepa dentro de la longitud de la etiqueta"

#: ../glade/gbwidgets/gblabel.c:179
msgid "Selectable:"
msgstr "Seleccionable:"

#: ../glade/gbwidgets/gblabel.c:180
msgid "If the label text can be selected with the mouse"
msgstr "Indica si el texto puede ser seleccionado con el ratón"

#: ../glade/gbwidgets/gblabel.c:182
msgid "The horizontal alignment of the entire label"
msgstr "Alineación horizontal de toda la etiqueta"

#: ../glade/gbwidgets/gblabel.c:185
msgid "The vertical alignment of the entire label"
msgstr "La alineación vertical de toda la etiqueta"

#: ../glade/gbwidgets/gblabel.c:191
msgid "Focus Target:"
msgstr "Objetivo del foco:"

#: ../glade/gbwidgets/gblabel.c:192
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"El widget al que se establecerá el foco del teclado cuando la combinación de "
"teclas sea usada"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:146
msgid "Ellipsize:"
msgstr "Elipsis:"

#: ../glade/gbwidgets/gblabel.c:199 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr "Cómo hacer la elipsis de una cadena"

#: ../glade/gbwidgets/gblabel.c:202
msgid "The width of the label in characters"
msgstr "La anchura de la etiqueta en caracteres"

#: ../glade/gbwidgets/gblabel.c:204
msgid "Single Line Mode:"
msgstr "Modo de línea única:"

#: ../glade/gbwidgets/gblabel.c:205
msgid "If the label is only given enough height for a single line"
msgstr "Si la etiqueta se le da suficiente altura para una sola línea"

#: ../glade/gbwidgets/gblabel.c:206
msgid "Angle:"
msgstr "Ángulo:"

#: ../glade/gbwidgets/gblabel.c:207
msgid "The angle of the label text"
msgstr "El ángulo del texto de la etiqueta"

#: ../glade/gbwidgets/gblabel.c:333 ../glade/gbwidgets/gblabel.c:348
#: ../glade/gbwidgets/gblabel.c:616
msgid "Auto"
msgstr "Automático"

#: ../glade/gbwidgets/gblabel.c:872 ../glade/glade_menu_editor.c:411
msgid "Label"
msgstr "Etiqueta"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Anchura del área:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "La anchura del área de colocación"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Altura del área:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "La altura del área de colocación"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "La coordenada X del widget en GtkLayout"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "La coordenada Y del widget en GtkLayout"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Colocación"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "El modo de selección de la lista"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Lista"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Elemento de la lista"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Menú emergente"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:215
msgid "_File"
msgstr "_Archivo"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:223 ../glade/glade_project_window.c:692
msgid "_Edit"
msgstr "_Editar"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:229 ../glade/glade_project_window.c:721
msgid "_View"
msgstr "_Ver"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:231 ../glade/glade_project_window.c:834
msgid "_Help"
msgstr "Ay_uda"

#: ../glade/gbwidgets/gbmenubar.c:232
msgid "_About"
msgstr "A_cerca de"

#: ../glade/gbwidgets/gbmenubar.c:291
msgid "Pack Direction:"
msgstr "Dirección de empaquetado:"

#: ../glade/gbwidgets/gbmenubar.c:292
msgid "The pack direction of the menubar"
msgstr "La dirección de empaquetado de la barra de menú"

#: ../glade/gbwidgets/gbmenubar.c:294
msgid "Child Direction:"
msgstr "Dirección hija:"

#: ../glade/gbwidgets/gbmenubar.c:295
msgid "The child pack direction of the menubar"
msgstr "La dirección de empaquetado de la hija de la barra de menú"

#: ../glade/gbwidgets/gbmenubar.c:300 ../glade/gbwidgets/gbmenubar.c:418
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Editar menús..."

#: ../glade/gbwidgets/gbmenubar.c:541
msgid "Menu Bar"
msgstr "Barra de menú"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Elemento del menú"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
msgid "Show Horizontal:"
msgstr "Mostrar horizontal:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
msgid "If the item is visible when the toolbar is horizontal"
msgstr ""
"Indica si el elemento es visible cuando la barra de herramientas es "
"horizontal"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
msgid "Show Vertical:"
msgstr "Mostrar vertical:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
msgid "If the item is visible when the toolbar is vertical"
msgstr ""
"Indica si el elemento es visible cuando la barra de herramientas es vertical"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr "Es importante:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""
"Indica si el texto del elemento debería mostrarse cuando el modo de la barra "
"de herramientas es GTK_TOOLBAR_BOTH_HORIZ"

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
msgid "Toolbar Button with Menu"
msgstr "Botón barra de herramientas con menú"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Cuaderno nuevo"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:125
msgid "Number of pages:"
msgstr "Número de páginas:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "Mostrar solapas:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Si se muestran las solapas del cuaderno"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Mostrar borde:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "Si se muestra el borde del cuaderno, cuando no se muestren las solapas"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Pos. solapas:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "La posición de las solapas del cuaderno"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Desplazable:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Si las solapas del cuaderno se pueden desplazar"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Borde solapa horz.:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "El tamaño del borde de las solapas horizontales del cuaderno"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Borde solapa vert.:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "El tamaño del borde de las solapas verticales del cuaderno"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Mostrar menú emergente:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "Si el menú emergente está activo"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Número de páginas:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "Número de páginas del cuaderno"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Página anterior"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "Página siguiente"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Borrar página"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "Cambiar a la siguiente"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Cambiar a la anterior"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Insertar página después"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Insertar página antes"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "La posición de la página en la lista de páginas"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Establecer a verdadero para permitir a la solapa expandirse"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr ""
"Establecer a verdadero para permitir a la solapa rellenar el área ocupada"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr ""
"Establecer a verdadero para empaquetar las solapas al comienzo de un "
"cuaderno."

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Etiqueta de menú:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "El texto a mostrar en el menú emergente"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Clasificador"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "No se puede añadir %s a un GtkOptionMenu."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Menú de opciones"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Color:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "Si la vista previa es en color o en escala de grises"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "Si la vista previa se expande hasta llenar el espacio ocupado"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Vista previa"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "La orientación de los contenidos de la barra de progreso"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Fracción:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "La fracción del trabajo que se ha completado"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Paso de pulsación:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""
"La fracción de la longitud de la barra de progreso para mover el bloque "
"saliente al ser pulsado"

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "El texto a mostrar en la barra de progreso"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Mostrar texto:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "Si el texto tiene que ser mostrado en la barra de progreso"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Modo de actividad:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "Si la barra de progreso debe activar como el frontal del coche Kit"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "La alineación horizontal del texto"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "La alineación vertical del texto"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "Barra de progreso"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "Si el botón de radio está inicialmente activo"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1039
msgid "Group:"
msgstr "Grupo:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr ""
"Grupo de botones de radio (de forma predeterminada, todos los botones de "
"radio con el mismo padre)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Grupo nuevo"

#: ../glade/gbwidgets/gbradiobutton.c:465
msgid "Radio Button"
msgstr "Botón de radio"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "Si el botón de radio está inicialmente seleccionado"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"Grupo de botones de radio (el valor predeterminado es que los botones de "
"radio tienen el mismo padre)"

#: ../glade/gbwidgets/gbradiomenuitem.c:388
msgid "Radio Menu Item"
msgstr "Elemento de menú radio"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr ""
"Grupo de botones de radio (de forma predeterminada, todos los botones de "
"radio en la barra de herramientas)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:530
msgid "Toolbar Radio Button"
msgstr "Botón de radio de barra de herramientas"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "Directiva h:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Cuando la barra de desplazamiento horizontal será mostrada"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "Directiva v:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Cuando la barra de desplazamiento vertical será mostrada"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Posición de la ventana"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr ""
"Indica si la posición del ventana hija se ubica respecto a las barras de "
"desplazamiento"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Tipo de sombra:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "La política de modificación de la barra de desplazamiento vertical"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Ventana de desplazamiento"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Separador de menú"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr "Dibujar:"

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr "Si se dibuja el separador, o sólo un blanco"

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
msgid "Toolbar Separator Item"
msgstr "Elemento separador de barra de herramientas"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Tasa de incremento:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr ""
"La tasa de incremento del botón de incremento, usado junto con el incremento "
"de página"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "Número de decimales a mostrar"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Numeración:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr "Si sólo se permite una entrada numérica"

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Directiva de actualización:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Cuando es emitida la señal «value_changed»"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Redondeo:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "Si el valor es redondeado a múltiplos del incremento de paso"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Bucle:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "Si el valor pasa del máximo al mínimo"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Botón de Incremento"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Pulsador de redimensionado:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr ""
"Indica si la barra de estado tiene un asa de redimensionado para "
"redimensionar la ventana."

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Barra de estado"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Nueva tabla"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Número de filas:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Filas:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "Número de filas en la tabla"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Columnas:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "Número de columnas en la tabla"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Si los hijos deben ser del mismo tamaño"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Espaciado de filas:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "Espacio entre cada fila"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Espaciado col:"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "Espacio entre cada columna"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Celda X:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "La posición horizontal del widget en la tabla"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Celda Y:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "La posición vertical del widget en la tabla"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Expansión de columnas:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "Número de columnas ocupadas por el widget en la tabla"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Expansión entre filas:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "Número de filas ocupadas por el widget en la tabla"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "Margen Horiz:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "Margen Vert.:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "Expand. en X:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "Permitir al widget expandirse horizontalmente"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Expand. en Y:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "Permitir al widget expandirse verticalmente"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "Dismi. en X:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "Permitir al widget disminuir horizontalmente"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Dismi. en Y:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "Permitir al widget disminuir verticalmente"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "Rell. en X:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr "Permitir al widget rellenar el área horizontal ocupada"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Rell. en Y:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "Permitir al widget rellenar el área vertical ocupada"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Insertar fila antes"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Insertar fila después"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Insertar columna antes"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Insertar columna después"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Borrar fila"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Borrar columna"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Tabla"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Centro"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Rellenar"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:543 ../glade/glade_menu_editor.c:830
#: ../glade/glade_menu_editor.c:1345 ../glade/glade_menu_editor.c:2255
#: ../glade/property.c:2432
msgid "None"
msgstr "Ninguno"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Carácter"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Palabra"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Cursor visible:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Indica si el cursor es visible"

#: ../glade/gbwidgets/gbtextview.c:119
msgid "Overwrite:"
msgstr "Sobreescribir:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr "Si el texto introducido sobreescribe el texto existente"

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr "Acepta tabulación:"

#: ../glade/gbwidgets/gbtextview.c:122
msgid "If tab characters can be entered"
msgstr "Si pueden introducirse caracteres de tabulación"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Justificación:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "La justificación del texto"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Autoajuste:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "Longitud máxima del texto"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Espaciado superior:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Píxeles de espacio en blanco ante de los párrafos"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Espaciado inferior:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Píxeles en espacio en blanco después de los párrafos"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Espaciado interior:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Pixels de espacio en blanco entre líneas ajustadas en un párrafo"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Margen izquierdo:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Ancho del margen izquierdo en píxeles"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Margen derecho:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Ancho del margen derecho en píxeles"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Sangrado:"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Cantidad de píxeles para sangrar los párrafos"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Vista del texto"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "Si el botón de dos estados está inicialmente activo"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Botón de dos estados"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
msgid "Toolbar Toggle Button"
msgstr "Botón de conmutación de barra de herramientas"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Nueva barra de herramientas"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Número de elementos:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "El número de elementos en la barra de herramientas"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "Orientación de la barra de herramientas"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Estilo:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "Estilo de la barra de herramientas"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Consejos:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Si los consejos están activos"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "Show Arrow:"
msgstr "Mostrar flecha:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""
"Indica si se debe mostrar una flecha para emerger un menú si no cabe toda la "
"barra de herramientas"

#: ../glade/gbwidgets/gbtoolbar.c:427
msgid "If the item should be the same size as other homogeneous items"
msgstr ""
"Si el elemento debería ser del mismo tamaño que otros elementos homogéneos"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Insertar antes"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Insertar después"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Barra de herramientas"

#: ../glade/gbwidgets/gbtoolbutton.c:586
msgid "Toolbar Button"
msgstr "Botón barra de herramientas"

#: ../glade/gbwidgets/gbtoolitem.c:201
msgid "Toolbar Item"
msgstr "Elemento de barra de herramientas"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Columna 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Columna 2"

#: ../glade/gbwidgets/gbtreeview.c:87
msgid "Column 3"
msgstr "Columna 3"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr "Fila %i"

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Cabeceras visibles:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Indica si se deben mostrar los botones de cabecera de columna"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Indicación de reglas:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""
"Indica si se establece una indicación de forma que el motor de temas deba "
"dibujar filas en colores alternos."

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Indica si la vista es reordenable"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Activar búsqueda:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr ""
"Indica si el usuario puede buscar interactivamente a través de las columnas"

#: ../glade/gbwidgets/gbtreeview.c:123
msgid "Fixed Height Mode:"
msgstr "Modo de altura fija:"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr "Pone todas las filas a la misma altura para mejorar el rendimiento"

#: ../glade/gbwidgets/gbtreeview.c:125
msgid "Hover Selection:"
msgstr "Selección al pasar por encima:"

#: ../glade/gbwidgets/gbtreeview.c:126
msgid "Whether the selection should follow the pointer"
msgstr "Indica si la selección debe seguir el puntero"

#: ../glade/gbwidgets/gbtreeview.c:127
msgid "Hover Expand:"
msgstr "Expandir al pasar por encima:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""
"Indica si las filas deben expandirse o contraerse cuando el puntero se mueve "
"sobre ellas"

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Lista o vista de árbol"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Caja vertical nueva"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Caja vertical"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Caja de botones vertical nueva"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Caja de botones vertical"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "El tipo de sombra de la vista"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Vista"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Panel vertical"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Regleta vertical"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Escala vertical"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Barra de desplazamiento vertical"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Separador vertical"

#: ../glade/gbwidgets/gbwindow.c:244
msgid "The title of the window"
msgstr "El título de la ventana"

#: ../glade/gbwidgets/gbwindow.c:247
msgid "The type of the window"
msgstr "El tipo de la ventana"

#: ../glade/gbwidgets/gbwindow.c:251
msgid "Type Hint:"
msgstr "Indicación de consejos:"

#: ../glade/gbwidgets/gbwindow.c:252
msgid "Tells the window manager how to treat the window"
msgstr "La dice al al gestor de ventanas cómo tratar la ventana"

#: ../glade/gbwidgets/gbwindow.c:257
msgid "The initial position of the window"
msgstr "La posición inicial de la ventana"

#: ../glade/gbwidgets/gbwindow.c:261 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Modal:"

#: ../glade/gbwidgets/gbwindow.c:261
msgid "If the window is modal"
msgstr "Si la ventana es modal"

#: ../glade/gbwidgets/gbwindow.c:266
msgid "Default Width:"
msgstr "Ancho predet.:"

#: ../glade/gbwidgets/gbwindow.c:267
msgid "The default width of the window"
msgstr "La anchura predeterminada de la ventana"

#: ../glade/gbwidgets/gbwindow.c:271
msgid "Default Height:"
msgstr "Alto predet.:"

#: ../glade/gbwidgets/gbwindow.c:272
msgid "The default height of the window"
msgstr "La altura predeterminada de la ventana"

#: ../glade/gbwidgets/gbwindow.c:278
msgid "Resizable:"
msgstr "Redimensionable:"

#: ../glade/gbwidgets/gbwindow.c:279
msgid "If the window can be resized"
msgstr "Indica si la ventana puede cambiar de tamaño"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be shrunk"
msgstr "Si la ventana se puede disminuir"

#: ../glade/gbwidgets/gbwindow.c:287
msgid "Grow:"
msgstr "Agrandar:"

#: ../glade/gbwidgets/gbwindow.c:288
msgid "If the window can be enlarged"
msgstr "Si la ventana se puede agrandar"

#: ../glade/gbwidgets/gbwindow.c:293
msgid "Auto-Destroy:"
msgstr "Auto destruir:"

#: ../glade/gbwidgets/gbwindow.c:294
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Indica si la ventana se destruye cuando su ventana madre es destruida."

#: ../glade/gbwidgets/gbwindow.c:298
msgid "The icon for this window"
msgstr "El icono de esta ventana"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "Role:"
msgstr "Rol:"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""
"Un identificador único para la ventana que será usado cuando se restablezca "
"una sesión"

#: ../glade/gbwidgets/gbwindow.c:308
msgid "Decorated:"
msgstr "Decorado:"

#: ../glade/gbwidgets/gbwindow.c:309
msgid "If the window should be decorated by the window manager"
msgstr "Si la ventana debe ser decorada por el gestor de ventanas"

#: ../glade/gbwidgets/gbwindow.c:312
msgid "Skip Taskbar:"
msgstr "Saltar barra de tareas:"

#: ../glade/gbwidgets/gbwindow.c:313
msgid "If the window should not appear in the task bar"
msgstr "Indica si la ventana no debe aparecer en la barra de tareas"

#: ../glade/gbwidgets/gbwindow.c:316
msgid "Skip Pager:"
msgstr "Saltar paginador:"

#: ../glade/gbwidgets/gbwindow.c:317
msgid "If the window should not appear in the pager"
msgstr "Indica si la ventana no debe aparecer en el paginador"

#: ../glade/gbwidgets/gbwindow.c:320
msgid "Gravity:"
msgstr "Gravedad:"

#: ../glade/gbwidgets/gbwindow.c:321
msgid "The reference point to use when the window coordinates are set"
msgstr ""
"El punto de referencia a usar cuando se establezcan las coordenadas de la "
"ventana"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "Focus On Map:"
msgstr "Enfocar al mapear:"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "If the window should receive the input focus when it is mapped"
msgstr "Si la ventana debe recibir el foco de entrada cuando se mapea"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "Urgency Hint:"
msgstr "Indicación de urgencia:"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "If the window should be brought to the user's attention"
msgstr "Si la ventana debe llamar la atención del usuario"

#: ../glade/gbwidgets/gbwindow.c:1232
msgid "Window"
msgstr "Ventana"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:75
msgid "Error"
msgstr "Error"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Error de sistema"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "Error abriendo archivo"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "Error leyendo archivo"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "Error escribiendo archivo"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "Directorio no válido"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "Valor no válido"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "Entidad XML no válida"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "Se esperaba inicio de elemento"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "Se esperaba final de elemento"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "Se esperaba dato tipo carácter"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "No se ha encontrado el identificador de clase"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Clase desconocida"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "Componente no válido"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "El archivo ha terminado antes de lo esperado"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Código de error desconocido"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Controlado por"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Controlador para"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Etiqueta para"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Etiquetado por"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Miembro de"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Nodo hijo de"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr "Fluye hacia"

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr "Fluye desde"

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr "Subventana de"

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr "Empotra"

#: ../glade/glade_atk.c:130
msgid "Embedded By"
msgstr "Empotrado por"

#: ../glade/glade_atk.c:131
msgid "Popup For"
msgstr "Emergente para"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr "Ventana madre de"

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Relación: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "Widget"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:773
#: ../glade/property.c:776
msgid "Name:"
msgstr "Nombre:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr "El nombre del widget que pasar a las tecnologías de accesibilidad"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "Descripción:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr "La descripción del widget a pasar a las tecnologías de accesibilidad"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Descripción de la tabla:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "La descripción de la tabla a pasar a las tecnologías de accesibilidad"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Selecciona los widgets con esta interrelación"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Pulsación"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Pulsar"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Liberación"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr ""
"Introduzca la descripción de la acción a pasar a las tecnologías de "
"accesibilidad"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Portapapeles"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Necesita seleccionar un widget sobre el cual pegar"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "No puede pegar dentro de ventanas o diálogos"

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"No puede pegar sobre el widget seleccionado ya que\n"
"ha sido creado automáticamente por su padre."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr ""
"Solo se pueden pegar elementos de menú en un menú o en una barra de menús."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr "Solo se pueden pegar botones en el área de acción de un diálogo."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "Solo se pueden pegar widgets GnomeDockItem en un GnomeDock."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "Solo se pueden pegar widgets GnomeDockItem sobre un GnomeDockItem."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "Disculpe - pegar sobre un GnomeDockItem no está implementado todavía."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "Los widgets GnomeDockItem solo pueden ser pegados en un GnomeDock"

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:633
msgid "_New"
msgstr "_Nuevo"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Crear un archivo nuevo"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "_Obsoletos"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "GTK+ _Básico"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "GTK+ _Adicional"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Seleccionar tecla aceleradora"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Teclas"

#: ../glade/glade_menu_editor.c:395
msgid "Menu Editor"
msgstr "Editor de menú"

#: ../glade/glade_menu_editor.c:412
msgid "Type"
msgstr "Tipo"

#: ../glade/glade_menu_editor.c:413
msgid "Accelerator"
msgstr "Acelerador"

#: ../glade/glade_menu_editor.c:414
msgid "Name"
msgstr "Nombre"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:1499
msgid "Handler"
msgstr "Manejador"

#: ../glade/glade_menu_editor.c:416 ../glade/property.c:102
msgid "Active"
msgstr "Activo"

#: ../glade/glade_menu_editor.c:417
msgid "Group"
msgstr "Grupo"

#: ../glade/glade_menu_editor.c:418
msgid "Icon"
msgstr "Icono"

#: ../glade/glade_menu_editor.c:459
msgid "Move the item and its children up one place in the list"
msgstr "Mueve el elemento y sus hijos una posición arriba en la lista"

#: ../glade/glade_menu_editor.c:471
msgid "Move the item and its children down one place in the list"
msgstr "Mueve el elemento y sus hijos una posición abajo en la lista"

#: ../glade/glade_menu_editor.c:483
msgid "Move the item and its children up one level"
msgstr "Mueve el elemento y sus hijos un nivel hacia arriba"

#: ../glade/glade_menu_editor.c:495
msgid "Move the item and its children down one level"
msgstr "Mueve el elemento y sus hijos un nivel hacia abajo"

#: ../glade/glade_menu_editor.c:525
msgid "The stock item to use."
msgstr "El elemento de inventario a utilizar."

#: ../glade/glade_menu_editor.c:528 ../glade/glade_menu_editor.c:643
msgid "Stock Item:"
msgstr "Elemento de inventario:"

#: ../glade/glade_menu_editor.c:641
msgid "The stock Gnome item to use."
msgstr "El elemento de inventario Gnome a utilizar."

#: ../glade/glade_menu_editor.c:746
msgid "The text of the menu item, or empty for separators."
msgstr "El texto del elemento de menú, o vacío para separadores."

#: ../glade/glade_menu_editor.c:770 ../glade/property.c:777
msgid "The name of the widget"
msgstr "El nombre del widget"

#: ../glade/glade_menu_editor.c:791
msgid "The function to be called when the item is selected"
msgstr "La función a llamar cuando el elemento es seleccionado"

#: ../glade/glade_menu_editor.c:793 ../glade/property.c:1547
msgid "Handler:"
msgstr "Manejador:"

#: ../glade/glade_menu_editor.c:812
msgid "An optional icon to show on the left of the menu item."
msgstr "Icono opcional a mostrar a la izquierda del elemento del menú."

#: ../glade/glade_menu_editor.c:935
msgid "The tip to show when the mouse is over the item"
msgstr "Sugerencia a mostrar si el ratón está sobre el elemento"

#: ../glade/glade_menu_editor.c:937 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Sugerencia:"

#: ../glade/glade_menu_editor.c:958
msgid "_Add"
msgstr "_Añadir"

#: ../glade/glade_menu_editor.c:963
msgid "Add a new item below the selected item."
msgstr "Añade un elemento nuevo debajo del seleccionado."

#: ../glade/glade_menu_editor.c:968
msgid "Add _Child"
msgstr "Añadir _hijo"

#: ../glade/glade_menu_editor.c:973
msgid "Add a new child item below the selected item."
msgstr "Añade un elemento hijo nuevo debajo del elemento seleccionado."

#: ../glade/glade_menu_editor.c:979
msgid "Add _Separator"
msgstr "Añadir _separador"

#: ../glade/glade_menu_editor.c:984
msgid "Add a separator below the selected item."
msgstr "Añade un separador debajo del elemento seleccionado."

#: ../glade/glade_menu_editor.c:989 ../glade/glade_project_window.c:242
msgid "_Delete"
msgstr "_Borrar"

#: ../glade/glade_menu_editor.c:994
msgid "Delete the current item"
msgstr "Borra el elemento actual"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:1000
msgid "Item Type:"
msgstr "Tipo de elemento:"

#: ../glade/glade_menu_editor.c:1016
msgid "If the item is initially on."
msgstr "Si el elemento está inicialmente activo."

#: ../glade/glade_menu_editor.c:1018
msgid "Active:"
msgstr "Activo:"

#: ../glade/glade_menu_editor.c:1023 ../glade/glade_menu_editor.c:1638
#: ../glade/property.c:2216 ../glade/property.c:2226
msgid "No"
msgstr "No"

#: ../glade/glade_menu_editor.c:1037
msgid "The radio menu item's group"
msgstr "El grupo de elementos de menú radio"

#: ../glade/glade_menu_editor.c:1054 ../glade/glade_menu_editor.c:2414
#: ../glade/glade_menu_editor.c:2554
msgid "Radio"
msgstr "Radio"

#: ../glade/glade_menu_editor.c:1061 ../glade/glade_menu_editor.c:2412
#: ../glade/glade_menu_editor.c:2552
msgid "Check"
msgstr "Marcar"

#: ../glade/glade_menu_editor.c:1068 ../glade/property.c:102
msgid "Normal"
msgstr "Normal"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1077
msgid "Accelerator:"
msgstr "Acelerador:"

#: ../glade/glade_menu_editor.c:1114 ../glade/property.c:1682
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1119 ../glade/property.c:1685
msgid "Shift"
msgstr "Mayús."

#: ../glade/glade_menu_editor.c:1124 ../glade/property.c:1688
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1129 ../glade/property.c:1695
msgid "Key:"
msgstr "Tecla:"

#: ../glade/glade_menu_editor.c:1135 ../glade/property.c:1674
msgid "Modifiers:"
msgstr "Modificadores:"

#: ../glade/glade_menu_editor.c:1638 ../glade/glade_menu_editor.c:2419
#: ../glade/glade_menu_editor.c:2562 ../glade/property.c:2216
msgid "Yes"
msgstr "Sí"

#: ../glade/glade_menu_editor.c:2008
msgid "Select icon"
msgstr "Seleccione un icono"

#: ../glade/glade_menu_editor.c:2353 ../glade/glade_menu_editor.c:2714
msgid "separator"
msgstr "separador"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3638 ../glade/glade_project_window.c:369
#: ../glade/property.c:5115
msgid "New"
msgstr "Nuevo"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Selector"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"El directorio del proyecto no se ha definido.\n"
"Defínalo empleando el diálogo Opciones del proyecto.\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"El directorio para el código fuente no se ha definido.\n"
"Defínalo empleando el diálogo Opciones del proyecto.\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"El directorio para el código fuente no es válido:\n"
"\n"
"El directorio para el código fuente debe ser el directorio del\n"
"proyecto o un subdirectorio de este.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"El directorio de las imágenes no se ha definido.\n"
"Defínalo empleando el diálogo Opciones del proyecto.\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr ""
"Disculpe - la generación de código fuente para %s no está implementado "
"todavía"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""
"Su proyecto usa widgets obsoletos que Gtkmm-2 no soporta\n"
"Busque en su proyecto estos widgets y use su reemplazos."

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"Se ha producido un error al ejecutar glade-- para generar el código fuente C+"
"+.\n"
"Compruebe que tiene instalado glade-- y que se encuentra en su variable "
"PATH.\n"
"Luego intente ejecutar «glade-- <archivo_proyecto.glade>» en un terminal."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"Se ha producido un error al ejecutar gate para generar el código fuente "
"Ada95.\n"
"Compruebe que tiene instalado gate y que se encuentra en su variable PATH.\n"
"Después intente ejecutar «gate <archivo_proyecto.glade>» en un terminal."

#: ../glade/glade_project.c:571
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"Se ha producido un error al ejecutar glade2perl para generar el código "
"fuente Perl.\n"
"Compruebe que tiene instalado glade2perl y que se encuentra en su variable "
"PATH.\n"
"Después intente ejecutar «glade2perl -- <archivo_proyecto.glade>» en un "
"terminal."

#: ../glade/glade_project.c:594
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"Se ha producido un error al ejecutar eglade para generar el código fuente "
"Eiffel.\n"
"Compruebe que tiene instalado eglade y que se encuentra en su variable "
"PATH.\n"
"Luego intente ejecutar «eglade-- <archivo_proyecto.glade>» en un terminal."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"El directorio de las imágenes pixmap no se ha establecido.\n"
"Establézcalo empleando el diálogo «Opciones del proyecto».\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "Error al escribir el archivo XML del proyecto\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:385
#: ../glade/glade_project_window.c:890
msgid "Project Options"
msgstr "Opciones del proyecto"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "General"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Opciones básicas:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "El directorio del proyecto"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Directorio del proyecto:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Examinar..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "El nombre del proyecto actual"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Nombre del proyecto:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "El nombre del programa"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "El archivo del proyecto"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Archivo del proyecto:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Subdirectorios:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "El directorio donde guardar el código fuente generado"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Directorio del código fuente:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "El directorio donde guardar las imágenes"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Directorio de imágenes:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "La licencia que será añadida al inicio de los archivos generados"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Lenguaje:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Activar soporte para Gnome"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Si se va a realizar una aplicación Gnome"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Activar soporte para Gnome DB"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Si se va a realizar una aplicación Gnome DB"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "Opciones de C"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""
"<b>Nota:</b> para aplicaciones grandes se recomienda el uso de libglade."

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Opciones generales:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Soporte para gettext"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "Si las cadenas de texto serán marcadas para ser traducidas por gettext"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "Establecer el nombre de los widgets"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Si el nombre de los widgets será fijado en el código fuente"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Guardar copia de respaldo de los archivos de código fuente"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Si se guardarán copias de los archivos de código fuente modificados"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Activar soporte para la ayuda de Gnome"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Si se debe incluir soporte para el sistema de ayuda de Gnome"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Opciones de archivos de salida:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "Generar el archivo main.c"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr ""
"Si se genera un archivo main.c que contenga la función main(), a no ser que "
"ya exista uno"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Generar funciones de apoyo"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Si se generan las funciones de apoyo"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Generar los archivos de construcción"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Si se generan los archivos necesarios para la compilación del código fuente, "
"incluyendo Makefile.am y configure.in, a no ser que ya existan"

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "Funciones para la creación de la interfaz:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr ""
"El archivo donde se escribirán las funciones necesarias para crear la "
"interfaz"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Archivo fuente:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr ""
"El archivo donde se escribirán las declaraciones de las funciones necesarias "
"para crear la interfaz"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Archivo de cabecera:"

#: ../glade/glade_project_options.c:594
msgid "Source file for interface creation functions"
msgstr "Archivo fuente para las funciones de creación del interfaz"

#: ../glade/glade_project_options.c:595
msgid "Header file for interface creation functions"
msgstr "Archivo cabecera para las funciones de creación del interfaz"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Funciones manejadoras de señales y de callback:"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr ""
"El archivo donde se escribirán las funciones de los manejadores de señales y "
"de callback vacíos"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"El archivo donde se escribirán las declaraciones de las funciones de los "
"manejadores de señales y de callbacks"

#: ../glade/glade_project_options.c:640
msgid "Source file for signal handler and callback functions"
msgstr "Archivo fuente para las funciones manejadoras de señales y de callback"

#: ../glade/glade_project_options.c:641
msgid "Header file for signal handler and callback functions"
msgstr ""
"El archivo cabecera para las funciones de los manejadores de señales y de "
"callback"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Funciones de apoyo:"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "El archivo donde se escribirán las funciones de apoyo"

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr ""
"El archivo donde se escribirán las declaraciones de las funciones de apoyo"

#: ../glade/glade_project_options.c:686
msgid "Source file for support functions"
msgstr "Archivo fuente para las funciones de apoyo"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr "Archivo cabecera para las funciones de apoyo"

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "Opciones de libGlade"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Cadenas traducibles:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr "<b>Nota:</b> esta opción es obsoleta - use intltool en su lugar."

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Guardar cadenas traducibles"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Indica si se guardarán las cadenas traducibles en un archivo de código C "
"separado, para habilitar la traducción de las interfaces cargadas por "
"libglade"

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "El archivo de código C en el que guardar todas las cadenas traducibles"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Archivo:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Seleccione el directorio del proyecto"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Necesita indicar un archivo para almacenar las cadenas traducibles"

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Necesita indicar el directorio del proyecto"

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Necesita indicar un archivo para almacenar el proyecto"

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Necesita indicar un nombre para el proyecto"

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Necesita indicar un nombre para el programa"

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Necesita indicar el directorio para el código fuente"

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Necesita indicar el directorio para las imágenes"

#: ../glade/glade_project_window.c:187
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"No se pudo encontrar el archivo de ayuda: %s\n"
"\n"
"Error: %s"

#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:635
msgid "Create a new project"
msgstr "Crea un proyecto nuevo"

#: ../glade/glade_project_window.c:219 ../glade/glade_project_window.c:655
#: ../glade/glade_project_window.c:906
msgid "_Build"
msgstr "_Construir"

#: ../glade/glade_project_window.c:220 ../glade/glade_project_window.c:666
msgid "Output the project source code"
msgstr "Escribe el código fuente del proyecto"

#: ../glade/glade_project_window.c:226 ../glade/glade_project_window.c:669
msgid "Op_tions..."
msgstr "O_pciones"

#: ../glade/glade_project_window.c:227 ../glade/glade_project_window.c:678
msgid "Edit the project options"
msgstr "Edita las opciones del proyecto"

#: ../glade/glade_project_window.c:242 ../glade/glade_project_window.c:717
msgid "Delete the selected widget"
msgstr "Borra el widget seleccionado"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:728
msgid "Show _Palette"
msgstr "Mostrar _paleta"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:733
msgid "Show the palette of widgets"
msgstr "Muestra la paleta de widgets"

#: ../glade/glade_project_window.c:266 ../glade/glade_project_window.c:738
msgid "Show Property _Editor"
msgstr "Mostrar _editor de propiedades"

#: ../glade/glade_project_window.c:267 ../glade/glade_project_window.c:744
msgid "Show the property editor"
msgstr "Muestra el editor de propiedades"

#: ../glade/glade_project_window.c:273 ../glade/glade_project_window.c:748
msgid "Show Widget _Tree"
msgstr "Mostrar á_rbol de widgets"

#: ../glade/glade_project_window.c:274 ../glade/glade_project_window.c:754
#: ../glade/main.c:82 ../glade/main.c:116
msgid "Show the widget tree"
msgstr "Muestra el árbol de widget del proyecto"

#: ../glade/glade_project_window.c:280 ../glade/glade_project_window.c:758
msgid "Show _Clipboard"
msgstr "Mostrar p_ortapapeles"

#: ../glade/glade_project_window.c:281 ../glade/glade_project_window.c:764
#: ../glade/main.c:86 ../glade/main.c:120
msgid "Show the clipboard"
msgstr "Muestra el contenido del portapapeles"

#: ../glade/glade_project_window.c:299
msgid "Show _Grid"
msgstr "Mostrar re_jilla"

#: ../glade/glade_project_window.c:300 ../glade/glade_project_window.c:800
msgid "Show the grid (in fixed containers only)"
msgstr "Muestra la rejilla (en los contenedores estáticos solamente)"

#: ../glade/glade_project_window.c:306
msgid "_Snap to Grid"
msgstr "_Alinear a la rejilla"

#: ../glade/glade_project_window.c:307
msgid "Snap widgets to the grid"
msgstr "Alinea los widgets a la rejilla"

#: ../glade/glade_project_window.c:313 ../glade/glade_project_window.c:772
msgid "Show _Widget Tooltips"
msgstr "Mostrar consejos del _widget"

#: ../glade/glade_project_window.c:314 ../glade/glade_project_window.c:780
msgid "Show the tooltips of created widgets"
msgstr "Muestra los consejos de los widgets creados"

#: ../glade/glade_project_window.c:323 ../glade/glade_project_window.c:803
msgid "Set Grid _Options..."
msgstr "_Opciones de la rejilla..."

#: ../glade/glade_project_window.c:324
msgid "Set the grid style and spacing"
msgstr "Configura el estilo y el espaciado de la rejilla"

#: ../glade/glade_project_window.c:330 ../glade/glade_project_window.c:824
msgid "Set Snap O_ptions..."
msgstr "O_pciones de alineación..."

#: ../glade/glade_project_window.c:331
msgid "Set options for snapping to the grid"
msgstr "Configura las opciones de la alineación a la rejilla"

#: ../glade/glade_project_window.c:343
msgid "_FAQ"
msgstr "_FAQ"

#: ../glade/glade_project_window.c:344
msgid "View the Glade FAQ"
msgstr "Ver las preguntas frecuentes de Glade"

#. create File menu
#: ../glade/glade_project_window.c:358 ../glade/glade_project_window.c:626
msgid "_Project"
msgstr "_Proyecto"

#: ../glade/glade_project_window.c:369 ../glade/glade_project_window.c:873
#: ../glade/glade_project_window.c:1055
msgid "New Project"
msgstr "Proyecto nuevo"

#: ../glade/glade_project_window.c:374
msgid "Open"
msgstr "Abrir"

#: ../glade/glade_project_window.c:374 ../glade/glade_project_window.c:878
#: ../glade/glade_project_window.c:1116
msgid "Open Project"
msgstr "Abrir proyecto"

#: ../glade/glade_project_window.c:379
msgid "Save"
msgstr "Guardar"

#: ../glade/glade_project_window.c:379 ../glade/glade_project_window.c:882
#: ../glade/glade_project_window.c:1481
msgid "Save Project"
msgstr "Guardar proyecto"

#: ../glade/glade_project_window.c:385
msgid "Options"
msgstr "Opciones"

#: ../glade/glade_project_window.c:390
msgid "Build"
msgstr "Construir"

#: ../glade/glade_project_window.c:390
msgid "Build the Source Code"
msgstr "Escribir código fuente"

#: ../glade/glade_project_window.c:639
msgid "Open an existing project"
msgstr "Abre un proyecto existente"

#: ../glade/glade_project_window.c:643
msgid "Save project"
msgstr "Guardar proyecto"

#: ../glade/glade_project_window.c:688
msgid "Quit Glade"
msgstr "Salir de Glade"

#: ../glade/glade_project_window.c:702
msgid "Cut the selected widget to the clipboard"
msgstr "Corta, al portapapeles, el widget seleccionado"

#: ../glade/glade_project_window.c:707
msgid "Copy the selected widget to the clipboard"
msgstr "Copia, al portapapeles, el widget seleccionado"

#: ../glade/glade_project_window.c:712
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Pega el widget del portapapeles sobre el widget seleccionado"

#: ../glade/glade_project_window.c:784
msgid "_Grid"
msgstr "_Rejilla"

#: ../glade/glade_project_window.c:792
msgid "_Show Grid"
msgstr "_Mostrar rejilla"

#: ../glade/glade_project_window.c:809
msgid "Set the spacing between grid lines"
msgstr "Espaciado entre las líneas de la rejilla"

#: ../glade/glade_project_window.c:812
msgid "S_nap to Grid"
msgstr "Ali_near con la rejilla"

#: ../glade/glade_project_window.c:820
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "Alinea los widgets con la rejilla (solo en los contenedores estáticos)"

#: ../glade/glade_project_window.c:830
msgid "Set which parts of a widget snap to the grid"
msgstr "Selecciona que parte de un widget se alinea a la rejilla"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:855
msgid "_About..."
msgstr "_Acerca de..."

#: ../glade/glade_project_window.c:896
msgid "Optio_ns"
msgstr "Opcio_nes"

#: ../glade/glade_project_window.c:900
msgid "Write Source Code"
msgstr "Escribir código fuente..."

#: ../glade/glade_project_window.c:992 ../glade/glade_project_window.c:1697
#: ../glade/glade_project_window.c:1986
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:999
msgid "Are you sure you want to create a new project?"
msgstr "¿Está seguro que quiere crear un proyecto nuevo?"

#: ../glade/glade_project_window.c:1059
msgid "New _GTK+ Project"
msgstr "Proyecto _GTK+"

#: ../glade/glade_project_window.c:1060
msgid "New G_NOME Project"
msgstr "Proyecto G_NOME"

#: ../glade/glade_project_window.c:1063
msgid "Which type of project do you want to create?"
msgstr "¿Que tipo de proyecto quiere crear?"

#: ../glade/glade_project_window.c:1097
msgid "New project created."
msgstr "Proyecto nuevo creado."

#: ../glade/glade_project_window.c:1187
msgid "Project opened."
msgstr "Se ha abierto el proyecto."

#: ../glade/glade_project_window.c:1201
msgid "Error opening project."
msgstr "Se ha producido un error al abrir el proyecto."

#: ../glade/glade_project_window.c:1265
msgid "Errors opening project file"
msgstr "Errores al abrir el archivo de proyecto"

#: ../glade/glade_project_window.c:1271
msgid " errors opening project file:"
msgstr " errores se han producido al abrir el archivo de proyecto:"

#: ../glade/glade_project_window.c:1344
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"No hay ningún proyecto abierto.\n"
"Cree un proyecto nuevo con el comando Proyecto/Nuevo."

#: ../glade/glade_project_window.c:1548
msgid "Error saving project"
msgstr "Error al guardar el proyecto"

#: ../glade/glade_project_window.c:1550
msgid "Error saving project."
msgstr "Se ha producido un error al guardar el proyecto."

#: ../glade/glade_project_window.c:1556
msgid "Project saved."
msgstr "Proyecto guardado."

#: ../glade/glade_project_window.c:1626
msgid "Errors writing source code"
msgstr "Errores al escribir el código fuente"

#: ../glade/glade_project_window.c:1628
msgid "Error writing source."
msgstr "Se ha producido un error al escribir el código fuente."

#: ../glade/glade_project_window.c:1634
msgid "Source code written."
msgstr "Código fuente escrito."

#: ../glade/glade_project_window.c:1665
msgid "System error message:"
msgstr "Mensaje de error del sistema:"

#: ../glade/glade_project_window.c:1704
msgid "Are you sure you want to quit?"
msgstr "¿Está seguro que quiere salir?"

#: ../glade/glade_project_window.c:1988 ../glade/glade_project_window.c:2048
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "© 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1989 ../glade/glade_project_window.c:2047
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr "Glade es un constructor de interfaces de usuario para GTK+ y GNOME."

#: ../glade/glade_project_window.c:2018
msgid "About Glade"
msgstr "Acerca de Glade"

#: ../glade/glade_project_window.c:2103
msgid "<untitled>"
msgstr "<sin título>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Navegador de la base de datos"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Cuadro combinado vinculado a datos"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr "GnomeDbConnectionProperties"

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Selector de conexión"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "Configurador de DSN"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "Druida de configuración de DSN"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr "Resaltar texto:"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr "Si se selecciona, el texto será resaltado dentro del widget"

#: ../glade/gnome-db/gnomedbeditor.c:178
msgid "GnomeDbEditor"
msgstr "GnomeDbEditor"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Visualizador de errores de la base de datos"

#: ../glade/gnome-db/gnomedberrordlg.c:219
msgid "Database error dialog"
msgstr "Dialogo de errores de la base de datos"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Formulario"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr "Texto dentro de la barra gris"

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr "Barra gris"

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Rejilla vinculada a datos"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Lista vinculada a datos"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Widget de inicio de sesión en la base de datos"

#: ../glade/gnome-db/gnomedblogindlg.c:78
msgid "Login"
msgstr "Inicio de sesión"

#: ../glade/gnome-db/gnomedblogindlg.c:221
msgid "Database login dialog"
msgstr "Dialogo de inicio de sesión en la base de datos"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Selector del proveedor"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr "GnomeDbQueryBuilder"

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Selector de la fuente de datos"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Editor de tablas "

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Permitir flotar:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Si se permiten elementos de anclaje flotantes"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Agregar banda de anclaje arriba"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Agregar banda de anclaje abajo"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Agregar banda de anclaje a la izquierda"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Agregar banda de anclaje a la derecha"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Agregar elemento de anclaje flotante"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Anclaje de Gnome"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Bloqueado:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Si el elemento de anclaje está anclado en una posición"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Exclusivo:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "Si el elemento de anclaje es siempre el único elemento de su banda"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Nunca flotar:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "Si no se permite que el elemento de anclaje flote en su propia ventana"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Nunca vertical:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "Si no se permite situar el elemento de anclaje en vertical"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Nunca horizontal:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "Si no se permite situar el elemento de anclaje en horizontal"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "Tipo de sombra alrededor del elemento de anclaje"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "Orientación de un elemento de anclaje flotante"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Agregar elemento de anclaje antes de"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Agregar elemento de anclaje después de"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Elemento de anclaje de Gnome"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr ""
"Información adicional, como por ejemplo una descripción del paquete y su "
"página web"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Diálogo «Acerca de» de Gnome"

#: ../glade/gnome/gnomeapp.c:171
msgid "New File"
msgstr "Archivo nuevo"

#: ../glade/gnome/gnomeapp.c:173
msgid "Open File"
msgstr "Abrir un archivo"

#: ../glade/gnome/gnomeapp.c:175
msgid "Save File"
msgstr "Guardar archivo"

#: ../glade/gnome/gnomeapp.c:204
msgid "Status Bar:"
msgstr "Barra de estado:"

#: ../glade/gnome/gnomeapp.c:205
msgid "If the window has a status bar"
msgstr "Si la ventana tiene una barra de estado"

#: ../glade/gnome/gnomeapp.c:206
msgid "Store Config:"
msgstr "Almacenar configuración:"

#: ../glade/gnome/gnomeapp.c:207
msgid "If the layout is saved and restored automatically"
msgstr "Si el tamaño se guarda y se restaura automáticamente"

#: ../glade/gnome/gnomeapp.c:443
msgid "Gnome Application Window"
msgstr "Ventana de aplicación Gnome"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Mensaje de estado"

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "Progreso:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "Si la barra de la aplicación tiene un indicador de progreso"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Estado:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr ""
"Si la barra de la aplicación tiene un área para los mensajes de estado y "
"entradas del usuario"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Barra de aplicación Gnome"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Suavizado de bordes:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr ""
"Si el lienzo tiene suavizado de bordes, para suavizar los límites de texto y "
"gráficos"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "La coordenada x inferior"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "La coordenada y inferior"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2:"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "La coordenada x superior"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "La coordenada y superior"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Pixels por unidad"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "El número de pixels correspondientes a una unidad"

#: ../glade/gnome/gnomecanvas.c:248
msgid "GnomeCanvas"
msgstr "GnomeCanvas"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Entramado:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Si la muestra debería usar entramado de color para aproximarse mejor"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Seleccione un color"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Selector de color Gnome"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "No se pudo crear el control Bonobo"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Control Bonobo nuevo"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Selecciona un control Bonobo"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3902
msgid "Description"
msgstr "Descripción"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Control Bonobo"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Mostrar hora:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Si además de la fecha se muestra la hora"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "Formato 24 horas:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Si la hora se muestra en formato de 24 horas"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Hora de inicio:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "La primera hora a mostrar en el menú emergente"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Hora de fin:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "La última hora a mostrar en el menú emergente"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "Editor de fecha Gnome"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "Auto Close:"
msgstr "Cerrar automáticamente:"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "If the dialog closes when any button is clicked"
msgstr "Si el diálogo se cierra cuando se pulse cualquier botón"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "Hide on Close:"
msgstr "Ocultar al cerrar:"

#: ../glade/gnome/gnomedialog.c:156 ../glade/gnome/gnomemessagebox.c:193
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "Si el diálogo se oculta cuando se cierra, en lugar de ser destruido"

#: ../glade/gnome/gnomedialog.c:342
msgid "Gnome Dialog Box"
msgstr "Caja de diálogo Gnome"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Asistente Gnome nuevo"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Mostrar ayuda"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Mostrar el botón de ayuda"

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Añadir página de inicio"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Añadir página de finalización"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Druida"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "El título de la página"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "El texto principal de la página, introduciendo el druida a la gente"

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Color del título"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "El color del texto del título"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Color del texto:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "El color del texto principal"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "El color del fondo de la página"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Color de fondo del logotipo:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "El color del fondo alrededor del logotipo"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Color de la caja de texto:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "El color de fondo del área de texto principal"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Imagen de logotipo:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "El logotipo a mostrar en la parte superior derecha de la página"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Marca de agua lateral:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "La imagen principal a mostrar al lado de la página."

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Marca de agua superior:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr "La marca de agua a mostrar al principio de la página."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Inicio del asistente o fin de página"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Contenidos del fondo. Color:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "El color de fondo alrededor del título"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "La imagen a mostrar junto al inicio de página"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Página estándar del asistente"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "ID del histórico:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "El ID con el que guardar las entradas del histórico"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Guardar como máximo:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "El número máximo de entradas del histórico a guardar"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Entrada de texto Gnome"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "El título del diálogo de selección de archivos"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Directorio:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Si se necesita un directorio en lugar de un archivo"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "Si el diálogo de selección de archivos debe ser modal"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr "Usar FileChooser:"

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr "Usar el widget GtkFileChooser nuevo en vez de GtkFileSelection"

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Entrada de archivos Gnome"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr ""
"El texto mostrado como vista previa en el diálogo de selección de tipo de "
"letra"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Modo:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "Que mostrar en el botón de selección de tipografía"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr ""
"El tamaño de la tipografía a emplear en el botón de selección de tipo de "
"letra"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Selector Gnome de tipos de letra"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "La URL a mostrar cuando el botón se pulse"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "El texto a mostrar en el botón"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Botón Gnome de enlaces web"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Entrada de iconos Gnome"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "El modo de selección"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Ancho del icono:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "La anchura de cada icono"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "El número de pixels entre filas de iconos"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "El número de pixels entre columnas de iconos"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Borde del icono:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "El número de pixels alrededor de los iconos (¿sin uso?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Espaciado del texto:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "El número de pixels entre el texto y el icono"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Texto editable:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Si el texto del icono puede ser editado por el usuario"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Texto estático:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr ""
"Si el texto del icono es estático, en cuyo caso lo copiara el GnomeIconList"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Lista de iconos"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Selección de iconos"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "Message Type:"
msgstr "Tipo de mensaje:"

#: ../glade/gnome/gnomemessagebox.c:176
msgid "The type of the message box"
msgstr "El tipo de caja de mensajes"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "Message:"
msgstr "Mensaje:"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "The message to display"
msgstr "El mensaje a mostrar"

#: ../glade/gnome/gnomemessagebox.c:499
msgid "Gnome Message Box"
msgstr "Caja de mensajes Gnome"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "El nombre del archivo de la imagen"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Escalado:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Si la imagen es escalada"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Ancho escalado:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "La anchura al cual escalar la imagen"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Alto escalado:"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "La altura a la cual escalar la imagen"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Imagen Gnome"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Vista previa:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Si se muestra una pequeña vista previa de la imagen"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmapEntry"

#: ../glade/gnome/gnomepropertybox.c:113
msgid "New GnomePropertyBox"
msgstr "GnomePropertyBox nuevo"

#: ../glade/gnome/gnomepropertybox.c:366
msgid "Property Dialog Box"
msgstr "Caja de diálogo de propiedades"

#: ../glade/main.c:70 ../glade/main.c:104
msgid "Write the source code and exit"
msgstr "Escribir el código fuente y salir"

#: ../glade/main.c:74 ../glade/main.c:108
msgid "Start with the palette hidden"
msgstr "Iniciar con la paleta de widgets oculta"

#: ../glade/main.c:78 ../glade/main.c:112
msgid "Start with the property editor hidden"
msgstr "Iniciar con el editor de propiedades oculto"

#: ../glade/main.c:460
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade: Debe especificar el archivo XML con la opción '-w' o '--write-"
"source'.\n"

#: ../glade/main.c:474
msgid "glade: Error loading XML file.\n"
msgstr "glade: Se ha producido un error al cargar el archivo XML.\n"

#: ../glade/main.c:481
msgid "glade: Error writing source.\n"
msgstr "glade: Se ha producido un error al escribir el código fuente.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Paleta"

#: ../glade/property.c:73
msgid "private"
msgstr "private"

#: ../glade/property.c:73
msgid "protected"
msgstr "protected"

#: ../glade/property.c:73
msgid "public"
msgstr "public"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "Iluminado"

#: ../glade/property.c:103
msgid "Selected"
msgstr "Seleccionado"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Insensible"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Cuando la ventana necesite redibujarse"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Cuando el ratón se mueva"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Sugerencias cuando se mueve el ratón"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Movimiento del ratón con cualquier botón pulsado"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Movimiento del ratón con el botón 1 pulsado"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Movimiento del ratón con el botón 2 pulsado"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Movimiento del ratón con el botón 3 pulsado"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "Cualquier botón del ratón pulsado"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "Cualquier botón del ratón sin pulsar"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "Cualquier tecla pulsada"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "Cualquier tecla sin pulsar"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Cuando el ratón entra en la ventana"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Cuando el ratón sale de la ventana"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Cualquier cambio en el foco de entrada"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Cualquier cambio en la estructura de la ventana"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "Cualquier cambio en las propiedades de X Windows"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Cualquier cambio en la visibilidad"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "Para cursores en programas que utilicen XInput"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Propiedades"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Empaquetado"

#: ../glade/property.c:625
msgid "Common"
msgstr "Comunes"

#: ../glade/property.c:631
msgid "Style"
msgstr "Estilo"

#: ../glade/property.c:637 ../glade/property.c:4646
msgid "Signals"
msgstr "Señales"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Propiedades:"

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Propiedades: <ninguna>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "Clase:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "La clase del widget"

#: ../glade/property.c:813
msgid "Width:"
msgstr "Ancho:"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""
"La anchura requerido del widget (usualmente empleado para definir el ancho "
"mínimo)"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Altura:"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""
"La altura requerida del widget (usualmente empleado para definir la altura "
"mínima)"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Visible:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "Si el widget está inicialmente visible"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Sensible:"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "Si el widget responde a las entradas"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "Texto a mostrar si el ratón se para en el widget"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Puede por omisión:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr ""
"Indica si el widget puede ser tener la acción predeterminada en el diálogo"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "Predeterminado:"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Indica si el widget tiene la acción predeterminada en el diálogo"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Activable:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "Si el widget puede aceptar el foco de entrada"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Tiene el foco:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "Si el widget tiene el foco de entrada"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Eventos:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "Los eventos X que el widget recibe"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Eventos ext.:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "Modo de eventos de las «X Extensions»"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Aceleradores:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Define las señales a emitir cuando se pulsan teclas"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Editar..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "Propagar:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "Poner verdadero para propagar el estilo a los widgets hijos"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Nombre del estilo:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "Nombre del estilo, que puede ser compartido por muchos widgets"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Tipografía:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "El tipografía a usar en cualquier texto del widget"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Copiar todo"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Primer Plano:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Fondo:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "Base:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Color del primer plano"

#: ../glade/property.c:928
msgid "Background color"
msgstr "Color del fondo"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Color del texto"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Color base"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Imagen de fondo:"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "Imagen a utilizar como fondo del widget"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "El archivo donde se escribirá el código fuente"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Público:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Si el widget es añadido a la estructura de datos de componentes"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Clase separada:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Poner el subárbol de este widget en una clase separada"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Archivo separado:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Pone este widget en un archivo de código fuente separado"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Visibilidad:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr ""
"Visibilidad de los widgets. Los widgets públicos se exportan a un mapa "
"global."

#: ../glade/property.c:1127
msgid "You need to select a color or background to copy"
msgstr "Se necesita seleccionar un color o fondo para copiar"

#: ../glade/property.c:1146
msgid "Invalid selection in on_style_copy()"
msgstr "Selección inválida en on_style_copy()"

#: ../glade/property.c:1188
msgid "You need to copy a color or background pixmap first"
msgstr "Se necesita copiar un color o una imagen de fondo primero"

#: ../glade/property.c:1194
msgid "You need to select a color to paste into"
msgstr "Se necesita seleccionar un color para pegar en él"

#: ../glade/property.c:1204
msgid "You need to select a background pixmap to paste into"
msgstr "Se necesita una imagen de fondo para pegar en él"

#: ../glade/property.c:1456
msgid "Couldn't create pixmap from file\n"
msgstr "No ha sido posible crear una imagen desde el archivo\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1498
msgid "Signal"
msgstr "Señal"

#: ../glade/property.c:1500
msgid "Data"
msgstr "Datos"

#: ../glade/property.c:1501
msgid "After"
msgstr "Después"

#: ../glade/property.c:1502
msgid "Object"
msgstr "Objeto"

#: ../glade/property.c:1533 ../glade/property.c:1697
msgid "Signal:"
msgstr "Señal:"

#: ../glade/property.c:1534
msgid "The signal to add a handler for"
msgstr "La señal a tratar"

#: ../glade/property.c:1548
msgid "The function to handle the signal"
msgstr "Función para manejar la señal"

#: ../glade/property.c:1551
msgid "Data:"
msgstr "Datos:"

#: ../glade/property.c:1552
msgid "The data passed to the handler"
msgstr "Los datos pasados al manejador"

#: ../glade/property.c:1553
msgid "Object:"
msgstr "Objeto:"

#: ../glade/property.c:1554
msgid "The object which receives the signal"
msgstr "El objeto que recibe la señal"

#: ../glade/property.c:1555
msgid "After:"
msgstr "Después:"

#: ../glade/property.c:1556
msgid "If the handler runs after the class function"
msgstr "Si el manejador se lanza después que la función de la clase"

#: ../glade/property.c:1569
msgid "Add"
msgstr "Añadir"

#: ../glade/property.c:1575
msgid "Update"
msgstr "Actualizar"

#: ../glade/property.c:1587
msgid "Clear"
msgstr "Despejar"

#: ../glade/property.c:1637
msgid "Accelerators"
msgstr "Aceleradores"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1650
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1651
msgid "Key"
msgstr "Tecla"

#: ../glade/property.c:1652
msgid "Signal to emit"
msgstr "Señal a emitir"

#: ../glade/property.c:1696
msgid "The accelerator key"
msgstr "La tecla aceleradora"

#: ../glade/property.c:1698
msgid "The signal to emit when the accelerator is pressed"
msgstr "Señal a emitir cuando el acelerador se activa"

#: ../glade/property.c:1847
msgid "Edit Text Property"
msgstr "Editar propiedad del texto"

#: ../glade/property.c:1885
msgid "<b>_Text:</b>"
msgstr "<b>_Texto:</b>"

#: ../glade/property.c:1895
msgid "T_ranslatable"
msgstr "_Traducible"

#: ../glade/property.c:1899
msgid "Has Context _Prefix"
msgstr "Tiene _prefijo de contexto"

#: ../glade/property.c:1925
msgid "<b>Co_mments For Translators:</b>"
msgstr "<b>Co_mentarios para los traductores:</b>"

#: ../glade/property.c:3892
msgid "Select X Events"
msgstr "Seleccionar eventos X"

#: ../glade/property.c:3901
msgid "Event Mask"
msgstr "Máscara de eventos"

#: ../glade/property.c:4031 ../glade/property.c:4080
msgid "You need to set the accelerator key"
msgstr "Se necesita una tecla aceleradora"

#: ../glade/property.c:4038 ../glade/property.c:4087
msgid "You need to set the signal to emit"
msgstr "Se necesita una señal que emitir"

#: ../glade/property.c:4314 ../glade/property.c:4370
msgid "You need to set the signal name"
msgstr "Se necesita un nombre para la señal"

#: ../glade/property.c:4321 ../glade/property.c:4377
msgid "You need to set the handler for the signal"
msgstr "Se necesita un manejador para la señal"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4580
#, c-format
msgid "%s signals"
msgstr "Señales de %s"

#: ../glade/property.c:4637
msgid "Select Signal"
msgstr "Seleccione la señal"

#: ../glade/property.c:4833
msgid "Value:"
msgstr "Valor:"

#: ../glade/property.c:4833
msgid "Min:"
msgstr "Mínimo:"

#: ../glade/property.c:4833
msgid "Step Inc:"
msgstr "Inc. paso:"

#: ../glade/property.c:4834
msgid "Page Inc:"
msgstr "Inc. página:"

#: ../glade/property.c:4834
msgid "Page Size:"
msgstr "Tamaño de página:"

#: ../glade/property.c:4836
msgid "H Value:"
msgstr "Valor H:"

#: ../glade/property.c:4836
msgid "H Min:"
msgstr "Min H:"

#: ../glade/property.c:4836
msgid "H Max:"
msgstr "Max H:"

#: ../glade/property.c:4836
msgid "H Step Inc:"
msgstr "Inc. paso H:"

#: ../glade/property.c:4837
msgid "H Page Inc:"
msgstr "Inc. página H:"

#: ../glade/property.c:4837
msgid "H Page Size:"
msgstr "Tamaño página H:"

#: ../glade/property.c:4839
msgid "V Value:"
msgstr "Valor V:"

#: ../glade/property.c:4839
msgid "V Min:"
msgstr "Min V:"

#: ../glade/property.c:4839
msgid "V Max:"
msgstr "Max V:"

#: ../glade/property.c:4839
msgid "V Step Inc:"
msgstr "Inc. paso V:"

#: ../glade/property.c:4840
msgid "V Page Inc:"
msgstr "Inc. página V:"

#: ../glade/property.c:4840
msgid "V Page Size:"
msgstr "Tamaño página V:"

#: ../glade/property.c:4843
msgid "The initial value"
msgstr "Valor inicial"

#: ../glade/property.c:4844
msgid "The minimum value"
msgstr "Valor mínimo"

#: ../glade/property.c:4845
msgid "The maximum value"
msgstr "Valor máximo"

#: ../glade/property.c:4846
msgid "The step increment"
msgstr "El incremento de paso"

#: ../glade/property.c:4847
msgid "The page increment"
msgstr "Incremento de página"

#: ../glade/property.c:4848
msgid "The page size"
msgstr "El tamaño de página"

#: ../glade/property.c:5003
msgid "The requested font is not available."
msgstr "El tipografía seleccionado no está disponible"

#: ../glade/property.c:5052
msgid "Select Named Style"
msgstr "Seleccione nombre de estilo"

#: ../glade/property.c:5063
msgid "Styles"
msgstr "Estilos"

#: ../glade/property.c:5122
msgid "Rename"
msgstr "Renombrar"

#: ../glade/property.c:5150
msgid "Cancel"
msgstr "Cancelar"

#: ../glade/property.c:5270
msgid "New Style:"
msgstr "Estilo nuevo:"

#: ../glade/property.c:5284 ../glade/property.c:5405
msgid "Invalid style name"
msgstr "Nombre de estilo inválido"

#: ../glade/property.c:5292 ../glade/property.c:5415
msgid "That style name is already in use"
msgstr "El nombre de estilo está en uso"

#: ../glade/property.c:5390
msgid "Rename Style To:"
msgstr "Renombrar estilo a:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"Imposible de renombrar el archivo:\n"
"  %s\n"
"a:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"No ha sido posible crear el archivo:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "Error escribiendo archivo XML\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Archivo de cadenas traducibles generado por Glade.\n"
" * Añada este archivo a su POTFILES.in de su proyecto.\n"
" * NO lo compile como parte de su aplicación.\n"
" */\n"
"\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"El nombre del archivo fuente de la interfaz es inválido: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"El nombre del archivo de cabecera de la interfaz es inválido: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"El nombre del archivo fuente de las callbacks es inválido: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"El nombre del archivo de cabecera de las callbacks es inválido: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"El nombre del archivo fuente de las funciones de apoyo es inválido: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"El nombre del archivo de cabecera de las funciones de apoyo es inválido: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"No ha sido posible añadir al archivo:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"Se ha producido un error al escribir en el archivo:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr ""
"El nombre del archivo debe de introducirse en el diálogo de Opciones del "
"proyecto."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"El nombre del archivo debe ser un nombre simple relativo.\n"
"Emplee el diálogo de Opciones del proyecto para introducirlo."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Árbol de widget"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "Widget no encontrado en la caja"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "Widget no encontrado en la tabla"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "Widget no encontrado en el contenedor fijo"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "Widget no encontrado en el empaquetador"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"No ha sido posible acceder al archivo:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"No ha sido posible abrir el archivo:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"Se ha producido un error al escribir en el archivo:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"No ha sido posible crear el directorio:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"No ha sido posible acceder al directorio:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"Directorio inválido:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Proyectos"

#: ../glade/utils.c:1628
msgid "project"
msgstr "proyecto"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"No ha sido posible abrir el directorio:\n"
"  %s\n"

#~ msgid "Design user interfaces"
#~ msgstr "Diseño de interfaces de usuario"
