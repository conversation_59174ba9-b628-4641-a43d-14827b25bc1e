# glade.glade-gnome2-branch.az.po faylının <PERSON> dilinə tərcüməsi
# -------------------------------------------------------
# Copyright (C) 2000,2003 Free Software Foundation, Inc.
# V<PERSON><PERSON> <<EMAIL>>, 2001.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2003
#
msgid ""
msgstr ""
"Project-Id-Version: glade.glade-gnome2-branch.az\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2005-08-26 13:38+0200\n"
"PO-Revision-Date: 2003-08-18 01:10+0300\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Azerbaijani <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: KBabel 1.0.1\n"

#: ../glade-2.desktop.in.h:1
msgid "Design user interfaces"
msgstr ""

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Glade Ara Üz Tərtib Edicisi"

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Qəfəs Qurğuları"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Üfüqi Boşluq:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Şaquli Boşluq:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Qəfəs Tərzi:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Nöqtələr"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Xəttlər"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Yapışma Qurğuları"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Üfüqi Yapışma:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Sol"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Sağ"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Şaquli Yapışma:"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Üst"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Alt"

#: ../glade/editor.c:741
#, fuzzy
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "GnomeDockItem'lər sadəcə olaraq GnomeDock'a yapışdırla bilər ."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "GtkScrolledWindow widgeti daxil edilə bilmədi."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "GtkViewport widgeti daxil edilə bilmədi."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "Yeni widget daxil edilə bilmədi."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"Seçili yerə widget əlavə edilə bilməz.\n"
"\n"
"Yardım: Gtk+ widgetləri düzəltmək üçün konteynerləri işlədir .\n"
"Varolan widgeti silib və onun yerinə bir qutu\n"
"ya da cədvəl konteyneri işlətməyə çalış.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "Widget silinə bilmədi."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "Widget silinə bilməz"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"Bu widget başqa bir widgetin bir parçası olaraq avtomatik olaraq "
"yaradılmışdır və buna görə də silinə bilməz."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Kənar Eni:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "Konteynerin ətrafındakı kənarın eni"

#: ../glade/gbwidget.c:1745
msgid "Select"
msgstr "Seç"

#: ../glade/gbwidget.c:1767
msgid "Remove Scrolled Window"
msgstr "Sürüşdürməli Pəncərəni Sil"

#: ../glade/gbwidget.c:1776
msgid "Add Scrolled Window"
msgstr "Sürüşdürməli Pəncərə Əlavə Et"

#: ../glade/gbwidget.c:1797
msgid "Remove Alignment"
msgstr "Tərəfləməni Sil"

#: ../glade/gbwidget.c:1805
msgid "Add Alignment"
msgstr "Tərəfləmə Əlavə Et"

#: ../glade/gbwidget.c:1820
msgid "Remove Event Box"
msgstr "Hadisə Qutusunu Sil"

#: ../glade/gbwidget.c:1828
msgid "Add Event Box"
msgstr "Hadisə Qutusu Əlavə Et"

#: ../glade/gbwidget.c:1838
msgid "Redisplay"
msgstr "Yenidən Göstər"

#: ../glade/gbwidget.c:1849
msgid "Cut"
msgstr "Kəs"

#: ../glade/gbwidget.c:1856 ../glade/property.c:892 ../glade/property.c:5135
msgid "Copy"
msgstr "Köçür"

#: ../glade/gbwidget.c:1865 ../glade/property.c:904
msgid "Paste"
msgstr "Yapışdır"

#: ../glade/gbwidget.c:1877 ../glade/property.c:1580 ../glade/property.c:5126
msgid "Delete"
msgstr "Sil"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2403 ../glade/gbwidget.c:2472
msgid "N/A"
msgstr "U/D"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3202
msgid "replacing child of container - not implemented yet\n"
msgstr "konteynerin törəməsini əvəz ediş - hələ yazılmayıb\n"

#: ../glade/gbwidget.c:3430
msgid "Couldn't insert GtkAlignment widget."
msgstr "GtkAlignment widgeti daxil edilə bilmədi."

#: ../glade/gbwidget.c:3470
msgid "Couldn't remove GtkAlignment widget."
msgstr "GtkAlignment widgeti silinə bilmədi."

#: ../glade/gbwidget.c:3494
msgid "Couldn't insert GtkEventBox widget."
msgstr "GtkEventBox widgeti daxil edilə bilmədi."

#: ../glade/gbwidget.c:3533
msgid "Couldn't remove GtkEventBox widget."
msgstr "GtkEventBox widgeti silinə bilmədi."

#: ../glade/gbwidget.c:3568
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "GtkScrolledWindow widgeti daxil edilə bilmədi."

#: ../glade/gbwidget.c:3607
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "GtkScrolledWindow widgeti silinə bilmədi."

#: ../glade/gbwidget.c:3721
msgid "Remove Label"
msgstr "Etiketi Sil"

#: ../glade/gbwidgets/gbaboutdialog.c:78
#, fuzzy
msgid "Application Name"
msgstr "Gnome proqram tə'minatı çubuğu"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Loqo:"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "Loqo olaraq işlədiləcək rəsm"

#: ../glade/gbwidgets/gbaboutdialog.c:104 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Proqram adı :"

#: ../glade/gbwidgets/gbaboutdialog.c:104
#, fuzzy
msgid "The name of the application"
msgstr "\"Widget\"in adı"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "İzahat :"

#: ../glade/gbwidgets/gbaboutdialog.c:105
#, fuzzy
msgid "Additional information, such as a description of the application"
msgstr ""
"Proqram haqqında kiçik mə'lumat və məsələn Internetdəki veb saytla əlaqəli "
"mə'lumatlar"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Tə'lif hüququ:"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "Tə'lif hüququ mə'lumatı"

#: ../glade/gbwidgets/gbaboutdialog.c:108
msgid "Website URL:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:108
#, fuzzy
msgid "The URL of the application's website"
msgstr "Bir Gnome Proqram tə'minatı yaradılacaqsa"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "Website Label:"
msgstr "Menyu Etiketi:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "The label to display for the link to the website"
msgstr "Səhifənin yuxarısı boyunca göstəriləcək rəsm"

#: ../glade/gbwidgets/gbaboutdialog.c:111 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Lisenziya:"

#: ../glade/gbwidgets/gbaboutdialog.c:111
#, fuzzy
msgid "The license details of the application"
msgstr "Düymənin relyefi"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Müəllif (lər):"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Proqram tə'minatının Müəllif (lər)i, sətir başına bir dənə"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Sənədləşdiricilər:"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Proqram tə'minatının sənədləşdiriciləri, sətir başına bir dənə"

#: ../glade/gbwidgets/gbaboutdialog.c:115
msgid "Artists:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:115
#, fuzzy
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr "Proqram tə'minatının Müəllif (lər)i, sətir başına bir dənə"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Tərcüməçilər:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Paketin tərcüməçiləri. Bura, tərcüməçilərin öz adlarını po fayllarına yaza "
"bilmələri üçün boş buraxılmalıdır"

#: ../glade/gbwidgets/gbaboutdialog.c:559
#, fuzzy
msgid "About Dialog"
msgstr "Gnome Mə'lumat Dialoqu"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Sür'ətləndiricili Etiket"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:180 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "X Tərəfləməsi:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "Törəmə widgetinin üfüqi tərəfləndirməsi"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:183 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Y Tərəfləməsi:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "Törəmə widgetinin şaquli tərəfləndirməsi"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "X Böyüməsi:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "Törəmə widgetinin üfüqi böyümə nisbəti"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Y Miqyası:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "Törəmə widgetinin şaquli böyümə nisbəti"

#: ../glade/gbwidgets/gbalignment.c:85
#, fuzzy
msgid "Top Padding:"
msgstr "Üfüqi aralanma :"

#: ../glade/gbwidgets/gbalignment.c:86
#, fuzzy
msgid "Space to put above the child widget"
msgstr "Törəmə widgetinin üfüqi böyümə nisbəti"

#: ../glade/gbwidgets/gbalignment.c:89
#, fuzzy
msgid "Bottom Padding:"
msgstr "Üfüqi aralanma :"

#: ../glade/gbwidgets/gbalignment.c:90
#, fuzzy
msgid "Space to put below the child widget"
msgstr "Törəmə widgetinin üfüqi böyümə nisbəti"

#: ../glade/gbwidgets/gbalignment.c:93
#, fuzzy
msgid "Left Padding:"
msgstr "Üfüqi aralanma :"

#: ../glade/gbwidgets/gbalignment.c:94
#, fuzzy
msgid "Space to put to the left of the child widget"
msgstr "Törəmə widgetinin üfüqi böyümə nisbəti"

#: ../glade/gbwidgets/gbalignment.c:97
#, fuzzy
msgid "Right Padding:"
msgstr "Üfüqi aralanma :"

#: ../glade/gbwidgets/gbalignment.c:98
#, fuzzy
msgid "Space to put to the right of the child widget"
msgstr "Törəmə widgetinin üfüqi tərəfləndirməsi"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Tərəfləmə"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "İstiqamət:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "Oxun istiqaməti"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Kölgə:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "Oxun kölgə növü"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "Oxun üfüqi tərəfləndirməsi"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "Oxun şaquli tərəfləndirməsi"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186
msgid "X Pad:"
msgstr "X Ara:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "Üfüqi ara"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188
msgid "Y Pad:"
msgstr "Y Ara:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "Şaquli ara"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Ox"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "Etiket X Tərəfləməsi:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "Çərçivənin etiket widgetinin üfüqi tərəfləməsi"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Etiketin Y Tərəfləməsi:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "Çərçivənin etiket widgetinin şaquli tərəfləməsi"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "Çərçivənin kölgə növü"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "Çərçivənin alt\"widget\"lərının üfüqi tərəfləndirməsi"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Nisbət:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "Çərçivənin alt widget'inin yaranma nisbəti"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Alt widget'i keç :"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Çərçivə nisbəti alt widget'lərə görə olacaqsa"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Nisbətli çərçivə"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Saxlama düyməsi"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "İşlədilələcək saxlama düyməsi"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:747
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Etiket :"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "Göstəriləcək mətn"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:295
#: ../glade/glade_menu_editor.c:813
msgid "Icon:"
msgstr "Timsal:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "Göstəriləcək timsal"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Düyməs kənarları :"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "Düymənin relyefi"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "Cavab ID:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"Düymə basılanda qaytarılacaq cavab kodu. Standart cavablardan birisini seçin "
"ya da müsbət bir dəyər girin"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
#, fuzzy
msgid "If the button grabs focus when it is clicked"
msgstr "Dialoqdakı bir düyməyə basılınca dailoq qapanacaqsa"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "Dğymələrin İçindəkiləri Çixart"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Düymə"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Baslıq :"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "İl və ay başda göstəriləcəksə"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Gün adları :"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Gün adları göstəriləcəksə"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Bildirilən ay :"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "İl və ay dəyişdiriləməz olacaqsa"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Həftə nömrələri :"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Həftənin nömrəsi göstəriləcəksə"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Bazar Ertəsi ilk olsun :"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "Həftə bazar ertəsi başlayacaqsa"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Təqvim"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Arxa plan rəngi :"

#: ../glade/gbwidgets/gbcellview.c:64
#, fuzzy
msgid "The background color"
msgstr "Arxa plan rəngi"

#: ../glade/gbwidgets/gbcellview.c:192
#, fuzzy
msgid "Cell View"
msgstr "Mətn Görünüşü"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Başlanğıcda açıq :"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Təsdiqləmə düyməsi başlanğıcda açıq olacaqsa"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Bir biriylə uymayan/Zidd:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Düymənin zidd vəziyyətdə göstəriləcəyi"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "İndikator:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "İndikator həmişə göstəriləcəksə"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Təsdiqləmə düyməsi"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Təsdiqləmə menyu vahidi başdan bəri açik olacaqsa"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Təsdiqləməli menyu vahidi"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Yeni sütunlu siyahı"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Sütunların miqdarı :"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:127 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Seçim Modu:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "Sütunlu siyahının seçim modu"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Üst yazıları göstər :"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Sütunların üst yazıları göstəriləcəksə"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "Sütunlu siyahının üytyazılarının kölgə növü"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Sütunlu siyahı"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Alfa kanalını işlət :"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Alfa kanalı işlədiləcəksə"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:85
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:242
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Üstyazı :"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "Rəng seçmənin üst yazısı"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
#, fuzzy
msgid "Pick a Color"
msgstr "Bir rəng seç"

#: ../glade/gbwidgets/gbcolorbutton.c:211
#, fuzzy
msgid "Color Chooser Button"
msgstr "Təsdiqləmə düyməsi"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Matlıq İdarəsi:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Şəffaflıq idarəsinin göstəriləcəyi"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Palet:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Palet göstəriləcəksə"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Rəng seçkisi"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:70
msgid "Select Color"
msgstr "Bir rəng seç"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:315 ../glade/property.c:1275
msgid "Color Selection Dialog"
msgstr "Rəng seçmə dialoqu"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Siyahıdakı qiymət :"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Qiymət siyahıda olmaq məcburiyyətində isə"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "Boş ola bilər :"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr "Boş bir qiymət de qəbul ediləcəksə ( 'Siyahıdakı qiymət' açıqsa )"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "Yazılıma diqqət et :"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Axtarmada yazılıma diqqət ediləcəksə"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Oxları işlət:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "Qiyməti dəyişdirmek üçün oxlar işlədilə bilərsə"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Həmişə:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "Oxlar qiymət siyahıda olmasa bile göstəriləcəksə"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Vahidlər:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Kombo siyahısındakı vahidlər, sətir başına bir dənə"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Kombo Qutusu"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
#, fuzzy
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Dialoqdakı bir düyməyə basılınca dailoq qapanacaqsa"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Çərçivəsi Olsun:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr ""

#: ../glade/gbwidgets/gbcomboboxentry.c:302
#, fuzzy
msgid "Combo Box Entry"
msgstr "Kombo Qutusu"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Yeni sütunlu ağaç"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "Sütunlu ağacın seçki modu"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "Sütunlu ağacın kənarlarının kölgə cinsi"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Sütunlu ağaç"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:245
msgid "Type:"
msgstr "Növ:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "çizginin növü"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "Minimal X :"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "Minimal üfüqi qiymət"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "Maksimal X :"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "Maksimal üfüqi qiymət"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Minimal Y:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "Minimal şaquli qiymət"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Maksimal Y :"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "Maksimal şaquli qiymət"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Əyri"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Yaratma funksiyası:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "\"Widget\"i yaradan funksiya"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Mətn1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "Funksiyaya veriləcək olan ilk mətni arqument"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Mətn2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "Funksiyaya veriləcək olan ikinci mətni arqument"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Int1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "Funksiyaya veriləcək olan ilk digital/inteqer arqument"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Int2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "Funksiyaya veriləcək olan ikince digital/inteqer arqument"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Xüsusi Widget"

#: ../glade/gbwidgets/gbdialog.c:292
msgid "New dialog"
msgstr "Yeni dialoq"

#: ../glade/gbwidgets/gbdialog.c:304
msgid "Cancel, OK"
msgstr "Ləğv Et, Oldu"

#: ../glade/gbwidgets/gbdialog.c:313 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1316 ../glade/property.c:5156
msgid "OK"
msgstr "Oldu"

#: ../glade/gbwidgets/gbdialog.c:322
msgid "Cancel, Apply, OK"
msgstr "Ləğv et, Tətbiq Et, Oldu"

#: ../glade/gbwidgets/gbdialog.c:331
msgid "Close"
msgstr "bağla"

#: ../glade/gbwidgets/gbdialog.c:340
msgid "_Standard Button Layout:"
msgstr "_Standart düymə düzülüşü:"

#: ../glade/gbwidgets/gbdialog.c:349
msgid "_Number of Buttons:"
msgstr "_Düymə miqdarı:"

#: ../glade/gbwidgets/gbdialog.c:366
msgid "Show Help Button"
msgstr "Yardım Düyməsini Göstər"

#: ../glade/gbwidgets/gbdialog.c:397
msgid "Has Separator:"
msgstr "Ayırıcı olsun:"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Dialoqda düymələrin yuxarısında üfqi ayırıcı olacaqsa"

#: ../glade/gbwidgets/gbdialog.c:605
msgid "Dialog"
msgstr "Dialoq"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Boyama Sahəsi"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Düzəldilə bilən:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Mətn düzəldilə bilən olacaqsa"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Mətn görünmə qabiliyyətləri:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"İstifadəçinin verdiyi mətn göstəriləcəksə. Qapadılırsa, verilən mətn qəriba "
"hərflər olaraq göstəriləcək, və bu da yenə parol istəmə hadisələrində işə "
"yarayar"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Maksimal uzunluq :"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "Metnin maksimal uzunluğu"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Mətn :"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Girişin ətrafında cərçivə olacaqsa"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Görünməz Xarakter:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"Mətnin göstərilməməsi lazım olanda istifadə ediləcək yazı növü, mis. parol "
"yazılanda"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "Əsası Fəallaşdırır:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr "Pəncərədəki əsas pəncərəciyin Enter-ə basılanda fəal olması"

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Xarakter olaraq Genişlik"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "Girişdə buraxılacaq boş yer miqdarı, xarakter olaraq"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Mətn vahidi"

#: ../glade/gbwidgets/gbeventbox.c:65
#, fuzzy
msgid "Visible Window:"
msgstr "Görünmə qabiliyyəti :"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr ""

#: ../glade/gbwidgets/gbeventbox.c:66
#, fuzzy
msgid "Above Child:"
msgstr "Alt widget'i keç :"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr ""

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Hadisə qutusu"

#: ../glade/gbwidgets/gbexpander.c:54
#, fuzzy
msgid "Initially Expanded:"
msgstr "Başlanğıcda açıq :"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr ""

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Boşluq :"

#: ../glade/gbwidgets/gbexpander.c:58
#, fuzzy
msgid "Space to put between the label and the child"
msgstr "Timsal və mətn arasındakı piksel miqdarı"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Etiket Pəncərəsi Əlavə Et"

#: ../glade/gbwidgets/gbexpander.c:228
#, fuzzy
msgid "Expander"
msgstr "Genişlə :"

#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#, fuzzy
msgid "The window title of the file chooser dialog"
msgstr "Fayl seçmə dialoqunun üst yazısı"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:156
#: ../glade/gnome/gnomefileentry.c:109
#, fuzzy
msgid "Action:"
msgstr "Hissə:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:157
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:90
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
msgid "Local Only:"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:160
msgid "Whether the selected files should be limited to local files"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
#, fuzzy
msgid "Show Hidden:"
msgstr "Zamanı Göstər:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether the hidden files and folders should be displayed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gblabel.c:200
#, fuzzy
msgid "Width in Chars:"
msgstr "Xarakter olaraq Genişlik"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#, fuzzy
msgid "The width of the button in characters"
msgstr "Düzülüş sahəsinin eni"

#: ../glade/gbwidgets/gbfilechooserbutton.c:283
#, fuzzy
msgid "File Chooser Button"
msgstr "Təsdiqləmə düyməsi"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
#, fuzzy
msgid "Select Multiple:"
msgstr "Fayl seç"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether to allow multiple files to be selected"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
#, fuzzy
msgid "File Chooser"
msgstr "Üst yazı rəngi :"

#: ../glade/gbwidgets/gbfilechooserdialog.c:421
#, fuzzy
msgid "File Chooser Dialog"
msgstr "Fayl seçmə dialoqu"

#: ../glade/gbwidgets/gbfileselection.c:71 ../glade/property.c:1365
msgid "Select File"
msgstr "Fayl seç"

#: ../glade/gbwidgets/gbfileselection.c:113
msgid "File Ops.:"
msgstr "Fayl əməliyyatları :"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "If the file operation buttons are shown"
msgstr "Fayl funksiya düymələri göstəriləcəksə"

#: ../glade/gbwidgets/gbfileselection.c:292
msgid "File Selection Dialog"
msgstr "Fayl seçmə dialoqu"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "GtkFixed içində pəncərənin X koordinatı"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "GtkFixed içində pəncərənin Y koordinatı"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Bildirilən yerlər"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "Yazı növü seçmə dialoqunun üst yazısı"

#: ../glade/gbwidgets/gbfontbutton.c:70
#, fuzzy
msgid "Show Style:"
msgstr "Üst yazıları göstər :"

#: ../glade/gbwidgets/gbfontbutton.c:71
#, fuzzy
msgid "If the font style is shown as part of the font information"
msgstr "Yazı növü mə'lumatlarında yazı növünün böyüklüyü də göstəriləcəkse"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Böyüklüyü göstər :"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "Yazı növü mə'lumatlarında yazı növünün böyüklüyü də göstəriləcəkse"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Yazı növünü işlət :"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr "Yazı növü mə'lumatları göstərilirken seçili yazı növü göstəriləcəkse"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Böyüklüyü işlət :"

#: ../glade/gbwidgets/gbfontbutton.c:77
#, fuzzy
msgid "if the selected font size is used when displaying the font information"
msgstr "Yazı növü mə'lumatları göstərilirken seçili yazı növü göstəriləcəkse"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Bir yazı növünü seç"

#: ../glade/gbwidgets/gbfontbutton.c:268
#, fuzzy
msgid "Font Chooser Button"
msgstr "Təsdiqləmə düyməsi"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Mətn Nümayişi:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "Göstəriləcək mətnin nümaişi"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Yazı növü seçkisi"

#: ../glade/gbwidgets/gbfontselectiondialog.c:69
msgid "Select Font"
msgstr "Yazı növünü seç"

#: ../glade/gbwidgets/gbfontselectiondialog.c:300
msgid "Font Selection Dialog"
msgstr "Yazı növü seçmə dialoqu"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Çərçivə"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Başlama növü :"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "Çizginin başlanğıcdaki növü"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Qamma çizgisi"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "İdarə qutusunun ətrafındakı kölgənin növü"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "İdarə yeri :"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "İdarənin yeri"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Kənarları yaxala :"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "İdarə qutusunun yerinə oturan kənarı"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "İdarə qutusu"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Yeni üfüqi qutu"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Böyüklük :"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "Qutudaki \"widget\"lərin miqdarı"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Eşit :"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Alt\"widget\"lər aynı böyüklükdə olacaqsa"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "Hər alt \"widget\" arasındakı boşluq"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "Heç bir alt \"widget\"ı silə bilmərəm ."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:254
msgid "Position:"
msgstr "Yer :"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "\"Widget\"in altwidget'lərə görə yeri"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Aralanma:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "\"Widget\"in aralanması"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Genişlə :"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "\"Widget\"in genişləmesi istənilirsə aç"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Doldur :"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "\"Widget\" bütün sahəni dolduracaqsa aç"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Yükləmə başı :"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "\"Widget\"ı qutunun başına yükləmək istəsən aç"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Əvvəldən əlavə et"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Sonradan əlavə et"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Üfüqi qutu"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Yeni üfüqi düymə qutusu"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "Düymələrin miqdarı"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Düzülüş:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "Düymələrin düzülüş növü"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "Düymələr arasındakı boşluq"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Üfüqi düymələr qutusu"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "Ayrımcının yeri"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:283
msgid "Shrink:"
msgstr "Toplanma :"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "\"Widget\" toplanacaq olacaqsa"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Böyüklüyünü Dəyişdir:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "\"Widget\"lərin böyüklüyünü dəyişdirmək mümkün isə"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Üfüqi panellər"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Metrik :"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "Xətkeşin giriş"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Alt qiymət :"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "Xətkeşin alt qiyməti"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Üst qiymət :"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "Xətkeşin üst qiyməti"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "Xətkeşin hazırkı yeri"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4827
msgid "Max:"
msgstr "Maksimal:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "Xətkeşin maksimal qiyməti"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Üfüqi xətkeşin"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Qiyməti göstər :"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Böyüdücünün qiymətləri göstəriləcəksə"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Rəqəmlər:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "Göstəriləcək sayıların miqdarı"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Qiymət yeri :"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "Qiymətin yeri"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Məram :"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "Xətkeşin yeniləmə modu"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Çevirilmiş:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Silsilə dəyərlərinin çevrilmiş olması"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Üfüqi böyüdücü"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "Sürüşdürmə çubuğunun yeniləmə modu"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Üfüqi sürüşdürma çubuğu"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Üfüqi ayrımcı"

#: ../glade/gbwidgets/gbiconview.c:106
#, fuzzy, c-format
msgid "Icon %i"
msgstr "Timsal Siyahısı"

#: ../glade/gbwidgets/gbiconview.c:128
#, fuzzy
msgid "The selection mode of the icon view"
msgstr "Sütunlu ağacın seçki modu"

#: ../glade/gbwidgets/gbiconview.c:130 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "İstiqamət :"

#: ../glade/gbwidgets/gbiconview.c:131
#, fuzzy
msgid "The orientation of the icons"
msgstr "İrəliləmə çubuğunun içindəkilərinin istiqamətləndirilməsi"

#: ../glade/gbwidgets/gbiconview.c:287
#, fuzzy
msgid "Icon View"
msgstr "Tımsal Böyüklüyü:"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:299
#, fuzzy
msgid "Named Icon:"
msgstr "Timsal:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:300
#, fuzzy
msgid "The named icon to use"
msgstr "İşlədilələcək saxlama vahidi ."

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Tımsal Böyüklüyü:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "Saxlama timsalının böyüklüyü"

#: ../glade/gbwidgets/gbimage.c:115
#, fuzzy
msgid "Pixel Size:"
msgstr "Səhifə böyüklüyü :"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "Üfüqi tərəfləmə"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "Şaquli tərəfləmə"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Rəsm"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "Hökmsüz saxlama menyu vahidi"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Rəsmli bir menyu vahidi"

#: ../glade/gbwidgets/gbinputdialog.c:256
msgid "Input Dialog"
msgstr "Mə'lumat dialoqu"

#: ../glade/gbwidgets/gblabel.c:169
msgid "Use Underline:"
msgstr "Altı Cızılı:"

#: ../glade/gbwidgets/gblabel.c:170
#, fuzzy
msgid "If the text includes an underlined access key"
msgstr "Mətn altı çızıxlı sürətləndirici xatarkeri daxil edəcəksə"

#: ../glade/gbwidgets/gblabel.c:171
msgid "Use Markup:"
msgstr "İşarətləmə İşlət:"

#: ../glade/gbwidgets/gblabel.c:172
msgid "If the text includes pango markup"
msgstr "Mətnin pango işarətləməsi daxil etdiyi"

#: ../glade/gbwidgets/gblabel.c:173
msgid "Justify:"
msgstr "Bəndləşdirmə:"

#: ../glade/gbwidgets/gblabel.c:174
msgid "The justification of the lines of the label"
msgstr "Etiketdəki mətnin tərəfləməsi"

#: ../glade/gbwidgets/gblabel.c:176
msgid "Wrap Text:"
msgstr "Mətni qır :"

#: ../glade/gbwidgets/gblabel.c:177
msgid "If the text is wrapped to fit within the width of the label"
msgstr "Etiketin enine uymaq üçün mətnin qırılsın"

#: ../glade/gbwidgets/gblabel.c:178
msgid "Selectable:"
msgstr "Seçilə Bilən:"

#: ../glade/gbwidgets/gblabel.c:179
msgid "If the label text can be selected with the mouse"
msgstr "Etiket mətninin siçan ilə seçilə bilməsi"

#: ../glade/gbwidgets/gblabel.c:181
msgid "The horizontal alignment of the entire label"
msgstr "Yekin yaftanın üfüqi tərəfləməsi"

#: ../glade/gbwidgets/gblabel.c:184
msgid "The vertical alignment of the entire label"
msgstr "Yekin yaftanın şaquli tərəfləməsi"

#: ../glade/gbwidgets/gblabel.c:190
msgid "Focus Target:"
msgstr "Fokuslama hədəfi :"

#: ../glade/gbwidgets/gblabel.c:191
#, fuzzy
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"Altı çizilmiş düyməyə basılınca klaviatura fokusu göndəriləcək olunduğu "
"\"widget\" "

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:197 ../glade/gbwidgets/gbprogressbar.c:146
#, fuzzy
msgid "Ellipsize:"
msgstr "Tək başına :"

#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:201
#, fuzzy
msgid "The width of the label in characters"
msgstr "Düzülüş sahəsinin eni"

#: ../glade/gbwidgets/gblabel.c:203
#, fuzzy
msgid "Single Line Mode:"
msgstr "Seçim Modu:"

#: ../glade/gbwidgets/gblabel.c:204
msgid "If the label is only given enough height for a single line"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:205
msgid "Angle:"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:206
#, fuzzy
msgid "The angle of the label text"
msgstr "Mətnin qırılması"

#: ../glade/gbwidgets/gblabel.c:332 ../glade/gbwidgets/gblabel.c:347
#: ../glade/gbwidgets/gblabel.c:614
msgid "Auto"
msgstr "Avtomatik"

#: ../glade/gbwidgets/gblabel.c:870 ../glade/glade_menu_editor.c:410
msgid "Label"
msgstr "Etiket"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Sahə eni :"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "Düzülüş sahəsinin eni"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Sahə hündürlüyü :"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "Düzülüş sahəsinin hündürlüyü"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "GtkLayout içində pəncərənin X koordinatı"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "GtkLayout içində pəncərənin Y koordinatı"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Düzülüş"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "Siyahının seçki cinsi"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Siyahı"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Siyahı vahidi"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Açılma menyusü"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:190
msgid "_File"
msgstr "_Fayl"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:198 ../glade/glade_project_window.c:691
msgid "_Edit"
msgstr "_Düzəlt"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:204 ../glade/glade_project_window.c:720
msgid "_View"
msgstr "_Görünüş"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:206 ../glade/glade_project_window.c:833
msgid "_Help"
msgstr "_Yardım"

#: ../glade/gbwidgets/gbmenubar.c:207
msgid "_About"
msgstr "_Haqqında"

#: ../glade/gbwidgets/gbmenubar.c:268 ../glade/gbwidgets/gbmenubar.c:346
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Menyuları dəyişdir ..."

#: ../glade/gbwidgets/gbmenubar.c:442
msgid "Menu Bar"
msgstr "Menyu çubuğu"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Menyu vahidi"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
#, fuzzy
msgid "Show Horizontal:"
msgstr "Üfüqi Olmayan:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
#, fuzzy
msgid "If the item is visible when the toolbar is horizontal"
msgstr "Liman vahidi qətiyyən üfüqi ola bilməyəcəksə"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
#, fuzzy
msgid "Show Vertical:"
msgstr "Qiyməti göstər :"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
#, fuzzy
msgid "If the item is visible when the toolbar is vertical"
msgstr "Liman vahidi şaquli ola bilməyəcəksə"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
#, fuzzy
msgid "Toolbar Button with Menu"
msgstr "Dəyişdirmə düyməsi"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Yeni dəftər"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:124
msgid "Number of pages:"
msgstr "Səhifələrin miqdarı :"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "\"Tab\"ləri göstər :"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Dəftər \"tab\"lərinin göstərilib göstərilməiyini bildirər"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Kənarı göstər :"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "Dəftərin kənarları, \"tab\"lər görünməzsə belə göstəriləcəkse"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Teb yeri :"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "Dəftər \"tab\"lərin yeri"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Sürüşdürməli:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Dəftər \"tab\"ləri sürüşdürməli olacaqsa"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Üfüqi kənar tab'i :"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "Dəftərin üfüqi kənarının böyüklüyü"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Şaquli kənar \"tab\"i :"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "Dəftərin şaquli kənarının böyüklüyü"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Açilma menyusünü göstər"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "Açilma menyusü açıq olacaqsa"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Səhifələrin miqdarı :"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "Dəftər səhifələrinin miqdarı"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Əvvəlki səhifə"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "İrəlidəki səhifə"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Səhifəni sil"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "İrəlidəkini dəyişdir"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Əvvəlkini dəyişdir"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Sonradan bir səhifə əlavə et"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Əvvəldən bir səhifə əlavə et"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "Səhifələr siyahısında səhifənin mövqeyi"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Səkmənin genişləməsinə icazə vermək üçün True seçin"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr "Səkmənin ona ayrılan sahəni doldurmasına icazə vermək üçün True seçin"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr "Səkməni qeyd dəftərinin başlanğıcına yerləşdirmək üçün True seçin"

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Menyu Etiketi:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "Hoppanan menyunun üstündə göstəriləcək yazı"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Dəftər"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "GtkOptionMenu-ya %s əlavə edə bilmirəm."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Seçənək menyusü"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Rəng :"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "Nümayiş rəngli ya da qara-ağ mı olacaq"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "Nümayiş ayrılmış alanını dolduracaqsa"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Nümayiş"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "İrəliləmə çubuğunun içindəkilərinin istiqamətləndirilməsi"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Hissə:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "Tamamlanan işin hissəsi"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Nəbz Addımı:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""
"Nəbz əmələ gələndə ilərləyən blokun daşınması lazım olan vəziyyət çubuğu "
"uzunluğunun hissəsi "

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "İrəliləmə çubuğunun üstündə göstəriləcək mətn"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Mətni göstər :"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "İrəliləmə çubuğunde mətn göstəriləcəkse"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Gediş cinsi :"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "İrəliləmə çubuğunun işləkliyini qurğular"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "Metnin üfüqi tərəfləməsi"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "Metnin şaquli tərəfləməsi"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "İrəliləmə çubuğu"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "Qərar düyməsi başlanğıcda açıq olacaqsa"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1038
msgid "Group:"
msgstr "Qrup:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr ""
"Qərar düyməsinin qrupu ( əsasda hər qərar düyməsi  eyni qrupun üzvüdür )"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Yeni Qrup"

#: ../glade/gbwidgets/gbradiobutton.c:463
msgid "Radio Button"
msgstr "Qərar düyməsi"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "Qərar menyu vahidi başlanğıcda açıq olacaqsa"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"Qərar menyu vahidinin ( əsasda hər qərar menyu vahidi eyni qrupun üzvüdür )"

#: ../glade/gbwidgets/gbradiomenuitem.c:386
msgid "Radio Menu Item"
msgstr "Qərar menyu vahidi"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
#, fuzzy
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr ""
"Qərar düyməsinin qrupu ( əsasda hər qərar düyməsi  eyni qrupun üzvüdür )"

#: ../glade/gbwidgets/gbradiotoolbutton.c:528
#, fuzzy
msgid "Toolbar Radio Button"
msgstr "Qərar düyməsi"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "Üfüqi məram :"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Üfüqi sürüşdürmə çubuğunun ne zaman göstəriləcəyi"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "Şaquli məram :"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Şaquli sürüşdürmə çubuğunun ne zaman göstəriləcəyi"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Pəncərə Mövqesi:"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr "Sürüşmə çubuqlarına nəzarən törəmə pəncərənin mövqeyi"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Kölgə Növü:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "Şaquli sürüşdürmə çubuğunun yeniləme modu"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Sürüşdürməli pəncərə"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Menyu Ayırıcısı"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
#, fuzzy
msgid "Toolbar Separator Item"
msgstr "Üfüqi ayrımcı"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Yüksəlmə addımlaması :"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr ""
"Sayı qutusunun yüksəlmə addımlaması, səhifə addınmlaması ilə bərabər "
"işlədilir"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "Göstəriləcək onlu sayıların miqdarı"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Numerik:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr ""

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Yenilə modu :"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Qiymət dəyişdi siqnalınin veriləcəyi an"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Düzəlt :"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "Qiymət addımlama eninə görə düzəldiləcəksə"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Qır :"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "Qiymət sərhədlərdə qırılacaqsa"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Sayma düyməsi"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Böyüklüyü Dəyişdirmə Qulpu:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr ""
"Vəziyyət çubuğunda pəncərənin böyüklüyünü dəyişdirmək üçün qulpun olacağı"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Vəziyyət çubuğu"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Yeni cədvəl"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Sıraların miqdarı :"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Sıra :"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "Cəvəldəki sıraların miqdarı"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Sütunlar :"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "Cəvəldəki sütunların miqdarı"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Alt\"widget\"lər həmişə eyni böyüklükdə olsun"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Sıra boşluqları :"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "Hər sıra arasındakı boşluq"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Sütun boşluqları :"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "Hər sütun arasındakı boşluq"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Hücrə X'i :"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "Cədvəldə o widget'in sol bucağı"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Hücrə Y'si :"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "Cədvəldə o widget'in üst bucağı"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Sıra qaplaması :"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "Cədvəldə bu \"widget\" tərəfindən qaplanan sütunların miqdarı"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Sütun qaplaması :"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "Cədvəldə bu \"widget\" tərəfindən qaplanan sıralar miqdarı"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "Üfüqi aralanma :"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "Şaquli aralanma :"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "X böyüməsi :"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "\"Widget\" üfüqi böyüyəcəksə"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Y böyüməsi :"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "\"Widget\" şaquli böyüyəcəksə"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "X toplaması :"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "\"Widget\" üfüqi toplanacaqsa"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Y toplaması :"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "\"Widget\" şaquli toplanacaqsa"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "X doldurması :"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr "\"Widget\" ayrılmış üfüqi sahəsini dolduracaqsa"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Y doldurması :"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "\"Widget\" ayrılmış şaquli sahəsini dolduracaqsa"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Əvvəldən bir sıra əlavə et"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Sonradan bir sıra əlavə et"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Əvvəldən bir sütun əlavə et"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Sonradan bir sütun əlavə et"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Sırayı sil"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Sütunu sil"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Cədvəl"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Mərkəz"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Doldur"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:542 ../glade/glade_menu_editor.c:829
#: ../glade/glade_menu_editor.c:1344 ../glade/glade_menu_editor.c:2251
#: ../glade/property.c:2431
msgid "None"
msgstr "Heç biri"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Xarakter"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Söz"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Ox Görünən:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Ox görünən olacaqsa"

#: ../glade/gbwidgets/gbtextview.c:119
#, fuzzy
msgid "Overwrite:"
msgstr "Çevirilmiş:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:122
#, fuzzy
msgid "If tab characters can be entered"
msgstr "Mətn düzəldilə bilən olacaqsa"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Bəndləşdirmə:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "Mətnin bəndləşdirməsi"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Qırılma:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "Mətnin qırılması"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Üstdəki Boşluq:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Paraqrafların üstündəki boşluqlardaki piksel miqdarı"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Altdakı Boşluq:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Paraqrafların altındakı boşluqlardaki piksel miqdarı"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Daxili Boşluq:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Paraqraflarda qırılmış sətirlər arasındaki piksel miqdarı"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Sol Kənar:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Sol kənarın piksel olaraq genişliyi"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Sağ Kənar:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Sağ kənarın piksel olaraq genişliyi"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Çərtmə"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Paraqrafların çərtmələrindəki piksel miqdarı"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Mətn Görünüşü"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "Dəyişdirmə düyməsi başlanğıcda açıq olacaqsa"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Dəyişdirmə düyməsi"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
#, fuzzy
msgid "Toolbar Toggle Button"
msgstr "Dəyişdirmə düyməsi"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Yeni vasitə çubuğu"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Vahidlərin miqdarı :"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "Vasitə çubuğundaki vahidlərin miqdarı"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "Vasitə çubuğu tərəfləməsi"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Tərz :"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "Vasitə çubuğu tərzi"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Balon yardımlar :"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Balon yardımları açıq olacaqsa"

#: ../glade/gbwidgets/gbtoolbar.c:277
#, fuzzy
msgid "Show Arrow:"
msgstr "Kənarı göstər :"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""

#: ../glade/gbwidgets/gbtoolbar.c:427
#, fuzzy
msgid "If the item should be the same size as other homogeneous items"
msgstr "Alt\"widget\"lər aynı böyüklükdə olacaqsa"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Əvvəldən bir vahid əlavə et"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Sonradan bir vahid əlavə et"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Vasitə çubuğu"

#: ../glade/gbwidgets/gbtoolbutton.c:586
#, fuzzy
msgid "Toolbar Button"
msgstr "Dəyişdirmə düyməsi"

#: ../glade/gbwidgets/gbtoolitem.c:201
#, fuzzy
msgid "Toolbar Item"
msgstr "Vasitə çubuğu"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Sütun 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Sütun 2"

#: ../glade/gbwidgets/gbtreeview.c:87
#, fuzzy
msgid "Column 3"
msgstr "Sütun 1"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Başlıqlar Görünsün:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Sütunların başlıq düymələri göstəriləcəksə"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Cədvəl İpucu:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr "Örtük motorunun sətrləri ayrı rənglərdə çəkəcəyi"

#: ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Yenidən Sıralana Bilən:"

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Nümayiş yenidən sıralana bilən olacaqsa"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Axtarmağı fəallaşdır:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr "İstifadəçinin sütunlar arasında interaktiv olaraq axtara biləcəyi"

#: ../glade/gbwidgets/gbtreeview.c:123
#, fuzzy
msgid "Fixed Height Mode:"
msgstr "Miqyaslandırılmış hündürlük :"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:125
#, fuzzy
msgid "Hover Selection:"
msgstr "Rəng seçkisi"

#: ../glade/gbwidgets/gbtreeview.c:126
#, fuzzy
msgid "Whether the selection should follow the pointer"
msgstr "Siyahının seçki cinsi"

#: ../glade/gbwidgets/gbtreeview.c:127
#, fuzzy
msgid "Hover Expand:"
msgstr "X böyüməsi :"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Ağaç Nümaişi ya da Siyahı"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Yeni şaquli qutu"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Şaquli qutu"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Yeki şaquli düymələr qutusu"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Şaquli düymələr qutusu"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "Nümayiş qutusunun kölgə növü"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Nümayiş qutusu"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Şaquli panellər"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Şaquli xətkeşin"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Şaquli böyüdücü"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Şaquli sürüşdürmə çubuğu"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Şaquli ayrımcı"

#: ../glade/gbwidgets/gbwindow.c:242
msgid "The title of the window"
msgstr "Pəncərənin üst yazısı"

#: ../glade/gbwidgets/gbwindow.c:245
msgid "The type of the window"
msgstr "Pəncərənin növü"

#: ../glade/gbwidgets/gbwindow.c:249
#, fuzzy
msgid "Type Hint:"
msgstr "Növ:"

#: ../glade/gbwidgets/gbwindow.c:250
msgid "Tells the window manager how to treat the window"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:255
msgid "The initial position of the window"
msgstr "Pəncərənin başlama yeri"

#: ../glade/gbwidgets/gbwindow.c:259 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Bağla :"

#: ../glade/gbwidgets/gbwindow.c:259
msgid "If the window is modal"
msgstr "Pəncərə istifadəçiyi bağlarsa"

#: ../glade/gbwidgets/gbwindow.c:264
msgid "Default Width:"
msgstr "Əsas en :"

#: ../glade/gbwidgets/gbwindow.c:265
msgid "The default width of the window"
msgstr "Pəncərənin əsas eni"

#: ../glade/gbwidgets/gbwindow.c:269
msgid "Default Height:"
msgstr "Əsas hündürlük :"

#: ../glade/gbwidgets/gbwindow.c:270
msgid "The default height of the window"
msgstr "Pəncərənin əsas hündürlüyü"

#: ../glade/gbwidgets/gbwindow.c:276
msgid "Resizable:"
msgstr "Böyüklüyü Dəyişdirilə Bilən:"

#: ../glade/gbwidgets/gbwindow.c:277
msgid "If the window can be resized"
msgstr "Pəncərə böyüklüyü dəyişdirilə bilinəcəksə"

#: ../glade/gbwidgets/gbwindow.c:284
msgid "If the window can be shrunk"
msgstr "Pəncərə toplana bilən ola biləcəksə"

#: ../glade/gbwidgets/gbwindow.c:285
msgid "Grow:"
msgstr "Böyü :"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be enlarged"
msgstr "Pəncərə böyüyəbilərsə"

#: ../glade/gbwidgets/gbwindow.c:291
msgid "Auto-Destroy:"
msgstr "Avtomatik-Yoxetmə:"

#: ../glade/gbwidgets/gbwindow.c:292
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Pəncərənin valideyn pəncərəsi yox ediləndə özünün də yox ediləcəyi"

#: ../glade/gbwidgets/gbwindow.c:296
msgid "The icon for this window"
msgstr "Bu pəncərə üçün timsal"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "Role:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:303
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:306
#, fuzzy
msgid "Decorated:"
msgstr "Kö_hnəlmiş"

#: ../glade/gbwidgets/gbwindow.c:307
#, fuzzy
msgid "If the window should be decorated by the window manager"
msgstr "Çərçivə nisbəti alt widget'lərə görə olacaqsa"

#: ../glade/gbwidgets/gbwindow.c:310
msgid "Skip Taskbar:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:311
#, fuzzy
msgid "If the window should not appear in the task bar"
msgstr "Pəncərənin vəziyyət çubuğu var olacaqsa"

#: ../glade/gbwidgets/gbwindow.c:314
msgid "Skip Pager:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:315
#, fuzzy
msgid "If the window should not appear in the pager"
msgstr "İrəliləmə çubuğunde mətn göstəriləcəkse"

#: ../glade/gbwidgets/gbwindow.c:318
#, fuzzy
msgid "Gravity:"
msgstr "Qəfəs Tərzi:"

#: ../glade/gbwidgets/gbwindow.c:319
msgid "The reference point to use when the window coordinates are set"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "Focus On Map:"
msgstr "Fokuslama hədəfi :"

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "If the window should receive the input focus when it is mapped"
msgstr "Çərçivə nisbəti alt widget'lərə görə olacaqsa"

#: ../glade/gbwidgets/gbwindow.c:1198
msgid "Window"
msgstr "Pəncərə"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:74
msgid "Error"
msgstr "Xəta"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Sistem xətası"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "Fayl açıla bilmədi"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "Fayl oxuna bilmədi"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "Fayl yazıla bilmədi"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "Hökmsüz cığır"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "Hökmsüz qiymət"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "Hökmsüz XML vahidi"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "Başlama tanımlayıcısı gözlənir"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "Bitiş kəlməsi gözlənilirdi"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "Hərf mə'lumatı gözlənir"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "Sinif tanıdıcısı əksikdir"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Na'mə'lum sinif"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "Hökmsüz parça"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "Fayl gözlənilməz şəkildə sona erdi"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Na'mə'lum xəta nömrəsi"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Nəzarətci:"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Nəzarətcisi"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Etiketin Sahibi"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Etiketləndirən"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Üzv"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Baş Törəməsi"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr ""

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr ""

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr ""

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr ""

#: ../glade/glade_atk.c:130
#, fuzzy
msgid "Embedded By"
msgstr "Etiketləndirən"

#: ../glade/glade_atk.c:131
#, fuzzy
msgid "Popup For"
msgstr "Açılma menyusü"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr ""

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Əlaqə: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "\"Widget\""

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:772
#: ../glade/property.c:776
msgid "Name:"
msgstr "Ad :"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr "Köməlçi texnologiyalara ötürüləcək pəncərənin adı"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "İzahat:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr "Köməlçi texnologiyalara ötürüləcək pəncərənin izahatı"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Cədvəl Başlığı:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "Köməlçi texnologiyalara ötürüləcək cədvəl başlığı"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Bu əlaqəsi olan pəncərələri seç"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Tıq"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Bas"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Dağıdım"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr "Köməlçi texnologiyalara ötürüləcək hərəkətin izahını girin"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Ara Yaddaş"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Yapışdırılacaq bir \"widget\" seçməlisən"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "Pəncərələrin ya da dailoqların içinə yapışdıra bilməzsən ."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"Seçili widget'in içinə yapışdıra bilməzsən, çünkü \n"
"bu \"widget\" avtomatiki başqa bir \"widget\" tərəfindən yaradıldı."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr ""
"Bir menyuya ya da menyu çubuğuna sadəcə olaraq menyu girişi yapıştıra bilər."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr "Dialogun gediş sahəsinə ancaq düymələr yapuşdırıla bilər."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr ""
"GnomeDock'ın içinə sadəcə olaraq GnomeDockItem \"widget\"ləri yapışdırla "
"bilər ."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr ""
"GnomeDockItem'in üstünə sadəcə olaraq başqa bir GnomeDockItem yapışdırla "
"bilər ."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "Təəsüf ki GnomeDockItem'in üstünə yapışdırmaq daha yazılmayıb ."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "GnomeDockItem'lər sadəcə olaraq GnomeDock'a yapışdırla bilər ."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:632
msgid "_New"
msgstr "Ye_ni"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Yeni bir fayl yarat"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "Kö_hnəlmiş"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "GTK+ Ə_sas"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "GTK+ Ə_lavə"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Sürətlandirmə Düyməsini Seç"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Düymələr"

#: ../glade/glade_menu_editor.c:394
msgid "Menu Editor"
msgstr "Menyu \teditoru"

#: ../glade/glade_menu_editor.c:411
msgid "Type"
msgstr "Növ"

#: ../glade/glade_menu_editor.c:412
msgid "Accelerator"
msgstr "Sür'ətləndirici"

#: ../glade/glade_menu_editor.c:413
msgid "Name"
msgstr "Adı"

#: ../glade/glade_menu_editor.c:414 ../glade/property.c:1498
msgid "Handler"
msgstr "Köməkçi"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:102
msgid "Active"
msgstr "Fəal"

#: ../glade/glade_menu_editor.c:416
msgid "Group"
msgstr "Qrup"

#: ../glade/glade_menu_editor.c:417
msgid "Icon"
msgstr "Timsal"

#: ../glade/glade_menu_editor.c:458
msgid "Move the item and its children up one place in the list"
msgstr "Vahidi və alt vahidlərini siyahıda bir yer yuxarı daşı"

#: ../glade/glade_menu_editor.c:470
msgid "Move the item and its children down one place in the list"
msgstr "Vahidi və alt vahidlərinin siyahıda bir yer aşağı daşı"

#: ../glade/glade_menu_editor.c:482
msgid "Move the item and its children up one level"
msgstr "Vahidi və alt vahidlərinin siyahıda bir səviyyə yuxarı daşı"

#: ../glade/glade_menu_editor.c:494
msgid "Move the item and its children down one level"
msgstr "Vahidi və alt vahidlərinin siyahıda bir səviyyə aşağı daşı"

#: ../glade/glade_menu_editor.c:524
msgid "The stock item to use."
msgstr "İşlədilələcək saxlama üzvü."

#: ../glade/glade_menu_editor.c:527 ../glade/glade_menu_editor.c:642
msgid "Stock Item:"
msgstr "Saxlama :"

#: ../glade/glade_menu_editor.c:640
msgid "The stock Gnome item to use."
msgstr "İşlədilələcək saxlama vahidi ."

#: ../glade/glade_menu_editor.c:745
msgid "The text of the menu item, or empty for separators."
msgstr ""

#: ../glade/glade_menu_editor.c:769 ../glade/property.c:777
msgid "The name of the widget"
msgstr "\"Widget\"in adı"

#: ../glade/glade_menu_editor.c:790
msgid "The function to be called when the item is selected"
msgstr "Vahidin seçildidi zaman icra ediləcək funksiya"

#: ../glade/glade_menu_editor.c:792 ../glade/property.c:1546
msgid "Handler:"
msgstr "Köməkçi:"

#: ../glade/glade_menu_editor.c:811
msgid "An optional icon to show on the left of the menu item."
msgstr "Menyu vahidinin solunda göstəriləcək arzuya bağlı bir rəsm ."

#: ../glade/glade_menu_editor.c:934
msgid "The tip to show when the mouse is over the item"
msgstr "Siçan vahidin üstündə olunca göstəriləcək balon yardımı"

#: ../glade/glade_menu_editor.c:936 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Balon yardımı :"

#: ../glade/glade_menu_editor.c:957
msgid "_Add"
msgstr "Ə_lavə et"

#: ../glade/glade_menu_editor.c:962
msgid "Add a new item below the selected item."
msgstr "Seçilmişin altına yeni bir dənə əlavə et"

#: ../glade/glade_menu_editor.c:967
msgid "Add _Child"
msgstr "_Törəmə Əlavə Et"

#: ../glade/glade_menu_editor.c:972
msgid "Add a new child item below the selected item."
msgstr "Seçilmiş üzvün altına yeni bir törəmə üzv əlavə et."

#: ../glade/glade_menu_editor.c:978
msgid "Add _Separator"
msgstr "_Ayırıcı əlavə et"

#: ../glade/glade_menu_editor.c:983
msgid "Add a separator below the selected item."
msgstr "Seçilisinin altına bir boşluq əlavə et"

#: ../glade/glade_menu_editor.c:988 ../glade/glade_project_window.c:239
msgid "_Delete"
msgstr "_Sil"

#: ../glade/glade_menu_editor.c:993
msgid "Delete the current item"
msgstr "İndiki vahidi sil"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:999
msgid "Item Type:"
msgstr "Vahid növü:"

#: ../glade/glade_menu_editor.c:1015
msgid "If the item is initially on."
msgstr "Vahid başdan fəal olacaqsa."

#: ../glade/glade_menu_editor.c:1017
msgid "Active:"
msgstr "Fəal:"

#: ../glade/glade_menu_editor.c:1022 ../glade/glade_menu_editor.c:1632
#: ../glade/property.c:2215 ../glade/property.c:2225
msgid "No"
msgstr "Xeyir"

#: ../glade/glade_menu_editor.c:1036
msgid "The radio menu item's group"
msgstr "Qərar menyu qrupu"

#: ../glade/glade_menu_editor.c:1053 ../glade/glade_menu_editor.c:2406
#: ../glade/glade_menu_editor.c:2546
msgid "Radio"
msgstr "Seçim"

#: ../glade/glade_menu_editor.c:1060 ../glade/glade_menu_editor.c:2404
#: ../glade/glade_menu_editor.c:2544
msgid "Check"
msgstr "İşarət"

#: ../glade/glade_menu_editor.c:1067 ../glade/property.c:102
msgid "Normal"
msgstr "Normal"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1076
msgid "Accelerator:"
msgstr "Sür'ətləndirici"

#: ../glade/glade_menu_editor.c:1113 ../glade/property.c:1681
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1118 ../glade/property.c:1684
msgid "Shift"
msgstr "Şift"

#: ../glade/glade_menu_editor.c:1123 ../glade/property.c:1687
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1128 ../glade/property.c:1694
msgid "Key:"
msgstr "Düymə:"

#: ../glade/glade_menu_editor.c:1134 ../glade/property.c:1673
msgid "Modifiers:"
msgstr "Təkmilləşdiriclər:"

#: ../glade/glade_menu_editor.c:1632 ../glade/glade_menu_editor.c:2411
#: ../glade/glade_menu_editor.c:2554 ../glade/property.c:2215
msgid "Yes"
msgstr "Bəli"

#: ../glade/glade_menu_editor.c:2002
msgid "Select icon"
msgstr "Timsalı seç"

#: ../glade/glade_menu_editor.c:2345 ../glade/glade_menu_editor.c:2706
msgid "separator"
msgstr "boşluq"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3624 ../glade/glade_project_window.c:366
#: ../glade/property.c:5109
msgid "New"
msgstr "Yeni"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Seçici"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Lahiyə cığırı bildirilməyib .\n"
"Xahiş edirik Lahiyə Seçənəkləri dialoqunda onu bildir .\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Mənbə cığırı bildirilməyib .\n"
"Xahiş edirik Lahiyə Seçənəkləri dialoqunda onu bildir .\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"Hökmsüz mənbə cığırı :\n"
"\n"
"Mənbə cığırı ya lahiyə cığırı ya da lahiyə cığırının bir \n"
"alt cığırı olmaq məcburiyyətindədir .\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Rasmlər cığırı bildirilməyib .\n"
"Xahiş edirik Lahiyə Seçənəkləri dialoqunda onu bildir .\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "Təəsüf ki %s üçün mənbələrin yaradılması indilik mümkün deyildir"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""
"Layihəniz Gtkmm-2'nin dəstəkləmədiyi üzvləri daxil edir.\n"
"Layihənizdə bunları tapın və onları əvəz ediciləri ilə\n"
"dəyişdirin."

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"C++ mənbələrini yaratmaq üçün lazımi olan glade--'i işə sala bilmədim .\n"
"Xahiş edirik glade--'in qurulu olduğunu və PATH'ın içində olub olmadığını "
"yoxlayın.\n"
"Sonra terminalda 'glade-- <layihə_faylı.glade>' əmrini icra edin."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"Ada95 mənbələrini yaratmaq gate işə salına bilmədi.\n"
"Xahiş edirik, gate'in qurulu olduğunu və PATH'ın içində olduğunu yoxlayın.\n"
"Sonra terminalda 'gate <layihə_faylı.glade>' əmrini icra edin."

#: ../glade/glade_project.c:571
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"Perl mənbələrini yaratmaq glade2perl işə salına bilmədi.\n"
"Xahiş edirik, glade2perl'in qurulu olduğunu və PATH'ın içində olduğunu "
"yoxlayın.\n"
"Sonra terminalda 'glade2perl <layihə_faylı.glade>' əmrini icra edin."

#: ../glade/glade_project.c:594
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"Eiffel mənbələrini yaratmaq eglade işə salına bilmədi.\n"
"Xahiş edirik, eglade'in qurulu olduğunu və PATH'ın içində olduğunu "
"yoxlayın.\n"
"Sonra terminalda 'eglade <layihə_faylı.glade>' əmrini icra edin."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Rasmlər cığırı bildirilməyib .\n"
"Xahiş edirik Lahiyə Seçənəkləri dialoqunda onu bildir .\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "Lahiyə XML faylı yazılırkən xəta oldu\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:382
#: ../glade/glade_project_window.c:889
msgid "Project Options"
msgstr "Lahiyə Seçənəkləri"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "Ümumi"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Ana qurğular :"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "Lahiyə cığırı"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Lahiyə cığırı :"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Gəz ..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "Lahiyənin adı"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Lahiyə adı :"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "Proqram tə'minatının adı"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "Glade lahiyə faylı"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Lahiyə faylı :"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Altcığırlar :"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "Yaradılacaq mənbənin getdiyi cığır"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Mənbə Qovluğu:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "Resimlərin saxlandığı cığır"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Rəsm cığırı:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr ""
"Yaradılacaq hər faylın başına əlavə ediləcək olan tə'lif hüququ mə'lumatı"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Dil :"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome :"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Gnome dəstə'yini aç"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Bir Gnome Proqram tə'minatı yaradılacaqsa"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Gnome DB dəstə'yini aç"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Bir Gnome DB Proqram tə'minatı yaradılacaqsa"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "C qurğuları"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Ümumi qurğular :"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Gettext dəstəyi"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "Gettext tərəfindən tərcümə ediləcək mətnlər işarətləndi isə"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "\"Widget\" adlarını bildir"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Mənbədə \"widget\" adları tapılacaqsa"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Mənbə fayllarının ehtiyat nüsxəsini çıxart"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Köhnə mənbə fayllarının nüsxələri varsa"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Gnome Kömək dəstə'yini aç"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Gnome Kömək sistemi dəstəyi lazımsa"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Fayl Yekun Seçənəkləri :"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "main.c faylını yaz"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr ""
"Bir main.c faylı yoxsa bir main() gedişatı daxil edən bir fayl yazıalcaqdır"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Dəstək Funksiyaları Yekunu"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Dəstək əməliyyatları yazılacaqsa"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Qurulum fayllarını yekunlaşdır"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Mənbəyi qurmaq üçün lazımi fayllar yazıalcaqsa ( Makefile.am və configure."
"in  yoxsa yazılacaqdır )"

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "İstifadəçi ara üzü əməliyyatları :"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "İstifadəçi ara üzünü yaradacaq funksiyaların yazılacağı fayllar"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Mənbə Faylı:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr ""
"İstifadəçi ara üzünü yaradacaq funksiyaların izahlarının yazılacaq olduğu "
"fayl"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Baslıq Faylı :"

#: ../glade/glade_project_options.c:594
#, fuzzy
msgid "Source file for interface creation functions"
msgstr "İstifadəçi ara üzü əməliyyatları :"

#: ../glade/glade_project_options.c:595
#, fuzzy
msgid "Header file for interface creation functions"
msgstr "İstifadəçi ara üzü əməliyyatları :"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Siqnal idarə edici və geri nəticə əməliyyatları :"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr ""
"Boş siqnal idarə edicilərini və geri nəticə funksiyalarının yazılacağı fayl"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"Boş siqnal idarə edicilərini və geri nəticə funksiyalarının izahatlarının "
"yazılacağı fayl"

#: ../glade/glade_project_options.c:640
#, fuzzy
msgid "Source file for signal handler and callback functions"
msgstr "Siqnal idarə edici və geri nəticə əməliyyatları :"

#: ../glade/glade_project_options.c:641
#, fuzzy
msgid "Header file for signal handler and callback functions"
msgstr ""
"Boş siqnal idarə edicilərini və geri nəticə funksiyalarının yazılacağı fayl"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Dəstək Funksiyaları:"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "Dəstək funksiyalarının yazıldığı fayl"

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "Dəstək funksiyaların izahatlarının yazıldığı fayl"

#: ../glade/glade_project_options.c:686
#, fuzzy
msgid "Source file for support functions"
msgstr "Dəstək Funksiyaları:"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr ""

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "LibGlade qurğuları"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Tercüme edilə bilən kəlmələr:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Tercüme edilə bilən kəlmələri qeyd et"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Libglade tərəfindən yüklənən istifadəçi ara üzlərinin tərcümə edilmesi üçün "
"lazım olan mətnlər bir fayla qeyd ediləcəksə"

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "Hər tərcümə edilə bilən mətnin qeyd ediləcəyi C mənbəyi"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Fayl :"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Lahiyə qovluğunu seç"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Tercüme edilə bilən mətnlər faylını bildirmək məcburiyyətindəsən"

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Lahiyə qovluğunu bildirmək məcburiyyətindəsən"

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Lahiyə fayllarını bildirmək məcburiyyətindəsən"

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Lahiyə adını bildirmək məcburiyyətindəsən"

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Proqram adını bildirmək məcburiyyətindəsən"

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Mənbə qovluğunu bildirmək məcburiyyətindəsən"

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Resimləri qovluğunu bildirmək məcburiyyətindəsən"

#: ../glade/glade_project_window.c:184
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"Yardım faylını göstərə bilmədim: %s.\n"
"\n"
"Xəta: %s"

#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:634
msgid "Create a new project"
msgstr "Yeni bir lahiyə yarat"

#: ../glade/glade_project_window.c:216 ../glade/glade_project_window.c:654
#: ../glade/glade_project_window.c:905
msgid "_Build"
msgstr "_Yarat"

#: ../glade/glade_project_window.c:217 ../glade/glade_project_window.c:665
msgid "Output the project source code"
msgstr "Lahiyənin mənbə kodunu yaz"

#: ../glade/glade_project_window.c:223 ../glade/glade_project_window.c:668
msgid "Op_tions..."
msgstr "Q_urğular..."

#: ../glade/glade_project_window.c:224 ../glade/glade_project_window.c:677
msgid "Edit the project options"
msgstr "Lahiyə qurğuları ilə oyna"

#: ../glade/glade_project_window.c:239 ../glade/glade_project_window.c:716
msgid "Delete the selected widget"
msgstr "Seçili widget'i sil"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:727
msgid "Show _Palette"
msgstr "Paleti _göstər"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:732
msgid "Show the palette of widgets"
msgstr "\"Widget\"lər paletini göstər"

#: ../glade/glade_project_window.c:263 ../glade/glade_project_window.c:737
msgid "Show Property _Editor"
msgstr "Xü_susiyyətlər editorunu göstər"

#: ../glade/glade_project_window.c:264 ../glade/glade_project_window.c:743
msgid "Show the property editor"
msgstr "Xüsusiyyətlər editorunu göstər"

#: ../glade/glade_project_window.c:270 ../glade/glade_project_window.c:747
msgid "Show Widget _Tree"
msgstr "\"Widget\"lər _ağacını göstər"

#: ../glade/glade_project_window.c:271 ../glade/glade_project_window.c:753
#: ../glade/main.c:82
msgid "Show the widget tree"
msgstr "\"Widget\"lər ağacını göstər"

#: ../glade/glade_project_window.c:277 ../glade/glade_project_window.c:757
msgid "Show _Clipboard"
msgstr "_Ara yaddaşı göstər"

#: ../glade/glade_project_window.c:278 ../glade/glade_project_window.c:763
#: ../glade/main.c:86
msgid "Show the clipboard"
msgstr "Ara yaddaşı göstər"

#: ../glade/glade_project_window.c:296
msgid "Show _Grid"
msgstr "Qəfəsi _göstər"

#: ../glade/glade_project_window.c:297 ../glade/glade_project_window.c:799
msgid "Show the grid (in fixed containers only)"
msgstr "Qəfəsi göstər (sadəcə olaraq bəlli böyüklüklədə olanlar üçün)"

#: ../glade/glade_project_window.c:303
msgid "_Snap to Grid"
msgstr "Qəfəsə görə _tərəfla"

#: ../glade/glade_project_window.c:304
msgid "Snap widgets to the grid"
msgstr "\"Widget\"ləri qəfəsə görə tərəflə"

#: ../glade/glade_project_window.c:310 ../glade/glade_project_window.c:771
msgid "Show _Widget Tooltips"
msgstr "\"_Widget\"in balon yardımlarını göstər"

#: ../glade/glade_project_window.c:311 ../glade/glade_project_window.c:779
msgid "Show the tooltips of created widgets"
msgstr "Yaradılan \"widget\"lərin balon yardımlarını göstər"

#: ../glade/glade_project_window.c:320 ../glade/glade_project_window.c:802
msgid "Set Grid _Options..."
msgstr "Qəfəs _seçenekləri ..."

#: ../glade/glade_project_window.c:321
msgid "Set the grid style and spacing"
msgstr "Qəfəs tərzinin və boşluqların qurğuları"

#: ../glade/glade_project_window.c:327 ../glade/glade_project_window.c:823
msgid "Set Snap O_ptions..."
msgstr "_Yaxalama seçənəkləri ,,,"

#: ../glade/glade_project_window.c:328
msgid "Set options for snapping to the grid"
msgstr "Tora yaxalama qurğuları"

#: ../glade/glade_project_window.c:340
msgid "_FAQ"
msgstr "_TSS"

#: ../glade/glade_project_window.c:341
msgid "View the Glade FAQ"
msgstr "Glade TSS-larını Göstər"

#. create File menu
#: ../glade/glade_project_window.c:355 ../glade/glade_project_window.c:625
msgid "_Project"
msgstr "_Lahiyə"

#: ../glade/glade_project_window.c:366 ../glade/glade_project_window.c:872
#: ../glade/glade_project_window.c:1049
msgid "New Project"
msgstr "Yeni lahiyə"

#: ../glade/glade_project_window.c:371
msgid "Open"
msgstr "Aç"

#: ../glade/glade_project_window.c:371 ../glade/glade_project_window.c:877
#: ../glade/glade_project_window.c:1110
msgid "Open Project"
msgstr "Lahiyə aç"

#: ../glade/glade_project_window.c:376
msgid "Save"
msgstr "Qeyd Et"

#: ../glade/glade_project_window.c:376 ../glade/glade_project_window.c:881
#: ../glade/glade_project_window.c:1475
msgid "Save Project"
msgstr "Lahiyəni qeyd et"

#: ../glade/glade_project_window.c:382
msgid "Options"
msgstr "Qurğular"

#: ../glade/glade_project_window.c:387
msgid "Build"
msgstr "Yarat"

#: ../glade/glade_project_window.c:387
msgid "Build the Source Code"
msgstr "Mənbə kodunu yarat"

#: ../glade/glade_project_window.c:638
msgid "Open an existing project"
msgstr "Varolan bir lahiyəni aç"

#: ../glade/glade_project_window.c:642
msgid "Save project"
msgstr "Lahiyəni qeyd et"

#: ../glade/glade_project_window.c:687
msgid "Quit Glade"
msgstr "\"Glade\"dən Çıx"

#: ../glade/glade_project_window.c:701
msgid "Cut the selected widget to the clipboard"
msgstr "Seçili pəncərəni ara yaddaşa kəs"

#: ../glade/glade_project_window.c:706
msgid "Copy the selected widget to the clipboard"
msgstr "Seçili pəncərəni ara yaddaşa köçür"

#: ../glade/glade_project_window.c:711
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Ara yaddaşdaki pəncərəni seçili pəncərənin üstündən yapışdır"

#: ../glade/glade_project_window.c:783
msgid "_Grid"
msgstr "_Qəfəs"

#: ../glade/glade_project_window.c:791
msgid "_Show Grid"
msgstr "Qəfəsi _Göstər"

#: ../glade/glade_project_window.c:808
msgid "Set the spacing between grid lines"
msgstr "Qəfəs xətlərinin arasındakı boşluğu quraşdır"

#: ../glade/glade_project_window.c:811
msgid "S_nap to Grid"
msgstr "Qəfəsə _Yapış"

#: ../glade/glade_project_window.c:819
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr ""
"\"Widget\"ləri qəfəsə yaxala ( sadəcə olaraq bəlli böyüklükdə qutularda "
"mümkündür )"

#: ../glade/glade_project_window.c:829
msgid "Set which parts of a widget snap to the grid"
msgstr "\"Widget\"in hansı parçaları qəfəsə yaxalansın"

#  Don't show these yet as we have no help pages.
#  menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#  gtk_container_add (GTK_CONTAINER (menu), menuitem);
#  gtk_widget_show (menuitem);
#  menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#  gtk_container_add (GTK_CONTAINER (menu), menuitem);
#  gtk_widget_show (menuitem);
#  menuitem = gtk_menu_item_new ();
#  gtk_container_add (GTK_CONTAINER (menu), menuitem);
#  gtk_widget_show (menuitem);
#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:854
msgid "_About..."
msgstr "_Haqqında..."

#: ../glade/glade_project_window.c:895
msgid "Optio_ns"
msgstr "Q_urğular"

#: ../glade/glade_project_window.c:899
msgid "Write Source Code"
msgstr "Mənbə Kodu Yaz"

#: ../glade/glade_project_window.c:986 ../glade/glade_project_window.c:1691
#: ../glade/glade_project_window.c:1980
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:993
msgid "Are you sure you want to create a new project?"
msgstr "Yeni bir lahiyə yaratmaq istəyirsən?"

#: ../glade/glade_project_window.c:1053
msgid "New _GTK+ Project"
msgstr "Yeni _GTK+ Lahiyəsi"

#: ../glade/glade_project_window.c:1054
msgid "New G_NOME Project"
msgstr "Yeni G_NOME Lahiyəsi"

#: ../glade/glade_project_window.c:1057
msgid "Which type of project do you want to create?"
msgstr "Hansı növ layihə yaradmaq istəyirsiniz?"

#: ../glade/glade_project_window.c:1091
msgid "New project created."
msgstr "Yeni Lahiyə yaradıldı ."

#: ../glade/glade_project_window.c:1181
msgid "Project opened."
msgstr "Lahiyə açilmadı ."

#: ../glade/glade_project_window.c:1195
msgid "Error opening project."
msgstr "Lahiyə açılma xətası."

#: ../glade/glade_project_window.c:1259
msgid "Errors opening project file"
msgstr "Lahiyə faylı açılırkən xəta oldu"

#: ../glade/glade_project_window.c:1265
msgid " errors opening project file:"
msgstr " lahiyə faylını açarken xəta oldu :"

#: ../glade/glade_project_window.c:1338
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"Hazırda açıq layihə mövcud deyil.\n"
"Layihə/Yeni əmriylə yeni layihə yaradın."

#: ../glade/glade_project_window.c:1542
msgid "Error saving project"
msgstr "Lahiyəni qeyd edərkən xəta oldu"

#: ../glade/glade_project_window.c:1544
msgid "Error saving project."
msgstr "Lahiyəni qeyd edərkən xəta oldu ."

#: ../glade/glade_project_window.c:1550
msgid "Project saved."
msgstr "Lahiyə qeyd edildi ."

#: ../glade/glade_project_window.c:1620
msgid "Errors writing source code"
msgstr "Mənbəyi yazarkən xəta oldu"

#: ../glade/glade_project_window.c:1622
msgid "Error writing source."
msgstr "Mənbəyi yazarkən xəta oldu ."

#: ../glade/glade_project_window.c:1628
msgid "Source code written."
msgstr "Mənbəyi yazdım ."

#: ../glade/glade_project_window.c:1659
msgid "System error message:"
msgstr "Sistem xəta ismarıcı :"

#: ../glade/glade_project_window.c:1698
msgid "Are you sure you want to quit?"
msgstr "Çıxmaq istəyirsən?"

#: ../glade/glade_project_window.c:1982 ../glade/glade_project_window.c:2042
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "(C) 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1983 ../glade/glade_project_window.c:2041
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr "Glade, GNOME və GTK+ üçün İstifadəçi Ara Üz Qurucusu/İnşa edicisidir."

#: ../glade/glade_project_window.c:2012
msgid "About Glade"
msgstr "Glade Haqqında"

#: ../glade/glade_project_window.c:2097
msgid "<untitled>"
msgstr "<adsız>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Databeyz Səyyahı"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Mə'lumatlı Kombo"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr ""

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Bağlantı Seçicisi"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "DSN Quraşdırıcısı"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "DSN Quraşdırıcısı"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:178
#, fuzzy
msgid "GnomeDbEditor"
msgstr "Gnome tarix vahidi"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Databeyz xəta səyyahı"

#: ../glade/gnome-db/gnomedberrordlg.c:218
msgid "Database error dialog"
msgstr "Databeyz xəta dialoqu"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Form"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr ""

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr ""

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Mə'lumatlı qəfəs"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Mə'lumatlı siyahı"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Databeyz giriş widget'i"

#: ../glade/gnome-db/gnomedblogindlg.c:76
msgid "Login"
msgstr "Giriş"

#: ../glade/gnome-db/gnomedblogindlg.c:219
msgid "Database login dialog"
msgstr "Databeyz giriş dialoqu"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Sağlayıcı Seçicisi"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr ""

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Mə'lumat Mənbə Seçicisi"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Cədvəl Editoru"

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Sürüşdürə bilər :"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Sürüşdürülə bilən liman giriş istənilirsə"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Zirvəyə bir liman əlavə et"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Alta bir liman bağını əlavə et"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Sola bir liman bağını əlavə et"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Sağa bir liman bağını əlavə et"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Sürüşdürülə bilən liman vahidi əlavə et"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Gnome Limanı"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Qıfıllı :"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Liman vahidi yeri qıfıllanıbsa"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Tək başına :"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "Liman vahidi bu top bağında tək başına olacaqsa"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Sürüşdürülə bilməyən:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "Liman vahidi öz pəncərəsini sürüşdürə bilməuəcəksə"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Şaquli Olmayan:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "Liman vahidi şaquli ola bilməyəcəksə"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Üfüqi Olmayan:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "Liman vahidi qətiyyən üfüqi ola bilməyəcəksə"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "Liman vahidinin ətrafındakı kölgəleme növü"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "Sürüşdürülə bilən liman vahidinin tərəfləməsi"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Liman vahidini əvvəldən əlavə et"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Liman vahidini sonradan əlavə et"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Gnome liman vahidi"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr ""
"Proqram haqqında kiçik mə'lumat və məsələn Internetdəki veb saytla əlaqəli "
"mə'lumatlar"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Gnome Mə'lumat Dialoqu"

#: ../glade/gnome/gnomeapp.c:170
msgid "New File"
msgstr "Yeni fayl"

#: ../glade/gnome/gnomeapp.c:172
msgid "Open File"
msgstr "Fayl aç"

#: ../glade/gnome/gnomeapp.c:174
msgid "Save File"
msgstr "Faylı Qeyd Et"

#: ../glade/gnome/gnomeapp.c:203
msgid "Status Bar:"
msgstr "Vəziyyət çubuğu :"

#: ../glade/gnome/gnomeapp.c:204
msgid "If the window has a status bar"
msgstr "Pəncərənin vəziyyət çubuğu var olacaqsa"

#: ../glade/gnome/gnomeapp.c:205
msgid "Store Config:"
msgstr "Qurğuları qeyd et :"

#: ../glade/gnome/gnomeapp.c:206
msgid "If the layout is saved and restored automatically"
msgstr "Yer və böyüklük avtomatiki qeyd edilip, başlanğıcda oxunacaqsa"

#: ../glade/gnome/gnomeapp.c:442
msgid "Gnome Application Window"
msgstr "Gnome proqram tə'minatı pəncərəsi"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Vəziyyət ismarıcı ."

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "İrəliləmə :"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "Proqram çubuğunun bir irəliləmə indikatoru olacaqsa"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Vəziyyət :"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr ""
"Proqram çubuğunun bir vəziyyət ismarıcları və istifadəçi mə'lumatları üçün "
"bir sahəsi olacaqsa"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Gnome proqram tə'minatı çubuğu"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Düzəldilməli :"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr ""
"Kanvas kənarları düzəldiləcəkse ( yə'ni mətn və rəsmlərin kənarları "
"düzəldiləcəksə )"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1 :"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "Minimal x qiyməti"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1 :"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "Minimal y qiyməti"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "x2 :"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "Maksimal x qiyməti"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2 :"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "Maksimal y qiyməti"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Vahid pikselləri :"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "Bir vahidə bərabər piksellərin miqdarı"

#: ../glade/gnome/gnomecanvas.c:239
msgid "GnomeCanvas"
msgstr "Gnome kanvas'i"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Təlaş:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Nümunə təlaş sayəsində daha doğru olacaqsa"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Bir rəng seç"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Gnome Rəng Alıcısı"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "Bonobo kontrolunu yarada bilmədim"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Yeni Bonobo Kontrolu"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Bonobo Kontrolunu Seçin"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3896
msgid "Description"
msgstr "İzahat"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Bonobo Kontrolu"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Zamanı Göstər:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Tarixin yanında zaman da göstəriləcəksə"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "24 saatlıq şəkil :"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Vaxt 24-saatlıq şakildə göstəriləcəksə"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Minimal saat :"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "Göstəriləcek ən erkən saat"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Maksimal saat :"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "Göstəriləcek en gec saat"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "Gnome tarix vahidi"

#: ../glade/gnome/gnomedialog.c:152 ../glade/gnome/gnomemessagebox.c:189
msgid "Auto Close:"
msgstr "Avtomatik bağla :"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "If the dialog closes when any button is clicked"
msgstr "Dialoqdakı bir düyməyə basılınca dailoq qapanacaqsa"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "Hide on Close:"
msgstr "bağlaılınca gizlət :"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "Dialoq qapadılınca silmək yerinə sadəcə olaraq gizlənəcəksə"

#: ../glade/gnome/gnomedialog.c:341
msgid "Gnome Dialog Box"
msgstr "Gnome dailoq qutusu"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Yeni Gnome sehirbazı"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Yardımı Göstər"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Yardım düyməsini göstər."

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Başlama Səhifəsi Əlavə Et"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Bitiş Səhifəsi Əlavə Et"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Sehirbaz"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "Səhifənin üst yazısı"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "Səhifənin ana mətni ( sehirbazı tanıdan bir mətn olması)."

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Üst yazı rəngi :"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "Üst yazı metninin rəngi"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Mətn rəngi :"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "Ana mətnin rəngi"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "Səhifənin arxa plan rəngi"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Loqo arxa plan rəngi :"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "Loqo ətrafının arxa plan rəngi"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Mətn qutusu rəngi :"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "Ana mətn sahəsinin arxa plan rəngi"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Loqo Rəsmi:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "Səhifənin sağ-üstündə gosteriləcek loqo"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Yan Damğa:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "Səhifənin yanənda göstəriləcək əsas rəsm."

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Təpə Damğası:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr "Səhifənin yuxarısında göstəriləcək damğa."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Başıanğıc ya da Bitiş Səhifəsi Sehirbazı"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Məzmun arxa plan rəngi:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "Üstyazı ətrafının arxa plan rəngi"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "Səhifənin yuxarısı boyunca göstəriləcək rəsm"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Sehirbaz səhifəsi"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "Keçmiş ID:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "Keçmiş vahidlərinin qeyd ediləcəyi ID"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Maksimal keçmiş miqdarı :"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "Keçmiş olaraq qeyd edilen vahid içindəkilərin maksimal miqdarı"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Gnome Girişi"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "Fayl seçmə dialoqunun üst yazısı"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Qovluq :"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Bir fayldan artıq bir cığır istənilirsə"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "Fayl seçmə dialoqu modal olacaqsa"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Gnome Fayl Girişi"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "Yazı növü seçmə dialoqunda göstəriləcək nümayiş mətni"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Mod:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "Yazı növü seçmə düyməsində nə göstəriləcək"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "Yazı növü seçmə düyməsindəki yazı növünün böyüklüyü"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Gnome Yazı Növü Alıcısı"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "Ünvan :"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "Düyməyə tıqlanınca göstəriləcək ünvan"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "Düymənin üstündəki yazı"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Gnome Körpü düyməsi"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Gnome timsal vahidi"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "Seçki modu"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Timsal Eni:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "Hər timsalın eni"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "Timsal sətirləri arasındakı piksel miqdarı"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "Timsal sütunları arasındakı piksel miqdarı"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Timsal Kənarı:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "Timsal ətrafındakı piksel miqdarı (işlədilmir?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Mətn Boşluğu :"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "Timsal və mətn arasındakı piksel miqdarı"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Düzəldilə bilən Mətn:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Timsal mətni istifadəçi tərəfindən düzəldilə bilməzsə"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Statik Mətn:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr "Tİmsal statiksə, GnomeIconList tərəfindən köçürülməyəcək"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Timsal Siyahısı"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Timsal Seçkisi"

#: ../glade/gnome/gnomemessagebox.c:174
msgid "Message Type:"
msgstr "İsmarıc Növü :"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "The type of the message box"
msgstr "İsmarıc qutusunun növü"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "Message:"
msgstr "İsmarıc :"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "The message to display"
msgstr "Göstəriləcək ismarıc"

#: ../glade/gnome/gnomemessagebox.c:498
msgid "Gnome Message Box"
msgstr "Gnome İsmarıc Qutusu"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "Rəsmin fayl adı"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Miqyaslandırılmış:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Rəsm miqyaslandırılmışsa"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Miqyaslama Eni :"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "Rəsmin miqyaslanacağı en"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Miqyaslandırılmış hündürlük :"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "Rəsmin miqyaslanacağı en"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Gnome Rəsmi"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Nümayiş :"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Rəsmin kiçik bir nümayişi göstərilirsə"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomeRəsmGirişi"

#: ../glade/gnome/gnomepropertybox.c:112
msgid "New GnomePropertyBox"
msgstr "Yeni GnomeSeçənəklərQutusu"

#: ../glade/gnome/gnomepropertybox.c:365
msgid "Property Dialog Box"
msgstr "Gnome Dialoq Qutusu"

#: ../glade/main.c:70
msgid "Write the source code and exit"
msgstr "Mənbə kodu yaz və çıx"

#: ../glade/main.c:74
msgid "Start with the palette hidden"
msgstr "Başlarkən paleti gizlət"

#: ../glade/main.c:78
msgid "Start with the property editor hidden"
msgstr "Başlarkən xüsusiyyətlər editorunu gizlət"

#: ../glade/main.c:436
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade : '-w' ya da '--write-source' seçənəkləri üçün XML faylının adı "
"bildirmək məcburiyyətindədir .\n"

#: ../glade/main.c:450
msgid "glade: Error loading XML file.\n"
msgstr "glade : XML faylını yükləyə bilmədim .\n"

#: ../glade/main.c:457
msgid "glade: Error writing source.\n"
msgstr "glade : mənbəyi yazarkən xəta oldu .\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Palet"

#: ../glade/property.c:73
msgid "private"
msgstr "xüsusi"

#: ../glade/property.c:73
msgid "protected"
msgstr "qorumalı"

#: ../glade/property.c:73
msgid "public"
msgstr "ümumi"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "Əvvəlində Parla"

#: ../glade/property.c:103
msgid "Selected"
msgstr "Seçili"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Həssasiyyət"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Pəncərənin yenidən boyanması lazım olsa"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Siçan hərəkət edərsə"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Siçan hərəkət etmə yardımları"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Bir düymə ilə bərabər olan siçan hərəkəti"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Sol siçan düyməsi basılıykən icra edilən siçan hərəkəti"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Orta siçan düyməsi basılıykən icra edilən siçan hərəkəti"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Sağ siçan düyməsi basılıykən icra edilən siçan hərəkəti"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "İstənilən bir siçan düyməsinin basılması"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "İstənilən bir siçan düyməsinin buraxılması"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "İstənilən bir düymənin basılması"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "İstənilən bir düymənin buraxılması"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Siçan pəncərəye girərsə"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Siçan pəncərədən çıxarsa"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Mə'lumat fokusunda istənilən bir dəyişiklik olarsa"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Pəncərə quruluşunda istənilən bir dəyişiklik olarsa"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "X pəncərə özelliklərinde istənilən bir dəyişiklik olarsa"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Görünmə qabiliyyətində istənilən bir dəyişiklik olarsa"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "XInput'u başa düşən proqramların oxları üçün"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Xüsusiyyətlər"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Paketlənir"

#: ../glade/property.c:625
msgid "Common"
msgstr "Ümumi"

#: ../glade/property.c:631
msgid "Style"
msgstr "Tərz"

#: ../glade/property.c:637 ../glade/property.c:4640
msgid "Signals"
msgstr "Siqnallar"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Xüsusiyyətlər : "

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Xüsusiyyətlər : <adsız> "

#: ../glade/property.c:778
msgid "Class:"
msgstr "Sinif:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "\"Widget\" sinifi"

#: ../glade/property.c:813
msgid "Width:"
msgstr "En :"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr "İstənilən pəncərə genişliyi"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Hündürlük :"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr "İstənilən pəncərə yüksəkliyi"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Görünmə qabiliyyəti :"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "\"Widget\" görünə bilən olacaqsa"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Həssasiyyət :"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "\"Widget\" mə'lumatlara cavab verəcəksə"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "Siçan widget'in üstündən keçərkən göstəriləcək balon yardımı"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Əsas Ola Bilər:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr "Pəncərəciyin dialoqdakı əsas hərəkət ola bilib bilməməsi"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "Əsas :"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Pəncərəcik dialoqdakı əsas hərəkət isə"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Fokuslana bilən :"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "\"Widget\" fokusu qəbul edə biləcəkse"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Fokuslu :"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "\"Widget\" giriş fokusuna sahib olacaqsa"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Hadisələr :"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "\"Widget\"in qəbul ettiği X hadisələri"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Başqa hadisələr :"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "X əlavə hadisələr cinsi"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Sür'ətləndiricilər :"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Düymələrə basılınca veriləcək siqnallar"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Düzəlt..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "İcra Etdir :"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "Alt-Widget'lərin icra edilməsi üçün \"TRUE\"nu qurun"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Adlı tərz :"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "Tərzin adı ( bu başqa Widget'lər tərəfindən də işlədilə bilər )"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Yazı növü :"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "Widget'dəki hər tip mətn üçün işlədiləcək yazı növü"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Hamısını köçür"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Ön plan:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Arxa plan:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "Əsas:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Ön plan rəngi"

#: ../glade/property.c:928
msgid "Background color"
msgstr "Arxa plan rəngi"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Mətn rəngi"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Əsas rəng"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Arxa plan rəsmi :"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "\"Widget\"in arxa planı olaraq işlədiləcək rəsm"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "Mənbənin yazılacağı fayl"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Ümumi:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Parçanın mə'lumat quruluşuna bu \"widget\" əlavə ediləcəksə"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Ayrı sinif :"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Bu widget'in alt ağacını başqa bir sinifə qoy"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Başqa fayl :"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Bu widget'i başqa bir mənbə fayla qoy"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Görünmə qabiliyyəti :"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr ""
"\"Widget\"lərin görünmə qabiliyyətləri . Ümumi \"widget\"lər bir xəritəyə "
"idxal edilir ."

#: ../glade/property.c:1126
msgid "You need to select a color or background to copy"
msgstr "Köçürüləcək bir rəng ya da arxa planı bildirmək məcburiyyətindəsən"

#: ../glade/property.c:1145
msgid "Invalid selection in on_style_copy()"
msgstr "on_style_copy()'de hökmsüz seçki"

#: ../glade/property.c:1187
msgid "You need to copy a color or background pixmap first"
msgstr "Birinci bir rəngi ya da arxa plan rəsmini köçürməlisən"

#: ../glade/property.c:1193
msgid "You need to select a color to paste into"
msgstr "Yapışdırılacaq bir rəngi seçmək məcburiyyətindəsən"

#: ../glade/property.c:1203
msgid "You need to select a background pixmap to paste into"
msgstr "Yapışdırılacaq bir arxa plan rəsmini seçmək məcburiyyətindəsən"

#: ../glade/property.c:1455
msgid "Couldn't create pixmap from file\n"
msgstr "Fayldan rəsmi yarada bilmədim \n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1497
msgid "Signal"
msgstr "Siqnal"

#: ../glade/property.c:1499
msgid "Data"
msgstr "Haqqında"

#: ../glade/property.c:1500
msgid "After"
msgstr "Sonra"

#: ../glade/property.c:1501
msgid "Object"
msgstr "Cism"

#: ../glade/property.c:1532 ../glade/property.c:1696
msgid "Signal:"
msgstr "Siqnal :"

#: ../glade/property.c:1533
msgid "The signal to add a handler for"
msgstr "idarəçisi əlavə ediləcək olan siqnal"

#: ../glade/property.c:1547
msgid "The function to handle the signal"
msgstr "Bu siqnalın idarəçisi"

#: ../glade/property.c:1550
msgid "Data:"
msgstr "Mə'lumat:"

#: ../glade/property.c:1551
msgid "The data passed to the handler"
msgstr "idarəçiyə verilən bilgilər"

#: ../glade/property.c:1552
msgid "Object:"
msgstr "Cism :"

#: ../glade/property.c:1553
msgid "The object which receives the signal"
msgstr "Siqnalı alan cism"

#: ../glade/property.c:1554
msgid "After:"
msgstr "Sonra :"

#: ../glade/property.c:1555
msgid "If the handler runs after the class function"
msgstr "idarəçi sinifi funksiyalardan sonra çalışacaqsa"

#: ../glade/property.c:1568
msgid "Add"
msgstr "Əlavə Et"

#: ../glade/property.c:1574
msgid "Update"
msgstr "Yenilə"

#: ../glade/property.c:1586
msgid "Clear"
msgstr "Təmizlə"

#: ../glade/property.c:1636
msgid "Accelerators"
msgstr "Sür'ətləndiricilər"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1649
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1650
msgid "Key"
msgstr "Düymə"

#: ../glade/property.c:1651
msgid "Signal to emit"
msgstr "Veriləcək siqnal"

#: ../glade/property.c:1695
msgid "The accelerator key"
msgstr "Sür'ətləndirici tuşu"

#: ../glade/property.c:1697
msgid "The signal to emit when the accelerator is pressed"
msgstr "Sür'ətləndirici basılınca veriləcək siqnal"

#: ../glade/property.c:1846
msgid "Edit Text Property"
msgstr ""

#: ../glade/property.c:1884
msgid "<b>_Text:</b>"
msgstr ""

#: ../glade/property.c:1894
#, fuzzy
msgid "T_ranslatable"
msgstr "Tercüme edilə bilən kəlmələr:"

#: ../glade/property.c:1898
msgid "Has Context _Prefix"
msgstr ""

#: ../glade/property.c:1924
msgid "<b>Co_mments For Translators:</b>"
msgstr ""

#: ../glade/property.c:3886
msgid "Select X Events"
msgstr "Bir X hadisəsi seç"

#: ../glade/property.c:3895
msgid "Event Mask"
msgstr "Hadisə maskası"

#: ../glade/property.c:4025 ../glade/property.c:4074
msgid "You need to set the accelerator key"
msgstr "Sür'ətləndirici düyməsini bildirmək məcburiyyətindəsən"

#: ../glade/property.c:4032 ../glade/property.c:4081
msgid "You need to set the signal to emit"
msgstr "Veriləcək sinyalı bildirmək məcburiyyətindəsən"

#: ../glade/property.c:4308 ../glade/property.c:4364
msgid "You need to set the signal name"
msgstr "Siqnalın adını bildirmək məcburiyyətindəsən"

#: ../glade/property.c:4315 ../glade/property.c:4371
msgid "You need to set the handler for the signal"
msgstr "Siqnalın idarəçisini bildirmək məcburiyyətindəsən"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4574
#, c-format
msgid "%s signals"
msgstr "%s siqnal"

#: ../glade/property.c:4631
msgid "Select Signal"
msgstr "Siqnalı seç"

#: ../glade/property.c:4827
msgid "Value:"
msgstr "Qiymət :"

#: ../glade/property.c:4827
msgid "Min:"
msgstr "Minimal:"

#: ../glade/property.c:4827
msgid "Step Inc:"
msgstr "Addımlama eni :"

#: ../glade/property.c:4828
msgid "Page Inc:"
msgstr "Səhifə addımləməsi :"

#: ../glade/property.c:4828
msgid "Page Size:"
msgstr "Səhifə böyüklüyü :"

#: ../glade/property.c:4830
msgid "H Value:"
msgstr "Üfüqi qiymət :"

#: ../glade/property.c:4830
msgid "H Min:"
msgstr "Minimal üfüqi qiymət :"

#: ../glade/property.c:4830
msgid "H Max:"
msgstr "Maksimal üfüqi qiymət :"

#: ../glade/property.c:4830
msgid "H Step Inc:"
msgstr "Üfüqi addımlama eni :"

#: ../glade/property.c:4831
msgid "H Page Inc:"
msgstr "Üfüqi səhifə addımləməsi :"

#: ../glade/property.c:4831
msgid "H Page Size:"
msgstr "Üfüqi səhifə böyüklüyü :"

#: ../glade/property.c:4833
msgid "V Value:"
msgstr "Şaquli qiymət :"

#: ../glade/property.c:4833
msgid "V Min:"
msgstr "Minimal şaquli qiymət :"

#: ../glade/property.c:4833
msgid "V Max:"
msgstr "Maksimal şaquli qiymət :"

#: ../glade/property.c:4833
msgid "V Step Inc:"
msgstr "Şaquli addımlama eni :"

#: ../glade/property.c:4834
msgid "V Page Inc:"
msgstr "Şaquli səhifə addımləməsi :"

#: ../glade/property.c:4834
msgid "V Page Size:"
msgstr "Şaquli səhifə böyüklüyü :"

#: ../glade/property.c:4837
msgid "The initial value"
msgstr "Başlanğıc qiyməti"

#: ../glade/property.c:4838
msgid "The minimum value"
msgstr "Minimal qiymət"

#: ../glade/property.c:4839
msgid "The maximum value"
msgstr "Maksimal qiymət"

#: ../glade/property.c:4840
msgid "The step increment"
msgstr "Addım eni"

#: ../glade/property.c:4841
msgid "The page increment"
msgstr "Səhifə addımləməsi"

#: ../glade/property.c:4842
msgid "The page size"
msgstr "Səhifə böyüklüyü"

#: ../glade/property.c:4997
msgid "The requested font is not available."
msgstr "İstənilən yazı növü yoxdur ."

#: ../glade/property.c:5046
msgid "Select Named Style"
msgstr "Adlı tərzi seç"

#: ../glade/property.c:5057
msgid "Styles"
msgstr "Tərzlər"

#: ../glade/property.c:5116
msgid "Rename"
msgstr "Yenidən adlandır"

#: ../glade/property.c:5144
msgid "Cancel"
msgstr "Ləğv Et"

#: ../glade/property.c:5264
msgid "New Style:"
msgstr "Yeni tərz :"

#: ../glade/property.c:5278 ../glade/property.c:5399
msgid "Invalid style name"
msgstr "Hökmsüz tərz adı"

#: ../glade/property.c:5286 ../glade/property.c:5409
msgid "That style name is already in use"
msgstr "Bu tərz adı onsuz da istifadədir"

#: ../glade/property.c:5384
msgid "Rename Style To:"
msgstr "Tərzi yenidən adlandır :"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"Faylı yenidən adlandıra bilmədim:\n"
"  %s\n"
"  =>\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"Faylı yarada bilmədim :\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "XML faylı yazılırken xəta oldu\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Glade tərəfindən yaradılan tərcümə edilə bilən mətnlər faylı .\n"
" * Bu faylı lahiyənin POTFILES.in faylına əlavə et .\n"
" * Fəqət bu faylı proqraımın bir parçası olaraq dərləmə .\n"
" */\n"
"\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"Hökmsüz istifadəçi ara üzü mənbə faylı : %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"Hökmsüz istifadəçi ara üzü mənbə başlığı : %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"Hökmsüz geri nəticəler mənbə faylı : %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"Hökmsüz geri nəticəler mənbə başlığı : %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"Hökmsüz dəstək mənbə faylı : %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"Hökmsüz dəstək mənbə başlığı : %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"Fayla əlavə edə bilmədim :\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"Fayla yazarkən xəta oldu :\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr ""
"Fayl adını lahiyə seçənəkləri dialoqunda bildirmək məcburiyyətindədir ."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"Fayl adı normal bir fayl adı olacaq .\n"
"Onu bildirmək üçün lahiyə seçənəkləri dialoqunu işlət ."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Widget ağacı"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "Widget qutuda tapılmadı"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "Widget cədvəldə tapılmadı"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "Widget bəlli qutuda tapılmadı"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "Widget paketləyicidə tapılmadı"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"Fayla çata bilmədim :\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"Faylı aça bilmədim :\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"Fayldan oxurkən xəta oldu :\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"Qovluğu yaradıla bilmədi :\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"Qovluğa çata bilmədim :\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"Hökmsüz cığır :\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Layihələr"

#: ../glade/utils.c:1628
msgid "project"
msgstr "lahiyə"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"Qovluğu aça bilmədim :\n"
"  %s\n"
