/*  Gtk+ User Interface Builder
 *  Copyright (C) 1998  <PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this program; if not, write to the Free Software
 *  Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 */

#include "gladeconfig.h"

#include <gtk/gtk.h>

const gchar * GladeNamedIcons[] =
{
  GTK_STOCK_DIALOG_AUTHENTICATION,
  GTK_STOCK_DIALOG_INFO,
  GTK_STOCK_DIALOG_WARNING,
  GTK_STOCK_DIALOG_ERROR,
  GTK_STOCK_DIALOG_QUESTION,
  GTK_STOCK_DND,
  GTK_STOCK_DND_MULTIPLE,
  GTK_STOCK_ABOUT,
  GTK_STOCK_ADD,
  GTK_STOCK_APPLY,
  GTK_STOCK_BOLD,
  GTK_STOCK_CANCEL,
  GTK_STOCK_CDROM,
  GTK_STOCK_CLEAR,
  GTK_STOCK_CLOSE,
  GTK_STOCK_COLOR_PICKER,
  GTK_STOCK_CONVERT,
  GTK_STOCK_CONNECT,
  GTK_STOCK_COPY,
  GTK_STOCK_CUT,
  GTK_STOCK_DELETE,
  GTK_STOCK_DIRECTORY,
  GTK_STOCK_DISCONNECT,
  GTK_STOCK_EDIT,
  GTK_STOCK_EXECUTE,
  GTK_STOCK_FILE,
  GTK_STOCK_FIND,
  GTK_STOCK_FIND_AND_REPLACE,
  GTK_STOCK_FLOPPY,
  GTK_STOCK_FULLSCREEN,
  GTK_STOCK_GOTO_BOTTOM,
  GTK_STOCK_GOTO_FIRST,
  GTK_STOCK_GOTO_LAST,
  GTK_STOCK_GOTO_TOP,
  GTK_STOCK_GO_BACK,
  GTK_STOCK_GO_DOWN,
  GTK_STOCK_GO_FORWARD,
  GTK_STOCK_GO_UP,
  GTK_STOCK_HARDDISK,
  GTK_STOCK_HELP,
  GTK_STOCK_HOME,
  GTK_STOCK_INDEX,
  GTK_STOCK_INDENT,
  GTK_STOCK_INFO,
  GTK_STOCK_UNINDENT,
  GTK_STOCK_ITALIC,
  GTK_STOCK_JUMP_TO,
  GTK_STOCK_JUSTIFY_CENTER,
  GTK_STOCK_JUSTIFY_FILL,
  GTK_STOCK_JUSTIFY_LEFT,
  GTK_STOCK_JUSTIFY_RIGHT,
  GTK_STOCK_LEAVE_FULLSCREEN,
  GTK_STOCK_MISSING_IMAGE,
  GTK_STOCK_MEDIA_FORWARD,
  GTK_STOCK_MEDIA_NEXT,
  GTK_STOCK_MEDIA_PAUSE,
  GTK_STOCK_MEDIA_PLAY,
  GTK_STOCK_MEDIA_PREVIOUS,
  GTK_STOCK_MEDIA_RECORD,
  GTK_STOCK_MEDIA_REWIND,
  GTK_STOCK_MEDIA_STOP,
  GTK_STOCK_NETWORK,
  GTK_STOCK_NEW,
  GTK_STOCK_NO,
  GTK_STOCK_OK,
  GTK_STOCK_OPEN,
  GTK_STOCK_PASTE,
  GTK_STOCK_PREFERENCES,
  GTK_STOCK_PRINT,
  GTK_STOCK_PRINT_PREVIEW,
  GTK_STOCK_PROPERTIES,
  GTK_STOCK_QUIT,
  GTK_STOCK_REDO,
  GTK_STOCK_REFRESH,
  GTK_STOCK_REMOVE,
  GTK_STOCK_REVERT_TO_SAVED,
  GTK_STOCK_SAVE,
  GTK_STOCK_SAVE_AS,
  GTK_STOCK_SELECT_COLOR,
  GTK_STOCK_SELECT_FONT,
  GTK_STOCK_SORT_ASCENDING,
  GTK_STOCK_SORT_DESCENDING,
  GTK_STOCK_SPELL_CHECK,
  GTK_STOCK_STOP,
  GTK_STOCK_STRIKETHROUGH,
  GTK_STOCK_UNDELETE,
  GTK_STOCK_UNDERLINE,
  GTK_STOCK_UNDO,
  GTK_STOCK_YES,
  GTK_STOCK_ZOOM_100,
  GTK_STOCK_ZOOM_FIT,
  GTK_STOCK_ZOOM_IN,
  GTK_STOCK_ZOOM_OUT,

#ifdef USE_GNOME
  "stock_chart",
  "stock_chart-autoformat",
  "stock_chart-data-in-columns",
  "stock_chart-data-in-rows",
  "stock_chart-edit-type",
  "stock_chart-reorganize",
  "stock_chart-toggle-axes",
  "stock_chart-toggle-axes-title",
  "stock_chart-toggle-hgrid",
  "stock_chart-toggle-legend",
  "stock_chart-toggle-title",
  "stock_chart-toggle-vgrid",
  "stock_glue",
  "stock_gluepoint-down",
  "stock_gluepoint-horizontal-center",
  "stock_gluepoint-horizontal-left",
  "stock_gluepoint-horizontal-right",
  "stock_gluepoint-left",
  "stock_gluepoint-relative",
  "stock_gluepoint-right",
  "stock_gluepoint-up",
  "stock_gluepoint-vertical-bottom",
  "stock_gluepoint-vertical-center",
  "stock_gluepoint-vertical-top",
  "stock_insert-chart",
  "stock_compile",
  "stock_error-next",
  "stock_error-next-16",
  "stock_error-previous",
  "stock_error-previous-16",
  "stock_macro-check-brackets",
  "stock_macro-insert",
  "stock_macro-insert-breakpoint",
  "stock_macro-jump-back",
  "stock_macro-objects",
  "stock_macro-organizer",
  "stock_macro-stop-after-command",
  "stock_macro-stop-after-procedure",
  "stock_macro-stop-watching",
  "stock_macro-watch-variable",
  "stock_record-macro",
  "stock_run-macro",
  "stock_script",
  "stock_tools-macro",
  "stock_add-decimal-place",
  "stock_advanced-filter",
  "stock_autofilter",
  "stock_data-delete-link",
  "stock_data-delete-query",
  "stock_data-delete-record",
  "stock_data-delete-sql-query",
  "stock_data-delete-table",
  "stock_data-edit-link",
  "stock_data-edit-query",
  "stock_data-edit-sql-query",
  "stock_data-edit-table",
  "stock_data-explorer",
  "stock_data-first",
  "stock_data-last",
  "stock_data-link",
  "stock_data-linked-table",
  "stock_data-links",
  "stock_data-new-link",
  "stock_data-new-query",
  "stock_data-new-record",
  "stock_data-new-sql-query",
  "stock_data-new-table",
  "stock_data-next",
  "stock_data-previous",
  "stock_data-queries",
  "stock_data-query",
  "stock_data-query-rename",
  "stock_data-save",
  "stock_data-sources",
  "stock_data-sources-delete",
  "stock_data-sources-hand",
  "stock_data-sources-modified",
  "stock_data-sources-new",
  "stock_data-table",
  "stock_data-tables",
  "stock_data-undo",
  "stock_database",
  "stock_delete-autofilter",
  "stock_delete-decimal-place",
  "stock_filter-data-by-criteria",
  "stock_filter-navigator",
  "stock_filters",
  "stock_format-default",
  "stock_format-percent",
  "stock_format-scientific",
  "stock_function-autopilot",
  "stock_goal-seek",
  "stock_insert-fields",
  "stock_lock",
  "stock_lock-broken",
  "stock_lock-ok",
  "stock_lock-open",
  "stock_record-number",
  "stock_signature",
  "stock_signature-bad",
  "stock_signature-ok",
  "stock_sort-ascending",
  "stock_sort-column-ascending",
  "stock_sort-criteria",
  "stock_sort-descending",
  "stock_sort-row-ascending",
  "stock_standard-filter",
  "stock_sum",
  "stock_update-data",
  "stock_attach",
  "stock_certificate",
  "stock_my-documents",
  "stock_new",
  "stock_new-drawing",
  "stock_new-formula",
  "stock_new-html",
  "stock_new-labels",
  "stock_new-master-document",
  "stock_new-presentation",
  "stock_new-spreadsheet",
  "stock_new-template",
  "stock_new-text",
  "stock_preview-four-pages",
  "stock_preview-two-pages",
  "stock_print-layout",
  "stock_print-options",
  "stock_print-resolution",
  "stock_samples",
  "stock_script",
  "stock_scripts",
  "stock_task",
  "stock_task-assigned",
  "stock_task-assigned-to",
  "stock_task-recurring",
  "stock_todo",
  "stock_view-html-source",
  "stock_smiley-1",
  "stock_smiley-10",
  "stock_smiley-11",
  "stock_smiley-12",
  "stock_smiley-13",
  "stock_smiley-14",
  "stock_smiley-15",
  "stock_smiley-16",
  "stock_smiley-17",
  "stock_smiley-18",
  "stock_smiley-19",
  "stock_smiley-2",
  "stock_smiley-20",
  "stock_smiley-21",
  "stock_smiley-22",
  "stock_smiley-23",
  "stock_smiley-24",
  "stock_smiley-25",
  "stock_smiley-26",
  "stock_smiley-3",
  "stock_smiley-4",
  "stock_smiley-5",
  "stock_smiley-6",
  "stock_smiley-7",
  "stock_smiley-8",
  "stock_smiley-9",
  "stock_form-activation-order",
  "stock_form-add-field",
  "stock_form-automatic-control-focus",
  "stock_form-autopilots",
  "stock_form-button",
  "stock_form-checkbox",
  "stock_form-combobox",
  "stock_form-control-properties",
  "stock_form-currency-field",
  "stock_form-date-field",
  "stock_form-design-mode",
  "stock_form-file-selection",
  "stock_form-formatted-field",
  "stock_form-frame",
  "stock_form-image-button",
  "stock_form-image-control",
  "stock_form-label",
  "stock_form-letter-dialog",
  "stock_form-line-horizontal",
  "stock_form-line-vertical",
  "stock_form-listbox",
  "stock_form-navigator",
  "stock_form-numerical-field",
  "stock_form-open-in-design-mode",
  "stock_form-pattern-field",
  "stock_form-progressbar",
  "stock_form-properties",
  "stock_form-radio",
  "stock_form-table-control",
  "stock_form-text-box",
  "stock_form-time-field",
  "stock_insert-form",
  "stock_macro-controls",
  "stock_show-form-dialog",
  "stock_show-hidden-controls",
  "stock_view-fields",
  "stock_3d-favourites",
  "stock_about",
  "stock_active",
  "stock_alarm",
  "stock_allow-effects",
  "stock_anchor",
  "stock_animation",
  "stock_autocompletion",
  "stock_autopilot",
  "stock_autopilot-24",
  "stock_bell",
  "stock_book_blue",
  "stock_book_green",
  "stock_book_open",
  "stock_book_red",
  "stock_book_yellow",
  "stock_briefcase",
  "stock_calc-accept",
  "stock_calc-cancel",
  "stock_calendar",
  "stock_calendar-and-tasks",
  "stock_calendar-view-day",
  "stock_calendar-view-list",
  "stock_calendar-view-month",
  "stock_calendar-view-week",
  "stock_calendar-view-work-week",
  "stock_calendar-view-year",
  "stock_cell-phone",
  "stock_check-filled",
  "stock_close",
  "stock_color",
  "stock_copy",
  "stock_creditcard",
  "stock_cut",
  "stock_default-folder",
  "stock_delete",
  "stock_dialog-error",
  "stock_dialog-info",
  "stock_dialog-question",
  "stock_dialog-warning",
  "stock_drag-mode",
  "stock_edit",
  "stock_equals",
  "stock_example",
  "stock_exit",
  "stock_extended-help",
  "stock_file-properties",
  "stock_flip-horizontally",
  "stock_flip-vertically",
  "stock_folder",
  "stock_folder-copy",
  "stock_folder-move",
  "stock_folder-properties",
  "stock_format-page",
  "stock_fullscreen",
  "stock_help",
  "stock_help-add-bookmark",
  "stock_help-agent",
  "stock_help-book",
  "stock_help-book-open",
  "stock_help-chat",
  "stock_help-document",
  "stock_help-pane-off",
  "stock_help-pane-on",
  "stock_home",
  "stock_id",
  "stock_init",
  "stock_keyring",
  "stock_landline-phone",
  "stock_leave-fullscreen",
  "stock_mark",
  "stock_new",
  "stock_new-24h-appointment",
  "stock_new-appointment",
  "stock_new-dir",
  "stock_new-meeting",
  "stock_news",
  "stock_not",
  "stock_notebook",
  "stock_notes",
  "stock_openoffice",
  "stock_paste",
  "stock_people",
  "stock_person",
  "stock_presentation-box",
  "stock_presentation-styles",
  "stock_properties",
  "stock_redo",
  "stock_refresh",
  "stock_score-high",
  "stock_score-higher",
  "stock_score-highest",
  "stock_score-low",
  "stock_score-lower",
  "stock_score-lowest",
  "stock_score-normal",
  "stock_scores",
  "stock_search",
  "stock_search-and-replace",
  "stock_send-fax",
  "stock_show-all",
  "stock_stop",
  "stock_styles",
  "stock_summary",
  "stock_test-mode",
  "stock_timer",
  "stock_timer_stopped",
  "stock_timezone",
  "stock_toggle-info",
  "stock_toggle-preview",
  "stock_toilet-paper",
  "stock_trash_full",
  "stock_undelete",
  "stock_undo",
  "stock_unknown",
  "stock_view-details",
  "stock_weather-cloudy",
  "stock_weather-few-clouds",
  "stock_weather-fog",
  "stock_weather-night-clear",
  "stock_weather-night-few-clouds",
  "stock_weather-showers",
  "stock_weather-snow",
  "stock_weather-storm",
  "stock_weather-sunny",
  "stock_web-calendar",
  "stock_web-support",
  "stock_3d-color-picker",
  "stock_3d-colors",
  "stock_3d-custom-color",
  "stock_3d-effects",
  "stock_arrowstyle",
  "stock_brightness",
  "stock_bucketfill",
  "stock_channel-blue",
  "stock_channel-green",
  "stock_channel-red",
  "stock_contrast",
  "stock_crop",
  "stock_display-grid",
  "stock_display-guides",
  "stock_distort",
  "stock_draw-arc",
  "stock_draw-callouts",
  "stock_draw-circle",
  "stock_draw-circle-arc",
  "stock_draw-circle-pie",
  "stock_draw-circle-pie-unfilled",
  "stock_draw-circle-segment",
  "stock_draw-circle-segment-unfilled",
  "stock_draw-circle-unfilled",
  "stock_draw-cone",
  "stock_draw-connector",
  "stock_draw-connector-ends-with-arrow",
  "stock_draw-connector-ends-with-circle",
  "stock_draw-connector-starts-with-arrow",
  "stock_draw-connector-starts-with-circle",
  "stock_draw-connector-with-arrows",
  "stock_draw-connector-with-circles",
  "stock_draw-cube",
  "stock_draw-curve",
  "stock_draw-curve-filled",
  "stock_draw-curved-connector",
  "stock_draw-curved-connector-ends-with-arrow",
  "stock_draw-curved-connector-ends-with-circle",
  "stock_draw-curved-connector-starts-with-arrow",
  "stock_draw-curved-connector-starts-with-circle",
  "stock_draw-curved-connector-with-arrows",
  "stock_draw-curved-connector-with-circles",
  "stock_draw-cylinder",
  "stock_draw-dimension-line",
  "stock_draw-ellipse",
  "stock_draw-ellipse-pie",
  "stock_draw-ellipse-pie-unfilled",
  "stock_draw-ellipse-segment",
  "stock_draw-ellipse-segment-unfilled",
  "stock_draw-ellipse-unfilled",
  "stock_draw-freeform-line",
  "stock_draw-freeform-line-filled",
  "stock_draw-half-sphere",
  "stock_draw-line",
  "stock_draw-line-45",
  "stock_draw-line-connector",
  "stock_draw-line-connector-ends-with-arrow",
  "stock_draw-line-connector-ends-with-circle",
  "stock_draw-line-connector-starts-with-arrow",
  "stock_draw-line-connector-starts-with-circle",
  "stock_draw-line-connector-with-arrows",
  "stock_draw-line-connector-with-circles",
  "stock_draw-line-ends-with-arrow",
  "stock_draw-line-starts-with-arrow",
  "stock_draw-line-with-arrow-circle",
  "stock_draw-line-with-arrow-square",
  "stock_draw-line-with-arrows",
  "stock_draw-line-with-circle-arrow",
  "stock_draw-line-with-square-arrow",
  "stock_draw-polygon",
  "stock_draw-polygon-45",
  "stock_draw-polygon-45-filled",
  "stock_draw-polygon-filled",
  "stock_draw-pyramid",
  "stock_draw-rectangle",
  "stock_draw-rectangle-unfilled",
  "stock_draw-rounded-rectangle",
  "stock_draw-rounded-rectangle-unfilled",
  "stock_draw-rounded-square",
  "stock_draw-rounded-square-unfilled",
  "stock_draw-selection",
  "stock_draw-shell",
  "stock_draw-sphere",
  "stock_draw-square",
  "stock_draw-square-unfilled",
  "stock_draw-straight-connector",
  "stock_draw-straight-connector-ends-with-arrow",
  "stock_draw-straight-connector-ends-with-circle",
  "stock_draw-straight-connector-starts-with-arrow",
  "stock_draw-straight-connector-starts-with-circle",
  "stock_draw-straight-connector-with-arrows",
  "stock_draw-straight-connector-with-circles",
  "stock_draw-torus",
  "stock_draw-vertical-callouts",
  "stock_edit-points",
  "stock_filters-aging",
  "stock_filters-charcoal",
  "stock_filters-invert",
  "stock_filters-pixelize",
  "stock_filters-pop-art",
  "stock_filters-posterize",
  "stock_filters-relief",
  "stock_filters-remove-noise",
  "stock_filters-sharpen",
  "stock_filters-smooth",
  "stock_filters-solarize",
  "stock_flip",
  "stock_gamma",
  "stock_gradient",
  "stock_graphic-styles",
  "stock_graphics-align-bottom",
  "stock_graphics-align-center",
  "stock_graphics-align-centered",
  "stock_graphics-align-left",
  "stock_graphics-align-right",
  "stock_graphics-align-top",
  "stock_guides",
  "stock_imagemap-editor",
  "stock_insert_image",
  "stock_linepen",
  "stock_modify-layout",
  "stock_node-add",
  "stock_node-close-path",
  "stock_node-convert",
  "stock_node-corner",
  "stock_node-corner-to-smooth",
  "stock_node-curve-split",
  "stock_node-delete",
  "stock_node-mark-for-deletion",
  "stock_node-move",
  "stock_node-smooth-to-symmetrical",
  "stock_placeholder-graphic",
  "stock_placeholder-line-contour",
  "stock_placeholder-picture",
  "stock_placeholder-text",
  "stock_quickmask",
  "stock_rotate",
  "stock_rotate-3d",
  "stock_rotation-mode",
  "stock_shadow",
  "stock_show-draw-functions",
  "stock_toggle-graphics",
  "stock_transform-circle-perspective",
  "stock_transform-circle-slant",
  "stock_transparency",
  "stock_wallpaper-center",
  "stock_wallpaper-fill",
  "stock_wallpaper-scale",
  "stock_wallpaper-tile",
  "stock_bluetooth",
  "stock_export",
  "stock_insert-file",
  "stock_network-printer",
  "stock_open",
  "stock_open-read-only",
  "stock_opensave",
  "stock_print",
  "stock_print-driver",
  "stock_print-duplex",
  "stock_print-duplex-no-tumble",
  "stock_print-duplex-tumble",
  "stock_print-non-duplex",
  "stock_print-preview",
  "stock_print-preview-print",
  "stock_print-setup",
  "stock_printers",
  "stock_reload",
  "stock_save",
  "stock_save-as",
  "stock_save-pdf",
  "stock_save-template",
  "stock_save_as",
  "stock_up-one-dir",
  "stock_3dsound",
  "stock_effects-sound",
  "stock_headphones",
  "stock_insert-video-plugin",
  "stock_line_in",
  "stock_media-fwd",
  "stock_media-next",
  "stock_media-pause",
  "stock_media-play",
  "stock_media-prev",
  "stock_media-rec",
  "stock_media-rew",
  "stock_media-shuffle",
  "stock_media-stop",
  "stock_mic",
  "stock_midi",
  "stock_music-library",
  "stock_playlist",
  "stock_repeat",
  "stock_shuffle",
  "stock_smart-playlist",
  "stock_sound",
  "stock_volume",
  "stock_volume-0",
  "stock_volume-max",
  "stock_volume-med",
  "stock_volume-min",
  "stock_volume-mute",
  "stock_bottom",
  "stock_down",
  "stock_down-with-subpoints",
  "stock_first",
  "stock_first-page",
  "stock_last",
  "stock_last-page",
  "stock_left",
  "stock_left-with-subpoints",
  "stock_live-mode",
  "stock_navigate-next",
  "stock_navigate-prev",
  "stock_navigator",
  "stock_navigator-all-or-sel-toggle",
  "stock_navigator-database-ranges",
  "stock_navigator-drag-mode",
  "stock_navigator-edit-entry",
  "stock_navigator-foonote-body-toggle",
  "stock_navigator-footer-body-toggle",
  "stock_navigator-header-body-toggle",
  "stock_navigator-headings",
  "stock_navigator-indexes",
  "stock_navigator-insert-as-copy",
  "stock_navigator-insert-as-link",
  "stock_navigator-insert-index",
  "stock_navigator-insert-linked",
  "stock_navigator-levels",
  "stock_navigator-list-box-toggle",
  "stock_navigator-master-toggle",
  "stock_navigator-next-object",
  "stock_navigator-open-toolbar",
  "stock_navigator-previous-object",
  "stock_navigator-range-names",
  "stock_navigator-references",
  "stock_navigator-reminder",
  "stock_navigator-scenarios",
  "stock_navigator-sections",
  "stock_navigator-shift-down",
  "stock_navigator-shift-left",
  "stock_navigator-shift-right",
  "stock_navigator-shift-up",
  "stock_navigator-table-formula",
  "stock_navigator-text",
  "stock_navigator-update-entry",
  "stock_navigator-wrong-table-formula",
  "stock_new-tab",
  "stock_next",
  "stock_next-page",
  "stock_previous",
  "stock_previous-page",
  "stock_right",
  "stock_right-with-subpoints",
  "stock_top",
  "stock_undo-history",
  "stock_up",
  "stock_up-with-subpoints",
  "stock_zoom",
  "stock_zoom-1",
  "stock_zoom-in",
  "stock_zoom-next",
  "stock_zoom-object",
  "stock_zoom-optimal",
  "stock_zoom-out",
  "stock_zoom-page",
  "stock_zoom-page-width",
  "stock_zoom-previous",
  "stock_zoom-shift",
  "stock_addressbook",
  "stock_appointment-reminder",
  "stock_appointment-reminder-excl",
  "stock_channel",
  "stock_connect",
  "stock_connect-to-url",
  "stock_contact",
  "stock_contact-list",
  "stock_directory-server",
  "stock_disconnect",
  "stock_exchange-connector",
  "stock_groupwise-connector",
  "stock_hand-signed",
  "stock_hyperlink",
  "stock_hyperlink-internet-search",
  "stock_hyperlink-target",
  "stock_hyperlink-toolbar",
  "stock_inbox",
  "stock_insert-url",
  "stock_internet",
  "stock_link",
  "stock_mail",
  "stock_mail-accounts",
  "stock_mail-compose",
  "stock_mail-copy",
  "stock_mail-druid",
  "stock_mail-druid-account",
  "stock_mail-filters-apply",
  "stock_mail-flag-for-followup",
  "stock_mail-flag-for-followup-done",
  "stock_mail-forward",
  "stock_mail-handling",
  "stock_mail-hide-deleted",
  "stock_mail-hide-read",
  "stock_mail-hide-selected",
  "stock_mail-import",
  "stock_mail-merge",
  "stock_mail-move",
  "stock_mail-open",
  "stock_mail-open-multiple",
  "stock_mail-priority-high",
  "stock_mail-receive",
  "stock_mail-replied",
  "stock_mail-reply",
  "stock_mail-reply-to-all",
  "stock_mail-send",
  "stock_mail-send-receive",
  "stock_mail-unread",
  "stock_mail-unread-multiple",
  "stock_message-display",
  "stock_not-spam",
  "stock_online-layout",
  "stock_outbox",
  "stock_post-message",
  "stock_proxy",
  "stock_sent-mail",
  "stock_shared-by-me",
  "stock_shared-to-me",
  "stock_spam",
  "stock_video-conferencing",
  "stock_3d-3d-attributes-only",
  "stock_3d-all-attributes",
  "stock_3d-geometry",
  "stock_3d-light",
  "stock_3d-light-off",
  "stock_3d-light-on",
  "stock_3d-material",
  "stock_3d-normals-double-sided",
  "stock_3d-normals-double-sided-closed-body",
  "stock_3d-normals-flat",
  "stock_3d-normals-flip-illumination",
  "stock_3d-normals-object-specific",
  "stock_3d-normals-spherical",
  "stock_3d-perspective",
  "stock_3d-shading",
  "stock_3d-texture",
  "stock_3d-texture-and-shading",
  "stock_3d-texture-object-specific",
  "stock_3d-texture-only",
  "stock_3d-texture-parallel",
  "stock_3d-texture-spherical",
  "stock_add-bookmark",
  "stock_auto-contour",
  "stock_bookmark",
  "stock_bring-backward",
  "stock_bring-forward",
  "stock_create-with-attributes",
  "stock_delete-bookmark",
  "stock_edit-bookmark",
  "stock_effects",
  "stock_effects-more-options",
  "stock_effects-object",
  "stock_effects-object-colorize",
  "stock_effects-object-hide",
  "stock_effects-play-in-full",
  "stock_effects-preview",
  "stock_enter-group",
  "stock_exit-group",
  "stock_file-with-objects",
  "stock_format-object",
  "stock_formula-cursor",
  "stock_frame",
  "stock_group",
  "stock_handles-big",
  "stock_handles-simple",
  "stock_insert-applet",
  "stock_insert-floating-frame",
  "stock_insert-gluepoint",
  "stock_insert-math-object",
  "stock_insert-note",
  "stock_insert-ole-object",
  "stock_insert-plugin",
  "stock_insert-rule",
  "stock_insert-single-column-text-frame",
  "stock_insert-slide",
  "stock_insert-sound-plugin",
  "stock_insert-text-frame",
  "stock_insert_graphic",
  "stock_insert_special_character",
  "stock_interaction",
  "stock_new-bcard",
  "stock_new-window",
  "stock_object-behind",
  "stock_object-infront",
  "stock_pin",
  "stock_position-size",
  "stock_reverse-order",
  "stock_slide-design",
  "stock_slide-duplicate",
  "stock_slide-expand",
  "stock_slide-reherse-timings",
  "stock_slide-show",
  "stock_slide-showhide",
  "stock_snap-grid",
  "stock_snap-guides",
  "stock_snap-margins",
  "stock_snap-object",
  "stock_snap-object-points",
  "stock_symbol-selection",
  "stock_to-3d",
  "stock_to-3d-rotation-object",
  "stock_to-bottom",
  "stock_to-curve",
  "stock_to-polygon",
  "stock_to-top",
  "stock_ungroup",
  "stock_unlink",
  "stock_view-function-selection",
  "stock_alignment",
  "stock_alignment-bottom",
  "stock_alignment-centered",
  "stock_alignment-centered-vertically",
  "stock_alignment-left",
  "stock_alignment-right",
  "stock_alignment-top",
  "stock_cell-align-bottom",
  "stock_cell-align-center",
  "stock_cell-align-top",
  "stock_choose-themes",
  "stock_datapilot",
  "stock_delete-column",
  "stock_delete-row",
  "stock_exchange-columns",
  "stock_exchange-rows",
  "stock_group-cells",
  "stock_insert-cells",
  "stock_insert-cells-down",
  "stock_insert-cells-right",
  "stock_insert-columns",
  "stock_insert-names-define",
  "stock_insert-rows",
  "stock_insert-table",
  "stock_select-cell",
  "stock_select-column",
  "stock_select-row",
  "stock_select-table",
  "stock_sort-table-column-ascending",
  "stock_sort-table-row-ascending",
  "stock_table-align-bottom",
  "stock_table-align-center",
  "stock_table-align-top",
  "stock_table-borders",
  "stock_table-combine",
  "stock_table-fit-height",
  "stock_table-fit-width",
  "stock_table-fixed",
  "stock_table-fixed-proportional",
  "stock_table-line-color",
  "stock_table-line-style",
  "stock_table-optimize",
  "stock_table-same-height",
  "stock_table-same-width",
  "stock_table-split",
  "stock_table-variable",
  "stock_table_borders",
  "stock_table_fill",
  "stock_ungroup-cells",
  "stock_autoformat",
  "stock_autospellcheck",
  "stock_autotext",
  "stock_chart-scale-text",
  "stock_decrease-font",
  "stock_directcursor",
  "stock_draw-text",
  "stock_draw-text-animation",
  "stock_draw-text-frame",
  "stock_draw-vertical-text",
  "stock_draw-vertical-text-frame",
  "stock_edit-contour",
  "stock_edit-headers-and-footers",
  "stock_effects-text",
  "stock_euro",
  "stock_font",
  "stock_font-formatting-toggle",
  "stock_font-size",
  "stock_fontwork",
  "stock_fontwork-2dshadow",
  "stock_fontwork-3dshadow",
  "stock_fontwork-adaptation-off",
  "stock_fontwork-adaptation-rotate",
  "stock_fontwork-adaptation-slant-h",
  "stock_fontwork-adaptation-slant-v",
  "stock_fontwork-adaptation-straight",
  "stock_fontwork-align-fill",
  "stock_fontwork-noshadow",
  "stock_fontwork-preview-spline",
  "stock_fontwork-reverse-text-flow",
  "stock_fontwork-shadow-angle",
  "stock_fontwork-shadow-length",
  "stock_fontwork-shadow-x-offset",
  "stock_fontwork-shadow-y-offset",
  "stock_fontwork-spline-distance",
  "stock_fontwork-spline-indent",
  "stock_fontwork-text-border",
  "stock_format-character",
  "stock_format-numbering-bullets",
  "stock_format-paragraph",
  "stock_increase-font",
  "stock_insert-caption",
  "stock_insert-cross-reference",
  "stock_insert-fields-author",
  "stock_insert-fields-subject",
  "stock_insert-fields-title",
  "stock_insert-footer",
  "stock_insert-header",
  "stock_insert_endnote",
  "stock_insert_footnote",
  "stock_insert_index_marker",
  "stock_insert_section",
  "stock_line-spacing-1",
  "stock_line-spacing-1.5",
  "stock_line-spacing-2",
  "stock_list-insert-unnumbered",
  "stock_list_bullet",
  "stock_list_enum",
  "stock_list_enum-off",
  "stock_list_enum-restart",
  "stock_nonprinting-chars",
  "stock_page-number",
  "stock_page-total-number",
  "stock_paragraph-spacing-decrease",
  "stock_paragraph-spacing-increase",
  "stock_select-all",
  "stock_spellcheck",
  "stock_styles-character-styles",
  "stock_styles-fill-format-mode",
  "stock_styles-frame-styles",
  "stock_styles-new-style-from-selection",
  "stock_styles-numbering-styles",
  "stock_styles-page-styles",
  "stock_styles-paragraph-styles",
  "stock_styles-update-style",
  "stock_subscript",
  "stock_superscript",
  "stock_text-direction-ltr",
  "stock_text-direction-ttb",
  "stock_text-double-click-to-edit",
  "stock_text-monospaced",
  "stock_text-outline",
  "stock_text-quickedit",
  "stock_text-select-text-only",
  "stock_text-shadow",
  "stock_text-spacing",
  "stock_text-strikethrough",
  "stock_text_bold",
  "stock_text_center",
  "stock_text_color_background",
  "stock_text_color_foreground",
  "stock_text_color_hilight",
  "stock_text_indent",
  "stock_text_italic",
  "stock_text_justify",
  "stock_text_left",
  "stock_text_right",
  "stock_text_underlined",
  "stock_text_underlined-double",
  "stock_text_unindent",
  "stock_thesaurus",
  "stock_to-background",
  "stock_to-foreground",
  "stock_tools-hyphenation",
  "stock_update-fields",
  "stock_view-field-shadings",
  "stock_wrap-around",
  "stock_wrap-behind",
  "stock_wrap-contour",
  "stock_wrap-interrupt",
  "stock_wrap-left",
  "stock_wrap-optimal",
  "stock_wrap-right"
#endif
};

int GladeNamedIconsSize = G_N_ELEMENTS (GladeNamedIcons);
