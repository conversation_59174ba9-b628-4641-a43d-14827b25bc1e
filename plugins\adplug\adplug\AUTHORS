AdPlug is copyright (C) 1999 - 2017 <PERSON> <<EMAIL>>, et al.
All library code is written by <PERSON>, except the parts listed below.

Additional Credits:
-------------------
<PERSON> <<EMAIL>>:
  - Player development for the following formats:
    - MID: MIDI Audio File Format
    - CMF: Creative Music File Format (original player)
    - SCI: Sierra's AdLib Audio File Format
    - LAA: LucasArts AdLib Audio File Format

Marc <PERSON> <remove space and vowels from "marc winterrowd" and append
                 "at yahoo" plus "dot com">:
  - M: Origin Music player development

<PERSON><PERSON><PERSON> <<EMAIL>>:
  - First YM3812 emulator development

<PERSON> <<EMAIL>>:
  - Second YM3812 emulator development

Death Adder <<EMAIL>>:
  - provided many file format specifications
  - moral support ;)

<PERSON><PERSON> <<EMAIL>>:
  - support for .SAT (early Surprise! Adlib Tracker) files

Akintunde Omitowoju <<EMAIL>>:
  - ROL: AdLib Visual Composer player development

Nikita <PERSON> <<EMAIL>>:
  - Player development:
    - XAD: shell player and custom module formats
  - Protracker loader development:
    - SNG: Faust Music Creator
    - MAD: Mlat Adlib Tracker
    - CFF: BoomTracker 4.0
    - DTM: DeFy Adlib Tracker
  - Scream Tracker 3 loader development:
    - DMO: Twin TrackPlayer
  - XMS-Tracker detection support in AMD loader

Sjoerd van der Berg <<EMAIL>>:
  - DRO: DOSBox Raw OPL player development

Goetz Waschk <<EMAIL>>:
  - Mandrake Linux packages
  - Red Hat RPM spec files

Mike Gorchak <<EMAIL>>:
  - QNX packages
  - QNX qpg file

Tyler Montbriand <<EMAIL>>:
  - AMD64 (x86_64) fixes
  - ROL: Added pitch handling

Borg Number One [ Borg No. One ] <<EMAIL>>:
  - Many thanks for the huge amount of work to find the original
    author (Andras Molnar) of the LOUDNESS Sound System and managing
    to get the sources from him.

Matthew Gambrell <<EMAIL>>:
  - Enhancements to the DRO (DOSBox Raw OPL) format
  - Added dual OPL2 and OPL3 support

BSPAL <bspal.ys168.com>,
Palxex <<EMAIL>>:
  - RIX: Softstar RIX OPL Music Format player development

Torbjorn Andersson,
Johannes Schickel 'lordhoto' <lordhoto at scummvm dot org>:
  - Development of original ADL (Westwood ADL File Format) player in
    ScummVM, which has been adapted for AdPlug.

Adam Nielsen <<EMAIL>>:
  - Fixes for CMF rhythm mode support in original player
  - Added entirely new (more accurate) CMF player
  - Added surround/harmonic synth
  - DRO: v2 player

Dennis Lindroos <<EMAIL>>:
  - JBM: JBM Adlib Music format player development

Riven the Mage <<EMAIL>>:
  - BMF: Easy AdLib 1.0

Sebastian Kienzl <<EMAIL>>:
  - CMF: SoundFX Macs Opera

Stas'M <<EMAIL>>
  - GOT: God of Thunder
  - HSQ/SQX/SDB/AGD/HA2: Herbulot AdLib System (HERAD)
  - MUS/IMS/MDI: AdLib Visual Composer ROL derivatives
  - SOP: sopepos' Note Player
  - VGM: Video Game Music
