﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="debug Windows|Win32">
      <Configuration>debug Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="release Windows|Win32">
      <Configuration>release Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{66B89A7C-52EF-9B0D-FBDB-8110E7F1170F}</ProjectGuid>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>mp4p</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <OutDir>.\</OutDir>
    <IntDir>obj\Windows\debug\mp4p\</IntDir>
    <TargetName>mp4p</TargetName>
    <TargetExt>.lib</TargetExt>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <OutDir>.\</OutDir>
    <IntDir>obj\Windows\release\mp4p\</IntDir>
    <TargetName>mp4p</TargetName>
    <TargetExt>.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>DEBUG;VERSION="1.10.0";_GNU_SOURCE;HAVE_LOG2=1;HAVE_ICONV=1;ENABLE_NLS;PACKAGE="deadbeef";USE_STDIO;HAVE_ICONV;_POSIX_C_SOURCE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>include;.;static-deps\lib-x86-64\include\x86_64-linux-gnu;static-deps\lib-x86-64\include;shared\windows\include;\include\opus;xdispatch_ddb\include;external\mp4p\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <AdditionalOptions>-include shared/windows/mingw32_layer.h -fno-builtin %(AdditionalOptions)</AdditionalOptions>
      <ExternalWarningLevel>Level3</ExternalWarningLevel>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Lib>
      <AdditionalDependencies>intl.lib;ws2_32.lib;psapi.lib;shlwapi.lib;iconv.lib;dl.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>static-deps\lib-x86-64\lib\x86_64-linux-gnu;static-deps\lib-x86-64\lib;xdispatch_ddb\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>-Wl,--export-all-symbols %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>VERSION="1.10.0";_GNU_SOURCE;HAVE_LOG2=1;HAVE_ICONV=1;ENABLE_NLS;PACKAGE="deadbeef";USE_STDIO;HAVE_ICONV;_POSIX_C_SOURCE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>include;.;static-deps\lib-x86-64\include\x86_64-linux-gnu;static-deps\lib-x86-64\include;shared\windows\include;\include\opus;xdispatch_ddb\include;external\mp4p\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <Optimization>Disabled</Optimization>
      <AdditionalOptions>-O2 -include shared/windows/mingw32_layer.h -fno-builtin %(AdditionalOptions)</AdditionalOptions>
      <ExternalWarningLevel>Level3</ExternalWarningLevel>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
    <Lib>
      <AdditionalDependencies>intl.lib;ws2_32.lib;psapi.lib;shlwapi.lib;iconv.lib;dl.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>static-deps\lib-x86-64\lib\x86_64-linux-gnu;static-deps\lib-x86-64\lib;xdispatch_ddb\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>-Wl,--export-all-symbols %(AdditionalOptions)</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ProjectReference Include="libwin.vcxproj">
      <Project>{EAFA6F0B-D69C-9AE1-BF57-AE35AB982132}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>