# Traducció del glade de l'equip de Softcatalà.
# Copyright (C) 2003, 2004 Free Software Foundation, Inc.
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2000.
# <PERSON> <<EMAIL>>, 2003, 2004, 2006
#
msgid ""
msgstr ""
"Project-Id-Version: glade\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2006-08-10 00:04+0200\n"
"PO-Revision-Date: 2006-08-10 00:06+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Catalan <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../glade-2.desktop.in.h:1
msgid "Create or open user interface designs for GTK+ or GNOME applications"
msgstr ""
"Obre o crea dissenys d'interfície d'usuari per a GTK+ o aplicacions del GNOME"

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Dissenyador d'interfícies Glade"

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Opcions de la quadrícula"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Separació horitzontal:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Separació vertical:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Estil de la quadrícula:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Punts"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Línies"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Opcions d'ajustament"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Ajustament horitzontal:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Esquerra"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Dreta"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Ajustament vertical:"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Superior"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Inferior"

#: ../glade/editor.c:741
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "Els elements GtkToolItem només es poden afegir a un GtkToolbar."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "No s'ha pogut inserir l'element GtkScrolledWindow."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "No s'ha pogut inserir l'element GtkViewport."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "No s'ha pogut afegir un nou element."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"No es pot afegir un element a la posició seleccionada.\n"
"\n"
"Consell: el GTK+ utilitza contenidors per disposar els elements.\n"
"Proveu de suprimir l'element existent i utilitzar\n"
"en el seu lloc una caixa o una taula contenidora.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "No s'ha pogut suprimir l'element."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "L'element no es pot suprimir"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"L'element es crea automàticament com a part de l'element pare, i no es pot "
"suprimir."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Gruix del contorn:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "El gruix de la vora al voltant del contenidor"

#: ../glade/gbwidget.c:1751
msgid "Select"
msgstr "Seleccioneu"

#: ../glade/gbwidget.c:1773
msgid "Remove Scrolled Window"
msgstr "Suprimeix una finestra amb desplaçament"

#: ../glade/gbwidget.c:1782
msgid "Add Scrolled Window"
msgstr "Afegeix una finestra amb desplaçament"

#: ../glade/gbwidget.c:1803
msgid "Remove Alignment"
msgstr "Suprimeix l'alineació"

#: ../glade/gbwidget.c:1811
msgid "Add Alignment"
msgstr "Afegeix alineació"

#: ../glade/gbwidget.c:1826
msgid "Remove Event Box"
msgstr "Suprimeix el quadre d'esdeveniments"

#: ../glade/gbwidget.c:1834
msgid "Add Event Box"
msgstr "Afegeix un quadre d'esdeveniments"

#: ../glade/gbwidget.c:1844
msgid "Redisplay"
msgstr "Torna a dibuixar"

#: ../glade/gbwidget.c:1859
msgid "Cut"
msgstr "Retalla"

#: ../glade/gbwidget.c:1866 ../glade/property.c:892 ../glade/property.c:5141
msgid "Copy"
msgstr "Copia"

#: ../glade/gbwidget.c:1875 ../glade/property.c:904
msgid "Paste"
msgstr "Enganxa"

#: ../glade/gbwidget.c:1887 ../glade/property.c:1581 ../glade/property.c:5132
msgid "Delete"
msgstr "Suprimeix"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2414 ../glade/gbwidget.c:2483
msgid "N/A"
msgstr "N/A"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3213
msgid "replacing child of container - not implemented yet\n"
msgstr ""
"encara no s'ha implementat la funcionalitat per reemplaçar el fill del "
"contenidor\n"

#: ../glade/gbwidget.c:3441
msgid "Couldn't insert GtkAlignment widget."
msgstr "No s'ha pogut inserir l'element GtkAlignment."

#: ../glade/gbwidget.c:3481
msgid "Couldn't remove GtkAlignment widget."
msgstr "No s'ha pogut suprimir l'element GtkAlignment."

#: ../glade/gbwidget.c:3505
msgid "Couldn't insert GtkEventBox widget."
msgstr "No s'ha pogut inserir l'element GtkEventBox."

#: ../glade/gbwidget.c:3544
msgid "Couldn't remove GtkEventBox widget."
msgstr "No s'ha pogut suprimir l'element GtkEventBox."

#: ../glade/gbwidget.c:3579
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "No s'ha pogut inserir l'element GtkScrolledWindow."

#: ../glade/gbwidget.c:3618
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "No s'ha pogut suprimir l'element GtkScrolledWindow."

#: ../glade/gbwidget.c:3732
msgid "Remove Label"
msgstr "Suprimeix l'etiqueta"

#: ../glade/gbwidgets/gbaboutdialog.c:79
msgid "Application Name"
msgstr "Nom de l'aplicació"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Logotipus:"

#: ../glade/gbwidgets/gbaboutdialog.c:103 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "El mapa de píxels que s'utilitzarà com a logotip"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Nom del programa:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
msgid "The name of the application"
msgstr "El nom de l'aplicació"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "Comentaris:"

#: ../glade/gbwidgets/gbaboutdialog.c:106
msgid "Additional information, such as a description of the application"
msgstr "Informació addicional, per exemple una descripció de l'aplicació"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Copyright:"

#: ../glade/gbwidgets/gbaboutdialog.c:107 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "La informació sobre el copyright"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "Website URL:"
msgstr "URL del lloc web:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
msgid "The URL of the application's website"
msgstr "La URL del lloc web de l'aplicació"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "Website Label:"
msgstr "Etiqueta del lloc web:"

#: ../glade/gbwidgets/gbaboutdialog.c:110
msgid "The label to display for the link to the website"
msgstr "L'etiqueta que es mostrarà com a enllaç al lloc web"

#: ../glade/gbwidgets/gbaboutdialog.c:112 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Llicència:"

#: ../glade/gbwidgets/gbaboutdialog.c:112
msgid "The license details of the application"
msgstr "Els detalls de la llicència de l'aplicació"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "Wrap License:"
msgstr "Talla les línies:"

#: ../glade/gbwidgets/gbaboutdialog.c:113
msgid "If the license text should be wrapped"
msgstr "Si s'han de tallar les línies del text de la llicència"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Autors:"

#: ../glade/gbwidgets/gbaboutdialog.c:115 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Els autors del paquet, un per cada línia"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Documentadors:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Els autors de la documentació del paquet, un per cada línia"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid "Artists:"
msgstr "Artistes:"

#: ../glade/gbwidgets/gbaboutdialog.c:117
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr ""
"Les persones que han creat les imatges per al paquet, una per cada línia"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Traductors:"

#: ../glade/gbwidgets/gbaboutdialog.c:118 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Els traductors del paquet. Normalment es deixa buit perquè els traductors "
"afegeixin els seus noms al fitxer po"

#: ../glade/gbwidgets/gbaboutdialog.c:588
msgid "About Dialog"
msgstr "Diàleg «Quant a»"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Etiqueta amb accelerador"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:181 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "Alineació X:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "L'alineació horitzontal de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:184 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Alineació Y:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "L'alineació vertical de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "Escala X:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "L'escala horitzontal de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Escala Y:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "L'escala vertical de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:85
msgid "Top Padding:"
msgstr "Encoixinament superior:"

#: ../glade/gbwidgets/gbalignment.c:86
msgid "Space to put above the child widget"
msgstr "L'espai que es posarà per sobre de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:89
msgid "Bottom Padding:"
msgstr "Encoixinament inferior:"

#: ../glade/gbwidgets/gbalignment.c:90
msgid "Space to put below the child widget"
msgstr "L'espai que es posarà per sota de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:93
msgid "Left Padding:"
msgstr "Encoixinament esquerre:"

#: ../glade/gbwidgets/gbalignment.c:94
msgid "Space to put to the left of the child widget"
msgstr "L'espai que es posarà a l'esquerre de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:97
msgid "Right Padding:"
msgstr "Encoixinament dret:"

#: ../glade/gbwidgets/gbalignment.c:98
msgid "Space to put to the right of the child widget"
msgstr "L'espai que es posarà a la dreta de l'element fill"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Alineació"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "Direcció:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "La direcció de la fletxa"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Ombra:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "El tipus d'ombrejat de la fletxa"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "L'alineació horitzontal de la fletxa"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "L'alineació vertical de la fletxa"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187
msgid "X Pad:"
msgstr "Enc. X:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:187 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "L'encoixinament horitzontal"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189
msgid "Y Pad:"
msgstr "Enc. Y:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:189 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "L'encoixinament vertical"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Fletxa"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "Alineació X de l'etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "L'alineació horitzontal de l'element etiqueta del marc"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Alineació Y de l'etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "L'alineació vertical de l'element etiqueta del marc"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "El tipus d'ombra del marc"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "L'alineació horitzontal del fill del marc"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Relació:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "La relació d'aspecte del fill del marc"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Obeeix al fill:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Si la relació d'aspecte ha de ser determinada pel fill"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Aspecte del marc"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Botó predeterminat:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "El botó predeterminat que es farà servir"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:748
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Etiqueta:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:169
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "El text per mostrar"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:297
#: ../glade/glade_menu_editor.c:814
msgid "Icon:"
msgstr "Icona:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "La icona que es mostrarà"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Relleu dels botons:"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "El tipus de relleu del botó"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "Id. de resposta:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"El codi de resposta retornat quan es premi el botó. Seleccioneu una de les "
"respostes predeterminades o introduïu un valor enter positiu"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr "Focus en clicar:"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "If the button grabs focus when it is clicked"
msgstr "Si el botó rep el focus quan s'hi fa clic"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "suprimeix el contingut del botó"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Botó"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Encapçalament:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "Si el mes i l'any s'han de mostrar al capdamunt"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Noms dels dies:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Si s'han de mostrar els noms dels dies"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Mes fix:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "Si no es poden canviar el mes i l'any"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Números de la setmana:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Si s'ha de mostrar el número de la setmana"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Primer el dilluns:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "Si la setmana ha de començar en dilluns"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Calendari"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Color de fons:"

#: ../glade/gbwidgets/gbcellview.c:64
msgid "The background color"
msgstr "Color de fons"

#: ../glade/gbwidgets/gbcellview.c:192
msgid "Cell View"
msgstr "Visualització de cel·les"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Inicialment activat:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Si el botó de comprovació està inicialment activat"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Inconsistent:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Si el botó es mostra en un estat inconsistent"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "Indicador:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "Si l'indicador es dibuixa sempre"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Botó de comprovació"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Si l'element de menú de comprovació està activat inicialment"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Element de menú de comprovació"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Nova llista amb columnes"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Nombre de columnes:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:128 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Mode de selecció:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "El mode de selecció de la llista amb columnes"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Mostra els títols:"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Si es mostren els títols de les columnes"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "El tipus d'ombra del contorn de la llista amb columnes"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Llista amb columnes"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Amb transparència:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Si s'ha d'utilitzar el canal alfa de transparència"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:244
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Títol:"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "El títol del diàleg de selecció de color"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
msgid "Pick a Color"
msgstr "Escolliu un color"

#: ../glade/gbwidgets/gbcolorbutton.c:211
msgid "Color Chooser Button"
msgstr "Botó de selecció de color"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Control d'opacitat:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Si es mostra el control d'opacitat"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Paleta:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Si es mostra la paleta"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Selecció de colors"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:71
msgid "Select Color"
msgstr "Seleccioneu el color"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:316 ../glade/property.c:1276
msgid "Color Selection Dialog"
msgstr "Diàleg de selecció de color"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Valor en la llista:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Si el valor ha de ser a la llista"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "Vàlid si és buit:"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr "Si s'accepta un valor buit quan 'Valor en la llista' està definit"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "Majúscules/minúscules:"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Si la cerca distingeix entre majúscules i minúscules"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Utilitza les fletxes:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "Si les fletxes es poden utilitzar per canviar el valor"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Utilitza sempre:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "Si la fletxa funciona encara que el valor no sigui a la llista"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Elements:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Els elements en la llista combinada, un per línia"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Quadre combinat"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr "Afegeix separadors:"

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr "Si els menús desplegables han de tenir un element de menú separable"

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Si el quadre combinat obté el focus si s'hi fa clic"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Té marc:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr "Si el quadre combinat dibuixa un marc al voltant del fill"

#: ../glade/gbwidgets/gbcomboboxentry.c:302
msgid "Combo Box Entry"
msgstr "Entrada del quadre combinat"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Nou arbre amb columnes"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "El mode de selecció de l'arbre amb columnes"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "El tipus d'ombra del contorn de l'arbre amb columnes"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Arbre amb columnes"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:247
msgid "Type:"
msgstr "Tipus:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "El tipus de corba"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "X mín:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "El valor mínim horitzontal"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "X màx:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "El valor màxim horitzontal"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Y mín:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "El valor mínim vertical"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Y màx:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "El valor màxim vertical"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Corba"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Funció de creació:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "La funció que crea l'element gràfic"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Cadena1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "El primer argument de tipus cadena que es passa a la funció"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Cadena2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "El segon argument de tipus cadena que es passa a la funció"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Int1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "El primer argument enter que es passa a la funció"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Int2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "El segon argument enter que es passa a la funció"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Element personalitzat"

#: ../glade/gbwidgets/gbdialog.c:293
msgid "New dialog"
msgstr "Nou diàleg"

#: ../glade/gbwidgets/gbdialog.c:305
msgid "Cancel, OK"
msgstr "Cancel·la, D'acord"

#: ../glade/gbwidgets/gbdialog.c:314 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1322 ../glade/property.c:5162
msgid "OK"
msgstr "D'acord"

#: ../glade/gbwidgets/gbdialog.c:323
msgid "Cancel, Apply, OK"
msgstr "Cancel·la, Aplica, D'acord"

#: ../glade/gbwidgets/gbdialog.c:332
msgid "Close"
msgstr "Tanca"

#: ../glade/gbwidgets/gbdialog.c:341
msgid "_Standard Button Layout:"
msgstr "Dispo_sició estàndar dels botons:"

#: ../glade/gbwidgets/gbdialog.c:350
msgid "_Number of Buttons:"
msgstr "_Nombre de botons:"

#: ../glade/gbwidgets/gbdialog.c:367
msgid "Show Help Button"
msgstr "Mostra el botó d'ajuda"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "Has Separator:"
msgstr "Amb separador:"

#: ../glade/gbwidgets/gbdialog.c:399
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Si el diàleg té un separador horitzontal per sobre dels botons"

#: ../glade/gbwidgets/gbdialog.c:606
msgid "Dialog"
msgstr "Diàleg"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Àrea de dibuix"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Editable:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Si es pot editar el text"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Text visible:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"Si es mostra el text introduït per l'usuari. Quan es desactiva, el text "
"teclejat es mostra amb caràcters asterisc. Resulta útil per introduir "
"contrasenyes"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Mida màxima:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "La mida màxima del text"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Text:"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Si l'entrada té un marc al seu voltant"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Caràcter invisible:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"El caràcter que es fa servir quan el text no sigui visible, p.e. quan "
"s'introdueixen contrasenyes"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "S'activa per defecte:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr ""
"Si l'element per defecte en la finestra s'activarà quan premeu la tecla de "
"retorn"

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Amplada en caràcters:"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "Per a quants caràcters s'haurà de deixar espai a l'entrada"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Entrada de text"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "Visible Window:"
msgstr "Finestra visible:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr "Si la caixa d'esdeveniments fa servir una finestra visible"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "Above Child:"
msgstr "Sobre el fill:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr ""
"Si la finestra de la caixa d'esdeveniments és sobre la finestra de l'element "
"fill"

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Caixa d'esdeveniments"

#: ../glade/gbwidgets/gbexpander.c:54
msgid "Initially Expanded:"
msgstr "Inicialment expandit:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr "Si l'expansor està obert inicialment per mostrar l'element fill"

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Espai:"

#: ../glade/gbwidgets/gbexpander.c:58
msgid "Space to put between the label and the child"
msgstr "L'espai entre l'etiqueta i el fill"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Afegeix un element etiqueta"

#: ../glade/gbwidgets/gbexpander.c:228
msgid "Expander"
msgstr "Expansor"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
msgid "The window title of the file chooser dialog"
msgstr "El títol de la finestra del diàleg de selecció de fitxers"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:158
#: ../glade/gnome/gnomefileentry.c:109
msgid "Action:"
msgstr "Acció:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:89
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr "El tipus d'operació de fitxers que es realitzarà"

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
msgid "Local Only:"
msgstr "Només local:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether the selected files should be limited to local files"
msgstr "Si les fitxers seleccionats només poden ser locals"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:165
msgid "Show Hidden:"
msgstr "Mostra els ocults:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:166
msgid "Whether the hidden files and folders should be displayed"
msgstr "Si s'han de visualitzar els fitxers i carpetes ocults"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#: ../glade/gbwidgets/gbfilechooserdialog.c:167
msgid "Confirm:"
msgstr "Confirmació:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:96
#: ../glade/gbwidgets/gbfilechooserdialog.c:168
msgid ""
"Whether a confirmation dialog will be displayed if a file will be overwritten"
msgstr ""
"Si s'ha de mostrar un diàleg de confirmació en el cas que s'hagi de "
"sobreescriure un fitxer"

#: ../glade/gbwidgets/gbfilechooserbutton.c:97
#: ../glade/gbwidgets/gblabel.c:201
msgid "Width in Chars:"
msgstr "Amplada en caràcters:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:98
msgid "The width of the button in characters"
msgstr "L'amplada del botó en caràcters"

#: ../glade/gbwidgets/gbfilechooserbutton.c:296
msgid "File Chooser Button"
msgstr "Botó de selecció de fitxers"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
msgid "Select Multiple:"
msgstr "Selecció múltiple:"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether to allow multiple files to be selected"
msgstr "Si s'ha de permetre seleccionar múltiples fitxers"

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
msgid "File Chooser"
msgstr "Selector de fitxers"

#: ../glade/gbwidgets/gbfilechooserdialog.c:435
msgid "File Chooser Dialog"
msgstr "Diàleg de selecció de fitxers"

#: ../glade/gbwidgets/gbfileselection.c:72 ../glade/property.c:1366
msgid "Select File"
msgstr "Seleccioneu un fitxer"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "File Ops.:"
msgstr "Ops. de fitxer:"

#: ../glade/gbwidgets/gbfileselection.c:115
msgid "If the file operation buttons are shown"
msgstr "Si es mostren els botons d'operació dels fitxers"

#: ../glade/gbwidgets/gbfileselection.c:293
msgid "File Selection Dialog"
msgstr "Diàleg de selecció de fitxers"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "La coordenada X de l'element en el GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "La coordenada Y de l'element en el GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Posicions fixes"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "El títol del diàleg de selecció del tipus de lletra"

#: ../glade/gbwidgets/gbfontbutton.c:70
msgid "Show Style:"
msgstr "Mostra l'estil:"

#: ../glade/gbwidgets/gbfontbutton.c:71
msgid "If the font style is shown as part of the font information"
msgstr ""
"Si es mostra l'estil del tipus de lletra com a part de la informació del "
"tipus de lletra"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Mostra la mida:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr ""
"Si es mostra la mida del tipus de lletra com a part de la informació del "
"tipus de lletra"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Tipus de lletra:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr ""
"Si s'utilitza el tipus de lletra seleccionat quan es mostra la informació "
"del tipus de lletra"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Utilitza la mida:"

#: ../glade/gbwidgets/gbfontbutton.c:77
msgid "if the selected font size is used when displaying the font information"
msgstr ""
"Si s'utilitza la mida seleccionada del tipus de lletra quan es mostra la "
"informació del tipus de lletra"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Trieu un tipus de lletra"

#: ../glade/gbwidgets/gbfontbutton.c:268
msgid "Font Chooser Button"
msgstr "Botó de selecció de tipus de lletra"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Text de previsualització:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "El text de previsualització que es mostrarà"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Selecció del tipus de lletra"

#: ../glade/gbwidgets/gbfontselectiondialog.c:70
msgid "Select Font"
msgstr "Seleccioneu el tipus de lletra"

#: ../glade/gbwidgets/gbfontselectiondialog.c:301
msgid "Font Selection Dialog"
msgstr "Diàleg de selecció del tipus de lletra"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Marc"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Tipus inicial:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "El tipus de corba inicial"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Corba gamma"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "El tipus d'ombra al voltant de la nansa"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "Posició de la nansa:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "La posició de la nansa"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Marge d'ajustament:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "El marge de la nansa que ajusta la posició"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "Nansa"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Nova caixa horitzontal"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Mida:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "El nombre d'elements de la caixa"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Homogeni:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Si els fills han de ser tots de la mateixa mida"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "L'espai entre cada fill"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "No es pot suprimir cap fill."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:256
msgid "Position:"
msgstr "Posició:"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "La posició de l'element en relació als seus germans"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Encoixinament:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "L'encoixinament de l'element"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Expandeix:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "Quan sigui activat, l'element es podrà expandir"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Omple:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "Quan sigui activat, l'element podrà omplir la seva àrea assignada"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Empaqueta a l'inici:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "Quan sigui activat, l'element s'empaquetarà al principi del quadre"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Insereix abans"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Insereix després"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Caixa horitzontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Nova caixa de botons horitzontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "El nombre de botons"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Disposició:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "L'estil de disposició dels botons"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "L'espai entre els botons"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Caixa de botons horitzontal"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "La posició del divisor"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:285
msgid "Shrink:"
msgstr "Encongeix:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "Quan sigui activat, l'element podrà encongir-se"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Canvia de mida:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "Quan sigui activat, es permet canviar la mida a l'element"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Subfinestres horitzontals"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Mètrica:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "Les unitats del regle"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Valor més baix:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "El valor més baix del regle"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Valor més alt:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "El valor més alt del regle"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "La posició actual del regle"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4833
msgid "Max:"
msgstr "Màx:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "El valor màxim del regle"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Regle horitzontal"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Mostra el valor:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Si es mostren els valors de l'escala"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Dígits:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "El nombre de dígits que es mostren"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Posició del valor:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "La posició del valor"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Política:"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "La política d'actualització de l'escala"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Invertit:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Si s'han d'invertir els valors de l'interval"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Escala horitzontal"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "La política d'actualització de la barra de desplaçament"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Barra de desplaçament horitzontal"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Separador horitzontal"

#: ../glade/gbwidgets/gbiconview.c:107
#, c-format
msgid "Icon %i"
msgstr "Icona %i"

#: ../glade/gbwidgets/gbiconview.c:129
msgid "The selection mode of the icon view"
msgstr "El mode de selecció de la visualització d'icones"

#: ../glade/gbwidgets/gbiconview.c:131 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "Orientació:"

#: ../glade/gbwidgets/gbiconview.c:132
msgid "The orientation of the icons"
msgstr "La orientació de les icones"

#: ../glade/gbwidgets/gbiconview.c:134 ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Reordenable:"

#: ../glade/gbwidgets/gbiconview.c:135
msgid "If the view can be reordered using Drag and Drop"
msgstr "Si la visualització es pot reordenar arrossegant i deixant anar"

#: ../glade/gbwidgets/gbiconview.c:308
msgid "Icon View"
msgstr "Visualització de la icona"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:301
msgid "Named Icon:"
msgstr "Nom de la icona:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:302
msgid "The named icon to use"
msgstr "La icona amb nom que es farà servir"

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Mida de la icona:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "La mida de la icona predeterminada"

#: ../glade/gbwidgets/gbimage.c:115
msgid "Pixel Size:"
msgstr "Mida del píxel:"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""
"La mida en píxels de la icona amb nom, o -1 per fer servir la propietat mida "
"de la icona"

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "L'alineació horitzontal"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "L'alineació vertical"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Imatge"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "L'element de menú predeterminat no és vàlid"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Element de menú amb mapa de píxels"

#: ../glade/gbwidgets/gbinputdialog.c:257
msgid "Input Dialog"
msgstr "Diàleg d'entrada"

#: ../glade/gbwidgets/gblabel.c:170
msgid "Use Underline:"
msgstr "Subratllat:"

#: ../glade/gbwidgets/gblabel.c:171
msgid "If the text includes an underlined access key"
msgstr "Si el text incorpora una tecla d'accés subratllada"

#: ../glade/gbwidgets/gblabel.c:172
msgid "Use Markup:"
msgstr "Amb marcatge:"

#: ../glade/gbwidgets/gblabel.c:173
msgid "If the text includes pango markup"
msgstr "Si el text incorpora marcatge Pango"

#: ../glade/gbwidgets/gblabel.c:174
msgid "Justify:"
msgstr "Justificació:"

#: ../glade/gbwidgets/gblabel.c:175
msgid "The justification of the lines of the label"
msgstr "La justificació de les línies de l'etiqueta"

#: ../glade/gbwidgets/gblabel.c:177
msgid "Wrap Text:"
msgstr "Ajusta el text:"

#: ../glade/gbwidgets/gblabel.c:178
msgid "If the text is wrapped to fit within the width of the label"
msgstr "Si el text s'ajusta per encabir-lo en l'amplada de l'etiqueta"

#: ../glade/gbwidgets/gblabel.c:179
msgid "Selectable:"
msgstr "Seleccionable:"

#: ../glade/gbwidgets/gblabel.c:180
msgid "If the label text can be selected with the mouse"
msgstr "Si el text de l'etiqueta es pot seleccionar amb el ratolí"

#: ../glade/gbwidgets/gblabel.c:182
msgid "The horizontal alignment of the entire label"
msgstr "L'alineació horitzontal de tota l'etiqueta"

#: ../glade/gbwidgets/gblabel.c:185
msgid "The vertical alignment of the entire label"
msgstr "L'alineació vertical de tota l'etiqueta"

#: ../glade/gbwidgets/gblabel.c:191
msgid "Focus Target:"
msgstr "Objectiu del focus:"

#: ../glade/gbwidgets/gblabel.c:192
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"L'element al qual s'hi assignarà el focus quan s'utilitzi la tecla d'accés "
"subratllada"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:146
msgid "Ellipsize:"
msgstr "Punts suspensius:"

#: ../glade/gbwidgets/gblabel.c:199 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr "Com s'ha de posar punts suspensius a la cadena"

#: ../glade/gbwidgets/gblabel.c:202
msgid "The width of the label in characters"
msgstr "L'amplada de l'etiqueta en caràcters"

#: ../glade/gbwidgets/gblabel.c:204
msgid "Single Line Mode:"
msgstr "Mode d'una línia:"

#: ../glade/gbwidgets/gblabel.c:205
msgid "If the label is only given enough height for a single line"
msgstr "Si l'etiqueta tindrà alçada per a una línia només"

#: ../glade/gbwidgets/gblabel.c:206
msgid "Angle:"
msgstr "Angle:"

#: ../glade/gbwidgets/gblabel.c:207
msgid "The angle of the label text"
msgstr "L'angle del text de l'etiqueta"

#: ../glade/gbwidgets/gblabel.c:333 ../glade/gbwidgets/gblabel.c:348
#: ../glade/gbwidgets/gblabel.c:616
msgid "Auto"
msgstr "Automàtic"

#: ../glade/gbwidgets/gblabel.c:872 ../glade/glade_menu_editor.c:411
msgid "Label"
msgstr "Etiqueta"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Amplada de l'àrea:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "L'amplada de l'àrea de disposició"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Alçada de l'àrea:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "L'alçada de l'àrea de disposició"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "La coordenada X de l'element en el GtkLayout"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "La coordenada Y de l'element en el GtkLayout"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Disposició"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "El mode de selecció de la llista"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Llista"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Element de la llista"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Menú emergent"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:215
msgid "_File"
msgstr "_Fitxer"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:223 ../glade/glade_project_window.c:692
msgid "_Edit"
msgstr "_Edita"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:229 ../glade/glade_project_window.c:721
msgid "_View"
msgstr "_Visualitza"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:231 ../glade/glade_project_window.c:834
msgid "_Help"
msgstr "_Ajuda"

#: ../glade/gbwidgets/gbmenubar.c:232
msgid "_About"
msgstr "_Quant a"

#: ../glade/gbwidgets/gbmenubar.c:291
msgid "Pack Direction:"
msgstr "Direcció de l'empaquetament:"

#: ../glade/gbwidgets/gbmenubar.c:292
msgid "The pack direction of the menubar"
msgstr "La direcció de l'empaquetament de la barra de menú"

#: ../glade/gbwidgets/gbmenubar.c:294
msgid "Child Direction:"
msgstr "Direcció dels fills:"

#: ../glade/gbwidgets/gbmenubar.c:295
msgid "The child pack direction of the menubar"
msgstr "La direcció de la fletxa"

#: ../glade/gbwidgets/gbmenubar.c:300 ../glade/gbwidgets/gbmenubar.c:418
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Edita els menús..."

#: ../glade/gbwidgets/gbmenubar.c:541
msgid "Menu Bar"
msgstr "Barra de menús"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Element del menú"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
msgid "Show Horizontal:"
msgstr "Mostra en horitzontal:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
msgid "If the item is visible when the toolbar is horizontal"
msgstr "Si l'element és visible quan la barra d'eines sigui horitzontal"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
msgid "Show Vertical:"
msgstr "Mostra en vertical:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
msgid "If the item is visible when the toolbar is vertical"
msgstr "Si l'element és visible quan la barra d'eines sigui vertical"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr "És important:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""
"Si s'ha de mostrar el text de l'element quan el mode de la barra d'eines "
"sigui GTK_TOOLBAR_BOTH_HORIZ"

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
msgid "Toolbar Button with Menu"
msgstr "Botó de la barra d'eines amb menú"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Nou bloc de notes"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:125
msgid "Number of pages:"
msgstr "Nombre de pàgines:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "Mostra pestanyes:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Si es mostren les pestanyes del bloc de notes"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Mostra el contorn:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr ""
"Si es mostra el contorn del bloc de notes, quan no es mostren les pestanyes"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Posició:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "La posició de les pestanyes del bloc de notes"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Pot desplaçar-se:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Si les pestanyes del bloc de notes es poden desplaçar"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Contorn hor. de pestanya:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "La mida del contorn horitzontal de les pestanyes del bloc de notes"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Contorn vert. de pestanya:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "La mida del contorn vertical de les pestanyes del bloc de notes"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Mostra el menú emergent:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "Si el menú emergent està activat"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Nombre de pàgines:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "El nombre de pàgines del bloc de notes"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Pàgina anterior"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "Pàgina següent"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Esborra la pàgina"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "Canvia a la següent"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Canvia a l'anterior"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Insereix una pàgina després"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Insereix una pàgina abans"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "La posició actual de la pàgina en la llista de pàgines"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Quan sigui activat, la pestanya es podrà expandir"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr "Quan sigui activat, la pestanya podrà omplir la seva àrea assignada"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr ""
"Quan sigui activat, la pestanya s'empaquetarà al principi del bloc de notes"

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Etiqueta del menú:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "El text que es mostra en el menú emergent"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Bloc de notes"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "No es pot afegir un %s a un GtkOptionMenu."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Menú d'opcions"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Color:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "Si la previsualització és en color o en escala de grisos"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "Si la previsualització s'expandeix per omplir la seva àrea assignada"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Previsualitza"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "La orientació dels continguts de la barra de progrés"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Fracció:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "La fracció de feina que s'ha completat"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Pas de pols:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""
"La fracció de la longitud de la barra de progrés que s'ha de moure el bloc "
"lliscant quan sigui polsat"

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "El text que es mostra sobre la barra de progrés"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Mostra el text:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "Si el text s'ha de mostrar en la barra de progrés"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Mode d'activitat:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr ""
"Si la barra de progrés ha d'actuar com el frontal del cotxe d'en Michael "
"Knight"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "L'alineació horitzontal del text"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "L'alineació vertical del text"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "Barra de progrés"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "Si el botó de grup està inicialment activat"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1039
msgid "Group:"
msgstr "Grup:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr ""
"El grup del botó de grup (per defecte tots els botons de grup tenen el "
"mateix pare)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Grup nou"

#: ../glade/gbwidgets/gbradiobutton.c:465
msgid "Radio Button"
msgstr "Botó de grup"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "Si l'element de grup de menú està inicialment activat"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"El grup de l'element de grup de menú (per defecte, tots els elements de grup "
"de menú tenen el mateix pare)"

#: ../glade/gbwidgets/gbradiomenuitem.c:388
msgid "Radio Menu Item"
msgstr "Element de grup de menú"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr ""
"El grup del botó de grup de l'eina (per defecte tots els botons de grup de "
"la barra d'eines)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:530
msgid "Toolbar Radio Button"
msgstr "Botó de grup de barra d'eines"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "Política hor.:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Quan es mostra la barra de desplaçament horitzontal"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "Política ver.:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Quan es mostra la barra de desplaçament vertical"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Pos. de la finestra:"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr "On s'ubica la finestra filla respecte les barres de desplaçament"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Tipus d'ombra:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "La política d'actualització de la barra de desplaçament vertical"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Finestra amb desplaçament"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Separador de menús"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr "Dibuixa:"

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr "Si es dibuixa el separador, o es deixa buit"

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
msgid "Toolbar Separator Item"
msgstr "Element separador de la barra d'eines"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Increment:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr ""
"La proporció d'increment del botó girador, utilitzat junt amb l'increment de "
"pàgina"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "El nombre de dígits decimals que es mostren"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Numèric:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr "Si només es poden introduir xifres"

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Política d'actualització:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Quan s'emeten senyals value_changed"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Ajusta:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "Si el valor s'ajusta a un múltiple de l'increment de passos"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Bucle:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "Si en superar-se el màxim, el valor salta al mínim, i a l'inrevés"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Botó girador"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Canvia de mida:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr ""
"Si la barra d'aplicació té una nansa per canviar la mida de la finestra"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Barra d'estat"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Taula nova"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Nombre de files:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Files:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "El nombre de files de la taula"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Columnes:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "El nombre de columnes de la taula"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Si tots els fills han de ser de la mateixa mida"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Espai entre files:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "L'espai entre cada fila"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Espai entre columnes:"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "L'espai entre cada columna"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Cel·la X:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "El límit esquerre de l'element a la taula"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Cel·la Y:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "El límit superior de l'element a la taula"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Despl. de columnes:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "El nombre de columnes desplaçades per l'element a la taula"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Despl. de files:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "El nombre de columnes desplaçades per l'element a la taula"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "Encoixinament hor.:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "Encoixinament ver.:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "Expansió X:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "Quan sigui activat, l'element es podrà expandir horitzontalment"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Expansió Y:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "Quan sigui activat, l'element es podrà expandir verticalment"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "Encongir X:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "Quan sigui activat, l'element es podrà encongir horitzontalment"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Encongir Y:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "Quan sigui activat, l'element es podrà encongir verticalment"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "Omple X:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr ""
"Quan sigui activat, l'element podrà omplir la seva àrea horitzontal assignada"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Omple Y:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr ""
"Quan sigui activat, l'element podrà omplir la seva àrea vertical assignada"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Insereix una fila abans"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Insereix una fila després"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Insereix una columna abans"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Insereix una columna després"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Suprimeix la fila"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Suprimeix la columna"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Taula"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Centra"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Omple"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:543 ../glade/glade_menu_editor.c:830
#: ../glade/glade_menu_editor.c:1345 ../glade/glade_menu_editor.c:2255
#: ../glade/property.c:2432
msgid "None"
msgstr "Cap"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Caràcter"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Paraula"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Cursor visible:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Si el cursor és visible"

#: ../glade/gbwidgets/gbtextview.c:119
msgid "Overwrite:"
msgstr "Sobreescriu:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr "Si el text introduït sobreescriu el text existent"

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr "Amb tabuladors:"

#: ../glade/gbwidgets/gbtextview.c:122
msgid "If tab characters can be entered"
msgstr "Si es permet escriure amb tabuladors"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Justificació:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "La justificació del text"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Tall de línia:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "El tall de línia del text"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Espai per sobre:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Píxels d'espai en blanc per sobre dels paràgrafs"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Espai per sota:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Pixels d'espai en blanc per sota dels paràgrafs"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Espai a dins:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Píxels d'espai en blanc entre línies tallades en un paràgraf"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Marge esquerre:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Amplada del marge esquerre en píxels"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Marge dret:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Amplada del marge dret en píxels"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Sagnat:"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Quantitat de píxels per sagnar els paràgrafs"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Visualització de text"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "Si el botó de commutació està inicialment activat"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Botó de commutació"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
msgid "Toolbar Toggle Button"
msgstr "Botó de commutació de la barra d'eines"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Nova barra d'eines"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Nombre d'elements:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "El nombre d'elements de la barra d'eines"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "L'orientació de la barra d'eines"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Estil:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "L'estil de la barra d'eines"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Consells flotants:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Si estan activats els consells flotants de les eines"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "Show Arrow:"
msgstr "Mostra la fletxa:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""
"Si s'ha de mostrar una fletxa per a un menú emergent en el cas que la barra "
"d'eines no hi càpiga"

#: ../glade/gbwidgets/gbtoolbar.c:427
msgid "If the item should be the same size as other homogeneous items"
msgstr ""
"Si l'element ha de tenir la mateixa mida que d'altres elements homogenis"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Insereix un element abans"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Insereix un element després"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Barra d'eines"

#: ../glade/gbwidgets/gbtoolbutton.c:586
msgid "Toolbar Button"
msgstr "Botó de la barra d'eines"

#: ../glade/gbwidgets/gbtoolitem.c:201
msgid "Toolbar Item"
msgstr "Element de la barra d'eines"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Columna 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Columna 2"

#: ../glade/gbwidgets/gbtreeview.c:87
msgid "Column 3"
msgstr "Columna 3"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr "Fila %i"

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Capçaleres visibles:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Si es mostren els botons de capçalera de columna"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Amb alternància:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""
"Si s'ha de definir un modificador perquè el motor del tema dibuixi les files "
"en colors alternants"

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Si es pot reordenar la visualització"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Permet cercar:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr "Si l'usuari pot cercar entre les columnes interactivament"

#: ../glade/gbwidgets/gbtreeview.c:123
msgid "Fixed Height Mode:"
msgstr "Mode d'alçada fixa:"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr ""
"Estableix totes les files a la mateixa alçada per millorar el rendiment"

#: ../glade/gbwidgets/gbtreeview.c:125
msgid "Hover Selection:"
msgstr "Selecciona:"

#: ../glade/gbwidgets/gbtreeview.c:126
msgid "Whether the selection should follow the pointer"
msgstr "Si la selecció hauria de seguir el punter"

#: ../glade/gbwidgets/gbtreeview.c:127
msgid "Hover Expand:"
msgstr "Expandeix:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""
"Si les files s'hauríen d'expandir o col·lapsar quan el punter s'hi mou per "
"sobre"

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Visualització de llista o arbre"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Nova caixa vertical"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Caixa vertical"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Nova caixa vertical de botons"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Caixa vertical de botons"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "El tipus d'ombra de la finestra de visualització"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Finestra de visualització"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Subfinestres verticals"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Regle vertical"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Escala vertical"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Barra de desplaçament vertical"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Separador vertical"

#: ../glade/gbwidgets/gbwindow.c:244
msgid "The title of the window"
msgstr "El títol de la finestra"

#: ../glade/gbwidgets/gbwindow.c:247
msgid "The type of the window"
msgstr "El tipus de finestra"

#: ../glade/gbwidgets/gbwindow.c:251
msgid "Type Hint:"
msgstr "Indicació de tipus:"

#: ../glade/gbwidgets/gbwindow.c:252
msgid "Tells the window manager how to treat the window"
msgstr "Informa el gestor de finestres com ha de tractar la finestra"

#: ../glade/gbwidgets/gbwindow.c:257
msgid "The initial position of the window"
msgstr "La posició inicial de la finestra"

#: ../glade/gbwidgets/gbwindow.c:261 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Modal:"

# una ajudeta...
#: ../glade/gbwidgets/gbwindow.c:261
msgid "If the window is modal"
msgstr "Si només la finestra actual de l'aplicació rep el focus"

#: ../glade/gbwidgets/gbwindow.c:266
msgid "Default Width:"
msgstr "Amplada per defecte:"

#: ../glade/gbwidgets/gbwindow.c:267
msgid "The default width of the window"
msgstr "L'amplada per defecte de la finestra"

#: ../glade/gbwidgets/gbwindow.c:271
msgid "Default Height:"
msgstr "Alçada per defecte:"

#: ../glade/gbwidgets/gbwindow.c:272
msgid "The default height of the window"
msgstr "L'alçada per defecte de la finestra"

#: ../glade/gbwidgets/gbwindow.c:278
msgid "Resizable:"
msgstr "Canvia de mida:"

#: ../glade/gbwidgets/gbwindow.c:279
msgid "If the window can be resized"
msgstr "Si es pot canviar la mida de la finestra"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be shrunk"
msgstr "Si es port encongir la finestra"

#: ../glade/gbwidgets/gbwindow.c:287
msgid "Grow:"
msgstr "Creix:"

#: ../glade/gbwidgets/gbwindow.c:288
msgid "If the window can be enlarged"
msgstr "Si la finestra es pot engrandir"

#: ../glade/gbwidgets/gbwindow.c:293
msgid "Auto-Destroy:"
msgstr "Autodestrucció:"

#: ../glade/gbwidgets/gbwindow.c:294
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Si s'ha de destruir la finestra quan el seu pare es destrueix"

#: ../glade/gbwidgets/gbwindow.c:298
msgid "The icon for this window"
msgstr "La icona per a aquesta finestra"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "Role:"
msgstr "Rol:"

#: ../glade/gbwidgets/gbwindow.c:305
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""
"Un identificador únic per a la finestra, que s'utilitzarà per recuperar una "
"sessió"

#: ../glade/gbwidgets/gbwindow.c:308
msgid "Decorated:"
msgstr "Decorada:"

#: ../glade/gbwidgets/gbwindow.c:309
msgid "If the window should be decorated by the window manager"
msgstr ""
"Si el gestor de finestres ha de posar decoracions al voltant de la finestra"

#: ../glade/gbwidgets/gbwindow.c:312
msgid "Skip Taskbar:"
msgstr "Ignora la barra de tasques:"

#: ../glade/gbwidgets/gbwindow.c:313
msgid "If the window should not appear in the task bar"
msgstr "Si la finestra no ha d'aparèixer a la barra de tasques"

#: ../glade/gbwidgets/gbwindow.c:316
msgid "Skip Pager:"
msgstr "Ignora el paginador:"

#: ../glade/gbwidgets/gbwindow.c:317
msgid "If the window should not appear in the pager"
msgstr "Si la finestra no ha d'aparèixer al paginador"

#: ../glade/gbwidgets/gbwindow.c:320
msgid "Gravity:"
msgstr "Gravetat:"

#: ../glade/gbwidgets/gbwindow.c:321
msgid "The reference point to use when the window coordinates are set"
msgstr ""
"El punt de referència que s'usarà quan es defineixin les coordenades de la "
"finestra"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "Focus On Map:"
msgstr "Focus en mapejar:"

#: ../glade/gbwidgets/gbwindow.c:325
msgid "If the window should receive the input focus when it is mapped"
msgstr ""
"Si el gestor de finestres ha de rebre el focus d'entrada quan està mapat"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "Urgency Hint:"
msgstr "Indicació d'urgència:"

#: ../glade/gbwidgets/gbwindow.c:328
msgid "If the window should be brought to the user's attention"
msgstr "Si la finestra s'ha de mostrar a l'usuari amb urgència"

#: ../glade/gbwidgets/gbwindow.c:1232
msgid "Window"
msgstr "Finestra"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:75
msgid "Error"
msgstr "Error"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Error del sistema"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "S'ha produït un error en obrir el fitxer"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "S'ha produït un error en llegir el fitxer"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "S'ha produït un error en escriure el fitxer"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "El directori no és vàlid"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "El valor no és apropiat"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "L'entitat XML no és vàlida"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "S'esperava un marcador d'inici"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "S'esperava un marcador final"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "S'esperava una dada de tipus caràcter"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "Falta l'identificador de classe"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Classe desconeguda"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "El component no és vàlid"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "Fi de fitxer inesperat"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Codi d'error desconegut"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Controlat per"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Controlat per a"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Etiquetat per a"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Etiquetat per"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Membre de"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Node fill de"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr "Segueix a"

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr "Ve de"

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr "Subfinestra de"

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr "Incrusta"

#: ../glade/glade_atk.c:130
msgid "Embedded By"
msgstr "Incrustat per"

#: ../glade/glade_atk.c:131
msgid "Popup For"
msgstr "Emergent per a"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr "Finestra pare de"

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Relació: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "Element d'interfície"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:773
#: ../glade/property.c:776
msgid "Name:"
msgstr "Nom:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr ""
"El nom de l'element d'interfície que es passarà a les tecnologies assistives"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "Descripció:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr ""
"La descripció de l'element d'interfície que es passarà a les tecnologies "
"assistives"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Títol de la taula:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "El títol de la taula que es passarà a les tecnologies assistives"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Selecciona els elements amb aquesta relació"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Clica"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Prem"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Allibera"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr ""
"Introduïu la descripció de l'acció que es pasarà a les tecnologies assistives"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Porta-retalls"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Heu de seleccionar un element per enganxar-hi a dins"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "No podeu enganxar dins de les finestres o els diàlegs."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"No podeu enganxar dins de l'element seleccionat, perquè\n"
"els seu pare l'ha creat automàticament."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr ""
"Tan sols els elements de menú poden ser enganxats en un menú o barra de "
"menús."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr ""
"Tan sols els botons poden ser enganxats dins d'una àrea d'acció de diàleg."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr ""
"Tan sols els elements GnomeDockItem poden ser enganxats dins d'un GnomeDock."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr ""
"Tan sols els elements GnomeDockItem poden ser enganxats sobre un "
"GnomeDockItem."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "Encara no es pot enganxar sobre un GnomeDockItem."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr ""
"Els elements GnomeDockItem tan sols poden ser enganxats en un GnomeDock."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:633
msgid "_New"
msgstr "_Nou"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Crea un nou fitxer"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "_Obsolets"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "GTK+ _bàsic"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "GTK+ _addicional"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Seleccioneu la tecla acceleradora"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Tecles"

#: ../glade/glade_menu_editor.c:395
msgid "Menu Editor"
msgstr "Editor de menú"

#: ../glade/glade_menu_editor.c:412
msgid "Type"
msgstr "Tipus"

#: ../glade/glade_menu_editor.c:413
msgid "Accelerator"
msgstr "Accelerador"

#: ../glade/glade_menu_editor.c:414
msgid "Name"
msgstr "Nom"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:1499
msgid "Handler"
msgstr "Gestor"

#: ../glade/glade_menu_editor.c:416 ../glade/property.c:102
msgid "Active"
msgstr "Actiu"

#: ../glade/glade_menu_editor.c:417
msgid "Group"
msgstr "Grup"

#: ../glade/glade_menu_editor.c:418
msgid "Icon"
msgstr "Icona"

#: ../glade/glade_menu_editor.c:459
msgid "Move the item and its children up one place in the list"
msgstr "Mou l'element i els seus fills un lloc amunt de la llista"

#: ../glade/glade_menu_editor.c:471
msgid "Move the item and its children down one place in the list"
msgstr "Mou l'element i els seus fills un lloc avall de la llista"

#: ../glade/glade_menu_editor.c:483
msgid "Move the item and its children up one level"
msgstr "Mou l'element i els seus fills un nivell amunt"

#: ../glade/glade_menu_editor.c:495
msgid "Move the item and its children down one level"
msgstr "Mou l'element i els seus fills un nivell avall"

#: ../glade/glade_menu_editor.c:525
msgid "The stock item to use."
msgstr "L'element predeterminat que s'utilitzarà."

#: ../glade/glade_menu_editor.c:528 ../glade/glade_menu_editor.c:643
msgid "Stock Item:"
msgstr "Element predeterminat:"

#: ../glade/glade_menu_editor.c:641
msgid "The stock Gnome item to use."
msgstr "L'element predeterminat del Gnome que s'utilitzarà."

#: ../glade/glade_menu_editor.c:746
msgid "The text of the menu item, or empty for separators."
msgstr "El text de l'element de menú, o buit per als separadors."

#: ../glade/glade_menu_editor.c:770 ../glade/property.c:777
msgid "The name of the widget"
msgstr "El nom de l'element"

#: ../glade/glade_menu_editor.c:791
msgid "The function to be called when the item is selected"
msgstr "La funció que es cridarà quan l'element sigui seleccionat"

#: ../glade/glade_menu_editor.c:793 ../glade/property.c:1547
msgid "Handler:"
msgstr "Gestor:"

#: ../glade/glade_menu_editor.c:812
msgid "An optional icon to show on the left of the menu item."
msgstr "Una icona opcional per mostrar a l'esquerra de l'element de menú."

#: ../glade/glade_menu_editor.c:935
msgid "The tip to show when the mouse is over the item"
msgstr "El consell que es mostra quan el ratolí és a sobre de l'element"

#: ../glade/glade_menu_editor.c:937 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Consell flotant:"

#: ../glade/glade_menu_editor.c:958
msgid "_Add"
msgstr "_Afegeix"

#: ../glade/glade_menu_editor.c:963
msgid "Add a new item below the selected item."
msgstr "Afegeix un nou element sota l'element seleccionat."

#: ../glade/glade_menu_editor.c:968
msgid "Add _Child"
msgstr "Afegeix un _fill"

#: ../glade/glade_menu_editor.c:973
msgid "Add a new child item below the selected item."
msgstr "Afegeix un nou element fill sota l'element seleccionat."

#: ../glade/glade_menu_editor.c:979
msgid "Add _Separator"
msgstr "Afegeix un _separador"

#: ../glade/glade_menu_editor.c:984
msgid "Add a separator below the selected item."
msgstr "Afegeix un separador a sota de l'element seleccionat."

#: ../glade/glade_menu_editor.c:989 ../glade/glade_project_window.c:242
msgid "_Delete"
msgstr "_Suprimeix"

#: ../glade/glade_menu_editor.c:994
msgid "Delete the current item"
msgstr "Suprimeix l'element actual"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:1000
msgid "Item Type:"
msgstr "Tipus d'element:"

#: ../glade/glade_menu_editor.c:1016
msgid "If the item is initially on."
msgstr "Si l'element és inicialment actiu."

#: ../glade/glade_menu_editor.c:1018
msgid "Active:"
msgstr "Actiu:"

#: ../glade/glade_menu_editor.c:1023 ../glade/glade_menu_editor.c:1638
#: ../glade/property.c:2216 ../glade/property.c:2226
msgid "No"
msgstr "No"

#: ../glade/glade_menu_editor.c:1037
msgid "The radio menu item's group"
msgstr "El grup de l'element del menú de grup"

#: ../glade/glade_menu_editor.c:1054 ../glade/glade_menu_editor.c:2414
#: ../glade/glade_menu_editor.c:2554
msgid "Radio"
msgstr "Grup"

#: ../glade/glade_menu_editor.c:1061 ../glade/glade_menu_editor.c:2412
#: ../glade/glade_menu_editor.c:2552
msgid "Check"
msgstr "Comprovació"

#: ../glade/glade_menu_editor.c:1068 ../glade/property.c:102
msgid "Normal"
msgstr "Normal"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1077
msgid "Accelerator:"
msgstr "Accelerador:"

#: ../glade/glade_menu_editor.c:1114 ../glade/property.c:1682
msgid "Ctrl"
msgstr "Control"

#: ../glade/glade_menu_editor.c:1119 ../glade/property.c:1685
msgid "Shift"
msgstr "Majúscula"

#: ../glade/glade_menu_editor.c:1124 ../glade/property.c:1688
msgid "Alt"
msgstr "Alternativa"

#: ../glade/glade_menu_editor.c:1129 ../glade/property.c:1695
msgid "Key:"
msgstr "Tecla:"

#: ../glade/glade_menu_editor.c:1135 ../glade/property.c:1674
msgid "Modifiers:"
msgstr "Modificadors:"

#: ../glade/glade_menu_editor.c:1638 ../glade/glade_menu_editor.c:2419
#: ../glade/glade_menu_editor.c:2562 ../glade/property.c:2216
msgid "Yes"
msgstr "Sí"

#: ../glade/glade_menu_editor.c:2008
msgid "Select icon"
msgstr "Seleccioneu una icona"

#: ../glade/glade_menu_editor.c:2353 ../glade/glade_menu_editor.c:2714
msgid "separator"
msgstr "separador"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3638 ../glade/glade_project_window.c:369
#: ../glade/property.c:5115
msgid "New"
msgstr "Nou"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Seleccionador"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"No s'ha establert el directori del projecte.\n"
"Definiu-lo utilitzant el diàleg d'opcions del projecte.\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"No s'ha establert el directori d'origen.\n"
"Definiu-lo utilitzant el diàleg d'opcions del projecte.\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"El directori d'origen no és vàlid:\n"
"\n"
"El directori d'origen ha de ser el directori del projecte\n"
"o bé un subdirectori del directori del projecte.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"No s'ha establert el directori dels mapes de píxels.\n"
"Definiu-lo utilitzant el diàleg d'opcions del projecte.\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "Encara no es pot generar codi per a %s"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""
"El vostre projecte fa servir elements d'interfície que ja\n"
"no existeixen i que Gtkmm-2 no proporciona.\n"
"Busqueu-los al vostre projecte i feu servir els seus reemplaçaments."

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"S'ha produït un error executant glade-- per generar codi font en C++.\n"
"Comproveu que teniu glade-- instal·lat i que és en el vostre PATH.\n"
"Després, proveu d'executar 'glade--<fitxer_projecte.glade>' en una terminal."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"S'ha produït un error en executar gate per generar el codi font en Ada95.\n"
"Comproveu que teniu instal·lat el gate i que es troba al vostre PATH.\n"
"Després, proveu d'executar 'gate <fitxer_projecte.glade>' en una terminal."

#: ../glade/glade_project.c:571
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"S'ha produït un error en executar el glade2perl per generar el codi font en "
"Perl.\n"
"Comproveu que teniu instal·lat el glade2perl i que es troba al vostre PATH.\n"
"Després, proveu d'executar 'glade2perl <fitxer_projecte.glade>' en una "
"terminal."

#: ../glade/glade_project.c:594
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"S'ha produït un error en executar l'eglade per generar el codi font en "
"Eiffel.\n"
"Comproveu que teniu instal·lat l'eglade i que es troba al vostre PATH.\n"
"Després, proveu d'executar 'eglade <fitxer_projecte.glade>' en una terminal."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"No s'ha establert el directori dels mapes de píxels .\n"
"Definiu-lo utilitzant el diàleg d'opcions del projecte.\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "S'ha produït un error en escriure el fitxer XML del projecte\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:385
#: ../glade/glade_project_window.c:890
msgid "Project Options"
msgstr "Opcions del projecte"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "General"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Opcions bàsiques:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "El directori del projecte"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Directori del projecte:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Navega..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "El nom del projecte actual"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Nom del projecte:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "El nom del programa"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "El fitxer del projecte"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Fitxer del projecte:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Subdirectoris:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "El directori on s'ha de desar el codi font generat"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Directori d'origen:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "El directori on desar els mapes de píxels"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Directori dels mapes de píxels:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "La llicència que s'afegeix al principi dels fitxers generats"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Llenguatge:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Habilita el suport per al Gnome"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Si es construirà una aplicació Gnome"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Habilita el suport per al Gnome DB"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Si es construirà una aplicació Gnome DB"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "Opcions de C"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""
"<b>Atenció:</b> es recomana fer servir libglade per a aplicacions complexes."

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Opcions generals:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Suport Gettext"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "Si les cadenes es marcan per traduir per al gettext"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "Anomena els elements"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Si els noms dels elements estan definits en el codi font"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Còpia de seguretat dels fitxers"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Si es fan còpies de seguretat dels fitxers de codi font anteriors"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Suport d'ajuda del Gnome"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Si s'ha d'incloure el suport per al sistema d'ajuda del Gnome"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Opcions del fitxer de sortida:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "Fitxer de sortida main.c"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr ""
"Si s'escriu un fitxer de sortida main.c que contingui la funció main(), en "
"el cas que no existeixi"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Genera funcions de suport"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Si es generen funcions de suport"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Genera fitxers de muntatge"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Si es generen fitxers per construir el codi font, incloent Makefile.am i "
"configure.in, en el cas que no existeixin"

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "Funcions de creació d'interfícies:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "El fitxer on s'escriuen les funcions per crear l'interfície"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Fitxer font:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr ""
"El fitxer on s'escriuen les declaracions de les funcions per crear la "
"interfície"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Fitxer de capçalera:"

#: ../glade/glade_project_options.c:594
msgid "Source file for interface creation functions"
msgstr "Fitxer font per a les funcions de creació d'interfícies"

#: ../glade/glade_project_options.c:595
msgid "Header file for interface creation functions"
msgstr "Fitxer de capçalera de funcions de creació d'interfícies:"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Gestor de senyals i funció de retorn de crida:"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr ""
"El fitxer on s'escriuen el gestor de senyals buit i les funcions de retorn "
"de crida"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"El fitxer on s'escriuen les declaracions del gestor de senyals i les "
"funcions de retorn de crida"

#: ../glade/glade_project_options.c:640
msgid "Source file for signal handler and callback functions"
msgstr "Fitxer font per al gestor de senyals i les funcions de retorn de crida"

#: ../glade/glade_project_options.c:641
msgid "Header file for signal handler and callback functions"
msgstr ""
"Fitxer de capçalera per al gestor de senyals i les funcions de retorn de "
"crida"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Funcions de suport:"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "El fitxer on s'escriuen les funcions de suport."

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "El fitxer on s'escriuen les declaracions de suport de les funcions"

#: ../glade/glade_project_options.c:686
msgid "Source file for support functions"
msgstr "Fitxer font per a les funcions de suport"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr "Fitxer de capçalera per a les funcions de suport"

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "Opcions de LibGlade"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Cadenes de text traduïbles:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""
"<b>Atenció:</b> aquesta opció ja no se suportarà en el futur. Feu servir "
"l'intltool."

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Desa les cadenes de text traduïbles"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Si les cadenes traduïbles es desen en un fitxer C per separat, per habilitar "
"les traduccions d'interfícies carregades per libglade"

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "El fitxer C on desar totes les cadenes traduïbles"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Fitxer:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Seleccioneu el directori del projecte"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Heu de definir la opció del fitxer de cadenes traduïbles"

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Heu de definir la opció del directori del projecte"

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Heu de definir la opció del fitxer del projecte"

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Heu de definir la opció del nom del projecte"

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Heu de definir la opció del nom del programa"

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Heu de definir la opció del directori font"

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Heu de definir la opció del directori de mapes de píxels"

#: ../glade/glade_project_window.c:187
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"No es pot mostrar el fitxer d'ajuda: %s.\n"
"\n"
"S'ha produït un error: %s"

#: ../glade/glade_project_window.c:211 ../glade/glade_project_window.c:635
msgid "Create a new project"
msgstr "Crea un nou projecte"

#: ../glade/glade_project_window.c:219 ../glade/glade_project_window.c:655
#: ../glade/glade_project_window.c:906
msgid "_Build"
msgstr "_Munta"

#: ../glade/glade_project_window.c:220 ../glade/glade_project_window.c:666
msgid "Output the project source code"
msgstr "Escriu el codi font del projecte"

#: ../glade/glade_project_window.c:226 ../glade/glade_project_window.c:669
msgid "Op_tions..."
msgstr "_Opcions..."

#: ../glade/glade_project_window.c:227 ../glade/glade_project_window.c:678
msgid "Edit the project options"
msgstr "Edita les opcions del projecte"

#: ../glade/glade_project_window.c:242 ../glade/glade_project_window.c:717
msgid "Delete the selected widget"
msgstr "Esborra l'element seleccionat"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:728
msgid "Show _Palette"
msgstr "Mostra la _paleta"

#: ../glade/glade_project_window.c:260 ../glade/glade_project_window.c:733
msgid "Show the palette of widgets"
msgstr "Mostra la paleta d'elements"

#: ../glade/glade_project_window.c:266 ../glade/glade_project_window.c:738
msgid "Show Property _Editor"
msgstr "Mostra l'_editor de propietats"

#: ../glade/glade_project_window.c:267 ../glade/glade_project_window.c:744
msgid "Show the property editor"
msgstr "Mostra l'editor de propietats"

#: ../glade/glade_project_window.c:273 ../glade/glade_project_window.c:748
msgid "Show Widget _Tree"
msgstr "Mos_tra l'arbre d'elements"

#: ../glade/glade_project_window.c:274 ../glade/glade_project_window.c:754
#: ../glade/main.c:82 ../glade/main.c:116
msgid "Show the widget tree"
msgstr "Mostra l'arbre d'elements"

#: ../glade/glade_project_window.c:280 ../glade/glade_project_window.c:758
msgid "Show _Clipboard"
msgstr "Mostra el _porta-retalls"

#: ../glade/glade_project_window.c:281 ../glade/glade_project_window.c:764
#: ../glade/main.c:86 ../glade/main.c:120
msgid "Show the clipboard"
msgstr "Mostra el porta-retalls"

#: ../glade/glade_project_window.c:299
msgid "Show _Grid"
msgstr "Mostra la _quadrícula"

#: ../glade/glade_project_window.c:300 ../glade/glade_project_window.c:800
msgid "Show the grid (in fixed containers only)"
msgstr "Mostra la quadrícula (només en contenidors fixos)"

#: ../glade/glade_project_window.c:306
msgid "_Snap to Grid"
msgstr "Aju_sta a la quadrícula"

#: ../glade/glade_project_window.c:307
msgid "Snap widgets to the grid"
msgstr "Ajusta els elements a la quadrícula"

#: ../glade/glade_project_window.c:313 ../glade/glade_project_window.c:772
msgid "Show _Widget Tooltips"
msgstr "Mostra els consells _flotants"

#: ../glade/glade_project_window.c:314 ../glade/glade_project_window.c:780
msgid "Show the tooltips of created widgets"
msgstr "Mostra els consells flotants dels elements creats"

#: ../glade/glade_project_window.c:323 ../glade/glade_project_window.c:803
msgid "Set Grid _Options..."
msgstr "Defineix les _opcions de la quadrícula..."

#: ../glade/glade_project_window.c:324
msgid "Set the grid style and spacing"
msgstr "Defineix l'estil i l'espaiat de la quadrícula"

#: ../glade/glade_project_window.c:330 ../glade/glade_project_window.c:824
msgid "Set Snap O_ptions..."
msgstr "Defineix les o_pcions d'ajustament..."

#: ../glade/glade_project_window.c:331
msgid "Set options for snapping to the grid"
msgstr "Defineix les opcions per ajustar a la quadrícula"

#: ../glade/glade_project_window.c:343
msgid "_FAQ"
msgstr "Preguntes _freqüents"

#: ../glade/glade_project_window.c:344
msgid "View the Glade FAQ"
msgstr "Visualitzeu les preguntes freqüents del Glade"

#. create File menu
#: ../glade/glade_project_window.c:358 ../glade/glade_project_window.c:626
msgid "_Project"
msgstr "_Projecte"

#: ../glade/glade_project_window.c:369 ../glade/glade_project_window.c:873
#: ../glade/glade_project_window.c:1055
msgid "New Project"
msgstr "Projecte nou"

#: ../glade/glade_project_window.c:374
msgid "Open"
msgstr "Obre"

#: ../glade/glade_project_window.c:374 ../glade/glade_project_window.c:878
#: ../glade/glade_project_window.c:1116
msgid "Open Project"
msgstr "Obre un projecte"

#: ../glade/glade_project_window.c:379
msgid "Save"
msgstr "Desa"

#: ../glade/glade_project_window.c:379 ../glade/glade_project_window.c:882
#: ../glade/glade_project_window.c:1481
msgid "Save Project"
msgstr "Desa un projecte"

#: ../glade/glade_project_window.c:385
msgid "Options"
msgstr "Opcions"

#: ../glade/glade_project_window.c:390
msgid "Build"
msgstr "Munta"

#: ../glade/glade_project_window.c:390
msgid "Build the Source Code"
msgstr "Munta el codi font"

#: ../glade/glade_project_window.c:639
msgid "Open an existing project"
msgstr "Obre un projecte existent"

#: ../glade/glade_project_window.c:643
msgid "Save project"
msgstr "Desa el projecte"

#: ../glade/glade_project_window.c:688
msgid "Quit Glade"
msgstr "Surt del Glade"

#: ../glade/glade_project_window.c:702
msgid "Cut the selected widget to the clipboard"
msgstr "Retalla l'element seleccionat en el porta-retalls"

#: ../glade/glade_project_window.c:707
msgid "Copy the selected widget to the clipboard"
msgstr "Copia l'element seleccionat en el porta-retalls"

#: ../glade/glade_project_window.c:712
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Enganxa l'element del porta-retalls a sobre de l'element seleccionat"

#: ../glade/glade_project_window.c:784
msgid "_Grid"
msgstr "_Quadrícula"

#: ../glade/glade_project_window.c:792
msgid "_Show Grid"
msgstr "Mo_stra la quadrícula"

#: ../glade/glade_project_window.c:809
msgid "Set the spacing between grid lines"
msgstr "Defineix l'espai entre les línies de quadrícules"

#: ../glade/glade_project_window.c:812
msgid "S_nap to Grid"
msgstr "A_justa a la quadrícula"

#: ../glade/glade_project_window.c:820
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "Ajusta els elements a la quadrícula (només en contenidors fixos)"

#: ../glade/glade_project_window.c:830
msgid "Set which parts of a widget snap to the grid"
msgstr "Defineix quines parts de l'element s'ajustaran a la quadrícula"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:855
msgid "_About..."
msgstr "_Quant a..."

#: ../glade/glade_project_window.c:896
msgid "Optio_ns"
msgstr "Opcio_ns"

#: ../glade/glade_project_window.c:900
msgid "Write Source Code"
msgstr "Escriu el codi font"

#: ../glade/glade_project_window.c:992 ../glade/glade_project_window.c:1697
#: ../glade/glade_project_window.c:1986
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:999
msgid "Are you sure you want to create a new project?"
msgstr "Esteu segur que voleu crear un nou projecte?"

#: ../glade/glade_project_window.c:1059
msgid "New _GTK+ Project"
msgstr "Nou projecte de _GTK+"

#: ../glade/glade_project_window.c:1060
msgid "New G_NOME Project"
msgstr "_Nou projecte del GNOME"

#: ../glade/glade_project_window.c:1063
msgid "Which type of project do you want to create?"
msgstr "Quin tipus de projecte voleu crear?"

#: ../glade/glade_project_window.c:1097
msgid "New project created."
msgstr "S'ha creat un nou projecte."

#: ../glade/glade_project_window.c:1187
msgid "Project opened."
msgstr "Projecte obert."

#: ../glade/glade_project_window.c:1201
msgid "Error opening project."
msgstr "S'ha produït un error en obrir el projecte."

#: ../glade/glade_project_window.c:1265
msgid "Errors opening project file"
msgstr "S'han produït errors en obrir el fitxer del projecte"

#: ../glade/glade_project_window.c:1271
msgid " errors opening project file:"
msgstr " errors en obrir el fitxer del projecte:"

#: ../glade/glade_project_window.c:1344
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"En aquest moment no hi ha cap projecte obert.\n"
"Heu de crear un nou projecte amb l'ordre Projecte/Nou."

#: ../glade/glade_project_window.c:1548
msgid "Error saving project"
msgstr "S'ha produït un error en desar el projecte"

#: ../glade/glade_project_window.c:1550
msgid "Error saving project."
msgstr "S'ha produït un error en desar el projecte."

#: ../glade/glade_project_window.c:1556
msgid "Project saved."
msgstr "S'ha desat el projecte."

#: ../glade/glade_project_window.c:1626
msgid "Errors writing source code"
msgstr "S'han produït error en escriure el codi font"

#: ../glade/glade_project_window.c:1628
msgid "Error writing source."
msgstr "S'ha produït un error en escriure el codi font."

#: ../glade/glade_project_window.c:1634
msgid "Source code written."
msgstr "S'ha escrit el codi font."

#: ../glade/glade_project_window.c:1665
msgid "System error message:"
msgstr "Missatge d'error del sistema:"

#: ../glade/glade_project_window.c:1704
msgid "Are you sure you want to quit?"
msgstr "Esteu segurs que voleu sortir?"

#: ../glade/glade_project_window.c:1988 ../glade/glade_project_window.c:2048
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "(C) 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1989 ../glade/glade_project_window.c:2047
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr ""
"El Glade és un constructor d'interfícies d'usuari per a GTK+ i el GNOME."

#: ../glade/glade_project_window.c:2018
msgid "About Glade"
msgstr "Quant al Glade"

#: ../glade/glade_project_window.c:2103
msgid "<untitled>"
msgstr "<sense títol>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Navegador de bases de dades"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Quadre combinat vinculat a dades"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr "GnomeDbConnectionProperties"

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Seleccionador de connexions"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "Configurador DSN"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "Auxiliar de configuració de DSN"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr "Ressalta el text:"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr "Si està seleccionat, es ressaltarà el text dins de l'element"

#: ../glade/gnome-db/gnomedbeditor.c:178
msgid "GnomeDbEditor"
msgstr "GnomeDbEditor"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Visualitzador d'errors de la base de dades"

#: ../glade/gnome-db/gnomedberrordlg.c:219
msgid "Database error dialog"
msgstr "Diàleg d'errors de la base de dades"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Formulari"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr "Text a dins de la barra grisa"

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr "Barra grisa"

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Graella vinculada a dades"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Llista vinculada a dades"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Element d'entrada de la base de dades"

#: ../glade/gnome-db/gnomedblogindlg.c:78
msgid "Login"
msgstr "Entrada"

#: ../glade/gnome-db/gnomedblogindlg.c:221
msgid "Database login dialog"
msgstr "Diàleg d'entrada a la base de dades"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Seleccionador de proveïdor"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr "GnomeDbQueryBuilder"

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Seleccionador de font de dades"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Editor de taules "

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Permetre que floti:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Si es poden fer servir elements adjuntables flotants"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Afegeix una banda adjuntable a sobre"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Afegeix una banda adjuntable a sota"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Afegeix una banda adjuntable a l'esquerra"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Afegeix una banda adjuntable a la dreta"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Afegeix un element adjuntable flotant"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Adjuntable del Gnome"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Bloquejat:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Si l'element adjuntable està bloquejat en la seva posició"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Exclusiu:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "Si l'element adjuntable és sempre l'únic de la seva banda"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Mai flotant:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "Si l'element adjuntable no pot flotar en la seva finestra"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Mai vertical:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "Si l'element adjuntable no pot estar en vertical"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Mai horitzontal:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "Si l'element adjuntable no pot estar en horitzontal"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "El tipus d'ombra al voltant d'un element adjuntable"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "La orientació d'un element adjuntable flotant"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Afegeix un element adjuntable abans"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Afegeix un element adjuntable després"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Element Gnome Dock"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr ""
"Informació addicional, per exemple una descripció del paquet i la seva "
"pàgina principal a la xarxa"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Diàleg de «Quant a» del Gnome"

#: ../glade/gnome/gnomeapp.c:171
msgid "New File"
msgstr "Nou Fitxer"

#: ../glade/gnome/gnomeapp.c:173
msgid "Open File"
msgstr "Obre el fitxer"

#: ../glade/gnome/gnomeapp.c:175
msgid "Save File"
msgstr "Desa el fitxer"

#: ../glade/gnome/gnomeapp.c:204
msgid "Status Bar:"
msgstr "Barra d'estat:"

#: ../glade/gnome/gnomeapp.c:205
msgid "If the window has a status bar"
msgstr "Si la finestra té barra d'estat"

#: ../glade/gnome/gnomeapp.c:206
msgid "Store Config:"
msgstr "Desa la configuració:"

#: ../glade/gnome/gnomeapp.c:207
msgid "If the layout is saved and restored automatically"
msgstr "Si la disposició es desa i es recupera automàticament"

#: ../glade/gnome/gnomeapp.c:443
msgid "Gnome Application Window"
msgstr "Finestra d'aplicació del Gnome"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Missatge d'estat."

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "Progrés:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "Si la barra d'aplicació té un indicador de progrés"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Estat:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr ""
"Si la barra d'aplicació té una àrea per a missatge d'estat i entrada de l' "
"usuari"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Barra d'aplicacions del Gnome"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Suavitzat:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr "Si el llenç de dibuix suavitza els contorns del text i els gràfics"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "La coordenada x mínima"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "La coordenada y mínima"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "La coordenada x màxima"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "La coordenada y màxima"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Píxels per unitat:"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "El nombre de píxels corresponents a una unitat"

#: ../glade/gnome/gnomecanvas.c:248
msgid "GnomeCanvas"
msgstr "GnomeCanvas"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Trama:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Si la mostra ha d'utilitzar un tramatge per ser més acurat"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Escolliu un color"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Seleccionador de color del Gnome"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "No s'ha pogut crear el control Bonobo"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Nou control del Bonobo"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Seleccioneu un control Bonobo"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3902
msgid "Description"
msgstr "Descripció"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Control Bonobo"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Mostra l'hora:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Si es mostren l'hora i la data"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "Format de 24 hores:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Si el temps es mostra en el format de 24 hores"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Hora més baixa:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "L'hora més baixa per mostrar al desplegable"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Hora més alta:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "L'hora més alta per mostrar al desplegable"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "GnomeDateEdit"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "Auto Close:"
msgstr "Tanca automàticament:"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "If the dialog closes when any button is clicked"
msgstr "Si es tanca el diàleg quan es cliqui a qualsevol botó"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "Hide on Close:"
msgstr "Amaga en tancar:"

#: ../glade/gnome/gnomedialog.c:156 ../glade/gnome/gnomemessagebox.c:193
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "Si s'amaga el diàleg quan es tanca, en comptes de destruir-se"

#: ../glade/gnome/gnomedialog.c:342
msgid "Gnome Dialog Box"
msgstr "Quadre de diàleg del Gnome"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Nou auxiliar del Gnome"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Mostra ajuda"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Mostra el botó d'ajuda."

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Afegeix una pàgina inicial"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Afegeix una pàgina final"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Auxiliar"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "El títol de la pàgina"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "El text principal de la pàgina. Es fa servir per presentar l'auxiliar."

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Color del títol:"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "El color del text del títol"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Color del text:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "El color del text principal"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "El color de fons de la pàgina"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Fons del logotipus:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "El color de fons al voltant del logotipus"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Color del quadre de text:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "El color de fons de l'àrea principal del text"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Imatge de logotip:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "El logotipus que es mostrarà a la part superior dreta de la pàgina"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Marca d'aigua lateral:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "La imatge principal que es mostrarà a l'esquerra de la pàgina."

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Marca d'aigua superior:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr ""
"La marca d'aigua que es mostra a la part superior de la pàgina superior."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Pàgina inicial o final de l'auxiliar"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Fons del contingut:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "El color de fons al voltant del títol"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "La imatge que es mostrarà a la part superior de la pàgina"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Pàgina estàndard de l'auxiliar"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "Id. d'historial:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr ""
"L'identificador amb el qual es relacionaran les entrades de l'historial"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Màx. desades:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "El nombre màxim d'entrades desades de l'historial"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Entrada del Gnome"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "El títol del diàleg de selecció de fitxers"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Directori:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Si es demana seleccionar un directori en lloc d'un fitxer"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "Si el diàleg de selecció del fitxer ha de ser modal"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr "Fes servir el FileChooser:"

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr ""
"Fes servir el nou element d'interfície GtkFileChooser en comptes del "
"GtkFileSelection"

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Entrada de fitxer del Gnome"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr ""
"El text de previsualització que es mostrarà en el diàleg de selecció de "
"tipus de lletra"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Mode:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "Allò que es mostra en el botó del seleccionador de tipus de lletra"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr ""
"La mida del tipus de lletra que s'utilitzarà en el botó de selecció de tipus "
"de lletra"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Seleccionador del tipus de lletra del Gnome"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "La URL que es mostrarà quan es cliqui el botó"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "El text que es mostrarà en el botó"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Botó d'enllaç a Internet del Gnome"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Entrada d'icona del Gnome"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "El mode de selecció"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Amplada de la icona:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "L'amplada de cada icona"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "El nombre de píxels entre files d'icones"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "El nombre de píxels entre columnes d'icones"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Contorn de la icona:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "El nombre de píxels al voltant de les icones (potser no s'utilitza)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Espaiat del text:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "El nombre de píxels entre el text i la icona"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Text editable:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Si l'usuari pot editar el text de la icona"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Text estàtic:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr ""
"Si el text de la icona és estàtic. En aquest cas el GnomeIconList no el "
"copiarà"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Llista d'icones"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Selecció d'icones"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "Message Type:"
msgstr "Tipus de missatge:"

#: ../glade/gnome/gnomemessagebox.c:176
msgid "The type of the message box"
msgstr "El tipus de quadre de missatge"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "Message:"
msgstr "Missatge:"

#: ../glade/gnome/gnomemessagebox.c:178
msgid "The message to display"
msgstr "El missatge per mostrar"

#: ../glade/gnome/gnomemessagebox.c:499
msgid "Gnome Message Box"
msgstr "Quadre de missatge del Gnome"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "El nom del fitxer del mapa de píxels"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Escalat:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Si s'escala el mapa de píxels"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Amplada escalada:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "L'amplada per escalar el mapa de píxels"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Alçada escalada:"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "L'alçada per escalar el mapa de píxels"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Mapa de píxels del Gnome"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Previsualitza:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Si es mostra una petita previsualització del mapa de píxels"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmapEntry"

#: ../glade/gnome/gnomepropertybox.c:113
msgid "New GnomePropertyBox"
msgstr "Nou quadre de propietats GnomePropertyBox"

#: ../glade/gnome/gnomepropertybox.c:366
msgid "Property Dialog Box"
msgstr "Quadre de diàleg de propietats"

#: ../glade/main.c:70 ../glade/main.c:104
msgid "Write the source code and exit"
msgstr "Escriu el codi font i surt"

#: ../glade/main.c:74 ../glade/main.c:108
msgid "Start with the palette hidden"
msgstr "Oculta la paleta al començament"

#: ../glade/main.c:78 ../glade/main.c:112
msgid "Start with the property editor hidden"
msgstr "Oculta l'editor de propietats al començament"

#: ../glade/main.c:460
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade: s'ha de definir el fitxer XML per a les opcions '-w' o '--write-"
"source'.\n"

#: ../glade/main.c:474
msgid "glade: Error loading XML file.\n"
msgstr "glade: s'ha produït un error en carregar el fitxer XML.\n"

#: ../glade/main.c:481
msgid "glade: Error writing source.\n"
msgstr "glade: s'ha produït un error en escriure el codi font.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Paleta"

#: ../glade/property.c:73
msgid "private"
msgstr "privat"

#: ../glade/property.c:73
msgid "protected"
msgstr "protegit"

#: ../glade/property.c:73
msgid "public"
msgstr "públic"

#: ../glade/property.c:102
msgid "Prelight"
msgstr ""

#: ../glade/property.c:103
msgid "Selected"
msgstr "Seleccionat"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Insensible"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Quan la finestra necessita tornar-se a dibuixar"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Quan es mou el ratolí"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Modificadors del moviment del ratolí"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Moviment del ratolí amb qualsevol botó premut"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Moviment del ratolí amb el botó 1 premut"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Moviment del ratolí amb el botó 2 premut"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Moviment del ratolí amb el botó 3 premut"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "Qualsevol botó del ratolí premut"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "Qualsevol botó del ratolí alliberat"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "Qualsevol tecla premuda"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "Qualsevol tecla alliberada"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Quan el ratolí entra a la finestra"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Quan el ratolí surt de la finestra"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Qualsevol canvi en el focus d'entrada"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Qualsevol canvi en l'estructura de la finestra"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "Qualsevol canvi en les propietats de l'X Windows"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Qualsevol canvi en la visibilitat"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "Per a cursors en programes que facin servir l'XInput"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Propietats"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Empaquetat"

#: ../glade/property.c:625
msgid "Common"
msgstr "Comú"

#: ../glade/property.c:631
msgid "Style"
msgstr "Estil"

#: ../glade/property.c:637 ../glade/property.c:4646
msgid "Signals"
msgstr "Senyals"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Propietats: "

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Propietats: <cap>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "Classe:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "La classe de l'element"

#: ../glade/property.c:813
msgid "Width:"
msgstr "Amplada:"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""
"L'amplada demanada de l'element (normalment s'usa per definir l'amplada "
"mínima)"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Alçada:"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""
"L'alçada demanada de l'element (normalment s'usa per definir l'alçada mínima)"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Visibilitat:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "Si l'element és inicialment visible"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Sensible:"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "Si l'element respon a l'entrada"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr ""
"El consell flotant que es mostrarà quan el ratolí sigui sobre l'element"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Pot ser predeterminat:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr "Si l'element pot ser l'acció predeterminada en el diàleg"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "Per defecte:"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Si l'element és l'acció predeterminada en el diàleg"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Pot tenir focus:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "Si l'element pot acceptar el focus d'entrada"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Té focus:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "Si l'element té el focus d'entrada"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Esdeveniments:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "Els esdeveniments d'X que rep l'element"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Esd. d'extensió:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "El mode dels esdeveniments d'extensió X"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Acceleradors:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Defineix els senyals que s'emterà quan es premin les tecles"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Edita..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "Propaga:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "Quan sigui activat, es propagarà l'estil de l'element al seu fill"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Nom de l'estil:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "El nom del estil. Pot ser compartit per més d'un element"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Tipus de lletra:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "El tipus de lletra que s'utilitzarà per a qualsevol text en l'element"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Copia tots"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Primer pla:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Fons:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "Base:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Color del primer pla"

#: ../glade/property.c:928
msgid "Background color"
msgstr "Color de fons"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Color del text"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Color de base"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Mapa de píxels de fons"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "La imatge que s'utilitzarà com a fons de l'element"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "El fitxer on escriure el codi font"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Públic:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Si l'element s'afegeix a l'estructura de dades del component"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Classe per separat:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Posa aquest subarbre d'element en una classe per separat"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Fitxer per separat:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Posa aquest element en un fitxer font separat"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Visibilitat:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr ""
"Visibilitat dels elements. Els elements públics s'exporten a un mapa global."

#: ../glade/property.c:1127
msgid "You need to select a color or background to copy"
msgstr "Heu de seleccionar un color o un fons per copiar"

#: ../glade/property.c:1146
msgid "Invalid selection in on_style_copy()"
msgstr "La selecció d'on_style_copy() no és vàlida"

#: ../glade/property.c:1188
msgid "You need to copy a color or background pixmap first"
msgstr "Heu de copiar un color o un mapa de píxels de fons primer"

#: ../glade/property.c:1194
msgid "You need to select a color to paste into"
msgstr "Heu de seleccionar un color per enganxar-lo"

#: ../glade/property.c:1204
msgid "You need to select a background pixmap to paste into"
msgstr "Heu de seleccionar un mapa de píxels de fons per enganxar-lo"

#: ../glade/property.c:1456
msgid "Couldn't create pixmap from file\n"
msgstr "No s'ha pogut crear un mapa de píxels del fitxer\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1498
msgid "Signal"
msgstr "Senyal"

#: ../glade/property.c:1500
msgid "Data"
msgstr "Dades"

#: ../glade/property.c:1501
msgid "After"
msgstr "Després"

#: ../glade/property.c:1502
msgid "Object"
msgstr "Objecte"

#: ../glade/property.c:1533 ../glade/property.c:1697
msgid "Signal:"
msgstr "Senyal:"

#: ../glade/property.c:1534
msgid "The signal to add a handler for"
msgstr "El senyal per al qual afegir un gestor"

#: ../glade/property.c:1548
msgid "The function to handle the signal"
msgstr "La funció per gestionar el senyal"

#: ../glade/property.c:1551
msgid "Data:"
msgstr "Dades:"

#: ../glade/property.c:1552
msgid "The data passed to the handler"
msgstr "Les dades que es passaran al gestor"

#: ../glade/property.c:1553
msgid "Object:"
msgstr "Objecte:"

#: ../glade/property.c:1554
msgid "The object which receives the signal"
msgstr "L'objecte que rep el senyal"

#: ../glade/property.c:1555
msgid "After:"
msgstr "Després:"

#: ../glade/property.c:1556
msgid "If the handler runs after the class function"
msgstr "Si el gestor s'executa després de la funció de la classe"

#: ../glade/property.c:1569
msgid "Add"
msgstr "Afegeix"

#: ../glade/property.c:1575
msgid "Update"
msgstr "Actualitza"

#: ../glade/property.c:1587
msgid "Clear"
msgstr "Neteja"

#: ../glade/property.c:1637
msgid "Accelerators"
msgstr "Acceleradors"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1650
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1651
msgid "Key"
msgstr "Tecla"

#: ../glade/property.c:1652
msgid "Signal to emit"
msgstr "Senyal a emetre"

#: ../glade/property.c:1696
msgid "The accelerator key"
msgstr "La tecla acceleradora"

#: ../glade/property.c:1698
msgid "The signal to emit when the accelerator is pressed"
msgstr "El senyal que s'ha d'emetre quan es premi la tecla acceleradora"

#: ../glade/property.c:1847
msgid "Edit Text Property"
msgstr "Edita la propietat del text"

#: ../glade/property.c:1885
msgid "<b>_Text:</b>"
msgstr "<b>_Text:</b>"

#: ../glade/property.c:1895
msgid "T_ranslatable"
msgstr "T_raduïble"

#: ../glade/property.c:1899
msgid "Has Context _Prefix"
msgstr "Té _prefix de context"

#: ../glade/property.c:1925
msgid "<b>Co_mments For Translators:</b>"
msgstr "<b>Co_mentaris per a traductors:</b>"

#: ../glade/property.c:3892
msgid "Select X Events"
msgstr "Seleccioneu l'esdeveniment X"

#: ../glade/property.c:3901
msgid "Event Mask"
msgstr "Màscara d'esdeveniments"

#: ../glade/property.c:4031 ../glade/property.c:4080
msgid "You need to set the accelerator key"
msgstr "Heu de definir la tecla acceleradora"

#: ../glade/property.c:4038 ../glade/property.c:4087
msgid "You need to set the signal to emit"
msgstr "Heu de definir el senyal a emetre"

#: ../glade/property.c:4314 ../glade/property.c:4370
msgid "You need to set the signal name"
msgstr "Heu de definir el nom del senyal"

#: ../glade/property.c:4321 ../glade/property.c:4377
msgid "You need to set the handler for the signal"
msgstr "Heu de definir el gestor per al senyal"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4580
#, c-format
msgid "%s signals"
msgstr "%s senyals"

#: ../glade/property.c:4637
msgid "Select Signal"
msgstr "Seleccioneu el senyal"

#: ../glade/property.c:4833
msgid "Value:"
msgstr "Valor:"

#: ../glade/property.c:4833
msgid "Min:"
msgstr "Mín:"

#: ../glade/property.c:4833
msgid "Step Inc:"
msgstr "Increment de pas:"

#: ../glade/property.c:4834
msgid "Page Inc:"
msgstr "Increment de pàgina:"

#: ../glade/property.c:4834
msgid "Page Size:"
msgstr "Mida de la pàgina:"

#: ../glade/property.c:4836
msgid "H Value:"
msgstr "Valor hor.:"

#: ../glade/property.c:4836
msgid "H Min:"
msgstr "Mínim hor.:"

#: ../glade/property.c:4836
msgid "H Max:"
msgstr "Màxim hor.:"

#: ../glade/property.c:4836
msgid "H Step Inc:"
msgstr "Increment de pas hor.:"

#: ../glade/property.c:4837
msgid "H Page Inc:"
msgstr "Increment de pàgina hor:"

#: ../glade/property.c:4837
msgid "H Page Size:"
msgstr "Mida de pàgina hor.:"

#: ../glade/property.c:4839
msgid "V Value:"
msgstr "Valor ver.:"

#: ../glade/property.c:4839
msgid "V Min:"
msgstr "Mín ver.:"

#: ../glade/property.c:4839
msgid "V Max:"
msgstr "Màxim ver.:"

#: ../glade/property.c:4839
msgid "V Step Inc:"
msgstr "Incr. ver. de pas:"

#: ../glade/property.c:4840
msgid "V Page Inc:"
msgstr "Incr. ver. de pàgina:"

#: ../glade/property.c:4840
msgid "V Page Size:"
msgstr "Mida ver. de la pàgina:"

#: ../glade/property.c:4843
msgid "The initial value"
msgstr "El valor inicial"

#: ../glade/property.c:4844
msgid "The minimum value"
msgstr "El valor mínim"

#: ../glade/property.c:4845
msgid "The maximum value"
msgstr "El valor màxim"

#: ../glade/property.c:4846
msgid "The step increment"
msgstr "L'increment del pas"

#: ../glade/property.c:4847
msgid "The page increment"
msgstr "L'increment de la pàgina"

#: ../glade/property.c:4848
msgid "The page size"
msgstr "La mida de la pàgina"

#: ../glade/property.c:5003
msgid "The requested font is not available."
msgstr "El tipus de lletra demanat no està disponible."

#: ../glade/property.c:5052
msgid "Select Named Style"
msgstr "Seleccioneu un nom d'estil"

#: ../glade/property.c:5063
msgid "Styles"
msgstr "Estils"

#: ../glade/property.c:5122
msgid "Rename"
msgstr "Canvia el nom"

#: ../glade/property.c:5150
msgid "Cancel"
msgstr "Cancel·la"

#: ../glade/property.c:5270
msgid "New Style:"
msgstr "Nou estil:"

#: ../glade/property.c:5284 ../glade/property.c:5405
msgid "Invalid style name"
msgstr "El nom de l'estil no és vàlid"

#: ../glade/property.c:5292 ../glade/property.c:5415
msgid "That style name is already in use"
msgstr "S'està usant el nom de l'estil"

#: ../glade/property.c:5390
msgid "Rename Style To:"
msgstr "Canvia el nom de l'estil per:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"No es pot canviar el nom del fitxer:\n"
"  %s\n"
"per:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"No s'ha pogut crear el fitxer:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "S'ha produït un error en escriure el fitxer XML\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Fitxer de cadenes traduïbles generat pel Glade.\n"
" * Afegiu aquest fitxer al POTFILES.in del vostre projecte.\n"
" * NO el compileu com a part de la vostra aplicació.\n"
" */\n"
"\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"El nom del fitxer font de la interfície no és vàlid: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"El nom del fitxer de capçalera de la interfície no és vàlid: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"El nom del fitxer font de les funcions de retorn de crida no és vàlid: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"El nom del fitxer de les capçaleres de les funcions de retorn de crida no és "
"vàlid: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"El nom del fitxer font de suport no és vàlid: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"El nom del fitxer de capçalera de suport no és vàlid: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"No es pot afegir al final del fitxer:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"S'ha produït un error en escriure al fitxer:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr "S'ha de definir el nom del fitxer en el diàleg d'opcions del projecte."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"El nom del fitxer ha de ser un nom senzill sense camí.\n"
"Utilitzeu el diàleg d'opcions del projecte per definir-lo."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Arbre d'elements"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "No s'ha trobat l'element a la caixa"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "No s'ha trobat l'element a la taula"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "No s'ha trobat l'element al contenidor fix"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "No s'ha trobat l'element a l'empaquetador"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"No es pot accedir el fitxer:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"No es pot obrir el fitxer:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"S'ha produït un error en llegir el fitxer:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"No es pot crear el directori:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"No es pot accedir al directori:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"El directori no és vàlid:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Projectes"

#: ../glade/utils.c:1628
msgid "project"
msgstr "projecte"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"No es pot obrir el directori:\n"
"  %s\n"

#~ msgid "Design user interfaces"
#~ msgstr "Dissenyeu interfícies d'usuari"
