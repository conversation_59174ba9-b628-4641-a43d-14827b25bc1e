## Process this file with automake to produce Makefile.in

noinst_LIBRARIES = libgnomewidgets.a

# maybe this should just be $(GLADE_GNOME_CFLAGS) ?
INCLUDES = $(GLADE_CFLAGS) \
	$(GLADE_DEPRECATION_CFLAGS)

libgnomewidgets_a_SOURCES = \
	bonobodock.c \
	bonobodockitem.c \
	gnomeabout.c \
	gnomeapp.c \
	gnomeappbar.c \
	gnomecanvas.c \
	gnomecolorpicker.c \
	gnomecontrol.c \
	gnomedateedit.c \
	gnomedialog.c \
	gnomedruid.c \
	gnomedruidpageedge.c \
	gnomedruidpagestandard.c \
	gnomeentry.c \
	gnomefileentry.c \
	gnomefontpicker.c \
	gnomehref.c \
	gnomeiconentry.c \
	gnomeiconlist.c \
	gnomeiconselection.c \
	gnomemessagebox.c \
	gnomepixmap.c \
	gnomepixmapentry.c \
	gnomepropertybox.c
