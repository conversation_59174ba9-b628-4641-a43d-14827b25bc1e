<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.pkware.zip-archive</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.m3u-playlist</string>
				<string>public.pls-playlist</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.mp3</string>
				<string>public.mpeg-4-audio</string>
				<string>public.ulaw-audio</string>
				<string>com.microsoft.waveform-audio</string>
				<string>com.microsoft.windows-media-wma</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>net.sourceforge.deadbeef.playlist</string>
			</array>
			<key>NSExportableTypes</key>
			<array>
				<string>net.sourceforge.deadbeef.playlist</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ogg</string>
				<string>oga</string>
				<string>ogx</string>
			</array>
			<key>CFBundleTypeMIMETypes</key>
			<array>
				<string>audio/ogg</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>OggVorbis Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>wv</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>WavPack Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>vtx</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>VTX Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>tta</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>TTA Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>paf</string>
				<string>svx</string>
				<string>nist</string>
				<string>voc</string>
				<string>ircam</string>
				<string>w64</string>
				<string>mat4</string>
				<string>mat5</string>
				<string>pvf</string>
				<string>xi</string>
				<string>htk</string>
				<string>sds</string>
				<string>avr</string>
				<string>wavex</string>
				<string>sd2</string>
				<string>wve</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Waveform Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>sid</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>SID Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>mpc</string>
				<string>mpp</string>
				<string>mp+</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>MusePack Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>mp1</string>
				<string>mp2</string>
				<string>mpga</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>MPEG-3 Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>sndh</string>
				<string>sc68</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>SC68 (Atari_ST_SNDH_YM2149) Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ay</string>
				<string>gbs</string>
				<string>gym</string>
				<string>hes</string>
				<string>kss</string>
				<string>nsf</string>
				<string>nsfe</string>
				<string>sap</string>
				<string>sfm</string>
				<string>spc</string>
				<string>vgm</string>
				<string>vgz</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Game Music Emu Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>flac</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>FLAC Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>ape</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Monkey&apos;s Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>shn</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Shorten Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>mod</string>
				<string>mdz</string>
				<string>stk</string>
				<string>fst</string>
				<string>oct</string>
				<string>s3m</string>
				<string>s3z</string>
				<string>stm</string>
				<string>stz</string>
				<string>it</string>
				<string>itz</string>
				<string>xm</string>
				<string>xmz</string>
				<string>ptm</string>
				<string>ptz</string>
				<string>mtm</string>
				<string>mtz</string>
				<string>669</string>
				<string>psm</string>
				<string>j2b</string>
				<string>dsm</string>
				<string>amf</string>
				<string>okt</string>
				<string>okta</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>DUMB Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>dts</string>
				<string>cpt</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>DTS Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>aac</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>AAC Audio</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleIconFile</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>${PRODUCT_NAME}</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSEnvironment</key>
	<dict>
		<key>PATH</key>
		<string>/usr/bin:/bin:/usr/sbin:/sbin:/usr/local/bin</string>
	</dict>
	<key>LSMinimumSystemVersion</key>
	<string>${MACOSX_DEPLOYMENT_TARGET}</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright © 2009-2014 Oleksiy Yakovenko and other contributors. All rights reserved.</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.data</string>
				<string>public.content</string>
			</array>
			<key>UTTypeIdentifier</key>
			<string>net.sourceforge.deadbeef.playlist</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>com.apple.ostype</key>
				<array>
					<string>DBPL</string>
				</array>
				<key>public.filename-extension</key>
				<array>
					<string>dbpl</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
