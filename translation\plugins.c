// external/ddb_output_pw/pw.c
// xgettext:no-c-format
_("PipeWire remote daemon name (empty for default)");
// xgettext:no-c-format
_("Custom properties (overrides existing ones):");
// xgettext:no-c-format
_("Use PipeWire volume control");
// plugins/adplug/plugin.c
// xgettext:no-c-format
_("Sample rate (real OPL is 49716Hz)");
// xgettext:no-c-format
_("Synth engine");
// xgettext:no-c-format
_("Nuked OPL3");
// xgettext:no-c-format
_("DOSBox OPL3 emulator");
// xgettext:no-c-format
_("<PERSON><PERSON><PERSON>'s OPL2 emulator");
// xgettext:no-c-format
_("<PERSON>'s OPL emulator");
// xgettext:no-c-format
_("<PERSON>'s OPL emulator");
// plugins/alsa/alsa.c
// xgettext:no-c-format
_("Use ALSA resampling");
// xgettext:no-c-format
_("Preferred buffer size");
// xgettext:no-c-format
_("Preferred period size");
// plugins/artwork/artwork.c
// xgettext:no-c-format
_("Refresh Cover Art");
// xgettext:no-c-format
_("Fetch from embedded tags");
// xgettext:no-c-format
_("Fetch from local folder");
// xgettext:no-c-format
_("Fetch from Last.fm");
// xgettext:no-c-format
_("Fetch from MusicBrainz");
// xgettext:no-c-format
_("Fetch from Albumart.org");
// xgettext:no-c-format
_("Fetch from World of Spectrum (AY files)");
// xgettext:no-c-format
_("Save downloaded files to music folders");
// xgettext:no-c-format
_("When no artwork is found");
// xgettext:no-c-format
_("leave blank");
// xgettext:no-c-format
_("use DeaDBeeF default cover");
// xgettext:no-c-format
_("display custom image");
// xgettext:no-c-format
_("Custom image path");
// xgettext:no-c-format
_("Save to file name");
// xgettext:no-c-format
_("Search masks (; separated)");
// xgettext:no-c-format
_("Search folders (; separated)");
// xgettext:no-c-format
_("Cache refresh (hrs)");
// xgettext:no-c-format
_("Simplified cache file names");
// xgettext:no-c-format
_("Image size");
// plugins/cdda/cdda.c
// xgettext:no-c-format
_("CD drive to load");
// xgettext:no-c-format
_("Audio CD Drive");
// xgettext:no-c-format
_("File");
// xgettext:no-c-format
_("Add Audio CD");
// xgettext:no-c-format
_("Use CDDB/GnuDb");
// xgettext:no-c-format
_("Prefer CD-Text over CDDB");
// xgettext:no-c-format
_("CDDB url (e.g. 'gnudb.gnudb.org')");
// xgettext:no-c-format
_("CDDB port number (e.g. '8880')");
// xgettext:no-c-format
_("Use CDDB protocol");
// xgettext:no-c-format
_("Enable NRG image support");
// xgettext:no-c-format
_("Drive speed for normal playback");
// xgettext:no-c-format
_("1x");
// xgettext:no-c-format
_("2x");
// xgettext:no-c-format
_("4x");
// xgettext:no-c-format
_("8x");
// xgettext:no-c-format
_("16x");
// xgettext:no-c-format
_("Max");
// xgettext:no-c-format
_("Use cdparanoia error correction when ripping");
// plugins/converter/convgui.c
// xgettext:no-c-format
_("Convert");
// plugins/dsp_libsrc/src.c
// xgettext:no-c-format
_("Autodetect samplerate from output device");
// xgettext:no-c-format
_("Set samplerate directly");
// xgettext:no-c-format
_("Quality / Algorithm");
// plugins/dumb/cdumb.c
// xgettext:no-c-format
_("Resampling quality");
// xgettext:no-c-format
_("Internal DUMB volume");
// xgettext:no-c-format
_("Volume ramping");
// xgettext:no-c-format
_("None");
// xgettext:no-c-format
_("On/Off Only");
// xgettext:no-c-format
_("Full");
// xgettext:no-c-format
_("8-bit output");
// plugins/ffmpeg/ffmpeg.c
// xgettext:no-c-format
_("Use all extensions supported by ffmpeg");
// xgettext:no-c-format
_("File Extensions (separate with ';')");
// xgettext:no-c-format
_("Enable DoP output");
// plugins/flac/flac.c
// xgettext:no-c-format
_("Ignore bad header errors");
// xgettext:no-c-format
_("Ignore unparsable stream errors");
// plugins/gme/cgme.c
// xgettext:no-c-format
_("Max song length (in minutes)");
// xgettext:no-c-format
_("Fadeout length (seconds)");
// xgettext:no-c-format
_("Play loops nr. of times (if available)");
// xgettext:no-c-format
_("ColecoVision BIOS (for SGC file format)");
// plugins/gtkui/gtkui.c
// xgettext:no-c-format
_("ReplayGain");
// xgettext:no-c-format
_("Remove ReplayGain Information");
// xgettext:no-c-format
_("Scan Selection As Albums (By Tags)");
// xgettext:no-c-format
_("Scan Selection As Single Album");
// xgettext:no-c-format
_("Scan Per-file Track Gain");
// xgettext:no-c-format
_("Scan Per-file Track Gain If Not Scanned");
// xgettext:no-c-format
_("Edit");
// xgettext:no-c-format
_("Deselect All");
// xgettext:no-c-format
_("Select All");
// xgettext:no-c-format
_("Quit");
// xgettext:no-c-format
_("Delete from Disk");
// xgettext:no-c-format
_("Add Location");
// xgettext:no-c-format
_("Add Folder(s)");
// xgettext:no-c-format
_("Add File(s)");
// xgettext:no-c-format
_("Open File(s)");
// xgettext:no-c-format
_("Track Properties");
// xgettext:no-c-format
_("Help");
// xgettext:no-c-format
_("Show Help Page");
// xgettext:no-c-format
_("Playback");
// xgettext:no-c-format
_("Cycle Repeat Mode");
// xgettext:no-c-format
_("Repeat - Off");
// xgettext:no-c-format
_("Repeat - Single Track");
// xgettext:no-c-format
_("Repeat - All");
// xgettext:no-c-format
_("Cycle Shuffle Mode");
// xgettext:no-c-format
_("Shuffle - Random Tracks");
// xgettext:no-c-format
_("Shuffle - Albums");
// xgettext:no-c-format
_("Shuffle - Tracks");
// xgettext:no-c-format
_("Shuffle - Off");
// xgettext:no-c-format
_("Toggle Cursor Follows Playback");
// xgettext:no-c-format
_("Toggle Scroll Follows Playback");
// xgettext:no-c-format
_("View");
// xgettext:no-c-format
_("Show\\/Hide menu");
// xgettext:no-c-format
_("Show\\/Hide statusbar");
// xgettext:no-c-format
_("Toggle Design Mode");
// xgettext:no-c-format
_("Preferences");
// xgettext:no-c-format
_("Sort Custom");
// xgettext:no-c-format
_("Crop Selected");
// xgettext:no-c-format
_("Remove Track(s) from Playlist");
// xgettext:no-c-format
_("Save Playlist");
// xgettext:no-c-format
_("Load Playlist");
// xgettext:no-c-format
_("Remove Current Playlist");
// xgettext:no-c-format
_("New Playlist");
// xgettext:no-c-format
_("Rename Current Playlist");
// xgettext:no-c-format
_("Show\\/Hide Equalizer");
// xgettext:no-c-format
_("Hide Equalizer");
// xgettext:no-c-format
_("Show Equalizer");
// xgettext:no-c-format
_("Show\\/Hide Player Window");
// xgettext:no-c-format
_("Hide Player Window");
// xgettext:no-c-format
_("Show Player Window");
// xgettext:no-c-format
_("Find");
// xgettext:no-c-format
_("Show\\/Hide Log window");
// xgettext:no-c-format
_("Undo");
// xgettext:no-c-format
_("Redo");
// xgettext:no-c-format
_("Ask confirmation to delete files from disk");
// xgettext:no-c-format
_("Status icon settings:");
// xgettext:no-c-format
_("Status icon volume control sensitivity");
// xgettext:no-c-format
_("Custom status icon");
// xgettext:no-c-format
_("Misc:");
// xgettext:no-c-format
_("Add separators between plugin context menu items");
// xgettext:no-c-format
_("Use unicode chars instead of images for track state");
// xgettext:no-c-format
_("Disable seekbar overlay text");
// plugins/hotkeys/hotkeys.c
// xgettext:no-c-format
_("Previous or Restart Current Track");
// xgettext:no-c-format
_("Duplicate Playlist");
// xgettext:no-c-format
_("Reload Metadata");
// xgettext:no-c-format
_("Jump to Currently Playing Track");
// xgettext:no-c-format
_("Skip to");
// xgettext:no-c-format
_("Previous Genre");
// xgettext:no-c-format
_("Previous Composer");
// xgettext:no-c-format
_("Previous Artist");
// xgettext:no-c-format
_("Previous Album");
// xgettext:no-c-format
_("Next Genre");
// xgettext:no-c-format
_("Next Composer");
// xgettext:no-c-format
_("Next Artist");
// xgettext:no-c-format
_("Next Album");
// xgettext:no-c-format
_("Next Playlist");
// xgettext:no-c-format
_("Previous Playlist");
// xgettext:no-c-format
_("Switch to Playlist 10");
// xgettext:no-c-format
_("Switch to Playlist 9");
// xgettext:no-c-format
_("Switch to Playlist 8");
// xgettext:no-c-format
_("Switch to Playlist 7");
// xgettext:no-c-format
_("Switch to Playlist 6");
// xgettext:no-c-format
_("Switch to Playlist 5");
// xgettext:no-c-format
_("Switch to Playlist 4");
// xgettext:no-c-format
_("Switch to Playlist 3");
// xgettext:no-c-format
_("Switch to Playlist 2");
// xgettext:no-c-format
_("Switch to Playlist 1");
// xgettext:no-c-format
_("Sort Randomize");
// xgettext:no-c-format
_("Sort by Date");
// xgettext:no-c-format
_("Sort by Artist");
// xgettext:no-c-format
_("Sort by Album");
// xgettext:no-c-format
_("Sort by Track Number");
// xgettext:no-c-format
_("Sort by Title");
// xgettext:no-c-format
_("Invert Selection");
// xgettext:no-c-format
_("Clear Playlist");
// xgettext:no-c-format
_("Remove from Playback Queue");
// xgettext:no-c-format
_("Add to Playback Queue");
// xgettext:no-c-format
_("Add to Front of Playback Queue");
// xgettext:no-c-format
_("Toggle in Playback Queue");
// xgettext:no-c-format
_("Move");
// xgettext:no-c-format
_("Move Tracks Down");
// xgettext:no-c-format
_("Move Tracks Up");
// xgettext:no-c-format
_("Toggle Mute");
// xgettext:no-c-format
_("Play");
// xgettext:no-c-format
_("Stop");
// xgettext:no-c-format
_("Previous");
// xgettext:no-c-format
_("Next");
// xgettext:no-c-format
_("Toggle Pause");
// xgettext:no-c-format
_("Play\\/Pause");
// xgettext:no-c-format
_("Play Next Album");
// xgettext:no-c-format
_("Play Previous Album");
// xgettext:no-c-format
_("Play Random Album");
// xgettext:no-c-format
_("Play Random");
// xgettext:no-c-format
_("Seek 1s Forward");
// xgettext:no-c-format
_("Seek 1s Backward");
// xgettext:no-c-format
_("Seek 5s Forward");
// xgettext:no-c-format
_("Seek 5s Backward");
// xgettext:no-c-format
_("Seek 1% Forward");
// xgettext:no-c-format
_("Seek 1% Backward");
// xgettext:no-c-format
_("Seek 5% Forward");
// xgettext:no-c-format
_("Seek 5% Backward");
// xgettext:no-c-format
_("Volume Up");
// xgettext:no-c-format
_("Volume Down");
// xgettext:no-c-format
_("Toggle Stop After Current Track");
// xgettext:no-c-format
_("Toggle Stop After Current Album");
// plugins/lastfm/lastfm.c
// xgettext:no-c-format
_("Lookup on Last.fm");
// xgettext:no-c-format
_("Enable scrobbler");
// xgettext:no-c-format
_("Disable nowplaying");
// xgettext:no-c-format
_("Username");
// xgettext:no-c-format
_("Password");
// xgettext:no-c-format
_("Scrobble URL");
// xgettext:no-c-format
_("Prefer Album Artist over Artist field");
// xgettext:no-c-format
_("Send MusicBrainz ID");
// xgettext:no-c-format
_("Submit tracks shorter than 30 seconds (not recommended)");
// xgettext:no-c-format
_("Enable logging");
// plugins/mono2stereo/mono2stereo.c
// xgettext:no-c-format
_("Left mix");
// xgettext:no-c-format
_("Right mix");
// plugins/mp3/mp3.c
// xgettext:no-c-format
_("Force 16 bit output");
// xgettext:no-c-format
_("Backend");
// plugins/notify/notify.c
// xgettext:no-c-format
_("Enable");
// xgettext:no-c-format
_("Notification title format");
// xgettext:no-c-format
_("Notification content format");
// xgettext:no-c-format
_("Show album art");
// xgettext:no-c-format
_("Album art size (px)");
// xgettext:no-c-format
_("Don't reuse notifications (KDE quirk)");
// plugins/oss/oss.c
// xgettext:no-c-format
_("Device file");
// plugins/pltbrowser/pltbrowser.c
// xgettext:no-c-format
_("Close playlists with middle mouse button");
// xgettext:no-c-format
_("Highlight current playlist");
// xgettext:no-c-format
_("Play on double-click");
// plugins/portaudio/portaudio.c
// xgettext:no-c-format
_("Buffer size (-1 to use optimal value chosen by portaudio)");
// xgettext:no-c-format
_("Device name encoding");
// xgettext:no-c-format
_("ASCII / UTF-8");
// xgettext:no-c-format
_("cp1250");
// xgettext:no-c-format
_("Defined below");
// xgettext:no-c-format
_("Custom device name encoding");
// plugins/pulse/pulse.c
// xgettext:no-c-format
_("PulseAudio server (leave empty for default)");
// plugins/sc68/in_sc68.c
// xgettext:no-c-format
_("Default song length (in minutes)");
// xgettext:no-c-format
_("Samplerate");
// xgettext:no-c-format
_("Skip when shorter than (sec)");
// plugins/shellexecui/shellexecui.c
// xgettext:no-c-format
_("Configure Custom Shell Commands");
// plugins/shn/shnplugin.c
// xgettext:no-c-format
_("Relative seek table path");
// xgettext:no-c-format
_("Absolute seek table path");
// xgettext:no-c-format
_("Swap audio bytes (toggle if all you hear is static)");
// plugins/sid/plugin.c
// xgettext:no-c-format
_("Enable HVSC Songlength DB");
// xgettext:no-c-format
_("Full path to Songlengths.md5/.txt");
// xgettext:no-c-format
_("Bits per sample");
// xgettext:no-c-format
_("Default song length (sec)");
// xgettext:no-c-format
_("Mono synth");
// plugins/supereq/supereq.c
// xgettext:no-c-format
_("Preamp");
// xgettext:no-c-format
_("1.2K");
// xgettext:no-c-format
_("1.8K");
// xgettext:no-c-format
_("2.5K");
// xgettext:no-c-format
_("3.5K");
// xgettext:no-c-format
_("5K");
// xgettext:no-c-format
_("7K");
// xgettext:no-c-format
_("10K");
// xgettext:no-c-format
_("14K");
// xgettext:no-c-format
_("20K");
// plugins/wildmidi/wildmidiplug.c
// xgettext:no-c-format
_("Timidity++ bank configuration file");
// src/scriptable/scriptable_encoder.c
// xgettext:no-c-format
_("File extension");
// xgettext:no-c-format
_("Encoder command line");
// xgettext:no-c-format
_("Data transfer method");
// xgettext:no-c-format
_("Pipe (stdin)");
// xgettext:no-c-format
_("Temporary file");
// xgettext:no-c-format
_("Source file");
// xgettext:no-c-format
_("ID3v2 version");
// xgettext:no-c-format
_("Write ID3v2 tag");
// xgettext:no-c-format
_("Write ID3v1 tag");
// xgettext:no-c-format
_("Write APEv2 tag");
// xgettext:no-c-format
_("Write FLAC tag");
// xgettext:no-c-format
_("Write OggVorbis tag");
// xgettext:no-c-format
_("Write MP4 tag");
