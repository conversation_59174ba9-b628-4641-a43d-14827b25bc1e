<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="512" height="512" viewBox="0, 0, 512, 512">
  <defs>
    <radialGradient id="Gradient_1" gradientUnits="userSpaceOnUse" cx="256" cy="128" r="256" gradientTransform="matrix(2, 0, 0, 1.5, -256, -64)">
      <stop offset="0" stop-color="#606060"/>
      <stop offset="1" stop-color="#202020"/>
    </radialGradient>
    <linearGradient id="Gradient_2" gradientUnits="userSpaceOnUse" x1="256" y1="0" x2="256" y2="512">
      <stop offset="0" stop-color="#FFFFFF" stop-opacity="0.5"/>
      <stop offset="1" stop-color="#FFFFFF" stop-opacity="0.251"/>
    </linearGradient>
    <radialGradient id="Gradient_3" gradientUnits="userSpaceOnUse" cx="256" cy="128" r="256" gradientTransform="matrix(2, 0, 0, 1.5, -256, -64)">
      <stop offset="0" stop-color="#404040"/>
      <stop offset="1" stop-color="#000000"/>
    </radialGradient>
    <radialGradient id="Gradient_4" gradientUnits="userSpaceOnUse" cx="256" cy="-42.667" r="256" gradientTransform="matrix(1.5, 0, 0, 1.5, -128, 64)">
      <stop offset="0" stop-color="#FFFFFF"/>
      <stop offset="1" stop-color="#FFFFFF" stop-opacity="0"/>
    </radialGradient>
    <clipPath id="Clip_1">
      <path d="M157,69 L451,69 L451,443 L157,443 z M184,96 L184,256 L274,196 L364,256 L184,376 L184,416 L424,256 L184,96 z"/>
    </clipPath>
    <filter id="Shadow_2">
      <feGaussianBlur in="SourceAlpha" stdDeviation="13"/>
      <feOffset dx="0" dy="0" result="offsetblur"/>
      <feFlood flood-color="#ABD582"/>
      <feComposite in2="offsetblur" operator="in"/>
    </filter>
  </defs>
  <g id="Ebene_1">
    <path d="M256,8 C244,8 220.066,18.903 193.594,37.531 C167.122,56.16 137.387,81.926 109.656,109.656 C81.926,137.387 56.16,167.122 37.531,193.594 C18.903,220.066 8,244 8,256 C8,268 18.903,291.934 37.531,318.406 C56.16,344.878 81.926,374.613 109.656,402.344 C137.387,430.074 167.122,455.84 193.594,474.469 C220.066,493.097 244,504 256,504 C268,504 291.934,493.097 318.406,474.469 C344.878,455.84 374.613,430.074 402.344,402.344 C430.074,374.613 455.84,344.878 474.469,318.406 C493.097,291.934 504,268 504,256 C504,244 493.097,220.066 474.469,193.594 C455.84,167.122 430.074,137.387 402.344,109.656 C374.613,81.926 344.878,56.16 318.406,37.531 C291.934,18.903 268,8 256,8 z" fill="url(#Gradient_1)" id="path2988"/>
    <path d="M256,8 C244,8 220.066,18.903 193.594,37.531 C167.122,56.16 137.387,81.926 109.656,109.656 C81.926,137.387 56.16,167.122 37.531,193.594 C18.903,220.066 8,244 8,256 C8,268 18.903,291.934 37.531,318.406 C56.16,344.878 81.926,374.613 109.656,402.344 C137.387,430.074 167.122,455.84 193.594,474.469 C220.066,493.097 244,504 256,504 C268,504 291.934,493.097 318.406,474.469 C344.878,455.84 374.613,430.074 402.344,402.344 C430.074,374.613 455.84,344.878 474.469,318.406 C493.097,291.934 504,268 504,256 C504,244 493.097,220.066 474.469,193.594 C455.841,167.122 430.074,137.387 402.344,109.656 C374.613,81.926 344.878,56.16 318.406,37.531 C291.934,18.903 268,8 256,8 z M256,23 C260.5,23 284.269,31.881 309.75,49.812 C335.231,67.744 364.524,93.024 391.75,120.25 C418.976,147.476 444.256,176.769 462.188,202.25 C480.119,227.731 489,251.5 489,256 C489,260.5 480.119,284.269 462.188,309.75 C444.256,335.231 418.976,364.524 391.75,391.75 C364.524,418.976 335.231,444.256 309.75,462.188 C284.269,480.119 260.5,489 256,489 C251.5,489 227.731,480.119 202.25,462.188 C176.769,444.256 147.476,418.976 120.25,391.75 C93.024,364.524 67.744,335.231 49.812,309.75 C31.881,284.269 23,260.5 23,256 C23,251.5 31.881,227.731 49.812,202.25 C67.744,176.769 93.024,147.476 120.25,120.25 C147.476,93.024 176.769,67.744 202.25,49.812 C227.731,31.881 251.5,23 256,23 z" fill="url(#Gradient_2)" id="path3826" opacity="0.98"/>
    <path d="M256,0 C192,0 0,192 0,256 C0,320 192,512 256,512 C320,512 512,320 512,256 C512,192 320,0 256,0 z M256,16 C264,16 287.838,25.837 313.781,44.094 C339.725,62.35 369.226,87.851 396.688,115.312 C424.149,142.774 449.65,172.275 467.906,198.219 C486.163,224.162 496,248 496,256 C496,264 486.163,287.838 467.906,313.781 C449.65,339.725 424.149,369.226 396.688,396.688 C369.226,424.149 339.725,449.65 313.781,467.906 C287.838,486.163 264,496 256,496 C248,496 224.162,486.163 198.219,467.906 C172.275,449.65 142.774,424.149 115.312,396.688 C87.851,369.226 62.35,339.725 44.094,313.781 C25.837,287.838 16,264 16,256 C16,248 25.837,224.162 44.094,198.219 C62.35,172.275 87.851,142.774 115.312,115.312 C142.774,87.851 172.275,62.35 198.219,44.094 C224.162,25.837 248,16 256,16 z" fill="url(#Gradient_3)" id="path3043"/>
    <path d="M256,0 C192,0 0,192 0,256 C64,256 128,128 256,128 C384,128 448,256 512,256 C512,192 320,0 256,0 z" fill="url(#Gradient_4)" id="path3875" opacity="0.25"/>
  </g>
  <g id="add">
    <g>
      <path d="M184,96 L184,136 L184,256 L274,196 L364,256 L184,376 L184,416 L424,256 L304,176 L184,96 z" fill="#ABD582"/>
      <g>
        <path d="M184,96 L424,256 L184,416 L184,376 L364,256 L274,196 L184,256 L184,96 z" fill="rgba(0,0,0,1)" clip-path="url(#Clip_1)" filter="url(#Shadow_2)"/>
        <path d="M180.394,101.408 L420.394,261.408 L420.394,250.592 L180.394,410.592 L190.5,416 L190.5,376 L187.606,381.408 L367.606,261.408 C371.465,258.835 371.465,253.165 367.606,250.592 L277.606,190.592 C275.422,189.136 272.578,189.136 270.394,190.592 L180.394,250.592 L190.5,256 L190.5,96 L180.394,101.408 z M187.606,90.592 C183.286,87.712 177.5,90.808 177.5,96 L177.5,256 C177.5,261.192 183.286,264.288 187.606,261.408 L277.606,201.408 L270.394,201.408 L360.394,261.408 L360.394,250.592 L180.394,370.592 C178.586,371.797 177.5,373.827 177.5,376 L177.5,416 C177.5,421.192 183.286,424.288 187.606,421.408 L427.606,261.408 C431.465,258.835 431.465,253.165 427.606,250.592 L187.606,90.592 z" fill="rgba(0,0,0,1)" clip-path="url(#Clip_1)" filter="url(#Shadow_2)"/>
      </g>
    </g>
    <g/>
  </g>
</svg>
