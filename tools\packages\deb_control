Package: deadbeef-static
Maintainer: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Depends: libasound2 (>> 1.0.16), libc6 (>= 2.7-1), lib<PERSON><PERSON>2 (>= 1.2.4), libgcc1 (>= 1:4.1.1), libglib2.0-0 (>= 2.16.0), libgtk2.0-0 (>= 2.12.0), libx11-6
Recommends: libpulse0, libdbus-1-3
Section: sound
Priority: optional
Homepage: http://deadbeef.sourceforge.net/
Description: The Ultimate Music Player
 DeaDBeeF (as in 0xDEADBEEF) is a cross-platform audio player
 .
 Features:
 .
  * mp3, ogg vorbis, flac, ape, wv, wav
  * ID3v1, ID3v2.2, ID3v2.3, ID3v2.4, APEv2, xing/info tags support
  * character set detection for non-unicode ID3 tags - supports cp1251
    and iso8859-1
  * unicode tags are fully supported as well (both utf8 and ucs2)
  * cuesheet support for mp3, ogg, flac, ape
  * autodetection of utf8/cp1251/iso8859-1 charsets in cuesheets
  * sid and some popular chiptune formats like nsf
  * tracker modules like mod, s3m, it, xm, etc
  * HVSC song length database support for sid
  * gtk2 interface with custom highly speed/memory optimized widgets
  * no GNOME or KDE dependencies - just gtk2 and several small libraries
  * minimize to tray, with scrollwheel volume control
  * drag and drop, both inside of playlist, and from filemanagers and such
  * control playback from command line (allows global hotkeys using xbindkeys)
  * seeking works in all supported formats
  * plugin support; ships with several standard plugins, such as global hotkeys
    and last.fm scrobbler; sdk is included
  * duration calculation is as precise as possible for vbr mp3
    files (with and without xing/info tags)
