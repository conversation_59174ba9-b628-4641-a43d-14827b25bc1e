/* XPM */
static char *dialog_xpm[] = {
/* columns rows colors chars-per-pixel */
"21 21 11 1",
"  c Gray0",
". c #00007b",
"X c #7b7b7b",
"o c Green",
"O c <PERSON>an",
"+ c <PERSON>",
"@ c Yellow",
"# c #d6d6d6",
"$ c #b3cece",
"% c Gray100",
"& c None",
/* pixels */
"&&&&&&&&&&&&&&&&&&&&&",
"&&&&&&&&&&&&&&&&&&&&&",
"&&&&&&&&&&&&&&&&&&&&&",
"&&&&&&&&&&&&&&&&&&&&&",
"&&XXXXXXXXXXXXXXXX&&&",
"&&X.+@............ &&",
"&&X.Oo.%%%....# #  &&",
"&&XXXXXXXXXXXXXXXX &&",
"&&X%%%%%%%%%%%%%%X &&",
"&&X%%%%%%%%%%%%%%X &&",
"&&X%%X%XX%XX%XX%%X &&",
"&&X%%%%%%%%%%%%%%X &&",
"&&X%%%%%%%%%%%%%%X &&",
"&&X%%%XXX%%XXX%%%X &&",
"&&X%%%X  %%X  %%%X &&",
"&&X%%%%%%%%%%%%%%X &&",
"&&XXXXXXXXXXXXXXXX &&",
"&&&                &&",
"&&&&&&&&&&&&&&&&&&&&&",
"&&&&&&&&&&&&&&&&&&&&&",
"&&&&&&&&&&&&&&&&&&&&&"
};
