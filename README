TABLE OF CONTENTS

1. Build dependencies
2. Runtime dependencies
3. Licensing

==========================================================

1. Build dependencies

Here's the bare minimum list of things which are expected to be installed to build Deadbeef.
Usually, you also need to install all runtime dependencies before building as well, see the next section for details.

* Clang: GCC is not supported, you need clang
* libdispatch
* libblockruntime
* Yasm: required to build assembly portions of ffap plugin on supported platforms (x86, x86_64)
* intltool: required for localization. If you don't need translations -- run ./configure --disable-nls (in this case you won't need to install intltool, as long as the configure script is already generated)

1.1 Compiling from git

If you're building from git, more things are needed:

* autoconf
* automake,
* libtool,
* autopoint

Run ./autogen.sh to generate configure script

1.2 Compiling

* Set CC and CXX environment variables to clang and clang++ respectively.
* Run `./configure --prefix=/usr`, and wait until it finishes. you may want to change prefix to another value. consult INSTALL file for more info. If you're new to the process, it may help to run `./configure --help`.
* Make sure all desired plugins have "Yes" status in the list that's printed by configure. If not -- install missing dependencies, and rerun configure. This is especially important for GTKUI, and ALSA or OSS plugins. Otherwise you won't get GUI and/or sound output.
* After satisfying all dependencies, run "make -j5" (change -j number to suit your number of CPUs/cores, e.g. 5 is quite good for single CPU dual-core machines).
* After the build finishes, run "make install" as root

2. Runtime dependencies

Many of the dependencies listed here are optional, which means deadbeef will build and run without them, but to make it useful - you'd probably need at least GTK UI plugin and some audio codecs built.

    libsamplerate: for dsp_libsrc plugin (resampler)
    gtk+-2.0 >= 2.16 (+ gthread, + glib): GTK+ 2.0 user interface
    gtk+-3.0 >= 3.10 (+ gthread, + glib): GTK+ 3.0 user interface
    jansson: mandatory for both gtk2 and gtk3 UIs, used for column configuration loading
    alsa-lib: ALSA support
    libvorbis and libogg: for ogg vorbis plugin, and for ogg container metadata in other formats, such as OggFlac
    libcurl >= 7.10: for last.fm, vfs_curl (shoutcast/icecast), artwork plugins
    imlib2: for artwork plugin; see libjpeg and libpng below
    libjpeg and libpng: for artwork plugin (when imlib2 is not installed, or --disable-artwork-imlib2 is used)
    libmad (optional): for mp3 plugin
    libmpg123 (optional): for mp3 plugin
        NOTE: the mp3 plugin has multiple backends since deadbeef 0.7, both libmad and libmpg123 are supported at the same time, and can be selected by the user from plugin configuration dialog. At least one of them is required to satisfy mp3 plugin dependencies. But having both is preferred. 
    libFLAC: for flac plugin
    wavpack: for wavpack plugin
    libsndfile: for sndfile plugin
    libcdio + libcddb: for cd audio plugin
    ffmpeg: for ffmpeg plugin
    xlib: for global hotkeys and gtkui
    dbus: for notification daemon support (OSD current song notifications)
    pulseaudio: for PulseAudio output plugin
    faad2: for AAC plugin
    zlib: for Audio Overload plugin (psf, psf2, etc), GME (for vgz)
    libzip: for vfs_zip plugin
    libdispatch
    libblockruntime

The actual package names for your Linux distribution may vary.

3. Licensing

DeaDBeeF core uses ZLIB license. See COPYING in each subdirectory for details.
