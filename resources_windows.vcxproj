﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="debug Windows|Win32">
      <Configuration>debug Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="release Windows|Win32">
      <Configuration>release Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{AA7791BA-168E-8F50-5F05-1B72CB5A60D8}</ProjectGuid>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>resources_windows</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <OutDir>bin\debug\plugins\</OutDir>
    <IntDir>obj\Windows\debug\resources_windows\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <OutDir>bin\release\plugins\</OutDir>
    <IntDir>obj\Windows\release\resources_windows\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="src\main.c">
      <FileType>Document</FileType>
      <Command Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">./scripts/windows_postbuild.sh bin/debug</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">./scripts/windows_postbuild.sh bin/release</Command>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">obj/Windows/debug/resources_windows/main.c_fake</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">obj/Windows/release/resources_windows/main.c_fake</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="libwin.vcxproj">
      <Project>{EAFA6F0B-D69C-9AE1-BF57-AE35AB982132}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>