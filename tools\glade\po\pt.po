# glade's Portuguese Translation
# Copyright (C) 2002 glade
# Distributed under the same license as the glade package
# <PERSON><PERSON> Lo<PERSON>o <<EMAIL>>, 2002, 2003
#
msgid ""
msgstr ""
"Project-Id-Version: 2.6\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2005-08-26 13:38+0200\n"
"PO-Revision-Date: 2003-11-30 21:15+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../glade-2.desktop.in.h:1
msgid "Design user interfaces"
msgstr "Desenhe interfaces de utilizador"

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Designer de Interfaces Glade"

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Opções de Grelha"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Espaçamento Horizontal:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Espaçamento Vertical:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Estilo Grelha:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Pontos"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Linhas"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Opções Anexação"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Anexação Horizontal:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Esquerda"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Direita"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Anexação Vertical:"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Topo"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Fundo"

#: ../glade/editor.c:741
#, fuzzy
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "Widgets GnomeDockItem apenas podem ser colados sobre um GnomeDock."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "Incapaz de inserir um widget GtkScrolledWindow."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "Incapaz de inserir um widget GtkViewport."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "Incapaz de adicionar um novo widget."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"Não pode adicionar um widget na posição selecionada.\n"
"\n"
"Dica: GTK+ utiliza contentores para dispor os widgets.\n"
"Tente apagar os widget existentes e utilizar\n"
"um contentor de caixa ou tabela.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "Incapaz de apagar widget."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "O widget não pode ser apagado"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"O widget é criado automaticamente como parte do widget pai, e não pode ser "
"apagado."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Espessura Margem:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "A espessura da margem à volta do contentor"

#: ../glade/gbwidget.c:1745
msgid "Select"
msgstr "Seleccionar"

#: ../glade/gbwidget.c:1767
msgid "Remove Scrolled Window"
msgstr "Remover Janela com Barras Rolamento"

#: ../glade/gbwidget.c:1776
msgid "Add Scrolled Window"
msgstr "Adicionar Janela com Barras Rolamento"

#: ../glade/gbwidget.c:1797
msgid "Remove Alignment"
msgstr "Remover Alinhamento"

#: ../glade/gbwidget.c:1805
msgid "Add Alignment"
msgstr "Adicionar Alinhamento"

#: ../glade/gbwidget.c:1820
msgid "Remove Event Box"
msgstr "Remover Caixa de Evento"

#: ../glade/gbwidget.c:1828
msgid "Add Event Box"
msgstr "Adicionar Caixa de Evento"

#: ../glade/gbwidget.c:1838
msgid "Redisplay"
msgstr "Mostrar novamente"

#: ../glade/gbwidget.c:1849
msgid "Cut"
msgstr "Cortar"

#: ../glade/gbwidget.c:1856 ../glade/property.c:892 ../glade/property.c:5135
msgid "Copy"
msgstr "Copiar"

#: ../glade/gbwidget.c:1865 ../glade/property.c:904
msgid "Paste"
msgstr "Colar"

#: ../glade/gbwidget.c:1877 ../glade/property.c:1580 ../glade/property.c:5126
msgid "Delete"
msgstr "Apagar"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2403 ../glade/gbwidget.c:2472
msgid "N/A"
msgstr "N/A"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3202
msgid "replacing child of container - not implemented yet\n"
msgstr "a substituir filho do contentor - ainda não implementado\n"

#: ../glade/gbwidget.c:3430
msgid "Couldn't insert GtkAlignment widget."
msgstr "Incapaz de inserir widget GtkAlignment."

#: ../glade/gbwidget.c:3470
msgid "Couldn't remove GtkAlignment widget."
msgstr "Incapaz de remover widget GtkAlignment."

#: ../glade/gbwidget.c:3494
msgid "Couldn't insert GtkEventBox widget."
msgstr "Incapaz de inserir widget GtkEventBox."

#: ../glade/gbwidget.c:3533
msgid "Couldn't remove GtkEventBox widget."
msgstr "Incapaz de remover widget GtkEventBox."

#: ../glade/gbwidget.c:3568
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "Incapaz de inserir widget GtkScrolledWindow."

#: ../glade/gbwidget.c:3607
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "Incapaz de remover widget GtkScrolledWindow."

#: ../glade/gbwidget.c:3721
msgid "Remove Label"
msgstr "Remover Etiqueta"

#: ../glade/gbwidgets/gbaboutdialog.c:78
#, fuzzy
msgid "Application Name"
msgstr "Barra de Aplicação Gnome"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Logo:"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "A imagem a utilizar como logotipo"

#: ../glade/gbwidgets/gbaboutdialog.c:104 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Nome Aplicação:"

#: ../glade/gbwidgets/gbaboutdialog.c:104
#, fuzzy
msgid "The name of the application"
msgstr "O nome do widget"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "Comentários:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
#, fuzzy
msgid "Additional information, such as a description of the application"
msgstr ""
"Informação adicional, tal como uma descrição do pacote e a sua página "
"principal na web"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Copyright:"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "O aviso de copyright"

#: ../glade/gbwidgets/gbaboutdialog.c:108
msgid "Website URL:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:108
#, fuzzy
msgid "The URL of the application's website"
msgstr "Se for ser construida uma aplciação Gnome"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "Website Label:"
msgstr "Etiqueta Menu:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "The label to display for the link to the website"
msgstr "A imagem a mostrar ao longo do topo da página"

#: ../glade/gbwidgets/gbaboutdialog.c:111 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Licença:"

#: ../glade/gbwidgets/gbaboutdialog.c:111
#, fuzzy
msgid "The license details of the application"
msgstr "O estilo de relevo do botão"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Autores:"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Os autores do pacote, um por linha"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Documentadores:"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Os documentadores do pacote, um por linha"

#: ../glade/gbwidgets/gbaboutdialog.c:115
msgid "Artists:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:115
#, fuzzy
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr "Os autores do pacote, um por linha"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Tradutores:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Os tradutores do módulo. Normalmente isto deverá estar vazio para que os "
"tradutores possam adicionar os seus nomes no ficheiro po"

#: ../glade/gbwidgets/gbaboutdialog.c:559
#, fuzzy
msgid "About Dialog"
msgstr "Diálogo Sobre do Gnome"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Etiqueta com Atalho"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:180 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "Alinh X:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "O alinhamento horizontal do widget filho"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:183 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Alinh Y:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "O alinhamento vertical do widget filho"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "Escala X:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "A escala horizontal do widget filho"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Escala Y:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "A escala vertical do widget filho"

#: ../glade/gbwidgets/gbalignment.c:85
msgid "Top Padding:"
msgstr "Espaçamento Topo:"

#: ../glade/gbwidgets/gbalignment.c:86
msgid "Space to put above the child widget"
msgstr "Espaço a colocar sobre o widget filho"

#: ../glade/gbwidgets/gbalignment.c:89
msgid "Bottom Padding:"
msgstr "Espaçamento Abaixo:"

#: ../glade/gbwidgets/gbalignment.c:90
msgid "Space to put below the child widget"
msgstr "Espaço a colocar sob o widget filho"

#: ../glade/gbwidgets/gbalignment.c:93
msgid "Left Padding:"
msgstr "Espaçamento Esquerda:"

#: ../glade/gbwidgets/gbalignment.c:94
msgid "Space to put to the left of the child widget"
msgstr "Espaço a colocar à esquerda do widget filho"

#: ../glade/gbwidgets/gbalignment.c:97
msgid "Right Padding:"
msgstr "Espaçamento Direita:"

#: ../glade/gbwidgets/gbalignment.c:98
msgid "Space to put to the right of the child widget"
msgstr "Espaço a colocar à direita do widget filho"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Alinhamento"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "Direcção:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "A direcção da seta"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Sombra:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "O tipo de sombra da seta"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "O alinhamento horizontal da seta"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "O alinhamento vertical da seta"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186
msgid "X Pad:"
msgstr "Esp X:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "O espaçamento horizontal"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188
msgid "Y Pad:"
msgstr "Esp Y:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "O espaçamento vertical"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Seta"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "Alinh X Etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "O alinhamento horizontal do widget de etiqueta da moldura"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Alinh Y Etiqueta:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "O alinhamento vertical do widget de etiqueta da moldura"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "O tipo de sombra da moldura"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "O alinhamento horizontal do filho da moldura"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Rácio:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "O rácio de aparência do filho da moldura"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Obedecer a Filho:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Se o rácio de aparência deverá ser determinado pelo filho"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Aspecto da Moldura"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Botão Normal:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "O botão normal a utilizar"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:747
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Etiqueta:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "O texto a mostrar"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:295
#: ../glade/glade_menu_editor.c:813
msgid "Icon:"
msgstr "Ícone:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "O ícone a mostrar"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Relevo de Botão:"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "O estilo de relevo do botão"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "ID resposta:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"O código de resposta devolvido quando o botão é primido. Seleccione uma das "
"respostas standard ou introduza um valor inteiro positivo"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr "Focus No Clique:"

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "If the button grabs focus when it is clicked"
msgstr "Se o botão obtém o focus quando é clicado"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "Remover Conteudo Botões"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Botão"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Cabeçalho:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "Se o mês e ano deverão ser mostrados no topo"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Nomes Dias:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Se os nomes de dias deverão ser mostrados"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Mês Fixo:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "Se o mês e dia não poderão ser modificados"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Números de Semana:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Se o número da semana deverá ser mostrado"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Segunda Primeiro:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "Se a semana deverá comçar à Segunda Feira"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Calendário"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Cor Fundo:"

#: ../glade/gbwidgets/gbcellview.c:64
#, fuzzy
msgid "The background color"
msgstr "Cor de fundo"

#: ../glade/gbwidgets/gbcellview.c:192
#, fuzzy
msgid "Cell View"
msgstr "Vista Texto"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Inicialmente Em:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Se o botão de visto está inicialmente activo"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Inconsistente:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Se o botão é mostrado num estado inconsistente"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "Indicador:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "Se o indicador é sempre desenhado"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Botão de Visto"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Se o item de menu de visto está inicialmente activo"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Item de Menu de Visto"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Nova lista em coluna"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Número de colunas:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:127 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Modo Seleccionado:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "O modo de selecção da lista em colunas"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Mostrar Títulos:"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Se os títulos de colunas são mostrados"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "O tipo de sombra da margem da lista em colunas"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Lista em Colunas"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Utilizar Alfa:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Se deverá ser utilizado o canal alfa"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:85
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:242
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Título:"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "O título do diálogo de selecção de cor"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
msgid "Pick a Color"
msgstr "Seleccione uma Cor"

#: ../glade/gbwidgets/gbcolorbutton.c:211
msgid "Color Chooser Button"
msgstr "Botão de Selecção Cor"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Controlo de Opacidade:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Se o controlo de opacidade é mostrado"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Paleta:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Se a paleta é mostrada"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Selecção de Cor"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:70
msgid "Select Color"
msgstr "Seleccionar Cor"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:315 ../glade/property.c:1275
msgid "Color Selection Dialog"
msgstr "Diálogo de Selecção de Cor"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Valor na Lista:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Se o valor tem de estar na lista"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "OK Se Vazio:"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr "Se é aceitável um valor vazio, quando 'Valor Na Lista' está definido"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "Sensível à Capitalização"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Se a procura é sesível à capitalização"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Utilizar Setas:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "Se as setas podem ser utilizadas para modificar o valor"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Usar Sempre:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "Se as setas funcionam mesmo se o valor não estiver na lista"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Itens:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Os itens na lista combo, uma por linha"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Caixa Combo"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
#, fuzzy
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Se o botão obtém o focus quando é clicado"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Tem Moldura:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr ""

#: ../glade/gbwidgets/gbcomboboxentry.c:302
msgid "Combo Box Entry"
msgstr "Entrada de Caixa Combo"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Nova árvore em coluna"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "O modo de selecção da árvore em coluna"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "O tipo de sombra da margem da árvore em coluna"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Árvore em Coluna"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:245
msgid "Type:"
msgstr "Tipo:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "O tipo da curva"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "Mín X:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "O valor horizontal mínimo"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "Máx X:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "O valor horizontal máximo"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Min Y:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "O valor vertical mínimo"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Máx Y:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "O valor vertical máximo"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Curva"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Função de Criação:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "A função que cria o widget"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Expressão1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "O primeiro parâmetro expressão a passar para a função"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Expressão2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "O segundo parâmetro expressão a passar para a função"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Int1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "O primeiro parâmetro inteiro a passar para a função"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Int2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "O segundo parâmetro inteiro a passar para a função"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Widget Personalizado"

#: ../glade/gbwidgets/gbdialog.c:292
msgid "New dialog"
msgstr "Novo diálogo"

#: ../glade/gbwidgets/gbdialog.c:304
msgid "Cancel, OK"
msgstr "Cancelar, OK"

#: ../glade/gbwidgets/gbdialog.c:313 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1316 ../glade/property.c:5156
msgid "OK"
msgstr "OK"

#: ../glade/gbwidgets/gbdialog.c:322
msgid "Cancel, Apply, OK"
msgstr "Cancelar, Aplicar, OK"

#: ../glade/gbwidgets/gbdialog.c:331
msgid "Close"
msgstr "Fechar"

#: ../glade/gbwidgets/gbdialog.c:340
msgid "_Standard Button Layout:"
msgstr "Disposição de Botões _Standard:"

#: ../glade/gbwidgets/gbdialog.c:349
msgid "_Number of Buttons:"
msgstr "_Número de Botões:"

#: ../glade/gbwidgets/gbdialog.c:366
msgid "Show Help Button"
msgstr "Mostrar Botão Ajuda"

#: ../glade/gbwidgets/gbdialog.c:397
msgid "Has Separator:"
msgstr "Tem Separadores:"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Se o diálogo tem um separador horizontal sobre os botões"

#: ../glade/gbwidgets/gbdialog.c:605
msgid "Dialog"
msgstr "Diálogo"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Área de Desenho"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Editável:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Se o texto pode ser editado"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Texto Visível:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"Se o texto introduzido pelo utilizador será mostrado. Quando desactivo, o "
"texto escrito é mostrado como caracteres asterisco, que é util para "
"introduzir senhas"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Tamanho Máx:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "O tamanho máximo do texto"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Texto:"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Se a entrada tem uma moldura à sua volta"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Caracter Invisível:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"O caracter a utilizar se o texto não deve ser visível, por ex. quando a "
"introduzir senhas"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "Activa Por Omissão:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr "Se o widget por omissão na janela é activado quando o Enter é primido"

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Largura em Caracteres:"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "O número de caracteres para os quais deixar espaço na entrada"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Entrada Texto"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "Visible Window:"
msgstr "Janela Visível:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr "Se a caixa de evento utiliza uma janela visível"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "Above Child:"
msgstr "Sobre Filho:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr "Se a janela da caixa de evento está sobre a janela do widget filho"

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Caixa Evento"

#: ../glade/gbwidgets/gbexpander.c:54
msgid "Initially Expanded:"
msgstr "Inicialmente Expandido:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr "Se o expansor está inicialmente aberto para revelar o widget filho"

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Espaçamento:"

#: ../glade/gbwidgets/gbexpander.c:58
msgid "Space to put between the label and the child"
msgstr "Espaço a colocar entre a etiqueta e o filho"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Adicionar Widget de Etiqueta"

#: ../glade/gbwidgets/gbexpander.c:228
msgid "Expander"
msgstr "Expansor"

#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#, fuzzy
msgid "The window title of the file chooser dialog"
msgstr "O título do diálogo de selecção de ficheiro"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:156
#: ../glade/gnome/gnomefileentry.c:109
msgid "Action:"
msgstr "Acção:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:157
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr "O tipo de operação de ficheiro a realizar"

#: ../glade/gbwidgets/gbfilechooserbutton.c:90
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
msgid "Local Only:"
msgstr "Apenas Local:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:160
msgid "Whether the selected files should be limited to local files"
msgstr ""
"Se os ficheiros seleccionados deverão ou não ser limitados a ficheiros locais"

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
msgid "Show Hidden:"
msgstr "Mostrar Escondidos:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether the hidden files and folders should be displayed"
msgstr "Se apresentar ou não ficheiros e pastas escondidas"

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gblabel.c:200
#, fuzzy
msgid "Width in Chars:"
msgstr "Largura em Caracteres:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#, fuzzy
msgid "The width of the button in characters"
msgstr "A largura da área de disposição"

#: ../glade/gbwidgets/gbfilechooserbutton.c:283
#, fuzzy
msgid "File Chooser Button"
msgstr "Botão de Selecção de Fonte"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
msgid "Select Multiple:"
msgstr "Seleccionar Múltiplos:"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether to allow multiple files to be selected"
msgstr "Se permitir ou não seleccionar múltiplos ficheiros"

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
msgid "File Chooser"
msgstr "Selecção de Ficheiros"

#: ../glade/gbwidgets/gbfilechooserdialog.c:421
msgid "File Chooser Dialog"
msgstr "Diálogo de Selecção de Ficheiros"

#: ../glade/gbwidgets/gbfileselection.c:71 ../glade/property.c:1365
msgid "Select File"
msgstr "Seleccionar Ficheiro"

#: ../glade/gbwidgets/gbfileselection.c:113
msgid "File Ops.:"
msgstr "Oper. Ficheiro:"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "If the file operation buttons are shown"
msgstr "Se os botões de operações de ficheiro são mostrados"

#: ../glade/gbwidgets/gbfileselection.c:292
msgid "File Selection Dialog"
msgstr "Diálogo de Selecção de Ficheiros"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "A coordenada X do widget na GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "A coordenada Y do widget na GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Posições Fixas"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "O título do diálogo de selcção de fonte"

#: ../glade/gbwidgets/gbfontbutton.c:70
msgid "Show Style:"
msgstr "Mostrar Estilo:"

#: ../glade/gbwidgets/gbfontbutton.c:71
msgid "If the font style is shown as part of the font information"
msgstr "Se o estilo da fonte é mostrado como parte da informação de fonte"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Mostrar Tamanho:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "Se o tamanho da fonte é mostrado como parte da informação de fonte"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Utilizar Fonte:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr ""
"Se a fonte seleccionada é utilizada ao apresentar a informação de fonte"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Utilizar Tamanho:"

#: ../glade/gbwidgets/gbfontbutton.c:77
msgid "if the selected font size is used when displaying the font information"
msgstr ""
"Se o tamanho de fonte seleccionado é utilizado ao apresentar a informação de "
"fonte"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Seleccione uma Fonte"

#: ../glade/gbwidgets/gbfontbutton.c:268
msgid "Font Chooser Button"
msgstr "Botão de Selecção de Fonte"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Antever Texto:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "O texto de antevisão a mostrar"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Selecção de Fonte"

#: ../glade/gbwidgets/gbfontselectiondialog.c:69
msgid "Select Font"
msgstr "Seleccione Fonte"

#: ../glade/gbwidgets/gbfontselectiondialog.c:300
msgid "Font Selection Dialog"
msgstr "Diálogo de Selecção de Fonte"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Moldura"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Tipo Inicial:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "O tipo inicial da curva"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Curva Gama"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "O tipo de sombra à volta da caixa de gestão"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "Pos Gestor:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "A posição do gestor"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Anexar Limite:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "O limite da caixa de gestão que se anexa à posição"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "Caixa Gestão"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Nova caixa horizontal"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Tamanho:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "O número de widgets na caixa"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Homogéneo:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Se os filhos deverão ser do mesmo tamanho"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "O espaçamento entre cada filho"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "Incapaz de apagar qualquer filho."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:254
msgid "Position:"
msgstr "Posição:"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "A posição do widget relativamente aos seus irmãos"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Espaçamento:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "O espaçamento do widget"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Expandir:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "Defina Verdade para permitir que o widget se expanda"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Enchimento:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "Defina Verdade para permitir que o widget encha a área alocada"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Início do Pacote:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "Defina Verdade para empacotar o widget no início da caixa"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Inserir Antes"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Inserir Depois"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Caixa Horizontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Nova caixa de botão horizontal"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "O número de botões"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Disposição:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "O estilo de disposição dos botões"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "O espaço entre botões"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Caixa de Botões Horizontal"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "A posição do divisor"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:283
msgid "Shrink:"
msgstr "Encolher:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "Definir Verdade para permitir que o widget encolha"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Redimensionar:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "Definir Verdade para permitir que o widget seja redimensionado"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Paineis Horizontais"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Métrica:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "As unidades da régua"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Valor Inferior:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "O valor inferior da régua"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Valor Superior:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "O valor superior da régua"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "A posição actual na régua"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4827
msgid "Max:"
msgstr "Máx:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "O valor máximo da régua"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Régua Horizontal"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Mostrar Valor:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Se a escala do valor é mostrada"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Dígitos:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "O número de dígitos a mostrar"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Pos Valor:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "A posição do valor"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Política:"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "A política de actualização da escala"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Invertida:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Se a lista de valores está invertida"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Escala Horizontal"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "A política de actualização da barra de rolamento"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Barra de Rolamento Horizontal"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Separador Horizontal"

#: ../glade/gbwidgets/gbiconview.c:106
#, fuzzy, c-format
msgid "Icon %i"
msgstr "Lista Ícones"

#: ../glade/gbwidgets/gbiconview.c:128
#, fuzzy
msgid "The selection mode of the icon view"
msgstr "O modo de selecção da árvore em coluna"

#: ../glade/gbwidgets/gbiconview.c:130 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "Orientação:"

#: ../glade/gbwidgets/gbiconview.c:131
#, fuzzy
msgid "The orientation of the icons"
msgstr "A orientação do conteudo da barra de progresso"

#: ../glade/gbwidgets/gbiconview.c:287
#, fuzzy
msgid "Icon View"
msgstr "Tamanho Ícone:"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:299
#, fuzzy
msgid "Named Icon:"
msgstr "Ícone:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:300
#, fuzzy
msgid "The named icon to use"
msgstr "O item Gnome normal a utilizar."

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Tamanho Ícone:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "O tamanho de ícone de item normal"

#: ../glade/gbwidgets/gbimage.c:115
#, fuzzy
msgid "Pixel Size:"
msgstr "Tamanho Página:"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "O alinhamento horizontal"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "O alinhamento vertical"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Imagem"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "Item de menu normal inválido"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Item de menu com imagem"

#: ../glade/gbwidgets/gbinputdialog.c:256
msgid "Input Dialog"
msgstr "Diálogo de Entrada"

#: ../glade/gbwidgets/gblabel.c:169
msgid "Use Underline:"
msgstr "Utilizar Sublinhado:"

#: ../glade/gbwidgets/gblabel.c:170
msgid "If the text includes an underlined access key"
msgstr "Se o texto inclui um caracter de atalho sublinhado"

#: ../glade/gbwidgets/gblabel.c:171
msgid "Use Markup:"
msgstr "Utilizar Markup:"

#: ../glade/gbwidgets/gblabel.c:172
msgid "If the text includes pango markup"
msgstr "Se o tetxo inclui markup pango"

#: ../glade/gbwidgets/gblabel.c:173
msgid "Justify:"
msgstr "Justificar:"

#: ../glade/gbwidgets/gblabel.c:174
msgid "The justification of the lines of the label"
msgstr "O alinhamento das linhas da etiqueta"

#: ../glade/gbwidgets/gblabel.c:176
msgid "Wrap Text:"
msgstr "Quebrar Texto:"

#: ../glade/gbwidgets/gblabel.c:177
msgid "If the text is wrapped to fit within the width of the label"
msgstr "Se o texto é quebrado para caber dentro da largura da etiqueta"

#: ../glade/gbwidgets/gblabel.c:178
msgid "Selectable:"
msgstr "Seleccionável:"

#: ../glade/gbwidgets/gblabel.c:179
msgid "If the label text can be selected with the mouse"
msgstr "Se o texto da etiqueta pode ser seleccionado com o rato"

#: ../glade/gbwidgets/gblabel.c:181
msgid "The horizontal alignment of the entire label"
msgstr "O alinhamento horizontal de toda a etiqueta"

#: ../glade/gbwidgets/gblabel.c:184
msgid "The vertical alignment of the entire label"
msgstr "O alinhamento vertical de toda a etiqueta"

#: ../glade/gbwidgets/gblabel.c:190
msgid "Focus Target:"
msgstr "Alvo do Focus:"

#: ../glade/gbwidgets/gblabel.c:191
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"O widget para onde apontar o focus de teclado quando a tecla de atalho "
"sublinhada é utilizada"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:197 ../glade/gbwidgets/gbprogressbar.c:146
#, fuzzy
msgid "Ellipsize:"
msgstr "Exclusivo:"

#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:201
#, fuzzy
msgid "The width of the label in characters"
msgstr "A largura da área de disposição"

#: ../glade/gbwidgets/gblabel.c:203
#, fuzzy
msgid "Single Line Mode:"
msgstr "Modo Seleccionado:"

#: ../glade/gbwidgets/gblabel.c:204
msgid "If the label is only given enough height for a single line"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:205
msgid "Angle:"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:206
#, fuzzy
msgid "The angle of the label text"
msgstr "A quebra do texto"

#: ../glade/gbwidgets/gblabel.c:332 ../glade/gbwidgets/gblabel.c:347
#: ../glade/gbwidgets/gblabel.c:614
msgid "Auto"
msgstr "Auto"

#: ../glade/gbwidgets/gblabel.c:870 ../glade/glade_menu_editor.c:410
msgid "Label"
msgstr "Etiqueta"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Largura Área:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "A largura da área de disposição"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Altura Área:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "A altura da área de disposição"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "A coordenada X do widget no GtkLayout"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "A coordenada Y do widget no GtkLayout"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Disposição"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "O modo de selecção da lista"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Lista"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Item Lista"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Menu Popup"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:190
msgid "_File"
msgstr "_Ficheiro"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:198 ../glade/glade_project_window.c:691
msgid "_Edit"
msgstr "_Editar"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:204 ../glade/glade_project_window.c:720
msgid "_View"
msgstr "_Ver"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:206 ../glade/glade_project_window.c:833
msgid "_Help"
msgstr "_Ajuda"

#: ../glade/gbwidgets/gbmenubar.c:207
msgid "_About"
msgstr "_Sobre"

#: ../glade/gbwidgets/gbmenubar.c:268 ../glade/gbwidgets/gbmenubar.c:346
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Editar Menus..."

#: ../glade/gbwidgets/gbmenubar.c:442
msgid "Menu Bar"
msgstr "Barra Menu"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Item Menu"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
#, fuzzy
msgid "Show Horizontal:"
msgstr "Nunca Horizontal:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
#, fuzzy
msgid "If the item is visible when the toolbar is horizontal"
msgstr "Se nunca é permitido ao item de anexar ser horizontal"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
#, fuzzy
msgid "Show Vertical:"
msgstr "Mostrar Valor:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
#, fuzzy
msgid "If the item is visible when the toolbar is vertical"
msgstr "Se nunca é permitido ao item de anexar ser vertical"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
#, fuzzy
msgid "Toolbar Button with Menu"
msgstr "Botão de Alternar"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Novo livro"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:124
msgid "Number of pages:"
msgstr "Número de páginas:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "Mostrar Separadores:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Se os separadores do livro são mostrados"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Mostrar Margem:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "Se a margem do livro é mostrada, quando os separadores não o são"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Pos Sep:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "A posição dos separadores do livro"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Com Rolamento:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Se os separadores do livro podem ter barras de rolamento"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Margem Hor Sep:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "O tamanho da margem horizontal dos separadores"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Margem Vert Sep:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "O tamanho da margem vertical dos separadores"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Mostrar Contexto:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "Se o menu de contexto está activo"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Número de Páginas:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "O número de páginas do livro"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Página Anterior"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "Página Seguinte"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Apagar Página"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "Trocar Seguinte"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Trocar Anterior"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Inserir Página Após"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Inserir Página Antes"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "A posição da página na lista de páginas"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Defina Verdade para permitir que a pasta se expanda"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr "Defina Verdade para permitir que a pasta encha a área alocada"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr "Defina Verdade para empacotar a pasta no início da caixa"

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Etiqueta Menu:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "O texto a mostrar no menu de contexto"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Livro"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "Incapaz de adicionar um %s ao GtkOptionMenu."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Menu Opções"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Cor:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "Se a antevisão é a cores ou cinzas"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "Se a antevisão se expande para encher a área alocada"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Antever"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "A orientação do conteudo da barra de progresso"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Fracção:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "A fracção de trabalho que foi completada"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Tamanho Incremento:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""
"A fracção do tamanho da barra de progresso a incrementar quando invocado"

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "O texto a mostrar sobre a barra de progresso"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Mostrar Texto:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "Se o texto deverá ser mostrado na barra de progresso"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Modo de Actividade:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "Se a barra de progresso deverá agir como a frente do carro Kit"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "O alinhamento horizontal do texto"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "O alinhamento vertical do texto"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "Barra Progresso"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "Se o botão de rádio está inicialmente activo"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1038
msgid "Group:"
msgstr "Grupo:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr ""
"O grupo de botões de rádio (por omissão todos os botões de rádio com o mesmo "
"pai)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Novo Grupo"

#: ../glade/gbwidgets/gbradiobutton.c:463
msgid "Radio Button"
msgstr "Botão de Rádio"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "Se o item de menu rádio está inicialmente activo"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"O grupo de item de menu de rádio (por omissão todos os itens de menu de "
"rádio com o mesmo pai)"

#: ../glade/gbwidgets/gbradiomenuitem.c:386
msgid "Radio Menu Item"
msgstr "Item de Menu de Rádio"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
#, fuzzy
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr ""
"O grupo de botões de rádio (por omissão todos os botões de rádio com o mesmo "
"pai)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:528
#, fuzzy
msgid "Toolbar Radio Button"
msgstr "Botão de Rádio"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "Política H:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Quando deverá ser mostrada a barra de rolamento horizontal"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "Política V:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Quando deverá ser mostrada a barra de rolamento vertical"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Pos Janela:"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr "Onde se localiza a janela filha relativamente às barras de rolamento"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Tipo Sombra:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "A política de actualização da barra de rolamento vertical"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Janela com Rolamento"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Separador para Menus"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
#, fuzzy
msgid "Toolbar Separator Item"
msgstr "Separador Horizontal"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Rácio de Subida:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr ""
"O rácio de subida do botão de rodar, utilizado conjuntamente com o "
"Incremento de Página"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "O número de dígitos decimais a mostrar"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Numérico:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr "Se apenas for permitida introdução de numéricos"

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Política de Actualização:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Quando são emitidos os sinais value_changed"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Ajustar:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "Se o valor é ajustado para múltiplos do incremento de passo"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Ciclar:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "Se o valor dá a volta nos limites"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Botão de Rodar"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Marca Redimensionamento:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr ""
"Se a barra da estados tem uma marca de redimensionamento para redimensionar "
"janela"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Barra de Estados"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Nova tabela"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Número de linhas:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Linhas:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "O número de linhas na tabela"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Colunas:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "O número de colunas na tabela"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Se os filhos deverão ser todos do mesmo tamanho"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Espaç Linha:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "O espaçamento entre cada linha"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Espaç Coluna"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "O espaçamento entre cada coluna"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Célula X:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "O limite esquerdo do widget na tabela"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Célula Y:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "O limite superior do widget na tabela"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Abrang Colunas:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "O número de colunas abrangidas pelo widget na tabela"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Abrang Linhas:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "O número de linhas abrangidas pelo widget na tabela"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "Espaçamento H:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "Espaçamento V:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "Expandir X:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "Definir Verdade para permitir expanção horizontal do widget"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Expandir Y:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "Definir Verdade para permitir expanção vertical do widget"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "Encolher X:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "Definir Verdade para permitir que widget encolha horizontalmente"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Encolher Y:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "Definir Verdade para permitir que widget encolha verticalmente"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "Encher X:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr ""
"Definir Verdade para permitir que widget encha a área alocada horizontal"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Encher Y:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "Definir Verdade para permitir que widget encha a área alocada vertical"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Inserir Linha Antes"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Inserir Linha Depois"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Inserir Coluna Antes"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Inserir Coluna Depois"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Apagar Linha"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Apagar Coluna"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Tabela"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Centrar"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Enchimento"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:542 ../glade/glade_menu_editor.c:829
#: ../glade/glade_menu_editor.c:1344 ../glade/glade_menu_editor.c:2251
#: ../glade/property.c:2431
msgid "None"
msgstr "Nenhum"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Caracter"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Palavra"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Cursor Visível:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Se o cursor é visível"

#: ../glade/gbwidgets/gbtextview.c:119
msgid "Overwrite:"
msgstr "Sobepor:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr "Se o texto introduzido sobrepõe o texto existente"

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr "Aceita Tab:"

#: ../glade/gbwidgets/gbtextview.c:122
msgid "If tab characters can be entered"
msgstr "Se podem ser introduzidos caracteres tab"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Alinhamento:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "O alinhamento do texto"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Quebra:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "A quebra do texto"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Espaço Acima:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Pixels de espaço em branco acima dos parágrafos"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Espaço Abaixo:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Pixels de espaço em branco abaixo dos parágrafos"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Espaço Dentro:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Pixels de espaço em branco entre linhas quebradas num parágrafo"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Margem Esquerda:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Largura da margem esquerda em pixels"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Margem Direita:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Largura da margem direita em pixels"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Indentação:"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Quantidade de pixels de indentação de parágrados"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Vista Texto"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "Se o botão de alternar está inicialmente activo"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Botão de Alternar"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
#, fuzzy
msgid "Toolbar Toggle Button"
msgstr "Botão de Alternar"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Nova barra ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Número de itens:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "O número de itens na barra ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "A orientação da barra ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Estilo:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "O estilo da barra ferramentas"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Dicas:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Se as dicas estão activas"

#: ../glade/gbwidgets/gbtoolbar.c:277
#, fuzzy
msgid "Show Arrow:"
msgstr "Mostrar Margem:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""

#: ../glade/gbwidgets/gbtoolbar.c:427
#, fuzzy
msgid "If the item should be the same size as other homogeneous items"
msgstr "Se os filhos deverão ser do mesmo tamanho"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Inserir Item Antes"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Inserir Item Depois"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Barra Ferramentas"

#: ../glade/gbwidgets/gbtoolbutton.c:586
#, fuzzy
msgid "Toolbar Button"
msgstr "Botão de Alternar"

#: ../glade/gbwidgets/gbtoolitem.c:201
#, fuzzy
msgid "Toolbar Item"
msgstr "Barra Ferramentas"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Coluna 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Coluna 2"

#: ../glade/gbwidgets/gbtreeview.c:87
#, fuzzy
msgid "Column 3"
msgstr "Coluna 1"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Cabeçalhos Visíveis:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Se os botões dos cabeçalhos das colunas são mostrados"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Dica de Regras:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""
"Se uma dica é definida tal que o motor de temas desenhe as linhas em cores "
"alternadas"

#: ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Reordenável:"

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Se a vista é reordenável"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Activar Procura:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr "Se o utilizador pode procurar nas colunas interactivamente"

#: ../glade/gbwidgets/gbtreeview.c:123
#, fuzzy
msgid "Fixed Height Mode:"
msgstr "Altura Escala:"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:125
#, fuzzy
msgid "Hover Selection:"
msgstr "Selecção de Cor"

#: ../glade/gbwidgets/gbtreeview.c:126
#, fuzzy
msgid "Whether the selection should follow the pointer"
msgstr ""
"Se os ficheiros seleccionados deverão ou não ser limitados a ficheiros locais"

#: ../glade/gbwidgets/gbtreeview.c:127
#, fuzzy
msgid "Hover Expand:"
msgstr "Expandir X:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Lista ou Vista em Árvore"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Nova caixa vertical"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Caixa Vertical"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Nova caixa de botões vertical"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Caixa de Botões Vertical"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "O tipo de sombra do visualizador"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Visualizador"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Paineis Verticais"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Régua Vertical"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Escala Vertical"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Barra de Rolamento Vertical"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Separador Vertical"

#: ../glade/gbwidgets/gbwindow.c:242
msgid "The title of the window"
msgstr "O título da janela"

#: ../glade/gbwidgets/gbwindow.c:245
msgid "The type of the window"
msgstr "O tipo de janela"

#: ../glade/gbwidgets/gbwindow.c:249
msgid "Type Hint:"
msgstr "Dica de Tipo:"

#: ../glade/gbwidgets/gbwindow.c:250
msgid "Tells the window manager how to treat the window"
msgstr "Indica ao gestor de janelas como tratar a janela"

#: ../glade/gbwidgets/gbwindow.c:255
msgid "The initial position of the window"
msgstr "A posição inicial da janela"

#: ../glade/gbwidgets/gbwindow.c:259 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Modal:"

#: ../glade/gbwidgets/gbwindow.c:259
msgid "If the window is modal"
msgstr "Se a janela for modal"

#: ../glade/gbwidgets/gbwindow.c:264
msgid "Default Width:"
msgstr "Largura Por Omissão:"

#: ../glade/gbwidgets/gbwindow.c:265
msgid "The default width of the window"
msgstr "A largura por omissão da janela"

#: ../glade/gbwidgets/gbwindow.c:269
msgid "Default Height:"
msgstr "Altura Por Omissão:"

#: ../glade/gbwidgets/gbwindow.c:270
msgid "The default height of the window"
msgstr "A altura por omissão da janela"

#: ../glade/gbwidgets/gbwindow.c:276
msgid "Resizable:"
msgstr "Redimensionável:"

#: ../glade/gbwidgets/gbwindow.c:277
msgid "If the window can be resized"
msgstr "Se a janela pode ser redimensionada"

#: ../glade/gbwidgets/gbwindow.c:284
msgid "If the window can be shrunk"
msgstr "Se a janela pode ser encolhida"

#: ../glade/gbwidgets/gbwindow.c:285
msgid "Grow:"
msgstr "Crescer:"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be enlarged"
msgstr "Se a janela pode ser aumentada"

#: ../glade/gbwidgets/gbwindow.c:291
msgid "Auto-Destroy:"
msgstr "Auto-destruição:"

#: ../glade/gbwidgets/gbwindow.c:292
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Se a janela é destruida quando o seu pai transiente é destruido"

#: ../glade/gbwidgets/gbwindow.c:296
msgid "The icon for this window"
msgstr "O ícone desta janela"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "Role:"
msgstr "Papel:"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "A unique identifier for the window to be used when restoring a session"
msgstr "Um identificador único para a janela a ser utilizado ao repor a sessão"

#: ../glade/gbwidgets/gbwindow.c:306
msgid "Decorated:"
msgstr "Decorada:"

#: ../glade/gbwidgets/gbwindow.c:307
msgid "If the window should be decorated by the window manager"
msgstr "Se a janela deverá ser decorada pelo gestor de janelas"

#: ../glade/gbwidgets/gbwindow.c:310
msgid "Skip Taskbar:"
msgstr "Ignorar Barra de Tarefas:"

#: ../glade/gbwidgets/gbwindow.c:311
msgid "If the window should not appear in the task bar"
msgstr "Se a janela não deverá aparecer na barra de tarefas"

#: ../glade/gbwidgets/gbwindow.c:314
msgid "Skip Pager:"
msgstr "Ignorar Paginador"

#: ../glade/gbwidgets/gbwindow.c:315
msgid "If the window should not appear in the pager"
msgstr "Se a janela não deverá aparecer no paginador"

#: ../glade/gbwidgets/gbwindow.c:318
msgid "Gravity:"
msgstr "Gravidade:"

#: ../glade/gbwidgets/gbwindow.c:319
msgid "The reference point to use when the window coordinates are set"
msgstr ""
"O ponto de referência a utilizar aquando da definição das coordenadas da "
"janela"

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "Focus On Map:"
msgstr "Focus No Clique:"

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "If the window should receive the input focus when it is mapped"
msgstr "Se a janela deverá ser decorada pelo gestor de janelas"

#: ../glade/gbwidgets/gbwindow.c:1198
msgid "Window"
msgstr "Janela"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:74
msgid "Error"
msgstr "Erro"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Erro de Sistema"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "Erro ao abrir ficheiro"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "Erro ao ler ficheiro"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "Erro ao escrever em ficheiro"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "Directório inválido"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "Valor inválido"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "Entidade XML inválida"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "Etiqueta inicial esperada"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "Esperada etiqueta final"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "Dados de caracter esperados"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "Falta ID de classe"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Classe desconhecida"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "Componente inválido"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "Final de ficheiro inesperado"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Código de erro desconhecido"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Controlado Por"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Controlador Para"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Etiqueta Para"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Etiquetado Por"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Membro De"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Nó Filho De"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr ""

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr ""

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr ""

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr ""

#: ../glade/glade_atk.c:130
#, fuzzy
msgid "Embedded By"
msgstr "Etiquetado Por"

#: ../glade/glade_atk.c:131
#, fuzzy
msgid "Popup For"
msgstr "Menu Popup"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr ""

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Relação: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "Widget"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:772
#: ../glade/property.c:776
msgid "Name:"
msgstr "Nome:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr "O nome do widget a passar a tecnologias assistentes"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "Descrição:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr "A descrição do widget a passara a tecnologias assistentes"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Título da Tabela:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "O título da tabela a passar a tecnologias assistentes"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Seleccione os widgets com este relacionamento"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Clicar"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Primir"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Largar"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr "Introduza a descrição da acção a passar a tecnologias assistentes"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Área de Transferência"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Tem de seleccionar um widget onde colar"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "Não pode colar em janelas ou diálogos."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"Não pode colar dentro do widget seleccionado, já\n"
"que é criado automaticamente pelo seu pai."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr "Apenas itens de menu podem ser colados num menu ou barra de menu."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr "Apenas botões podem ser colados numa área de acção de diálogo."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "Apenas widgets GnomeDockItem podem ser colados numa GnomeDock."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "Apenas widgets GnomeDockItem podem ser colados sobre um GnomeDockItem."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "Desculpe - colar sobre um GnomeDockItem ainda não está implementado."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "Widgets GnomeDockItem apenas podem ser colados sobre um GnomeDock."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:632
msgid "_New"
msgstr "_Novo"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Criar novo ficheiro"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "O_bsoleto"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "_Básico GTK+"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "_Adicional GTK+"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Seleccionar Teclas Atalho"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Teclas"

#: ../glade/glade_menu_editor.c:394
msgid "Menu Editor"
msgstr "Editor Menu"

#: ../glade/glade_menu_editor.c:411
msgid "Type"
msgstr "Tipo"

#: ../glade/glade_menu_editor.c:412
msgid "Accelerator"
msgstr "Atalho"

#: ../glade/glade_menu_editor.c:413
msgid "Name"
msgstr "Nome"

#: ../glade/glade_menu_editor.c:414 ../glade/property.c:1498
msgid "Handler"
msgstr "Handler"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:102
msgid "Active"
msgstr "Activo"

#: ../glade/glade_menu_editor.c:416
msgid "Group"
msgstr "Grupo"

#: ../glade/glade_menu_editor.c:417
msgid "Icon"
msgstr "Ícone"

#: ../glade/glade_menu_editor.c:458
msgid "Move the item and its children up one place in the list"
msgstr "Mover o item e seus filhos uma posição acima na lista"

#: ../glade/glade_menu_editor.c:470
msgid "Move the item and its children down one place in the list"
msgstr "Mover o item e seus filhos uma posição abaixo na lista"

#: ../glade/glade_menu_editor.c:482
msgid "Move the item and its children up one level"
msgstr "Mover o item e seus filhos uma posição acima"

#: ../glade/glade_menu_editor.c:494
msgid "Move the item and its children down one level"
msgstr "Mover o item e seus filhos uma posição abaixo"

#: ../glade/glade_menu_editor.c:524
msgid "The stock item to use."
msgstr "O item normal a utilizar."

#: ../glade/glade_menu_editor.c:527 ../glade/glade_menu_editor.c:642
msgid "Stock Item:"
msgstr "Item Normal:"

#: ../glade/glade_menu_editor.c:640
msgid "The stock Gnome item to use."
msgstr "O item Gnome normal a utilizar."

#: ../glade/glade_menu_editor.c:745
msgid "The text of the menu item, or empty for separators."
msgstr "O texto do item de menu, ou vazio para separadores."

#: ../glade/glade_menu_editor.c:769 ../glade/property.c:777
msgid "The name of the widget"
msgstr "O nome do widget"

#: ../glade/glade_menu_editor.c:790
msgid "The function to be called when the item is selected"
msgstr "A função a ser chamada quando o item é seleccionado"

#: ../glade/glade_menu_editor.c:792 ../glade/property.c:1546
msgid "Handler:"
msgstr "Handler:"

#: ../glade/glade_menu_editor.c:811
msgid "An optional icon to show on the left of the menu item."
msgstr "Um ícone opcional para mostrar à esquerda do item de menu."

#: ../glade/glade_menu_editor.c:934
msgid "The tip to show when the mouse is over the item"
msgstr "A dica a mostrar quando o rato estiver sobre o item"

#: ../glade/glade_menu_editor.c:936 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Dica:"

#: ../glade/glade_menu_editor.c:957
msgid "_Add"
msgstr "_Adicionar"

#: ../glade/glade_menu_editor.c:962
msgid "Add a new item below the selected item."
msgstr "Adicionar um novo item abaixo do item seleccionado."

#: ../glade/glade_menu_editor.c:967
msgid "Add _Child"
msgstr "Adicionar Fil_ho"

#: ../glade/glade_menu_editor.c:972
msgid "Add a new child item below the selected item."
msgstr "Adicionar um novo item filho abaixo do item seleccionado."

#: ../glade/glade_menu_editor.c:978
msgid "Add _Separator"
msgstr "Adicionar _Separador"

#: ../glade/glade_menu_editor.c:983
msgid "Add a separator below the selected item."
msgstr "Adicionar um separador abaixo do item seleccionado."

#: ../glade/glade_menu_editor.c:988 ../glade/glade_project_window.c:239
msgid "_Delete"
msgstr "_Apagar"

#: ../glade/glade_menu_editor.c:993
msgid "Delete the current item"
msgstr "Apagar o item actual"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:999
msgid "Item Type:"
msgstr "Tipo Item:"

#: ../glade/glade_menu_editor.c:1015
msgid "If the item is initially on."
msgstr "Se o item incialmente estiver activo."

#: ../glade/glade_menu_editor.c:1017
msgid "Active:"
msgstr "Activo:"

#: ../glade/glade_menu_editor.c:1022 ../glade/glade_menu_editor.c:1632
#: ../glade/property.c:2215 ../glade/property.c:2225
msgid "No"
msgstr "Não"

#: ../glade/glade_menu_editor.c:1036
msgid "The radio menu item's group"
msgstr "O grupo de ítens de menu rádio"

#: ../glade/glade_menu_editor.c:1053 ../glade/glade_menu_editor.c:2406
#: ../glade/glade_menu_editor.c:2546
msgid "Radio"
msgstr "Rádio"

#: ../glade/glade_menu_editor.c:1060 ../glade/glade_menu_editor.c:2404
#: ../glade/glade_menu_editor.c:2544
msgid "Check"
msgstr "Verificar"

#: ../glade/glade_menu_editor.c:1067 ../glade/property.c:102
msgid "Normal"
msgstr "Normal"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1076
msgid "Accelerator:"
msgstr "Atalho:"

#: ../glade/glade_menu_editor.c:1113 ../glade/property.c:1681
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1118 ../glade/property.c:1684
msgid "Shift"
msgstr "Shift"

#: ../glade/glade_menu_editor.c:1123 ../glade/property.c:1687
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1128 ../glade/property.c:1694
msgid "Key:"
msgstr "Tecla:"

#: ../glade/glade_menu_editor.c:1134 ../glade/property.c:1673
msgid "Modifiers:"
msgstr "Modificadores:"

#: ../glade/glade_menu_editor.c:1632 ../glade/glade_menu_editor.c:2411
#: ../glade/glade_menu_editor.c:2554 ../glade/property.c:2215
msgid "Yes"
msgstr "Sim"

#: ../glade/glade_menu_editor.c:2002
msgid "Select icon"
msgstr "Seleccionar Ícone"

#: ../glade/glade_menu_editor.c:2345 ../glade/glade_menu_editor.c:2706
msgid "separator"
msgstr "separador"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3624 ../glade/glade_project_window.c:366
#: ../glade/property.c:5109
msgid "New"
msgstr "Novo"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Seleccionador"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directório de projecto não está definido.\n"
"Defina-o utilizando o diálogo de Opções do Projecto.\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directório origem não está definido.\n"
"Defina-o utilizando o diálogo de Opções do Projecto.\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"Directório origem inválido:\n"
"\n"
"O directório origem tem de ser o directório do projecto\n"
"ou um subdirectório do directório do projecto.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directório das imagens não está definido.\n"
"Defina-o utilizando o diálogo de Opções do Projecto.\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "Desculpe - a geração de código para %s ainda não está implementado"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""
"O seu projecto utiliza widgets obsoletos que o Gtkmm-2\n"
"não suporta. Analise o seu código para procurar estes\n"
"widgets e utilize os seus substitutos."

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"Erro ao executar glade-- para gerar o código fonte C++.\n"
"Verifique que possui o glade-- instalado e que está no seu PATH.\n"
"Tente executar 'glade-- <ficheiro_projecto.glade>' numa consola."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"Erro ao executar gate para gerar o código fonte Ada95.\n"
"Verifique que possui o gate instalado e que está no seu PATH.\n"
"Depois tente executar 'gate <ficheiro_projecto.glade>' numa consola."

#: ../glade/glade_project.c:571
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"Erro ao executar glade2perl para gerar o código fonte Perl.\n"
"Verifique que possui o glade2perl instalado e que está no seu PATH.\n"
"Depois tente executar 'glade2perl <ficheiro_projecto.glade>' numa consola."

#: ../glade/glade_project.c:594
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"Erro ao executar eglade para gerar o código fonte Eiffel.\n"
"Verifique que possui o eglade instalado e que está no seu PATH.\n"
"Depois tente executar 'eglade <ficheiro_projecto.glade>' numa consola."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"O directório de imagens não está definido.\n"
"Defina-o utilizando o diálogo de Opções do Projecto.\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "Erro ao escrever ficheiro XML do projecto\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:382
#: ../glade/glade_project_window.c:889
msgid "Project Options"
msgstr "Opções do Projecto"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "Geral"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Opções Básicas:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "O directório do projecto"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Directório do Projecto:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Pesquisar..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "O nome do projecto actual"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Nome Projecto:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "O nome da aplicação"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "O ficheiro de projecto"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Ficheiro Projecto:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Subdirectórios:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "O directório onde gravar o código fonte gerado"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Directório Origem:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "O directório onde armazenar imagens"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Directório Imagens:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "A licença que é adicionada no topo dos ficheiros gerados"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Linguagem:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Activar Suporte Gnome"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Se for ser construida uma aplciação Gnome"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Activar suporte Gnome DB"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Se for ser construida uma aplciação Gnome DB"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "Opções C"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Opções Gerais:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Suporte Gettext"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "Se expressões são marcadas para tradução pelo gettext"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "Definir Nomes Widgets"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Se os nomes dos widgets são definidos no código fonte"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Backup dos Ficheiros de Código"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Se são feitas cópias de ficheiros de código antigos"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Suporte Ajuda Gnome"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Se deverá ser incluido suporte para o sistema de Ajuda Gnome"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Opções de Geração de Ficheiros:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "Gerar Ficheiro main.c"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr ""
"Se deverá ser gerado um ficheiro main.c contendo uma função main() function, "
"caso não exista ainda"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Gerar Funções de Suporte"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Se as funções de suporte deverão ser geradas"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Gerar Ficheiros de Compilação"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Se os ficheiros para compilar o código fonte deverão ser gerados, incluindo "
"Makefile.am e configure.in, caso não existam ainda"

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "Funções de Criação de Interface:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "O ficheiro onde são escritas as funções de criação do interface"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Ficheiro Código:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr ""
"O ficheiro onde são escritas as declarações das funções para criar o "
"interface"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Ficheiro Cabeçalho:"

#: ../glade/glade_project_options.c:594
msgid "Source file for interface creation functions"
msgstr "Ficheiro de código para funções de criação de interface"

#: ../glade/glade_project_options.c:595
msgid "Header file for interface creation functions"
msgstr "Ficheiro de cabeçalho para funções de criação de interface"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Funções de Gestão de Sinais & Chamadas:"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr ""
"O ficheiro onde são escritas as funções vazias de gestão de sinais e chamadas"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"O ficheiro onde são escritas as declarações das funções de gestão de sinais "
"e chamadas"

#: ../glade/glade_project_options.c:640
msgid "Source file for signal handler and callback functions"
msgstr "Ficheiro de código para funções de gestão de sinais e chamadas"

#: ../glade/glade_project_options.c:641
msgid "Header file for signal handler and callback functions"
msgstr "Ficheiro de cabeçalho para funções de gestão de sinais e chamadas"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Funções de Suporte:"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "O ficheiro onde são escritas as funções de suporte"

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "O ficheiro onde são escritas as declarações das funções de suporte"

#: ../glade/glade_project_options.c:686
msgid "Source file for support functions"
msgstr "Ficheiro de código para funções de suporte"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr "Ficheiro de cabeçalho para funções de suporte"

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "Opções LibGlade"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Expressões Traduzíveis:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Gravar Expressões Traduzíveis"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Se expressões traduzíveis são gravadas num ficheiro C à parte, para permitir "
"a tradução de interfaces carregados pela libglade"

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "O ficheiro de código C onde gravar todas as expressões traduzíveis"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Ficheiro:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Seleccione o Directório de Projecto"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Tem de definir a opção de Ficheiro de Expressões Traduzíveis"

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Tem de definir a opção de Directório de Projecto"

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Tem de definir a opção de Ficheiro de Projecto"

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Tem de definir a opção de Nome de Projecto"

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Tem de definir a opção de Nome de Aplicação"

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Tem de definir a opção de Directório Origem"

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Tem de definir a opção de Directório de Imagens"

#: ../glade/glade_project_window.c:184
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"Incapaz de mostrar ficheiro de ajuda: %s.\n"
"\n"
"Erro: %s"

#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:634
msgid "Create a new project"
msgstr "Criar um projecto novo"

#: ../glade/glade_project_window.c:216 ../glade/glade_project_window.c:654
#: ../glade/glade_project_window.c:905
msgid "_Build"
msgstr "_Compilar"

#: ../glade/glade_project_window.c:217 ../glade/glade_project_window.c:665
msgid "Output the project source code"
msgstr "Gerar o código fonte do projecto"

#: ../glade/glade_project_window.c:223 ../glade/glade_project_window.c:668
msgid "Op_tions..."
msgstr "Opçõ_es..."

#: ../glade/glade_project_window.c:224 ../glade/glade_project_window.c:677
msgid "Edit the project options"
msgstr "Editar as opções de projecto"

#: ../glade/glade_project_window.c:239 ../glade/glade_project_window.c:716
msgid "Delete the selected widget"
msgstr "Apagar o widget seleccionado"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:727
msgid "Show _Palette"
msgstr "Mostrar _Paleta"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:732
msgid "Show the palette of widgets"
msgstr "Mostrar a paleta de widgets"

#: ../glade/glade_project_window.c:263 ../glade/glade_project_window.c:737
msgid "Show Property _Editor"
msgstr "Mostrar _Editor de Propriedades"

#: ../glade/glade_project_window.c:264 ../glade/glade_project_window.c:743
msgid "Show the property editor"
msgstr "Mostrar o editor de propriedades"

#: ../glade/glade_project_window.c:270 ../glade/glade_project_window.c:747
msgid "Show Widget _Tree"
msgstr "Mostrar Widge_t de Árvore"

#: ../glade/glade_project_window.c:271 ../glade/glade_project_window.c:753
#: ../glade/main.c:82
msgid "Show the widget tree"
msgstr "Mostrar o widget de árvore"

#: ../glade/glade_project_window.c:277 ../glade/glade_project_window.c:757
msgid "Show _Clipboard"
msgstr "Mostrar Área de Transferên_cia"

#: ../glade/glade_project_window.c:278 ../glade/glade_project_window.c:763
#: ../glade/main.c:86
msgid "Show the clipboard"
msgstr "Mostrar área de transferência"

#: ../glade/glade_project_window.c:296
msgid "Show _Grid"
msgstr "Mostrar _Grelha"

#: ../glade/glade_project_window.c:297 ../glade/glade_project_window.c:799
msgid "Show the grid (in fixed containers only)"
msgstr "Mostrar a grelha (apenas em contentores fixos)"

#: ../glade/glade_project_window.c:303
msgid "_Snap to Grid"
msgstr "Ane_xar à Grelha"

#: ../glade/glade_project_window.c:304
msgid "Snap widgets to the grid"
msgstr "Anexar os widgets à grelha"

#: ../glade/glade_project_window.c:310 ../glade/glade_project_window.c:771
msgid "Show _Widget Tooltips"
msgstr "Mostrar Dicas de _Widgets"

#: ../glade/glade_project_window.c:311 ../glade/glade_project_window.c:779
msgid "Show the tooltips of created widgets"
msgstr "Mostrar as dicas dos widgets seleccionados"

#: ../glade/glade_project_window.c:320 ../glade/glade_project_window.c:802
msgid "Set Grid _Options..."
msgstr "Definir _Opções de Grelha..."

#: ../glade/glade_project_window.c:321
msgid "Set the grid style and spacing"
msgstr "Definir o espaçamento e estilo da grelha"

#: ../glade/glade_project_window.c:327 ../glade/glade_project_window.c:823
msgid "Set Snap O_ptions..."
msgstr "Definir O_pções de Anexação..."

#: ../glade/glade_project_window.c:328
msgid "Set options for snapping to the grid"
msgstr "Definir opções para o anexar à grelha"

#: ../glade/glade_project_window.c:340
msgid "_FAQ"
msgstr "_FAQ"

#: ../glade/glade_project_window.c:341
msgid "View the Glade FAQ"
msgstr "Ver a FAQ do Glade"

#. create File menu
#: ../glade/glade_project_window.c:355 ../glade/glade_project_window.c:625
msgid "_Project"
msgstr "_Projecto"

#: ../glade/glade_project_window.c:366 ../glade/glade_project_window.c:872
#: ../glade/glade_project_window.c:1049
msgid "New Project"
msgstr "Novo Projecto"

#: ../glade/glade_project_window.c:371
msgid "Open"
msgstr "Abrir"

#: ../glade/glade_project_window.c:371 ../glade/glade_project_window.c:877
#: ../glade/glade_project_window.c:1110
msgid "Open Project"
msgstr "Abrir Projecto"

#: ../glade/glade_project_window.c:376
msgid "Save"
msgstr "Gravar"

#: ../glade/glade_project_window.c:376 ../glade/glade_project_window.c:881
#: ../glade/glade_project_window.c:1475
msgid "Save Project"
msgstr "Gravar Projecto"

#: ../glade/glade_project_window.c:382
msgid "Options"
msgstr "Opções"

#: ../glade/glade_project_window.c:387
msgid "Build"
msgstr "Compilar"

#: ../glade/glade_project_window.c:387
msgid "Build the Source Code"
msgstr "Compilar o Código Fonte"

#: ../glade/glade_project_window.c:638
msgid "Open an existing project"
msgstr "Abrir um projecto existente"

#: ../glade/glade_project_window.c:642
msgid "Save project"
msgstr "Gravar projecto"

#: ../glade/glade_project_window.c:687
msgid "Quit Glade"
msgstr "Sair do Glade"

#: ../glade/glade_project_window.c:701
msgid "Cut the selected widget to the clipboard"
msgstr "Cortar o widget seleccionado para a área de transferência"

#: ../glade/glade_project_window.c:706
msgid "Copy the selected widget to the clipboard"
msgstr "Copiar o widget seleccionado para a área de transferência"

#: ../glade/glade_project_window.c:711
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Colar o widget d aárea de transferência sobre o widget seleccionado"

#: ../glade/glade_project_window.c:783
msgid "_Grid"
msgstr "_Grelha"

#: ../glade/glade_project_window.c:791
msgid "_Show Grid"
msgstr "_Mostrar Grelha"

#: ../glade/glade_project_window.c:808
msgid "Set the spacing between grid lines"
msgstr "Definir o espaçamento entre linhas da grelha"

#: ../glade/glade_project_window.c:811
msgid "S_nap to Grid"
msgstr "A_nexar à Grelha"

#: ../glade/glade_project_window.c:819
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "Anexar widgets à grelha (apenas em contentores fixos)"

#: ../glade/glade_project_window.c:829
msgid "Set which parts of a widget snap to the grid"
msgstr "Definir que partes do widget anexam à grelha"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:854
msgid "_About..."
msgstr "_Sobre..."

#: ../glade/glade_project_window.c:895
msgid "Optio_ns"
msgstr "O_pções"

#: ../glade/glade_project_window.c:899
msgid "Write Source Code"
msgstr "Escrever Código Fonte"

#: ../glade/glade_project_window.c:986 ../glade/glade_project_window.c:1691
#: ../glade/glade_project_window.c:1980
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:993
msgid "Are you sure you want to create a new project?"
msgstr "Tem a certeza que deseja criar um projecto novo?"

#: ../glade/glade_project_window.c:1053
msgid "New _GTK+ Project"
msgstr "Novo Projecto _GTK+"

#: ../glade/glade_project_window.c:1054
msgid "New G_NOME Project"
msgstr "Novo Projecto G_NOME"

#: ../glade/glade_project_window.c:1057
msgid "Which type of project do you want to create?"
msgstr "Que tipo de projecto deseja criar?"

#: ../glade/glade_project_window.c:1091
msgid "New project created."
msgstr "Projecto novo criado."

#: ../glade/glade_project_window.c:1181
msgid "Project opened."
msgstr "Projecto aberto."

#: ../glade/glade_project_window.c:1195
msgid "Error opening project."
msgstr "Erro ao abrir projecto."

#: ../glade/glade_project_window.c:1259
msgid "Errors opening project file"
msgstr "Erros na abertura do ficheiro de projecto"

#: ../glade/glade_project_window.c:1265
msgid " errors opening project file:"
msgstr " erros ao abrir o ficheiro de projecto:"

#: ../glade/glade_project_window.c:1338
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"De momento não existe nenhum projecto aberto.\n"
"Criar um novo projecto com o comando Projecto/Novo."

#: ../glade/glade_project_window.c:1542
msgid "Error saving project"
msgstr "Erro ao gravar projecto"

#: ../glade/glade_project_window.c:1544
msgid "Error saving project."
msgstr "Erro ao gravar projecto."

#: ../glade/glade_project_window.c:1550
msgid "Project saved."
msgstr "Projecto gravado."

#: ../glade/glade_project_window.c:1620
msgid "Errors writing source code"
msgstr "Erros ao escrever código fonte"

#: ../glade/glade_project_window.c:1622
msgid "Error writing source."
msgstr "Erro ao escrever código fonte."

#: ../glade/glade_project_window.c:1628
msgid "Source code written."
msgstr "Código fonte escrito."

#: ../glade/glade_project_window.c:1659
msgid "System error message:"
msgstr "Mensagem de erro de sistema:"

#: ../glade/glade_project_window.c:1698
msgid "Are you sure you want to quit?"
msgstr "Tem a certeza que deseja sair?"

#: ../glade/glade_project_window.c:1982 ../glade/glade_project_window.c:2042
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "(C) 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1983 ../glade/glade_project_window.c:2041
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr "Glade é um Construtor de Interfaces de Utilizador para GTK+ e GNOME."

#: ../glade/glade_project_window.c:2012
msgid "About Glade"
msgstr "Sobre o Glade"

#: ../glade/glade_project_window.c:2097
msgid "<untitled>"
msgstr "<sem título>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Navegador Base de Dados"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Combo ligada-dados"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr "GnomeDbConnectionProperties"

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Selecção Ligação"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "Configurador DSN"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "Druida Configuração DSN"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr "Realçar texto:"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr "Se seleccionado, o texto será realçado dentro do widget"

#: ../glade/gnome-db/gnomedbeditor.c:178
msgid "GnomeDbEditor"
msgstr "GnomeDbEditor"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Visualizador de erros de base de dados"

#: ../glade/gnome-db/gnomedberrordlg.c:218
msgid "Database error dialog"
msgstr "Diálogo de erro de base de dados"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Formulário"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr "Texto dentro da barra cinzenta"

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr "Barra Cinzenta"

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Grelha ligada-dados"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Lista ligada-dados"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Widget de início sessão base de dados"

#: ../glade/gnome-db/gnomedblogindlg.c:76
msgid "Login"
msgstr "Início Sessão"

#: ../glade/gnome-db/gnomedblogindlg.c:219
msgid "Database login dialog"
msgstr "Diálogo de início de sessão em base de dados"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Selector Fornecedor"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr "GnomeDbQueryBuilder"

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Selector Fonte Dados"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Editor Tabela "

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Permitir Flutuar:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Se são permitidos itens de anexação flutuante"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Adicionar zona de anexação ao topo"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Adicionar zona de anexação em baixo"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Adicionar zona de anexação à esquerda"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Adicionar zona de anexação à direita"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Adicionar item de anexação flutuante"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Anexar Gnome"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Trancado:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Se o item de anexar está trancado na posição"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Exclusivo:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "Se o item de anexar é sempre o único na zona de anexação"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Nunca Flutuar:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "Se nunca é permitido ao item de anexar flutuar na sua própria janela"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Nunca Vertical:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "Se nunca é permitido ao item de anexar ser vertical"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Nunca Horizontal:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "Se nunca é permitido ao item de anexar ser horizontal"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "O tipo de sombra à volta do item de anexar"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "A orientação do item de anexação flutuante"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Adicionar item de anexar antes"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Adicionar item de anexar depois"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Item de Anexar Gnome"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr ""
"Informação adicional, tal como uma descrição do pacote e a sua página "
"principal na web"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Diálogo Sobre do Gnome"

#: ../glade/gnome/gnomeapp.c:170
msgid "New File"
msgstr "Novo Ficheiro"

#: ../glade/gnome/gnomeapp.c:172
msgid "Open File"
msgstr "Abrir Ficheiro"

#: ../glade/gnome/gnomeapp.c:174
msgid "Save File"
msgstr "Gravar Ficheiro"

#: ../glade/gnome/gnomeapp.c:203
msgid "Status Bar:"
msgstr "Barra Estados:"

#: ../glade/gnome/gnomeapp.c:204
msgid "If the window has a status bar"
msgstr "Se a janela tem barra de estados"

#: ../glade/gnome/gnomeapp.c:205
msgid "Store Config:"
msgstr "Gravar Configuração:"

#: ../glade/gnome/gnomeapp.c:206
msgid "If the layout is saved and restored automatically"
msgstr "Se a disposição é gravada e reposta automaticamente"

#: ../glade/gnome/gnomeapp.c:442
msgid "Gnome Application Window"
msgstr "Janela de Aplicação Gnome"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Mensagem de Estado."

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "Progresso:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "Se a barra da aplicação tem um indicador de progresso"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Estado:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr ""
"Se a barra da aplicação tem uma área para mensagens de estado e dados do "
"utilizador"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Barra de Aplicação Gnome"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Anti-Alias:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr "Se a tela tem anti-alias, para suavizar os limites do texto e gráficos"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "A coordenada x mínima"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "A coordenada y mínima"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2:"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "A coordenada x máxima"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "A coordenada y máxima"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Pixels Por Unidade:"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "O número de pixels correspondentes a uma unidade"

#: ../glade/gnome/gnomecanvas.c:239
msgid "GnomeCanvas"
msgstr "GnomeCanvas"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Esbater:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Se a amostra deverá ser esbatida para ser mais precisa"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Seleccione uma cor"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Selector de Cores Gnome"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "Incapaz de criar o controlo Bonobo"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Novo Controlo Bonobo"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Seleccione um Controlo Bonobo"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3896
msgid "Description"
msgstr "Descrição"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Controlo Bonobo"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Mostrar Hora:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Se a hora deverá ser mostrada, bem como a data"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "Formato 24 Horas:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Se a hora deverá ser mostrada no formato 24 horas"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Hora Inferior:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "A hora mais pequena a mostrar no popup"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Hora Superior:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "A hora mais alta a mostrar no popup"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "EditorDatasGnome"

#: ../glade/gnome/gnomedialog.c:152 ../glade/gnome/gnomemessagebox.c:189
msgid "Auto Close:"
msgstr "Auto Fechar:"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "If the dialog closes when any button is clicked"
msgstr "Se o diálogo se fecha logo que qualquer botão seja primido"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "Hide on Close:"
msgstr "Esconder ao Fechar:"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "Se o diálogo é escondido ao ser fechado, em vez de ser destruido"

#: ../glade/gnome/gnomedialog.c:341
msgid "Gnome Dialog Box"
msgstr "Caixa de Diálogo Gnome"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Novo Druida Gnome"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Mostrar Ajuda"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Mostrar botão de ajuda."

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Adicionar Página Inicial"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Adicionar Página Final"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Druida"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "Adicionar título da página"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "O texto principal na página, a introduzir o druida aos utilizadores."

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Cor do Título:"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "A cor do texto do título"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Cor Texto:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "A cor do texto principal"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "A cor do fundo da página"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Cor do Logo Fundo:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "A cor de fundo à volta do logotipo"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Cor da Caixa Texto:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "A cor de fundo na área principal do texto"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Imagem Logo:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "O logotipo a mostrar no canto superior direito da página"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Marca Água Lateral:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "A imagem principal a mostrar no lado da página."

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Marca Água Topo:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr "A marca de água a mostrar no topo da página."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Página Inicial ou Final do Druida"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Cor de Fundo do Conteudo:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "A cor de fundo à volta do título"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "A imagem a mostrar ao longo do topo da página"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Página Normal Druida"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "ID Histórico:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "O ID sob o qual gravar as entradas de histórico"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Máx Gravações:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "O número máximo de entradas de histórico gravadas"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Entrada Gnome"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "O título do diálogo de selecção de ficheiro"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Directório:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Se é necessário um directório em vez de um ficheiro"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "Se o diálogo de selecção de ficheiro deve ser modal"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
#, fuzzy
msgid "Use FileChooser:"
msgstr "Selecção de Ficheiros"

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Entrada de Ficheiro Gnome"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "O texto de antevisão a mostrar no diálogo de selecção de fonte"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Modo:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "O que mostrar no botão de selecção de fonte"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "O tamanho de fonte a utilizar no botão de selecção de fonte"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Selector de Fontes Gnome"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "O URL a mostrar quando o botão é primido"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "O texto a mostrar no botão"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Botão de Ligação HREF Gnome"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Entrada de Ícone Gnome"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "O modo de selecção"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Largura Ícone:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "A largura de cada ícone"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "O número de pixels entre linhas de ícones"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "O número de pixels entre colunas de ícones"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Margem Ícone:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "O número de pixels à volta dos ícones (não utilizado?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Espaç Texto:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "O número de pixels entre o texto e os ícones"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Texto Editável:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Se o texto dos ícones pode ser modificado pelo utilizador"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Texto Estático:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr ""
"Se o texto do ícone é estático, situação em que não será copiado pelo "
"GnomeIconList"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Lista Ícones"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Selecção Ícone"

#: ../glade/gnome/gnomemessagebox.c:174
msgid "Message Type:"
msgstr "Tipo Mensagem:"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "The type of the message box"
msgstr "O tipo de caixa de mensagem"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "Message:"
msgstr "Mensagem:"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "The message to display"
msgstr "A mensagem a mostrar"

#: ../glade/gnome/gnomemessagebox.c:498
msgid "Gnome Message Box"
msgstr "Caixa de Mensagem Gnome"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "O nome do ficheiro de imagem"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Escala:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Se a imagem é escalada"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Largura Escala:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "A largura à qual escalar a imagem"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Altura Escala:"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "A altura à qual escalar a imagem"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Imagem Gnome"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Antever:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Se é mostrada uma pequena antevisão das imagens"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmapEntry"

#: ../glade/gnome/gnomepropertybox.c:112
msgid "New GnomePropertyBox"
msgstr "Nova GnomePropertyBox"

#: ../glade/gnome/gnomepropertybox.c:365
msgid "Property Dialog Box"
msgstr "Caixa de Diálogo de Propriedades"

#: ../glade/main.c:70
msgid "Write the source code and exit"
msgstr "Escrever o código fonte e sair"

#: ../glade/main.c:74
msgid "Start with the palette hidden"
msgstr "Começar com a paleta escondida"

#: ../glade/main.c:78
msgid "Start with the property editor hidden"
msgstr "Começar com o editor de propriedades escondido"

#: ../glade/main.c:436
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade: O ficheiro XML tem de estar definido para a opção '-w' ou '--write-"
"source'.\n"

#: ../glade/main.c:450
msgid "glade: Error loading XML file.\n"
msgstr "glade: Erro ao ler ficheiro XML.\n"

#: ../glade/main.c:457
msgid "glade: Error writing source.\n"
msgstr "glade: Erro ao escrever código.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Paleta"

#: ../glade/property.c:73
msgid "private"
msgstr "privado"

#: ../glade/property.c:73
msgid "protected"
msgstr "protegido"

#: ../glade/property.c:73
msgid "public"
msgstr "público"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "Pré-seleccionado"

#: ../glade/property.c:103
msgid "Selected"
msgstr "Seleccionado"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Imbutido"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Quando a janela precisa de ser redesenhada"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Quando o rato se move"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Dicas de movimento de rato"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Movimento de rato com qualquer botão primido"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Movimento de rato com o botão 1 primido"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Movimento de rato com o botão 2 primido"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Movimento de rato com o botão 3 primido"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "Qualquer botão de rato primido"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "Qualquer botão de rato largado"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "Qualquer tecla primida"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "Qualquer tecla largada"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Quando o rato entra na janela"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Quando o rato sai da janela"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Qualquer alteração no focus do campo"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Qualquer alteração na estrutura de janela"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "Qualquer modificação na propriedade X Windows"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Qualquer alteração na visibilidade"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "Para cursores em aplicações sensíveis-XInput"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Propriedades"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Arranjo"

#: ../glade/property.c:625
msgid "Common"
msgstr "Comun"

#: ../glade/property.c:631
msgid "Style"
msgstr "Estilo"

#: ../glade/property.c:637 ../glade/property.c:4640
msgid "Signals"
msgstr "Sinais"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Propriedades: "

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Propriedades: <nenhuma>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "Classe:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "A classe do widget"

#: ../glade/property.c:813
msgid "Width:"
msgstr "Largura:"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""
"A largura pedida do widget (normalmente utilizado para definir largura "
"mínima)"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Altura:"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""
"A altura pedida do widget (normalmente utilizado para definir altura mínima)"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Visível:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "Se o widget está inicialmente visível"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Sensibilidade:"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "Se o widget responde a eventos"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "A dica a mostrar se o rato ficar sobre o widget"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Pode Ser Omissão:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr "Se o widget pode ser a acção por omissão no diálogo"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "Tem Omissão:"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Se o widget é a acção por omissão no diálogo"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Pode Ter Focus:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "Se o widget pode aceitar o focus de introdução"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Tem Focus:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "Se o widget tem o focus de introdução"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Eventos:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "Os eventos X que o widget recebe"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Eventos Ext.:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "O modo de eventos de Extensão X"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Atalhos:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Define os sinais a emitir quando as teclas são primidas"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Editar..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "Propagar:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "Definir como Verdade para propagar o estilo aos widgets filhos"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Nome Estilo:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "O nome do estilo, que pode ser partilhado por vários widgets"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Fonte:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "A fonte a ser utilizada por qualquer texto no widget"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Copiar Todos"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Primeiro Plano:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Fundo:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "Base:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Cor de primeiro plano"

#: ../glade/property.c:928
msgid "Background color"
msgstr "Cor de fundo"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Cor texto"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Cor base"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Imagem Fundo:"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "A imagem a utilizar como fundo do widget"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "O ficheiro para onde escrever o código fonte"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Público:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Se o widget é adicionado à estrutura de dados do componente"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Classe Separada:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Colocar a subárvore do widget numa classe separada"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Ficheiro Separado:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Colocar este widget num ficheiro de código separado"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Visibilidade:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr ""
"Visibilidade dos widgets. Widgets públicos são exportados para um mapa "
"global."

#: ../glade/property.c:1126
msgid "You need to select a color or background to copy"
msgstr "Tem de seleccionar uma cor ou fundo para copiar"

#: ../glade/property.c:1145
msgid "Invalid selection in on_style_copy()"
msgstr "Selecção inválida no on_style_copy()"

#: ../glade/property.c:1187
msgid "You need to copy a color or background pixmap first"
msgstr "Tem primeiro de copiar uma cor ou imagem de fundo"

#: ../glade/property.c:1193
msgid "You need to select a color to paste into"
msgstr "Tem de seleccionar uma cor para onde colar"

#: ../glade/property.c:1203
msgid "You need to select a background pixmap to paste into"
msgstr "Tem de seleccionar uma imagem de fundo para onde colar"

#: ../glade/property.c:1455
msgid "Couldn't create pixmap from file\n"
msgstr "Incapaz de criar imagem do ficheiro\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1497
msgid "Signal"
msgstr "Sinal"

#: ../glade/property.c:1499
msgid "Data"
msgstr "Dados"

#: ../glade/property.c:1500
msgid "After"
msgstr "Após"

#: ../glade/property.c:1501
msgid "Object"
msgstr "Objecto"

#: ../glade/property.c:1532 ../glade/property.c:1696
msgid "Signal:"
msgstr "Sinal:"

#: ../glade/property.c:1533
msgid "The signal to add a handler for"
msgstr "O sinal ao qual adicionar um handler"

#: ../glade/property.c:1547
msgid "The function to handle the signal"
msgstr "A função que irá processar o sinal"

#: ../glade/property.c:1550
msgid "Data:"
msgstr "Dados:"

#: ../glade/property.c:1551
msgid "The data passed to the handler"
msgstr "Os dados passados para o handler"

#: ../glade/property.c:1552
msgid "Object:"
msgstr "Objecto:"

#: ../glade/property.c:1553
msgid "The object which receives the signal"
msgstr "O objecto que recebe o sinal"

#: ../glade/property.c:1554
msgid "After:"
msgstr "Após:"

#: ../glade/property.c:1555
msgid "If the handler runs after the class function"
msgstr "Se o handler é executado após a função de classe"

#: ../glade/property.c:1568
msgid "Add"
msgstr "Adicionar"

#: ../glade/property.c:1574
msgid "Update"
msgstr "Actualizar"

#: ../glade/property.c:1586
msgid "Clear"
msgstr "Limpar"

#: ../glade/property.c:1636
msgid "Accelerators"
msgstr "Atalhos"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1649
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1650
msgid "Key"
msgstr "Tecla"

#: ../glade/property.c:1651
msgid "Signal to emit"
msgstr "Sinal a emitir"

#: ../glade/property.c:1695
msgid "The accelerator key"
msgstr "A tecla de atalho"

#: ../glade/property.c:1697
msgid "The signal to emit when the accelerator is pressed"
msgstr "O sinal a emitir quando a tecla de atalho é primida"

#: ../glade/property.c:1846
msgid "Edit Text Property"
msgstr ""

#: ../glade/property.c:1884
msgid "<b>_Text:</b>"
msgstr ""

#: ../glade/property.c:1894
#, fuzzy
msgid "T_ranslatable"
msgstr "Expressões Traduzíveis:"

#: ../glade/property.c:1898
msgid "Has Context _Prefix"
msgstr ""

#: ../glade/property.c:1924
msgid "<b>Co_mments For Translators:</b>"
msgstr ""

#: ../glade/property.c:3886
msgid "Select X Events"
msgstr "Seleccionar Eventos X"

#: ../glade/property.c:3895
msgid "Event Mask"
msgstr "Máscara de Evento"

#: ../glade/property.c:4025 ../glade/property.c:4074
msgid "You need to set the accelerator key"
msgstr "Tem de definir a tecla de evento"

#: ../glade/property.c:4032 ../glade/property.c:4081
msgid "You need to set the signal to emit"
msgstr "Tem de definir o sinal a emitir"

#: ../glade/property.c:4308 ../glade/property.c:4364
msgid "You need to set the signal name"
msgstr "Tem de definir o nome de sinal"

#: ../glade/property.c:4315 ../glade/property.c:4371
msgid "You need to set the handler for the signal"
msgstr "Tem de definir o handler para o sinal"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4574
#, c-format
msgid "%s signals"
msgstr "%s sinais"

#: ../glade/property.c:4631
msgid "Select Signal"
msgstr "Seleccionar Sinal"

#: ../glade/property.c:4827
msgid "Value:"
msgstr "Valor:"

#: ../glade/property.c:4827
msgid "Min:"
msgstr "Mín:"

#: ../glade/property.c:4827
msgid "Step Inc:"
msgstr "Inc Passo:"

#: ../glade/property.c:4828
msgid "Page Inc:"
msgstr "Inc Página:"

#: ../glade/property.c:4828
msgid "Page Size:"
msgstr "Tamanho Página:"

#: ../glade/property.c:4830
msgid "H Value:"
msgstr "Valor H:"

#: ../glade/property.c:4830
msgid "H Min:"
msgstr "Mín H:"

#: ../glade/property.c:4830
msgid "H Max:"
msgstr "Máx H:"

#: ../glade/property.c:4830
msgid "H Step Inc:"
msgstr "Inc Passo H:"

#: ../glade/property.c:4831
msgid "H Page Inc:"
msgstr "Inc Página H:"

#: ../glade/property.c:4831
msgid "H Page Size:"
msgstr "Tamanho Página H:"

#: ../glade/property.c:4833
msgid "V Value:"
msgstr "Valor V:"

#: ../glade/property.c:4833
msgid "V Min:"
msgstr "Mín V:"

#: ../glade/property.c:4833
msgid "V Max:"
msgstr "Máx V:"

#: ../glade/property.c:4833
msgid "V Step Inc:"
msgstr "Inc Passo V:"

#: ../glade/property.c:4834
msgid "V Page Inc:"
msgstr "Inc Página V:"

#: ../glade/property.c:4834
msgid "V Page Size:"
msgstr "Tamanho de Página V:"

#: ../glade/property.c:4837
msgid "The initial value"
msgstr "O valor inicial"

#: ../glade/property.c:4838
msgid "The minimum value"
msgstr "O valor mínimo"

#: ../glade/property.c:4839
msgid "The maximum value"
msgstr "O valor máximo"

#: ../glade/property.c:4840
msgid "The step increment"
msgstr "O incremento de passo"

#: ../glade/property.c:4841
msgid "The page increment"
msgstr "O incremento de página"

#: ../glade/property.c:4842
msgid "The page size"
msgstr "O tamanho de página"

#: ../glade/property.c:4997
msgid "The requested font is not available."
msgstr "A fonte requerida não está disponível."

#: ../glade/property.c:5046
msgid "Select Named Style"
msgstr "Nome de Estilo Seleccionado"

#: ../glade/property.c:5057
msgid "Styles"
msgstr "Estilos"

#: ../glade/property.c:5116
msgid "Rename"
msgstr "Renomear"

#: ../glade/property.c:5144
msgid "Cancel"
msgstr "Cancelar"

#: ../glade/property.c:5264
msgid "New Style:"
msgstr "Novo Estilo:"

#: ../glade/property.c:5278 ../glade/property.c:5399
msgid "Invalid style name"
msgstr "Nome de estilo inválido"

#: ../glade/property.c:5286 ../glade/property.c:5409
msgid "That style name is already in use"
msgstr "Esse nome de estilo já está a ser utilizado"

#: ../glade/property.c:5384
msgid "Rename Style To:"
msgstr "Renomear Estilo Para:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"Incapaz de renomear ficheiro:\n"
"  %s\n"
"para:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"Incapaz de criar ficheiro:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "Erro ao escrever ficheiro XML\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Expressões traduzíveis geradas pelo Glade.\n"
" * Adicione este ficheiro ao POTFILES.in do seu projecto.\n"
" * NÃO o compile como parte da sua aplicação.\n"
" */\n"
"\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"Nome de ficheiro de código de interface inválido: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"Nome de ficheiro de cabeçalho de interface inválido: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"Nome de ficheiro de fonte de chamadas inválido: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"Nome de ficheiro de cabeçalho de chamadas inválido: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"Nome de ficheiro de código de suporte inválido: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"Nome de ficheiro de cabeçalho de suporte inválido: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"Incapaz de acrescentar ao ficheiro:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"Erro ao escrever para ficheiro:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr ""
"O nome de ficheiro tem de ser definido no diálogo de Opções de Projecto."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"O nome de ficheiro tem de ser relativo e simples.\n"
"Utilize o diálogo de Opções de Projecto para o definir."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Widget Árvore"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "Widget não encontrado na caixa"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "Widget não encontrado na tabela"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "Widget não encontrado no contentor fixo"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "Widget não encontrado no pacote"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"Incapaz de aceder a ficheiro:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"Incapaz de abrir ficheiro:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"Erro a ler de ficheiro:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"Incapaz de criar directório:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"Incapaz de aceder a directório:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"Directório inválido:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Projectos"

#: ../glade/utils.c:1628
msgid "project"
msgstr "projecto"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"Incapaz de abrir directório:\n"
"  %s\n"
