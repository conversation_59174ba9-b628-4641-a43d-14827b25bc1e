<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="19455" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="19455"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="TrackPropertiesWindowController">
            <connections>
                <outlet property="addFieldAlreadyExists" destination="x7e-xG-aal" id="EXk-bR-tM0"/>
                <outlet property="addFieldName" destination="FkB-fG-UDj" id="Jbx-Zk-0Jr"/>
                <outlet property="addFieldPanel" destination="Wvw-8e-FjO" id="9kx-B7-h2X"/>
                <outlet property="apeStripAPEv2" destination="hIg-o4-UUx" id="whc-WK-5lf"/>
                <outlet property="apeStripID3v2" destination="5LG-8B-V5Y" id="UZU-sg-jNH"/>
                <outlet property="apeWriteAPEv2" destination="pj6-4k-mZ8" id="ESB-BN-n5D"/>
                <outlet property="apeWriteID3v2" destination="v1h-NM-qKW" id="Yg9-2w-Lls"/>
                <outlet property="currentTrackPath" destination="sXi-dN-WuM" id="zGD-YV-PWb"/>
                <outlet property="editMultipleValuesPanel" destination="jBF-bj-4TO" id="zGO-Cp-H2X"/>
                <outlet property="editValuePanel" destination="X7d-wt-Bpc" id="0sQ-fY-k8t"/>
                <outlet property="fieldName" destination="Gfu-7R-rdm" id="lZT-fk-rNG"/>
                <outlet property="fieldValue" destination="dyp-1d-Sb5" id="FbY-qQ-7AY"/>
                <outlet property="filename" destination="z74-7V-xI3" id="CxU-qA-ghg"/>
                <outlet property="metadataTableView" destination="3K9-jX-20s" id="Enf-SN-jU2"/>
                <outlet property="mp3ID3v1Charset" destination="Mlq-sS-Uzy" id="4UK-zB-2cr"/>
                <outlet property="mp3ID3v2Version" destination="vNc-eC-e1z" id="CkF-2x-14g"/>
                <outlet property="mp3StripAPEv2" destination="dbC-Xj-n5r" id="rYS-6i-v2r"/>
                <outlet property="mp3StripID3v1" destination="Rbd-3g-GW6" id="xs1-TS-vqX"/>
                <outlet property="mp3StripID3v2" destination="bBF-e0-1cT" id="lUI-tK-Feu"/>
                <outlet property="mp3WriteAPEv2" destination="OZR-AQ-7gr" id="4oV-wQ-bNe"/>
                <outlet property="mp3WriteID3v1" destination="qPw-ml-B2c" id="hjL-CC-mOJ"/>
                <outlet property="mp3WriteID3v2" destination="pnV-tv-6F6" id="o0J-62-iSi"/>
                <outlet property="multiValueFieldName" destination="E4X-Mq-zGu" id="m8b-Ir-bAf"/>
                <outlet property="multiValueSingle" destination="HW1-0g-xzq" id="16l-9G-4l6"/>
                <outlet property="multiValueTabView" destination="N98-om-YUS" id="KJc-ii-9kf"/>
                <outlet property="multiValueTableView" destination="ava-EC-Y5p" id="7a2-aY-8Hy"/>
                <outlet property="progressPanel" destination="Go9-AM-N3h" id="NY4-4K-IcC"/>
                <outlet property="propertiesTableView" destination="bz2-tU-emD" id="WHi-41-egZ"/>
                <outlet property="tagWriterSettingsPanel" destination="K2G-ka-jb1" id="FlX-Ub-dHZ"/>
                <outlet property="window" destination="SOU-Vm-TZp" id="8G0-sd-Azp"/>
                <outlet property="wvStripAPEv2" destination="9Wb-gR-2Ep" id="8IU-U3-IW8"/>
                <outlet property="wvStripID3v1" destination="S5W-Sk-8gE" id="qew-Ip-wnm"/>
                <outlet property="wvWriteAPEv2" destination="VAe-Oo-Pio" id="ct6-Ir-sby"/>
                <outlet property="wvWriteID3v1" destination="u9s-YX-mSj" id="fMY-SL-ROi"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Track Properties" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="trkproperties" animationBehavior="default" titlebarAppearsTransparent="YES" id="SOU-Vm-TZp">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="167" y="107" width="480" height="339"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1680" height="920"/>
            <view key="contentView" id="iZJ-f1-AAo">
                <rect key="frame" x="0.0" y="0.0" width="480" height="339"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="z74-7V-xI3">
                        <rect key="frame" x="14" y="317" width="452" height="22"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" usesSingleLineMode="YES" bezelStyle="round" id="WA0-9S-EHs">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <tabView drawsBackground="NO" initialItem="lhD-Ti-6zn" translatesAutoresizingMaskIntoConstraints="NO" id="WRA-LF-8XP">
                        <rect key="frame" x="7" y="32" width="466" height="283"/>
                        <font key="font" metaFont="system"/>
                        <tabViewItems>
                            <tabViewItem label="Metadata" identifier="1" id="lhD-Ti-6zn">
                                <view key="view" id="j5f-te-AAV">
                                    <rect key="frame" x="10" y="33" width="446" height="237"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jAy-fZ-tk6">
                                            <rect key="frame" x="0.0" y="0.0" width="446" height="237"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <clipView key="contentView" drawsBackground="NO" id="MJY-yW-6V7">
                                                <rect key="frame" x="1" y="1" width="444" height="235"/>
                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                <subviews>
                                                    <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnReordering="NO" autosaveColumns="NO" headerView="TRT-0c-OiD" id="3K9-jX-20s" customClass="DdbTableViewRightClickActivate">
                                                        <rect key="frame" x="0.0" y="0.0" width="444" height="218"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                        <size key="intercellSpacing" width="3" height="2"/>
                                                        <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                        <tableColumns>
                                                            <tableColumn identifier="name" editable="NO" width="116" minWidth="40" maxWidth="1000" id="5qB-8n-P6l">
                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Name">
                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                                </tableHeaderCell>
                                                                <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="DAE-ij-yyL">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            </tableColumn>
                                                            <tableColumn identifier="value" width="272" minWidth="40" maxWidth="1000" id="XCh-hN-2Rs">
                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Value">
                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                                </tableHeaderCell>
                                                                <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="Vgr-h1-hDG">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            </tableColumn>
                                                        </tableColumns>
                                                    </tableView>
                                                </subviews>
                                                <nil key="backgroundColor"/>
                                            </clipView>
                                            <scroller key="horizontalScroller" hidden="YES" verticalHuggingPriority="750" horizontal="YES" id="0uB-0n-ZjV">
                                                <rect key="frame" x="1" y="119" width="223" height="15"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                            <scroller key="verticalScroller" hidden="YES" verticalHuggingPriority="750" horizontal="NO" id="sjC-jK-Uwm">
                                                <rect key="frame" x="224" y="17" width="15" height="102"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                            <tableHeaderView key="headerView" wantsLayer="YES" id="TRT-0c-OiD">
                                                <rect key="frame" x="0.0" y="0.0" width="444" height="17"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </tableHeaderView>
                                            <connections>
                                                <outlet property="menu" destination="rFk-uf-gVv" id="IjQ-8A-xPw"/>
                                            </connections>
                                        </scrollView>
                                    </subviews>
                                </view>
                            </tabViewItem>
                            <tabViewItem label="Properties" identifier="2" id="2Mj-Bh-Ca7">
                                <view key="view" id="JFG-rr-3OH">
                                    <rect key="frame" x="10" y="33" width="434" height="208"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="clz-kQ-2YA">
                                            <rect key="frame" x="0.0" y="0.0" width="434" height="208"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <clipView key="contentView" ambiguous="YES" id="hMo-4S-6FI">
                                                <rect key="frame" x="1" y="1" width="432" height="206"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <subviews>
                                                    <tableView verticalHuggingPriority="750" ambiguous="YES" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnReordering="NO" columnSelection="YES" multipleSelection="NO" autosaveColumns="NO" headerView="2QI-u0-6vk" id="bz2-tU-emD" customClass="DdbTableViewRightClickActivate">
                                                        <rect key="frame" x="0.0" y="0.0" width="432" height="189"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                        <size key="intercellSpacing" width="3" height="2"/>
                                                        <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                        <tableColumns>
                                                            <tableColumn identifier="name" editable="NO" width="116" minWidth="40" maxWidth="1000" id="Hvy-Pb-VMG">
                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Name">
                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                                </tableHeaderCell>
                                                                <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="coM-Bz-Vqk">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            </tableColumn>
                                                            <tableColumn identifier="value" editable="NO" width="272" minWidth="40" maxWidth="1000" id="wV6-Jd-Ciz">
                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Value">
                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                                </tableHeaderCell>
                                                                <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="Syw-eQ-sIt">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            </tableColumn>
                                                        </tableColumns>
                                                    </tableView>
                                                </subviews>
                                            </clipView>
                                            <scroller key="horizontalScroller" hidden="YES" verticalHuggingPriority="750" horizontal="YES" id="3F4-Jb-4Tp">
                                                <rect key="frame" x="1" y="119" width="223" height="15"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                            <scroller key="verticalScroller" hidden="YES" verticalHuggingPriority="750" horizontal="NO" id="O00-91-QbG">
                                                <rect key="frame" x="224" y="17" width="15" height="102"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                            <tableHeaderView key="headerView" id="2QI-u0-6vk">
                                                <rect key="frame" x="0.0" y="0.0" width="432" height="17"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </tableHeaderView>
                                        </scrollView>
                                    </subviews>
                                </view>
                            </tabViewItem>
                        </tabViewItems>
                    </tabView>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="jxY-7Q-v5B">
                        <rect key="frame" x="7" y="7" width="94" height="32"/>
                        <buttonCell key="cell" type="push" title="Configure" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="zhY-lZ-dL9">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="configureTagWritingAction:" target="-2" id="lhi-ZB-EJJ"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ccs-Rn-ESN">
                        <rect key="frame" x="95" y="7" width="76" height="32"/>
                        <buttonCell key="cell" type="push" title="Reload" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="ejh-9G-xSQ">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="reloadTrackPropertiesAction:" target="-2" id="5JQ-hH-UwX"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="95R-UB-ln2">
                        <rect key="frame" x="420" y="7" width="53" height="32"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="mL6-Xn-rv1">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="okTrackPropertiesAction:" target="-2" id="Zoe-22-tuh"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="T1Z-yz-IPn">
                        <rect key="frame" x="350" y="7" width="76" height="32"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="lEn-2H-rX9">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="cancelTrackPropertiesAction:" target="-2" id="l5X-U2-U9D"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="f09-Mu-e9x">
                        <rect key="frame" x="287" y="7" width="69" height="32"/>
                        <buttonCell key="cell" type="push" title="Apply" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="PrG-Dk-zQ3">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="applyTrackPropertiesAction:" target="-2" id="lL2-Sm-Jxc"/>
                            <binding destination="-2" name="enabled" keyPath="self.modified" id="50p-hd-YEo"/>
                        </connections>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstItem="z74-7V-xI3" firstAttribute="top" secondItem="iZJ-f1-AAo" secondAttribute="top" id="2hd-V6-flj"/>
                    <constraint firstItem="ccs-Rn-ESN" firstAttribute="leading" secondItem="jxY-7Q-v5B" secondAttribute="trailing" constant="8" id="AH6-A3-v5O"/>
                    <constraint firstItem="T1Z-yz-IPn" firstAttribute="leading" secondItem="f09-Mu-e9x" secondAttribute="trailing" constant="8" id="G5i-oc-MCr"/>
                    <constraint firstAttribute="bottom" secondItem="jxY-7Q-v5B" secondAttribute="bottom" constant="14" id="H2l-JU-atP"/>
                    <constraint firstItem="f09-Mu-e9x" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="ccs-Rn-ESN" secondAttribute="trailing" constant="20" id="Iws-o0-Nv7"/>
                    <constraint firstItem="95R-UB-ln2" firstAttribute="trailing" secondItem="WRA-LF-8XP" secondAttribute="trailing" id="Jng-c3-0jV"/>
                    <constraint firstItem="jxY-7Q-v5B" firstAttribute="top" secondItem="WRA-LF-8XP" secondAttribute="bottom" constant="8" id="Nnk-lh-AI0"/>
                    <constraint firstItem="jxY-7Q-v5B" firstAttribute="leading" secondItem="WRA-LF-8XP" secondAttribute="leading" id="W3j-aj-Obm"/>
                    <constraint firstItem="f09-Mu-e9x" firstAttribute="top" secondItem="T1Z-yz-IPn" secondAttribute="top" id="Wrn-sC-3wR"/>
                    <constraint firstItem="WRA-LF-8XP" firstAttribute="leading" secondItem="z74-7V-xI3" secondAttribute="leading" id="aIj-Ut-NtI"/>
                    <constraint firstItem="T1Z-yz-IPn" firstAttribute="top" secondItem="95R-UB-ln2" secondAttribute="top" id="gkx-jc-Rzc"/>
                    <constraint firstItem="95R-UB-ln2" firstAttribute="top" secondItem="jxY-7Q-v5B" secondAttribute="top" id="ncG-4s-Gvq"/>
                    <constraint firstItem="WRA-LF-8XP" firstAttribute="trailing" secondItem="z74-7V-xI3" secondAttribute="trailing" id="oBb-Nn-V2y"/>
                    <constraint firstItem="z74-7V-xI3" firstAttribute="leading" secondItem="iZJ-f1-AAo" secondAttribute="leading" constant="14" id="p7G-rp-7Xc"/>
                    <constraint firstItem="WRA-LF-8XP" firstAttribute="top" secondItem="z74-7V-xI3" secondAttribute="bottom" constant="8" symbolic="YES" id="r1f-oq-dhP"/>
                    <constraint firstItem="ccs-Rn-ESN" firstAttribute="top" secondItem="jxY-7Q-v5B" secondAttribute="top" id="rYB-Lh-u45"/>
                    <constraint firstItem="95R-UB-ln2" firstAttribute="leading" secondItem="T1Z-yz-IPn" secondAttribute="trailing" constant="8" id="u3I-wY-zbI"/>
                    <constraint firstAttribute="trailing" secondItem="z74-7V-xI3" secondAttribute="trailing" constant="14" id="uDZ-Sx-1zA"/>
                </constraints>
            </view>
            <point key="canvasLocation" x="442" y="176.5"/>
        </window>
        <window title="Writing Progress" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="Go9-AM-N3h" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="139" y="149" width="427" height="91"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1680" height="920"/>
            <view key="contentView" id="N0k-tN-FWw">
                <rect key="frame" x="0.0" y="0.0" width="427" height="91"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="sXi-dN-WuM">
                        <rect key="frame" x="20" y="49" width="387" height="22"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="3FT-hs-m6e">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Ts3-9D-i7Q">
                        <rect key="frame" x="342" y="13" width="71" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="k3m-g1-J7D">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="cancelWritingAction:" target="-2" id="W7y-Ey-oW1"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <point key="canvasLocation" x="1204.5" y="42.5"/>
        </window>
        <window title="Tag Writer Settings" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="K2G-ka-jb1" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="167" y="107" width="493" height="263"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1680" height="920"/>
            <value key="minSize" type="size" width="493" height="263"/>
            <view key="contentView" id="rWc-j3-tF4">
                <rect key="frame" x="0.0" y="0.0" width="493" height="263"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="C7J-dX-exC">
                        <rect key="frame" x="18" y="226" width="33" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="MP3" id="BkH-L6-XJE">
                            <font key="font" metaFont="systemBold"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="pnV-tv-6F6">
                        <rect key="frame" x="30" y="202" width="94" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Write ID3v2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="c67-Ea-KCg">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="mp3WriteID3v2Action:" target="-2" id="08d-Gw-QlJ"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="bBF-e0-1cT">
                        <rect key="frame" x="30" y="182" width="91" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Strip ID3v2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="2HW-hT-MQx">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="mp3StripID3v2Action:" target="-2" id="dF4-Y5-8Xm"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="qPw-ml-B2c">
                        <rect key="frame" x="128" y="202" width="94" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Write ID3v1" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="dgA-jb-3rz">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="mp3WriteID3v1Action:" target="-2" id="rNl-Wh-bim"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="OZR-AQ-7gr">
                        <rect key="frame" x="226" y="202" width="99" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Write APEv2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="NwL-Sc-Mcw">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="mp3WriteAPEv2Action:" target="-2" id="zaa-XU-bLJ"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dbC-Xj-n5r">
                        <rect key="frame" x="226" y="182" width="96" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Strip APEv2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="0ZV-GO-OaI">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="mp3StripAPEv2Action:" target="-2" id="7yw-Bg-rnM"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Rbd-3g-GW6">
                        <rect key="frame" x="128" y="182" width="91" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Strip ID3v1" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="839-Lz-82A">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="mp3StripID3v1Action:" target="-2" id="PNt-do-wxW"/>
                        </connections>
                    </button>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="AXD-p9-yL8">
                        <rect key="frame" x="30" y="157" width="88" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="ID3v2 version" id="weU-wA-xp6">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="vNc-eC-e1z">
                        <rect key="frame" x="122" y="152" width="354" height="26"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="Oht-Vf-tGq">
                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="menu"/>
                            <menu key="menu" id="7S8-PU-dtg">
                                <items>
                                    <menuItem title="2.3 (Recommended)" id="xIc-LZ-sC5" userLabel="I">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                    </menuItem>
                                    <menuItem title="2.4" id="YQc-xA-gKs"/>
                                </items>
                            </menu>
                        </popUpButtonCell>
                        <connections>
                            <action selector="mp3ID3v2VersionChangeAction:" target="-2" id="69H-bH-vYZ"/>
                        </connections>
                    </popUpButton>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="gnF-cL-io2">
                        <rect key="frame" x="30" y="128" width="163" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="ID3v1 character encoding" id="anM-lS-6sK">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Mlq-sS-Uzy">
                        <rect key="frame" x="199" y="125" width="274" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" title="iso8859-1" drawsBackground="YES" id="BG6-uL-zu8">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <action selector="mp3ID3v1CharsetChangeAction:" target="-2" id="o71-cL-0iX"/>
                        </connections>
                    </textField>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2GR-u7-ih4">
                        <rect key="frame" x="18" y="103" width="30" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="APE" id="rSi-RZ-go7">
                            <font key="font" metaFont="systemBold"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="v1h-NM-qKW">
                        <rect key="frame" x="30" y="79" width="105" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Write ID3v2.4" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="wly-cz-D0z">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="apeWriteID3v2Action:" target="-2" id="HPf-dW-vwU"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5LG-8B-V5Y">
                        <rect key="frame" x="30" y="59" width="91" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Strip ID3v2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="G9l-gF-jcL">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="apeStripID3v2Action:" target="-2" id="iyl-Xj-g0w"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="pj6-4k-mZ8">
                        <rect key="frame" x="139" y="79" width="99" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Write APEv2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="qAd-Ol-Mbb">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="apeWriteAPEv2Action:" target="-2" id="dsz-XQ-clL"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="hIg-o4-UUx">
                        <rect key="frame" x="139" y="59" width="96" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Strip APEv2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="zgD-Cu-qAf">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="apeStripAPEv2Action:" target="-2" id="XUY-Ty-zFo"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="u9s-YX-mSj">
                        <rect key="frame" x="381" y="79" width="94" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Write ID3v1" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="tNF-xr-F7B">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="wvWriteID3v1Action:" target="-2" id="0Ov-nC-poH"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="S5W-Sk-8gE">
                        <rect key="frame" x="381" y="59" width="91" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Strip ID3v1" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="qrQ-vP-w36">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="wvStripID3v1Action:" target="-2" id="fS0-i7-bvL"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dKd-kg-lir">
                        <rect key="frame" x="400" y="13" width="76" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="push" title="Close" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="G4j-Pr-T4O">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="tagWriterSettingsCloseAction:" target="-2" id="Deg-YJ-4m3"/>
                        </connections>
                    </button>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="cAt-nd-DyC">
                        <rect key="frame" x="266" y="103" width="61" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="WavPack" id="4au-e5-ozt">
                            <font key="font" metaFont="systemBold"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9Wb-gR-2Ep">
                        <rect key="frame" x="278" y="59" width="96" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Strip APEv2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="rRb-vH-3sZ">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="wvStripAPEv2Action:" target="-2" id="Yfn-vI-GSC"/>
                        </connections>
                    </button>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="VAe-Oo-Pio">
                        <rect key="frame" x="278" y="79" width="99" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="check" title="Write APEv2" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="wAc-by-ORg">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="wvWriteAPEv2Action:" target="-2" id="9Ep-5N-FY7"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <point key="canvasLocation" x="681.5" y="550.5"/>
        </window>
        <menu id="rFk-uf-gVv">
            <items>
                <menuItem title="Edit" id="1QF-k7-M8X">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="editValueAction:" target="-2" id="tjO-gd-axn"/>
                        <binding destination="-2" name="enabled" keyPath="singleValueSelected" id="ErX-m2-r4N"/>
                    </connections>
                </menuItem>
                <menuItem title="Edit (in-place)" id="8kT-p7-IkM">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="editInPlaceAction:" target="-2" id="LeB-gi-rSK"/>
                        <binding destination="-2" name="enabled" keyPath="singleValueSelected" id="NaZ-Ux-RVn"/>
                    </connections>
                </menuItem>
                <menuItem title="Remove" id="Ch6-4e-5ZL">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="delete:" target="-2" id="nVn-P7-i3b"/>
                    </connections>
                </menuItem>
                <menuItem title="Crop" id="cb8-tM-4ht">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="editCropAction:" target="-2" id="Xme-re-Oad"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="Cgu-8X-mdT"/>
                <menuItem title="Cut" id="bg5-Wl-MUE">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem title="Copy" id="kcl-Xc-8ew">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem title="Paste" id="BrT-o2-yeQ">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="WYa-XA-cpR"/>
                <menuItem title="Capitalize" id="nrR-yu-O7O">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="editCapitalizeAction:" target="-2" id="dNM-ML-NyO"/>
                    </connections>
                </menuItem>
                <menuItem title="Clean up" id="fLE-ar-dOh">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem title="Format from other fields..." id="y0k-UM-tTS">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="CR5-qJ-M3V"/>
                <menuItem title="Add new field..." id="ncW-KU-0jS">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="addNewField:" target="-2" id="TqS-Wo-4sk"/>
                    </connections>
                </menuItem>
                <menuItem title="Paste fields" id="AXg-4X-eyu">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem title="Automatically fill values..." id="giZ-O8-CWS">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
            </items>
            <point key="canvasLocation" x="872.5" y="469.5"/>
        </menu>
        <window title="Edit Value" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" id="X7d-wt-Bpc" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="167" y="107" width="412" height="237"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1680" height="920"/>
            <value key="minSize" type="size" width="412" height="237"/>
            <view key="contentView" id="YV3-iK-vda">
                <rect key="frame" x="0.0" y="0.0" width="412" height="237"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="LfM-af-kG6">
                        <rect key="frame" x="18" y="200" width="376" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Field name:" id="cP6-qb-QKM">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Gfu-7R-rdm">
                        <rect key="frame" x="20" y="170" width="372" height="22"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="WQs-qQ-kbY">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="zrb-t1-PRc">
                        <rect key="frame" x="18" y="145" width="376" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Field value:" id="ooR-xW-mW4">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <scrollView fixedFrame="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="e4p-0U-7FG">
                        <rect key="frame" x="20" y="49" width="372" height="88"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" drawsBackground="NO" id="1Lf-Y3-NZd">
                            <rect key="frame" x="1" y="1" width="355" height="86"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <textView importsGraphics="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" continuousSpellChecking="YES" allowsUndo="YES" usesRuler="YES" allowsNonContiguousLayout="YES" quoteSubstitution="YES" dashSubstitution="YES" spellingCorrection="YES" smartInsertDelete="YES" id="dyp-1d-Sb5">
                                    <rect key="frame" x="0.0" y="0.0" width="355" height="86"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="355" height="86"/>
                                    <size key="maxSize" width="463" height="10000000"/>
                                    <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                </textView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="ddp-Wh-dlO">
                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="RJh-LH-mg4">
                            <rect key="frame" x="356" y="1" width="15" height="86"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="P7h-NM-a8T">
                        <rect key="frame" x="339" y="13" width="59" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="7BC-CI-Mh1">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="okEditValuePanelAction:" target="-2" id="rnP-Si-WSt"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Ojd-2p-eV5">
                        <rect key="frame" x="257" y="13" width="82" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="Q4W-Rv-bQi">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="cancelEditValuePanelAction:" target="-2" id="Vg9-c2-gU3"/>
                        </connections>
                    </button>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Zer-HN-RES">
                        <rect key="frame" x="18" y="20" width="273" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Use ; character to separate multiple values." id="sNm-qj-ouB">
                            <font key="font" metaFont="menu" size="11"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                </subviews>
            </view>
            <point key="canvasLocation" x="700" y="-196"/>
        </window>
        <window title="Edit Value" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" id="jBF-bj-4TO" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="167" y="107" width="419" height="328"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1680" height="920"/>
            <value key="minSize" type="size" width="412" height="237"/>
            <view key="contentView" id="dgD-Wd-dcE">
                <rect key="frame" x="0.0" y="0.0" width="419" height="328"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="80o-j9-3mB">
                        <rect key="frame" x="18" y="291" width="376" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Field name:" id="drh-YT-Ibl">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="E4X-Mq-zGu">
                        <rect key="frame" x="20" y="261" width="379" height="22"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="aqy-f6-KHc">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="OAp-dz-S8H">
                        <rect key="frame" x="346" y="13" width="59" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="mzR-WI-CoN">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="okEditMultipleValuesAction:" target="-2" id="Aje-WM-WQ4"/>
                        </connections>
                    </button>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="rxu-f8-jc7">
                        <rect key="frame" x="18" y="20" width="273" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Use ; character to separate multiple values." id="6D4-Dz-z6u">
                            <font key="font" metaFont="menu" size="11"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <tabView fixedFrame="YES" initialItem="pVu-66-Scr" translatesAutoresizingMaskIntoConstraints="NO" id="N98-om-YUS">
                        <rect key="frame" x="13" y="40" width="393" height="219"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <font key="font" metaFont="system"/>
                        <tabViewItems>
                            <tabViewItem label="Single Value" identifier="singleValue" id="DgD-uQ-MzG">
                                <view key="view" id="T7M-Kp-0sj">
                                    <rect key="frame" x="10" y="33" width="373" height="173"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <scrollView fixedFrame="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FXC-gS-8hi">
                                            <rect key="frame" x="-3" y="-3" width="379" height="173"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <clipView key="contentView" drawsBackground="NO" id="rdI-Ut-P5v">
                                                <rect key="frame" x="1" y="1" width="362" height="171"/>
                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                <subviews>
                                                    <textView importsGraphics="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" continuousSpellChecking="YES" allowsUndo="YES" usesRuler="YES" allowsNonContiguousLayout="YES" quoteSubstitution="YES" dashSubstitution="YES" spellingCorrection="YES" smartInsertDelete="YES" id="HW1-0g-xzq">
                                                        <rect key="frame" x="0.0" y="0.0" width="362" height="171"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                        <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        <size key="minSize" width="362" height="171"/>
                                                        <size key="maxSize" width="463" height="10000000"/>
                                                        <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                    </textView>
                                                </subviews>
                                            </clipView>
                                            <scroller key="horizontalScroller" hidden="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="uoh-Ma-JGu">
                                                <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                            <scroller key="verticalScroller" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="Mcn-GB-Vtq">
                                                <rect key="frame" x="363" y="1" width="15" height="171"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                        </scrollView>
                                    </subviews>
                                </view>
                            </tabViewItem>
                            <tabViewItem label="Individual Values" identifier="individualValues" id="pVu-66-Scr">
                                <view key="view" id="BGY-4P-QzG">
                                    <rect key="frame" x="10" y="33" width="373" height="173"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Js4-FC-fsc">
                                            <rect key="frame" x="-3" y="-3" width="379" height="173"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <clipView key="contentView" ambiguous="YES" id="KnF-MJ-mJ8">
                                                <rect key="frame" x="1" y="1" width="377" height="171"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <subviews>
                                                    <tableView verticalHuggingPriority="750" ambiguous="YES" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnReordering="NO" columnSelection="YES" multipleSelection="NO" autosaveName="EditMultipleValuesSave" rowSizeStyle="automatic" headerView="6nd-wk-F6a" viewBased="YES" id="ava-EC-Y5p">
                                                        <rect key="frame" x="0.0" y="0.0" width="377" height="148"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                        <size key="intercellSpacing" width="3" height="2"/>
                                                        <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                        <tableColumns>
                                                            <tableColumn identifier="Index" width="40" minWidth="40" maxWidth="1000" id="lJ1-k5-jYS">
                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" title="#">
                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                                </tableHeaderCell>
                                                                <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" title="Text Cell" id="A3z-WH-Ulv">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                                <prototypeCellViews>
                                                                    <tableCellView id="wGT-Eb-bj9">
                                                                        <rect key="frame" x="11" y="1" width="45" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <subviews>
                                                                            <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="uFi-It-kwV">
                                                                                <rect key="frame" x="0.0" y="0.0" width="45" height="17"/>
                                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                                <textFieldCell key="cell" lineBreakMode="truncatingTail" enabled="NO" sendsActionOnEndEditing="YES" title="Table View Cell" id="Onq-lT-nn1">
                                                                                    <font key="font" metaFont="system"/>
                                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                </textFieldCell>
                                                                            </textField>
                                                                        </subviews>
                                                                        <connections>
                                                                            <outlet property="textField" destination="uFi-It-kwV" id="Qrb-xT-ha1"/>
                                                                        </connections>
                                                                    </tableCellView>
                                                                </prototypeCellViews>
                                                            </tableColumn>
                                                            <tableColumn identifier="Item" width="155" minWidth="40" maxWidth="1000" id="Qbe-Fv-Stq">
                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" title="Item">
                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                                                </tableHeaderCell>
                                                                <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" title="Text Cell" id="48G-sT-B62">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                                <prototypeCellViews>
                                                                    <tableCellView id="X33-n8-fNi">
                                                                        <rect key="frame" x="59" y="1" width="155" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <subviews>
                                                                            <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5vY-L6-az5">
                                                                                <rect key="frame" x="0.0" y="0.0" width="155" height="17"/>
                                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                                <textFieldCell key="cell" lineBreakMode="truncatingTail" enabled="NO" sendsActionOnEndEditing="YES" title="Table View Cell" id="apv-eY-BJL">
                                                                                    <font key="font" metaFont="system"/>
                                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                </textFieldCell>
                                                                            </textField>
                                                                        </subviews>
                                                                        <connections>
                                                                            <outlet property="textField" destination="5vY-L6-az5" id="O17-Cp-CFB"/>
                                                                        </connections>
                                                                    </tableCellView>
                                                                </prototypeCellViews>
                                                            </tableColumn>
                                                            <tableColumn identifier="Field" width="135" minWidth="10" maxWidth="3.4028234663852886e+38" id="frW-QU-IzL">
                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Field">
                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </tableHeaderCell>
                                                                <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="7fO-iw-aIN">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                                <prototypeCellViews>
                                                                    <tableCellView id="Yb3-a9-RuJ">
                                                                        <rect key="frame" x="217" y="1" width="139" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <subviews>
                                                                            <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="yhC-rP-aI4">
                                                                                <rect key="frame" x="0.0" y="0.0" width="139" height="17"/>
                                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                                <textFieldCell key="cell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" title="Table View Cell" id="CeQ-f9-g1m">
                                                                                    <font key="font" metaFont="system"/>
                                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                </textFieldCell>
                                                                            </textField>
                                                                        </subviews>
                                                                        <connections>
                                                                            <outlet property="textField" destination="yhC-rP-aI4" id="6Xy-hL-XKh"/>
                                                                        </connections>
                                                                    </tableCellView>
                                                                </prototypeCellViews>
                                                            </tableColumn>
                                                        </tableColumns>
                                                    </tableView>
                                                </subviews>
                                            </clipView>
                                            <scroller key="horizontalScroller" hidden="YES" verticalHuggingPriority="750" horizontal="YES" id="j71-7Y-hYS">
                                                <rect key="frame" x="1" y="74" width="370" height="15"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                            <scroller key="verticalScroller" hidden="YES" verticalHuggingPriority="750" horizontal="NO" id="toW-TF-aev">
                                                <rect key="frame" x="224" y="17" width="15" height="102"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </scroller>
                                            <tableHeaderView key="headerView" id="6nd-wk-F6a">
                                                <rect key="frame" x="0.0" y="0.0" width="377" height="23"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                            </tableHeaderView>
                                        </scrollView>
                                    </subviews>
                                </view>
                            </tabViewItem>
                        </tabViewItems>
                    </tabView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ypR-hz-AA3">
                        <rect key="frame" x="270" y="13" width="82" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="pdM-yV-LE4">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="cancelEditMultipleValuesPanel:" target="-2" id="Gmr-c2-HJG"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <point key="canvasLocation" x="998.5" y="182"/>
        </window>
        <window title="New Field" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" id="Wvw-8e-FjO" userLabel="New Field" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="167" y="107" width="412" height="115"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1680" height="920"/>
            <view key="contentView" id="bJn-ft-ys2">
                <rect key="frame" x="0.0" y="0.0" width="412" height="115"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="8vS-pJ-KU0">
                        <rect key="frame" x="18" y="78" width="376" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Field name:" id="gnd-YD-Ruj">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="FkB-fG-UDj">
                        <rect key="frame" x="20" y="48" width="372" height="22"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="OIS-jC-bf2">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="JWr-Zf-VQR">
                        <rect key="frame" x="339" y="13" width="59" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="GTg-3s-dxT">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="okAddFieldPanelAction:" target="-2" id="NsV-XD-zbx"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="dak-yo-1Ay">
                        <rect key="frame" x="257" y="13" width="82" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="NdS-hd-Vht">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="cancelAddFieldPanelAction:" target="-2" id="5vr-2l-FbZ"/>
                        </connections>
                    </button>
                    <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="x7e-xG-aal">
                        <rect key="frame" x="18" y="20" width="273" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="The field already exists." id="i0Z-nU-uch">
                            <font key="font" metaFont="menu" size="11"/>
                            <color key="textColor" red="1" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                </subviews>
            </view>
            <point key="canvasLocation" x="1208" y="-258"/>
        </window>
    </objects>
</document>
