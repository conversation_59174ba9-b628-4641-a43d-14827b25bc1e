## Process this file with automake to produce Makefile.in

noinst_LIBRARIES = libgbwidgets.a

# maybe this should be $(GLADE_GTK_CFLAGS) ?
INCLUDES = $(GLADE_GTK_CFLAGS) \
	$(GLADE_DEPRECATION_CFLAGS)

libgbwidgets_a_SOURCES = \
	gbaboutdialog.c \
	gbaccellabel.c \
	gbalignment.c \
	gbarrow.c \
	gbaspectframe.c \
	gbbutton.c \
	gbcalendar.c \
	gbcellview.c \
	gbcheckbutton.c \
	gbcheckmenuitem.c \
	gbclist.c \
	gbcolorbutton.c \
	gbcolorselection.c \
	gbcolorselectiondialog.c \
	gbcombo.c \
	gbcombobox.c \
	gbcomboboxentry.c \
	gbctree.c \
	gbcustom.c \
	gbcurve.c \
	gbdialog.c \
	gbdrawingarea.c \
	gbentry.c \
	gbeventbox.c \
	gbexpander.c \
	gbfilechooserbutton.c \
	gbfilechooserwidget.c \
	gbfilechooserdialog.c \
	gbfileselection.c \
	gbfixed.c \
	gbfontbutton.c \
	gbfontselection.c \
	gbfontselectiondialog.c \
	gbframe.c \
	gbgammacurve.c \
	gbhandlebox.c \
	gbhbox.c \
	gbhbuttonbox.c \
	gbhpaned.c \
	gbhruler.c \
	gbhscale.c \
	gbhscrollbar.c \
	gbhseparator.c \
	gbiconview.c \
	gbimage.c \
	gbimagemenuitem.c \
	gbinputdialog.c \
	gblabel.c \
	gblayout.c \
	gblist.c \
	gblistitem.c \
	gbmenu.c \
	gbmenubar.c \
	gbmenuitem.c \
	gbmenutoolbutton.c \
	gbnotebook.c \
	gboptionmenu.c \
	gbpreview.c \
	gbprogressbar.c \
	gbradiobutton.c \
	gbradiomenuitem.c \
	gbradiotoolbutton.c \
	gbscrolledwindow.c \
	gbseparatormenuitem.c \
	gbseparatortoolitem.c \
	gbspinbutton.c \
	gbstatusbar.c \
	gbtable.c \
	gbtextview.c \
	gbtogglebutton.c \
	gbtoggletoolbutton.c \
	gbtoolbar.c \
	gbtoolbutton.c \
	gbtoolitem.c \
	gbtreeview.c \
	gbvbox.c \
	gbvbuttonbox.c \
	gbviewport.c \
	gbvpaned.c \
	gbvruler.c \
	gbvscale.c \
	gbvscrollbar.c \
	gbvseparator.c \
	gbwindow.c
