﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="debug Windows|Win32">
      <Configuration>debug Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="release Windows|Win32">
      <Configuration>release Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{202B5BA7-8C95-0F1E-D5D3-C0C3417DED72}</ProjectGuid>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>resources</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <OutDir>bin\debug\plugins\</OutDir>
    <IntDir>obj\Windows\debug\resources\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <OutDir>bin\release\plugins\</OutDir>
    <IntDir>obj\Windows\release\resources\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="src\main.c">
      <FileType>Document</FileType>
      <Command Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">mkdir -p bin/debug/pixmaps
cp icons/32x32/deadbeef.png bin/debug
cp pixmaps/*.png bin/debug/pixmaps/
mkdir -p bin/debug/plugins/convpresets
cp -r plugins/converter/convpresets bin/debug/plugins/</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">mkdir -p bin/release/pixmaps
cp icons/32x32/deadbeef.png bin/release
cp pixmaps/*.png bin/release/pixmaps/
mkdir -p bin/release/plugins/convpresets
cp -r plugins/converter/convpresets bin/release/plugins/</Command>
      <Outputs>src/main.c_fake</Outputs>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="libwin.vcxproj">
      <Project>{EAFA6F0B-D69C-9AE1-BF57-AE35AB982132}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>