NOTE: this version of GLADE is a fork of glade 2.12.2
made specifically for DeaDBeeF project.
it is NOT THE ORIGINAL VERSION.

main changes:
* builds with latest GTK2 runtime
* generates code which is compatible with both GTK2 and GTK3

=======================================================================

			     Welcome to Glade
                             ================

	 (a user interface builder for the GTK+ toolkit and GNOME)

                 Glade's Home Page : http://glade.gnome.org


Glade 2.12 Release Notes
=======================

Glade 2.12 requires GTK+ 2.8, and GNOME 2.12 (if you want GNOME support). 
Interfaces created with Glade 2.12 will also require GTK+ 2.8 and GNOME 2.12,
so if you want your application to work with previous versions of GTK+ or
GNOME you should use a previous version of Glade.

Compatable Versions:

  Glade 2.12.x - GNOME 2.12.x			(GTK+ 2.8.x)
  Glade 2.10.x - GNOME 2.10.x			(GTK+ 2.6.x)
  Glade 2.6.x  - GNOME 2.6.x/2.8.x		(GTK+ 2.4.x)
  Glade 2.0.x  - GNOME 2.0.x/2.2.x/2.4.x	(GTK+ 2.0.x/2.2.x)

  Glade 0.6.4  - GNOME 1.4.x			(GTK+ 1.2.x) (*Unsupported*)


Glade GTK+ 2 / GNOME 2 Release Notes
====================================

Note that the GTK+ 2 / GNOME 2 version of Glade is called glade-2. This is
so that it can be installed easily alongside the older version of Glade.

Also note that the use of libglade is recommend for large projects, rather
than using the builtin C code generation. libglade loads the Glade XML files
at runtime, and you can keep each window/dialog in a separate XML file,
making it more easily scalable.


General Information
===================

Glade is a RAD tool to enable quick & easy development of user interfaces
for the GTK+ toolkit and the GNOME desktop environment. It also contains
built-in support for generating the C source code needed to recreate the
interfaces.

The user interfaces designed in Glade are stored in XML format,
enabling easy integration with external tools. Several tools are already
available which can turn the XML files into source code in other languages
such as C++, Perl and Python. Other tools such as libglade can load the XML
files and create the interfaces at runtime (see the Languages Supported
section below). The DTD for the XML files is included with libglade,
and is also at http://glade.gnome.org/glade-2.0.dtd.

Glade is primarily aimed at Linux and other Unix variants, though it can be
run on Windows platforms (without the GNOME support). A port to Windows can
be found at http://wingtk.sourceforge.net.


Glade Documentation
===================

A User Guide and FAQ are available from the 'Help' menu in Glade.

See the 'TODO' file for current status & planned future developments.


License
=======

Glade is distributed under the GNU General Public License (GPL), as described
in the COPYING file.

Note that you are free to use whatever license you like for the source code
generated by Glade. (We do not consider the code generated by Glade to be
'a work based on the Program' as described at the start of the GPL.)


Requirements
============

 o GTK+ 2.8.0 or above - http://www.gtk.org
   You also need the glib, pango and atk libraries.
   Make sure you have the devel packages as well, as these will contain the
   header files which you will need to compile C applications.

 o GNOME 2.12.0 or above if you want GNOME support. http://www.gnome.org
   This is comprised of a number of packages and is quite tricky to build,
   so it is better to use the packages from your distribution if possible.

 o gail - provides support for accessibility for GTK+ applications.

 o libgail-gnome - accessibility support for GNOME applications, but I think
   you only need this if you are using Bonobo controls.

 o libxml 2.4.1 - used to parse the XML files. If you have GNOME 2 you
   should already have this.

 o Automake 1.4 - ftp://ftp.gnu.org/pub/gnu/automake
   This is needed to build the C source code generated by Glade.

 o Autoconf 2.52 - ftp://ftp.gnu.org/pub/gnu/autoconf
   This is needed to build the C source code generated by Glade.

 o Gettext 0.10.40 - ftp://ftp.gnu.org/pub/gnu/gettext
   This is needed if you want to build applications with support for gettext.
   It is included in most Linux distributions already.
   (Gettext provides support for translating the strings in your application
    into other languages such as French, German, Spanish etc.)

 o Scrollkeeper 0.3.10 - http://scrollkeeper.sourceforge.net
   This manages the DocBook documentation on a system.



Languages Supported
===================

Note that some of these may not have been updated to handle the latest
versions of GTK+ and GNOME. You'll have to check.

 o C - Glade can generate C code itself.
       The libglade library is also available. It creates the user interface
       at runtime by loading the XML files generated by Glade.
       See http://www.daa.com.au/~james/gnome/
       For large projects, libglade is the preferred method, since it has
       proved to be more easily scalable.

GNOME Platform bindings (these have stable bindings for GTK+ and GNOME):

 o C++ - the Gtkmm C++ bindings for GTK+ include support for libglade.
       See http://www.gtkmm.org/
       Alternatively Glade-- can be used to generate C++ code.
       See http://home.wtal.de/petig/

 o Java - the Java-GNOME bindings include support for libglade.
       See http://java-gnome.sourceforge.net/

 o Perl - the Gtk-Perl bindings include support for libglade.
       See http://www.gtkperl.org/
       Alternatively Glade-Perl can generate Perl code.
       See http://www.glade-perl.connectfree.co.uk/index.html

 o Python - the PyGtk python bindings for GTK+ include support for libglade.
       See http://www.pygtk.org/

Other bindings:

 o C# - the Gtk# bindings include support for libglade.
       See http://gtk-sharp.sourceforge.net/

 o Ada - the GtkAda bindings for GTK+ includes support for generating Ada code.
       See http://libre.act-europe.fr/GtkAda/

 o Pike - the Pike binding for GTK+ includes support for libglade in the
       GTK.GladeXML class. See http://pike.ida.liu.se/

 o Eiffel - eGlade can generate Eiffel code.
       See http://www.lfix.co.uk/eiflib/index.html#eglade

 o Ruby - there are Ruby bindings for libglade.
       See http://www.ruby-lang.org/en/raa-list.rhtml?name=Ruby%2FLibGlade

 o Haskell - the Gtk2Hs Haskell bindings include support for libglade.
       See http://gtk2hs.sourceforge.net/

 o Objective Caml - the LablGTK Objective Caml bindings support libglade.
       See http://wwwfun.kurims.kyoto-u.ac.jp/soft/olabl/lablgtk.html

 o Scheme - the guile-gnome bindings include support for libglade.
       See http://home.gna.org/guile-gnome/

The Glade web site may contain more up-to-date links.
Also see the GTK+ Language Bindings Page at http://www.gtk.org/bindings.html
and the GNOME page at http://developer.gnome.org/arch/lang/.
If a language includes bindings for libglade you can use that.

Please note that these packages are maintained independently of Glade, and
so any bug reports should be sent to the appropriate package maintainer.


Installation
============

See the file 'INSTALL'


Mailing Lists
=============

There are two mailing lists related to Glade:

  <EMAIL>    - For discussions about using Glade to build
			      applications.

  <EMAIL>    - For discussions about the development of
			      Glade itself.

To subscribe, unsubscribe, or view the archives of the lists go to the
mailman web pages at:

  http://lists.ximian.com/mailman/listinfo/glade-users
  http://lists.ximian.com/mailman/listinfo/glade-devel


Alternatively you can use email to perform commands by mailing:

  <EMAIL>
  <EMAIL>

with commands such as 'subscribe' and 'help' in the subject line or
the message body.


There is also a mailing list for the C++ code generator here:
  http://lists.gnome.org/mailman/listinfo/glademm-list


How to report bugs
==================

To report a bug in Glade itself, go to http://bugzilla.gnome.org and fill out
a bug report, specifying 'glade' as the product.

Alternatively send <NAME_EMAIL>, or to the glade-users list
if you are subscribed.

(Please don't send bug reports about the external code generators.
They should be sent to the appropriate package maintainer.)

In the bug report include:

* The version of Glade.

* Information about your system. For instance:

   - What version of GTK+.
   - If you are using the GNOME version of Glade, and if so which version
     of GNOME you are using.
   - What operating system and version.

  And anything else you think is relevant.

* How to reproduce the bug. 

* If you can, send a stack trace. To do that, start gdb with 'gdb glade-2',
  start glade with 'r', do whatever causes the crash, and then type 'bt' in
  the debugger to print a stack trace.
  See http://bugzilla.gnome.org/getting-traces.cgi for more details.


Submitting Patches
==================

Please use diff -ru to create the patch, and send it as an attachment
(gzipped if it is very large). If it entails major changes to Glade, please
discuss it on the glade-devel list first.


Damon Chaplin (<EMAIL>), 9 Oct 2005
