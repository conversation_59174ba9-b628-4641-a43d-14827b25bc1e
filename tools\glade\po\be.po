# translation of glade.glade-gnome2-branch.be.po to Belarusian
# translation of glade.glade-gnome2-branch.be.po to belarusian
# Copyright (C) 1998-2002, The Free Software Foundation
# <PERSON><PERSON> <<EMAIL>>
# <PERSON><PERSON> <<EMAIL>>, 2003
#
msgid ""
msgstr ""
"Project-Id-Version: glade.glade-gnome2-branch.be\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2005-08-26 13:38+0200\n"
"PO-Revision-Date: 2003-04-14 07:34+0300\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Belarusian <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: KBabel 0.9.6\n"

#: ../glade-2.desktop.in.h:1
msgid "Design user interfaces"
msgstr ""

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr "Glade - распрацоўнік спалучэньняў"

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "Парамэтры экранае сеткі"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "Гар. прамежак:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "Вэрт. прамежак:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "Стыль экранае сеткі:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "Кропкі"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "Лініі"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "Парамэтры сеткі прывязкі"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "Гар. крок прывязкі:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "Левы"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "Правы"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "Вэрт. крок прывязкі"

#: ../glade/editor.c:532
msgid "Top"
msgstr "Верх"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "Ніз"

#: ../glade/editor.c:741
#, fuzzy
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "Віджэт GnomeDockItem можа быць устаўлены толькі ў GnomeDock."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "Немагчыма уставіць віджэт GtkScrolledWindow."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "Немагчыма ўставіць віджэт GtkViewport."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "Немагчыма дадаць новы віджэт."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"Немагчыма дадаць элемэнт у пазначаную пазыцыю.\n"
"\n"
"Падказка: GTK+ выкарыстоўвае кантейнэры для разьмяшчэньня элемэнтаў.\n"
"Паспрабуйце выдаліць існуючы элемэнт і выкарыстоўваць замест\n"
"яго box ці таблічны кантэйнэр.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "Ня атрымалася выдаліць віджэт."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "Віджэт ня можа быць выдалены"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"Элемэнт створаны аўтаматычна як частка бацькоўскага элемэнта й ня можа быць "
"выдалены."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "Шырыня мяжы:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "Шырыня мяжы вакол кантэйнэру"

#: ../glade/gbwidget.c:1745
msgid "Select"
msgstr "Выбраць"

#: ../glade/gbwidget.c:1767
msgid "Remove Scrolled Window"
msgstr "Выдаліць акно з пракруткай"

#: ../glade/gbwidget.c:1776
msgid "Add Scrolled Window"
msgstr "Дадаць акно з пракруткай"

#: ../glade/gbwidget.c:1797
msgid "Remove Alignment"
msgstr "Выбраць раўнаньне"

#: ../glade/gbwidget.c:1805
msgid "Add Alignment"
msgstr "Дадаць раўнаньне"

#: ../glade/gbwidget.c:1820
msgid "Remove Event Box"
msgstr "Выдаліць элемэнт падзеі"

#: ../glade/gbwidget.c:1828
msgid "Add Event Box"
msgstr "Дадаць элемэнт падзеі"

#: ../glade/gbwidget.c:1838
msgid "Redisplay"
msgstr "Перарысаваць"

#: ../glade/gbwidget.c:1849
msgid "Cut"
msgstr "Выразаць"

#: ../glade/gbwidget.c:1856 ../glade/property.c:892 ../glade/property.c:5135
msgid "Copy"
msgstr "Капіяваць"

#: ../glade/gbwidget.c:1865 ../glade/property.c:904
msgid "Paste"
msgstr "Уставіць"

#: ../glade/gbwidget.c:1877 ../glade/property.c:1580 ../glade/property.c:5126
msgid "Delete"
msgstr "Выдаліць"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2403 ../glade/gbwidget.c:2472
msgid "N/A"
msgstr "Адсутнічае"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3202
msgid "replacing child of container - not implemented yet\n"
msgstr "замена падпарадкаванага аб'екта кантэйнэра яшчэ ня рэалізавана\n"

#: ../glade/gbwidget.c:3430
msgid "Couldn't insert GtkAlignment widget."
msgstr "Ня атрымалася ўставіць віджэт GtkAlignment."

#: ../glade/gbwidget.c:3470
msgid "Couldn't remove GtkAlignment widget."
msgstr "Ня атрымалася выдаліць віджэт GtkAlignment."

#: ../glade/gbwidget.c:3494
msgid "Couldn't insert GtkEventBox widget."
msgstr "Ня атрымалася уставіць віджэт GtkEventBox."

#: ../glade/gbwidget.c:3533
msgid "Couldn't remove GtkEventBox widget."
msgstr "Ня атрымалася ўставіць віджэт GtkEventBox."

#: ../glade/gbwidget.c:3568
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "Ня атрымалася ўставіць віджэт GtkScrolledWindow."

#: ../glade/gbwidget.c:3607
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "Ня атрымалася ўставіць віджэт GtkScrolledWindow."

#: ../glade/gbwidget.c:3721
msgid "Remove Label"
msgstr "Выдаліць метку"

#: ../glade/gbwidgets/gbaboutdialog.c:78
#, fuzzy
msgid "Application Name"
msgstr "Радок дастасаваньня Gnome"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "Лягатып:"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "Карцінка якая выкарыстоўваецца як лягатып"

#: ../glade/gbwidgets/gbaboutdialog.c:104 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "Назва праграмы:"

#: ../glade/gbwidgets/gbaboutdialog.c:104
#, fuzzy
msgid "The name of the application"
msgstr "Назва гэтага віджэта"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "Камэнтары:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
#, fuzzy
msgid "Additional information, such as a description of the application"
msgstr ""
"Дадатковая інфармацыя, як апісаньне пакету й яго хатняя старонка ў Інтэрнэце"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "Аўтарскія правы:"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "Звесткі аб аўтарскім праве"

#: ../glade/gbwidgets/gbaboutdialog.c:108
msgid "Website URL:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:108
#, fuzzy
msgid "The URL of the application's website"
msgstr "Ці мусіць быць пабудавана дастасаваньне Gnome"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "Website Label:"
msgstr "Метка мэню:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "The label to display for the link to the website"
msgstr "Відарыс які адлюстроўваецца ў самым версе старонкі"

#: ../glade/gbwidgets/gbaboutdialog.c:111 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "Ліцэнзыя:"

#: ../glade/gbwidgets/gbaboutdialog.c:111
#, fuzzy
msgid "The license details of the application"
msgstr "Стыль рэльефа для кнопкі"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "Аўтары:"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "Аўтары пакету, па адным на радок"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "Documenters:"
msgstr "Стваральнікі дакумэнтацыі:"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
msgid "The documenters of the package, one on each line"
msgstr "Аўтары дакумэнтацыі, па адным на радок"

#: ../glade/gbwidgets/gbaboutdialog.c:115
msgid "Artists:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:115
#, fuzzy
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr "Аўтары пакету, па адным на радок"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid "Translators:"
msgstr "Перакладчыкі:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""
"Перакладчыкі пакета. Тутака будзе зьмешчана інфармацыя аб перакладчыках, "
"якая бярэцца з .ро файлаў"

#: ../glade/gbwidgets/gbaboutdialog.c:559
#, fuzzy
msgid "About Dialog"
msgstr "Дыялёг Gnome \"Пра праграму\""

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "Метка з клявішай паскарэньня"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:180 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "Раўн. па X:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "Гарызантальнае раўнаньне віджэту-нашчадку"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:183 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Раўн. па Y:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "Вэртыкальнае раўнаньне віджэта-нашчадка"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "X Маштаб:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "Маштаб па гарызанталі віджэту-нашчадку"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Y Маштаб:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "Маштаб па вэртыкалі віджэта-нашчадка"

#: ../glade/gbwidgets/gbalignment.c:85
#, fuzzy
msgid "Top Padding:"
msgstr "Гар. запаўненьне:"

#: ../glade/gbwidgets/gbalignment.c:86
#, fuzzy
msgid "Space to put above the child widget"
msgstr "Маштаб па гарызанталі віджэту-нашчадку"

#: ../glade/gbwidgets/gbalignment.c:89
#, fuzzy
msgid "Bottom Padding:"
msgstr "Гар. запаўненьне:"

#: ../glade/gbwidgets/gbalignment.c:90
#, fuzzy
msgid "Space to put below the child widget"
msgstr "Маштаб па гарызанталі віджэту-нашчадку"

#: ../glade/gbwidgets/gbalignment.c:93
#, fuzzy
msgid "Left Padding:"
msgstr "Гар. запаўненьне:"

#: ../glade/gbwidgets/gbalignment.c:94
#, fuzzy
msgid "Space to put to the left of the child widget"
msgstr "Маштаб па гарызанталі віджэту-нашчадку"

#: ../glade/gbwidgets/gbalignment.c:97
#, fuzzy
msgid "Right Padding:"
msgstr "Гар. запаўненьне:"

#: ../glade/gbwidgets/gbalignment.c:98
#, fuzzy
msgid "Space to put to the right of the child widget"
msgstr "Гарызантальнае раўнаньне віджэту-нашчадку"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "Раўнаньне"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "Накірунак:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "Накірунак стрэлкі"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "Цень:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "Від ценю стрэлкі"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "Гарызантальнае раўнаньне стрэлкі"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "Вэртыкальнае раўнаньне стрэлкі"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186
msgid "X Pad:"
msgstr "X Прамежак:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "Гарызантальны інтэрвал"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188
msgid "Y Pad:"
msgstr "Y Прамежак:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "Вэртыкальны інтэрвал"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "Стрэлка"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "X Раўн. Меткі:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
msgid "The horizontal alignment of the frame's label widget"
msgstr "Раўнаньне па гарызанталі меткі рамкі"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
msgid "Label Y Align:"
msgstr "Y Раўн. Меткі:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
msgid "The vertical alignment of the frame's label widget"
msgstr "Вэртыкальнае раўнаньне меткі"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "Від ценю рамкі"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "Раўнаньне па гарызанталі нашчадка элемэнта рамкі"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "Стасунак:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr ""
"Стасунак вэртыкальнага і гарызантальнага памераў нашчадка элемэнта рамкі"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "Выкарыст. нашч.:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "Калі патрабуецца вызначаць стасунак памераў па элемэнту-нашчадку"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "Рамка стасунка"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "Гатовая кнопка:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
msgid "The stock button to use"
msgstr "Гатовая кнопка для выкарыстаньня"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:747
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "Метка:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "Тэкст для адлюстраваньня"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:295
#: ../glade/glade_menu_editor.c:813
msgid "Icon:"
msgstr "Значка:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
msgid "The icon to display"
msgstr "Значка для адлюстраваньня"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "Рэльеф кнопак:"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "Стыль рэльефа для кнопкі"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr "Ідэнтэфікатар адказу:"

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""
"Код адказу вяртаецца калі націснута клявіша. Выбярыце адзін са стандартных "
"адказаў ці увядзіце станоўчае цэлае значэньне."

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
#, fuzzy
msgid "If the button grabs focus when it is clicked"
msgstr "Ці зачыняецца дыялёг калі націснута любая кнопка"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr "Выдаліць зьмест кнопкі"

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "Кнопка"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "Загаловак:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "Ці патрабуецца разьмясьціць назву месяца зверху"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "Назвы дзён:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "Калі мусяць адлюстроўвацца назвы дзён"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "Фікс. месяц:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "Ня даць мажлівасьць лістаць месяцы і годы"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "Нумары тыдняў:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "Ці патрабуецца адлюстроўваць нумар тыдню"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "Пнд спачатку:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "Тыдзень пачынаецца з панядзелка"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "Каляндар"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "Колер тла:"

#: ../glade/gbwidgets/gbcellview.c:64
#, fuzzy
msgid "The background color"
msgstr "Колер тла"

#: ../glade/gbwidgets/gbcellview.c:192
#, fuzzy
msgid "Cell View"
msgstr "Прагляд тэкста"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "Першапачаткова Укл.:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "Калі патрабуецца, каб (check) кнопка была першапачаткова уключана"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
msgid "Inconsistent:"
msgstr "Несумяшчальны:"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
msgid "If the button is shown in an inconsistent state"
msgstr "Калі кнопка адлюстравана ў несумяшчальным рэжыме"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "Індыкатар:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "Калі трэба прарысоўваць заўсёды індыкатар"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "Кнопка адзначэньня"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "Калі першапачаткова мэню адзначэньня уключана"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "Элемэнт мэню адзначэньня"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "Новы сьпіс са слупкоў"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "Колькасьць слупкоў:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:127 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "Рэжым Выбару:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "Рэжым выбару сьпіса са слупкоў"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "Адлюстроўваць загалоўкі:"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "Ці патрабуецца адлюстроўваць загалоўкі"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "Тып ценю рамкі сьпіса са слупкоў"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "Сьпіс са слупкоў"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "Выкарыстоўваць альфа-канал:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "Калі мусіць выкарыстоўвацца альфа-канал"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:85
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:242
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "Загаловак:"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "Загаловак дыялёгу выбару колеру"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
#, fuzzy
msgid "Pick a Color"
msgstr "Выбар колеру"

#: ../glade/gbwidgets/gbcolorbutton.c:211
#, fuzzy
msgid "Color Chooser Button"
msgstr "Кнопка адзначэньня"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr "Кіраваньне зацяненьнем:"

#: ../glade/gbwidgets/gbcolorselection.c:63
msgid "If the opacity control is shown"
msgstr "Калі адлюстроўваецца кіраваньне зацяненьнем"

#: ../glade/gbwidgets/gbcolorselection.c:64
msgid "Palette:"
msgstr "Палітра:"

#: ../glade/gbwidgets/gbcolorselection.c:65
msgid "If the palette is shown"
msgstr "Калі палітра адлюстроўваецца"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "Выбар колера"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:70
msgid "Select Color"
msgstr "Выбярыце колер"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:315 ../glade/property.c:1275
msgid "Color Selection Dialog"
msgstr "Акно выбару колера"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "Значэньне ў сьпісе:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "Ці мусіць значэньне быць у сьпісе"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "Добра калі пустое:"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr ""
"Калі дапушчальна пустое значэньне пад час усталяваньня \"Значэньне ў сьпісе\""

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "З улікам рэгістра:"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "Пошук чулы да рэгістра"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "Вык. стрэлак:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "Стрэлкі могуць выкарыстоўвацца для зьмяненьня значэньня"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "Вык. заўсёды:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "Ці працуюць стрэлкі заўсёды, нават калі значэньня няма ў сьпісе"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "Элемэнты:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "Элемэнты камбінаванага сьпіса па адным на радок"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "Камб. Сьпіс"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
#, fuzzy
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "Ці зачыняецца дыялёг калі націснута любая кнопка"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
msgid "Has Frame:"
msgstr "Мае рамку:"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr ""

#: ../glade/gbwidgets/gbcomboboxentry.c:302
#, fuzzy
msgid "Combo Box Entry"
msgstr "Камб. Сьпіс"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "Новае дрэва са слупкоў"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "Рэжым выбару дрэва са слупкоў"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "Від ценю рамкі дрэва са слупкоў"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "Дрэва са слупкоў"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:245
msgid "Type:"
msgstr "Тып:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "Тып дугі"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "X мін.:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "Мінімальнае гарызантальнае значэньне"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "X макс.:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "Максымальнае гарызантальнае значэньне"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Y мін.:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "Мінімальнаее вэртыкальнае значэньне"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Y макс.:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "Максымальнае вэртыкальнае значэньне"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "Дуга"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "Функцыя стварэньня:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "Функцыя, якая стварае віджэт"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "Радок 1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "Першы радковы аргумэнт, які перадаецца функцыі"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "Радок 2:"

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "Другі радковы аргумэнт які перадаецца функцыі"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Цэлае 1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "Першы аргумэнт тыпа \"цэлае\", які перадаецца функцыі"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Цэлае 2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "Другі аргумэнт тыпа \"цэлае\", які перадаецца функцыі"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "Вызначаны карыстальнікам віджэтаў"

#: ../glade/gbwidgets/gbdialog.c:292
msgid "New dialog"
msgstr "Новы дыялёг"

#: ../glade/gbwidgets/gbdialog.c:304
msgid "Cancel, OK"
msgstr "Адмена, Добра"

#: ../glade/gbwidgets/gbdialog.c:313 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1316 ../glade/property.c:5156
msgid "OK"
msgstr "Добра"

#: ../glade/gbwidgets/gbdialog.c:322
msgid "Cancel, Apply, OK"
msgstr "Адмена, Ужыць, Добра"

#: ../glade/gbwidgets/gbdialog.c:331
msgid "Close"
msgstr "Зачыніць"

#: ../glade/gbwidgets/gbdialog.c:340
msgid "_Standard Button Layout:"
msgstr "_Стандартны выгляд кнопкі:"

#: ../glade/gbwidgets/gbdialog.c:349
msgid "_Number of Buttons:"
msgstr "_Колькасьць кнопак:"

#: ../glade/gbwidgets/gbdialog.c:366
msgid "Show Help Button"
msgstr "Адлюстроўваць кнопку дапамогі"

#: ../glade/gbwidgets/gbdialog.c:397
msgid "Has Separator:"
msgstr "Мае дзельнік:"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "Калі дыялёг мае гарызантальны падзяляльнік паміж кнопкамі"

#: ../glade/gbwidgets/gbdialog.c:605
msgid "Dialog"
msgstr "Дыялёг"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "Вобласьць для рысаваньня"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "Рэдакт.:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "Ці можа тэкст рэдагавацца"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "Бачнасьць тэкста:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"Ці будзе адлюстроўвацца тэкст які ўводзіцца карыстальнікам. Калі выключана, "
"тады тэкст які друкуецца будзе адлюстроўвацца зоркамі, што карысна пад час "
"уводу пароляў."

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "Макс. даўжыня:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "Максымальная даўжыня тэкста"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "Тэкст:"

#: ../glade/gbwidgets/gbentry.c:102
msgid "If the entry has a frame around it"
msgstr "Калі элемэнт мае рамку вакол сябе"

#: ../glade/gbwidgets/gbentry.c:103
msgid "Invisible Char:"
msgstr "Нябачны сымбаль:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""
"Сымбаль які выкарыстоўваецца калі тэкст ня бачны, напрыклад пад час уводу "
"пароля"

#: ../glade/gbwidgets/gbentry.c:104
msgid "Activates Default:"
msgstr "Дапомна задзейнічанае:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr ""
"Калі віджэт у вакне задзейнічаны дапомна па націску на клявішу \"Enter\""

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr "Шырыня ў сымбалях:"

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr "Колькасьць сымбаляў у прасторы для поля ўводу"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "Поле уводу тэкста"

#: ../glade/gbwidgets/gbeventbox.c:65
#, fuzzy
msgid "Visible Window:"
msgstr "Бачнасьць:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr ""

#: ../glade/gbwidgets/gbeventbox.c:66
#, fuzzy
msgid "Above Child:"
msgstr "Выкарыст. нашч.:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr ""

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "Элемэнт падзеяў"

#: ../glade/gbwidgets/gbexpander.c:54
#, fuzzy
msgid "Initially Expanded:"
msgstr "Першапачаткова Укл.:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr ""

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "Прастора:"

#: ../glade/gbwidgets/gbexpander.c:58
#, fuzzy
msgid "Space to put between the label and the child"
msgstr "Колькасьць піксэляў між тэкстам і значкай"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
msgid "Add Label Widget"
msgstr "Дадаць віджэт меткі"

#: ../glade/gbwidgets/gbexpander.c:228
#, fuzzy
msgid "Expander"
msgstr "Пашырэньне:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#, fuzzy
msgid "The window title of the file chooser dialog"
msgstr "Загаловак дыялёгу выбару файла"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:156
#: ../glade/gnome/gnomefileentry.c:109
#, fuzzy
msgid "Action:"
msgstr "Разрыў:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:157
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:90
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
msgid "Local Only:"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:160
msgid "Whether the selected files should be limited to local files"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
#, fuzzy
msgid "Show Hidden:"
msgstr "Адлюстроўваць час:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether the hidden files and folders should be displayed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gblabel.c:200
#, fuzzy
msgid "Width in Chars:"
msgstr "Шырыня ў сымбалях:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#, fuzzy
msgid "The width of the button in characters"
msgstr "Шырыня макета"

#: ../glade/gbwidgets/gbfilechooserbutton.c:283
#, fuzzy
msgid "File Chooser Button"
msgstr "Кнопка адзначэньня"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
#, fuzzy
msgid "Select Multiple:"
msgstr "Выбярыце файл"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether to allow multiple files to be selected"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
#, fuzzy
msgid "File Chooser"
msgstr "Колер загалоўка:"

#: ../glade/gbwidgets/gbfilechooserdialog.c:421
#, fuzzy
msgid "File Chooser Dialog"
msgstr "Дыялёг выбару файлаў"

#: ../glade/gbwidgets/gbfileselection.c:71 ../glade/property.c:1365
msgid "Select File"
msgstr "Выбярыце файл"

#: ../glade/gbwidgets/gbfileselection.c:113
msgid "File Ops.:"
msgstr "Файлавыя апэр.:"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "If the file operation buttons are shown"
msgstr "Ці адлюстроўваюцца кнопкі кіраваньня файламі"

#: ../glade/gbwidgets/gbfileselection.c:292
msgid "File Selection Dialog"
msgstr "Дыялёг выбару файлаў"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X:"

#: ../glade/gbwidgets/gbfixed.c:140
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "X каардыната віджэта у GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y:"

#: ../glade/gbwidgets/gbfixed.c:143
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "Y каардыната віджэта у GtkFixed"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "Зафіксаваная пазыцыя"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "Загаловак дыялёгу выбару шрыфту"

#: ../glade/gbwidgets/gbfontbutton.c:70
#, fuzzy
msgid "Show Style:"
msgstr "Адлюстроўваць загалоўкі:"

#: ../glade/gbwidgets/gbfontbutton.c:71
#, fuzzy
msgid "If the font style is shown as part of the font information"
msgstr "Ці адлюстроўваць памер шрыфту як часткі інфармацыі пра яго"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "Адлюстроўваць памер:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "Ці адлюстроўваць памер шрыфту як часткі інфармацыі пра яго"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "Выкарыстоўваць шрыфт:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr "Ці выкарыстоўваць выбраны шрыфт для паказу інфармацыі аб шрыфце"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "Выкарыстоўваць памер:"

#: ../glade/gbwidgets/gbfontbutton.c:77
#, fuzzy
msgid "if the selected font size is used when displaying the font information"
msgstr "Ці выкарыстоўваць выбраны шрыфт для паказу інфармацыі аб шрыфце"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "Выбраць шрыфт"

#: ../glade/gbwidgets/gbfontbutton.c:268
#, fuzzy
msgid "Font Chooser Button"
msgstr "Кнопка адзначэньня"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "Тэкст перадпрагляду:"

#: ../glade/gbwidgets/gbfontselection.c:64
msgid "The preview text to display"
msgstr "Перадпрагляд тэкста для адлюстраваньня"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "Выбар шрыфта"

#: ../glade/gbwidgets/gbfontselectiondialog.c:69
msgid "Select Font"
msgstr "Выбярыце шрыфт"

#: ../glade/gbwidgets/gbfontselectiondialog.c:300
msgid "Font Selection Dialog"
msgstr "Дыялёг выбару шрыфта"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "Рамка"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "Пачатковы тып.:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "Пачатковы тып дугі"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Гама-дуга"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "Тып ценю вакол элемэнта рэгулятара"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "Пазыцыя рэгулятара:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "Пазыцыя рэгулятара"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "Прывязываць край:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "Край рэгулятара, які ўсталёўваецца ў пазначаную пазыцыю"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "Рэгулятар"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "Новы гарызантальны бокс"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "Памер:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "Колькасьць элемэнтаў кіраваньня ў боксе"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "Гамагенныя:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "Ці мусяць нашчадкі быць аднаго памера"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "Прамежак між усімі нашчадкамі"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "Немагчыма выдаліць нашчадкаў."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:254
msgid "Position:"
msgstr "Пазыцыя:"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "Пазыцыя віджэтаў адносна ягоных братоў"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "Інтэрвал:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "Усталёўка інтэрвалаў віджэту"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "Пашырэньне:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "Усталяваць Так, калі віджэт мусіць пашырацца"

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "Запаўненьне:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr ""
"Усталяваць Так, калі віджэт мусіць запаўняць усю вылучаную для яго вобласьць"

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "Пункт упакоўкі:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "Усталяваць Так, калі віджэт мусіць разьмяшчацца ў пачатку прамакутніка"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "Уставіць перад"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "Уставіць пасьля"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "Гарызантальны бокс"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "Новы гарызантальны бокс з кнопкамі"

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "Колькасьць кнопак"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "Макет:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "Стыль макета кнопак"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "Прамежак між кнопкамі"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "Гарызантальны бокс з кнопкамі"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "Пазыцыя дзельніка"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:283
msgid "Shrink:"
msgstr "Сьцісканьне:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "Усталяваць \"Так\", калі віджэт можа сьціскацца"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "Зьмена памера:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "Усталяваць \"Так\", калі віджэт можа зьмяняць памер"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "Гарызантальныя панэлі"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "Паказьнік:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "Адзінкі лінаркі"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "Найм. значэньне:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "Найменшае значэньне лінаркі"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "Найб. значэньне:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "Найбольшае значэньне лінаркі"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "Бягучая пазыцыя указальніка лінаркі"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4827
msgid "Max:"
msgstr "Макс.:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "Максымальнае значэньне лінаркі"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "Гарызантальная лінарка"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "Адлюстроўваць значэньне:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "Ці патрэбна адлюстроўваць значэньне шкалы"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "Дакладнасьць:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "Колькасьць лічбаў пасьля коскі (дакладнасьць)"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "Пазыцыя значэньня:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "Пазыцыя лічбаў значэньня шкалы"

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "Мэтад:"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "Мэтад абнаўленьня шкалы"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "Inverted:"
msgstr "Інвертавана:"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
msgid "If the range values are inverted"
msgstr "Калі мноства значэньняў інвэртавана"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "Гарызантальная шкала"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "Мэтад абнаўленьня паласы пракруткі"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "Гарызантальная паласа пракруткі"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "Гарызантальны дзельнік"

#: ../glade/gbwidgets/gbiconview.c:106
#, fuzzy, c-format
msgid "Icon %i"
msgstr "Сьпіс значак"

#: ../glade/gbwidgets/gbiconview.c:128
#, fuzzy
msgid "The selection mode of the icon view"
msgstr "Рэжым выбару дрэва са слупкоў"

#: ../glade/gbwidgets/gbiconview.c:130 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "Арыентацыя:"

#: ../glade/gbwidgets/gbiconview.c:131
#, fuzzy
msgid "The orientation of the icons"
msgstr "Арыентацыя зьместа індыкатара выкананьня"

#: ../glade/gbwidgets/gbiconview.c:287
#, fuzzy
msgid "Icon View"
msgstr "Памер значкі:"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:299
#, fuzzy
msgid "Named Icon:"
msgstr "Значка:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:300
#, fuzzy
msgid "The named icon to use"
msgstr "Убудаваны элемэнт GNOME для выкарыстаньня."

#: ../glade/gbwidgets/gbimage.c:112
msgid "Icon Size:"
msgstr "Памер значкі:"

#: ../glade/gbwidgets/gbimage.c:113
msgid "The stock icon size"
msgstr "Памер убудаванае значкі"

#: ../glade/gbwidgets/gbimage.c:115
#, fuzzy
msgid "Pixel Size:"
msgstr "Памер стар.:"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "Гарызантальнае раўнаньне"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "Вэртыкальнае раўнаньне"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "Відарыс"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "Няправільны убудаваны элемэнт мэню"

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "Элемэнт мэню з карцінкай"

#: ../glade/gbwidgets/gbinputdialog.c:256
msgid "Input Dialog"
msgstr "Дыялёг уводу"

#: ../glade/gbwidgets/gblabel.c:169
msgid "Use Underline:"
msgstr "Вык. падкрэсьленьне:"

#: ../glade/gbwidgets/gblabel.c:170
#, fuzzy
msgid "If the text includes an underlined access key"
msgstr "Калі тэкст утрымлівае сымбаль падкрэсьленьня для паскарэньня выбару"

#: ../glade/gbwidgets/gblabel.c:171
msgid "Use Markup:"
msgstr "Выкарыстоўваць разьметку:"

#: ../glade/gbwidgets/gblabel.c:172
msgid "If the text includes pango markup"
msgstr "Калі тэкст уключае разьметку pango"

#: ../glade/gbwidgets/gblabel.c:173
msgid "Justify:"
msgstr "Вылучэньне:"

#: ../glade/gbwidgets/gblabel.c:174
msgid "The justification of the lines of the label"
msgstr "Вылучэньне радкоў у метке"

#: ../glade/gbwidgets/gblabel.c:176
msgid "Wrap Text:"
msgstr "Перанос тэкста:"

#: ../glade/gbwidgets/gblabel.c:177
msgid "If the text is wrapped to fit within the width of the label"
msgstr "Калі тэкст пераносіцца для запаўненьня шырыні меткі"

#: ../glade/gbwidgets/gblabel.c:178
msgid "Selectable:"
msgstr "Вылучаецца:"

#: ../glade/gbwidgets/gblabel.c:179
msgid "If the label text can be selected with the mouse"
msgstr "Калі тэкст можа быць вылучаны з дапамогай мышы"

#: ../glade/gbwidgets/gblabel.c:181
msgid "The horizontal alignment of the entire label"
msgstr "Гарызантальнае раўнаньне меткі"

#: ../glade/gbwidgets/gblabel.c:184
msgid "The vertical alignment of the entire label"
msgstr "Вэртыкальнае раўнаньне меткі"

#: ../glade/gbwidgets/gblabel.c:190
msgid "Focus Target:"
msgstr "Мэта засяроджаньня:"

#: ../glade/gbwidgets/gblabel.c:191
#, fuzzy
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr ""
"Віджэт усталёўвае засяроджаньне клявіятуры пад час націску падкрэсьленай "
"клявішы-паскаральніка"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:197 ../glade/gbwidgets/gbprogressbar.c:146
#, fuzzy
msgid "Ellipsize:"
msgstr "Эксклюзыўны:"

#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:201
#, fuzzy
msgid "The width of the label in characters"
msgstr "Шырыня макета"

#: ../glade/gbwidgets/gblabel.c:203
#, fuzzy
msgid "Single Line Mode:"
msgstr "Рэжым Выбару:"

#: ../glade/gbwidgets/gblabel.c:204
msgid "If the label is only given enough height for a single line"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:205
msgid "Angle:"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:206
#, fuzzy
msgid "The angle of the label text"
msgstr "Зацісканьне тэксту"

#: ../glade/gbwidgets/gblabel.c:332 ../glade/gbwidgets/gblabel.c:347
#: ../glade/gbwidgets/gblabel.c:614
msgid "Auto"
msgstr "Аўта"

#: ../glade/gbwidgets/gblabel.c:870 ../glade/glade_menu_editor.c:410
msgid "Label"
msgstr "Метка"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "Шырыня вобласьці:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "Шырыня макета"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "Вышыня вобласьці:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "Вышыня макета"

#: ../glade/gbwidgets/gblayout.c:222
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "X каардыната віджэта ў GtkLayout"

#: ../glade/gbwidgets/gblayout.c:225
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "Y каардыната віджэта у GtkFixed"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "Макет"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "Рэжым выбару са сьпіса"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "Сьпіс"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "Элемэнт сьпіса"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "Мэню якое выпадае"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:190
msgid "_File"
msgstr "_Файл"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:198 ../glade/glade_project_window.c:691
msgid "_Edit"
msgstr "Р_эдагаваць"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:204 ../glade/glade_project_window.c:720
msgid "_View"
msgstr "_Выгляд"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:206 ../glade/glade_project_window.c:833
msgid "_Help"
msgstr "_Дапамога"

#: ../glade/gbwidgets/gbmenubar.c:207
msgid "_About"
msgstr "_Пра праграму"

#: ../glade/gbwidgets/gbmenubar.c:268 ../glade/gbwidgets/gbmenubar.c:346
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "Рэдагаваньне мэню..."

#: ../glade/gbwidgets/gbmenubar.c:442
msgid "Menu Bar"
msgstr "Панэль мэню"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "Элемэнт мэню"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
#, fuzzy
msgid "Show Horizontal:"
msgstr "Ніколі па гарызанталі:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
#, fuzzy
msgid "If the item is visible when the toolbar is horizontal"
msgstr "Ці можа элемэнт, які ўбудоўваецца, быць гарызантальным"

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
#, fuzzy
msgid "Show Vertical:"
msgstr "Адлюстроўваць значэньне:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
#, fuzzy
msgid "If the item is visible when the toolbar is vertical"
msgstr "Ці можа элемэнт, які ўбудоўваецца, быць вэртыкальным"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
#, fuzzy
msgid "Toolbar Button with Menu"
msgstr "Кнопка-пераключальнік"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "Новы нататнік"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:124
msgid "Number of pages:"
msgstr "Колькасьць старонак:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "Пазыцыі:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "Ці адлюстроўваць пазыцыі табуляцыі"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "Адлюстроўваць мяжу:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "Ці адлюстроўваць мяжу нататніка калі меткі не адлюстроўваюцца"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "Пазыцыя метак:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "Пазыцыя метак нататніка"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "Пракручваецца:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "Ці можна пракручваць меткі"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "Гарыз. мяжа метак:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "Памер гарызантальнае мяжы метак"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "Вэрт. мяжа метак:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "Памер крокаў табуляцыі вэртыкальнае мяжы"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "Адл. падмэню:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "Калі дазволены мэню якія ўсплываюць"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "Колькасьць старонак:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "Колькасьць старонак нататніка"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "Папяр. старонка"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "Наст. старонка"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "Выдаліць старонку"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "Пераключальнік на наступную"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "Пераключальнік на папярэднюю"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "Уставіць старонку пасьля"

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "Уставіць старонку перад"

#: ../glade/gbwidgets/gbnotebook.c:670
msgid "The page's position in the list of pages"
msgstr "Пазыцыя старонак у сьпісе старонак"

#: ../glade/gbwidgets/gbnotebook.c:673
msgid "Set True to let the tab expand"
msgstr "Усталяваць ТАК, калі укладка мусіць пашырацца"

#: ../glade/gbwidgets/gbnotebook.c:675
msgid "Set True to let the tab fill its allocated area"
msgstr ""
"Усталяваць ТАК, калі укладка мусіць запаўняць усю вылучаную ёй прастору"

#: ../glade/gbwidgets/gbnotebook.c:677
msgid "Set True to pack the tab at the start of the notebook"
msgstr "Усталяваць ІСЬЦІНА каб укладкі зьяўляліся з пачатку нататніка"

#: ../glade/gbwidgets/gbnotebook.c:678
msgid "Menu Label:"
msgstr "Метка мэню:"

#: ../glade/gbwidgets/gbnotebook.c:679
msgid "The text to display in the popup menu"
msgstr "Тэкст для вываду ў мэню якое усплывае"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "Нататнік"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr "Немагчыма дадаць %s да GtkOptionMenu."

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "Мэню парамэтраў"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "Колер:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "Ці зьяўляецца папярэдні прагляд каляровым ці чорна-белым"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "Ці патрэбна папярэдні прагляд пашыраць на ўсю даступную вобласьць"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "Перадпрагляд"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "Арыентацыя зьместа індыкатара выкананьня"

#: ../glade/gbwidgets/gbprogressbar.c:137
msgid "Fraction:"
msgstr "Разрыў:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr "Фракцыя калі дзеяньне скончана"

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr "Крок пульса:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr "Фракцыя прагрэсу"

#: ../glade/gbwidgets/gbprogressbar.c:144
msgid "The text to display over the progress bar"
msgstr "Тэкст для адлюстраваньня на індыкатары выкананьня"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "Адлюстраваць тэкст:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "Калі на індыкатары выкананьня мусіць адлюстроўвацца тэкст"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "Рэжым актыўн.:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "Калі індыкатар выкананьня рушыць з боку ў бок"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "Гарызантальнае раўнаньне тэкста"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "Вэртыкальнае раўнаньне тэкста"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "Індыкатар выкананьня"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "Кнопка выбару першапачаткова ўключана"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1038
msgid "Group:"
msgstr "Група:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr "Група кнопак выбару (дапомна ўсе кнопкі з аднолькавым продкам)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
msgid "New Group"
msgstr "Новая група"

#: ../glade/gbwidgets/gbradiobutton.c:463
msgid "Radio Button"
msgstr "Кнопка выбару"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "Кнопка выбару першапачаткова ўключана"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr "Група кнопак выбару (дапомна усе з аднолькавым продкам)"

#: ../glade/gbwidgets/gbradiomenuitem.c:386
msgid "Radio Menu Item"
msgstr "Элемэнт мэню выбару"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
#, fuzzy
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr "Група кнопак выбару (дапомна ўсе кнопкі з аднолькавым продкам)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:528
#, fuzzy
msgid "Toolbar Radio Button"
msgstr "Кнопка выбару"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "Гар. паводзіны:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "Калі адлюстроўваць гарызантальную паласу пракруткі"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "Вэрт. паводзіны:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "Калі адлюстроўваць вэртыкальную паласу пракруткі"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
msgid "Window Pos:"
msgstr "Становішча акна:"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr ""

#: ../glade/gbwidgets/gbscrolledwindow.c:140
msgid "Shadow Type:"
msgstr "Тып ценю:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "Мэтад абнаўленьня вэртыкальнай паласы пракруткі"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "Акно з пракруткай"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr "Дзельнік для мэню"

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
#, fuzzy
msgid "Toolbar Separator Item"
msgstr "Гарызантальны дзельнік"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "Хуткасьць пад'ёму:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr ""
"Хуткасьць набору значэньня кнопкі якая круціцца, выкарыстоўваецца сумесна з "
"павялічэньнем значэньня пракруткі"

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "Колькасьць дзесяткавых лічбаў для адлюстраваньня"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "Колькаснае:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr ""

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "Палітыка абнаўленьня:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "Пад час зьмены значэньня value_changed сыгнал будзе ўзбуджаны"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "Зьвязка:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "Ці прывязанае значэньне да кроку пракруткі"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "Заціск:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "Ці патрэбна значэньне разьмяшчаць у межах"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "Кнопка якая круціцца"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "Resize Grip:"
msgstr "Зьмяняльнік памера акна:"

#: ../glade/gbwidgets/gbstatusbar.c:64
msgid "If the status bar has a resize grip to resize the window"
msgstr "Ці ўтрымлівае радок стану зьмяняльнік памера акна"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "Радок стану"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "Новая табліца"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "Колькасьць радкоў:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "Радкі:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "Колькасьць радкоў у табліцы"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "Слупкі:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "Колькасьць слупкоў табліцы"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "Ці мусяць элемэнты нашчадкаў быць аднаго й таго ж памера"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "Рад. Прамеж.:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "Прамежак між радкамі"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "Слуп. Прамеж.:"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "Прамежак між слупкамі"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Ячэя X:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "Левы край віджэта ў табліцы"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Ячэя Y:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "Верхні край віджэта ў табліцы"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "Слупкі:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "Колькасьць слупкоў, якія займае віджэт у табліцы"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "Радкі:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "Колькасьць радкоў, якія займае віджэт ў табліцы"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "Гар. запаўненьне:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "Вэрт. запаўненьне:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "X Пашырэньне:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "Усталяваць \"Так\", калі віджэт мусіць пашырацца па гарызанталі"

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Y Пашырэньне:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "Усталяваць \"Так\", калі віджэт мусіць пашырацца па вэртыкалі"

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "X Сьцісканьне:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "Усталяваць \"Так\", калі віджэт мусіць сьціскацца па гарызанталі"

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Y Сьцісканьне:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "Усталяваць \"Так\", калі віджэт мусіць сьціскацца па вэртыкалі"

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "X Запаўн.:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr ""
"Усталяваць \"Так\", калі віджэт разьмешчаны на усім полі па гарызанталі"

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Y Запаўн.:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "Усталяваць \"Так\", калі віджэт разьмешчаны на усім полі па вэртыкалі"

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "Уставіць радок перад"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "Уставіць радок пасьля"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "Уставіць слупок перад"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "Уставіць слупок пасьля"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "Выдаліць радок"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "Выдаліць слупок"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "Табліца"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "Цэнтар"

#: ../glade/gbwidgets/gbtextview.c:52
msgid "Fill"
msgstr "Запаўненьне"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:542 ../glade/glade_menu_editor.c:829
#: ../glade/glade_menu_editor.c:1344 ../glade/glade_menu_editor.c:2251
#: ../glade/property.c:2431
msgid "None"
msgstr "Няма"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr "Сымбаль"

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr "Слова"

#: ../glade/gbwidgets/gbtextview.c:117
msgid "Cursor Visible:"
msgstr "Бачнасьць курсору:"

#: ../glade/gbwidgets/gbtextview.c:118
msgid "If the cursor is visible"
msgstr "Ці бачны курсор"

#: ../glade/gbwidgets/gbtextview.c:119
#, fuzzy
msgid "Overwrite:"
msgstr "Інвертавана:"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:122
#, fuzzy
msgid "If tab characters can be entered"
msgstr "Ці можа тэкст рэдагавацца"

#: ../glade/gbwidgets/gbtextview.c:126
msgid "Justification:"
msgstr "Выключэньне:"

#: ../glade/gbwidgets/gbtextview.c:127
msgid "The justification of the text"
msgstr "Выключэньне з тэксту"

#: ../glade/gbwidgets/gbtextview.c:129
msgid "Wrapping:"
msgstr "Заціск:"

#: ../glade/gbwidgets/gbtextview.c:130
msgid "The wrapping of the text"
msgstr "Зацісканьне тэксту"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr "Прастора вышэй:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr "Піксэляў прасторы над параграфамі"

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr "Прастора ніжэй:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr "Піксэляў прасторы пад параграфамі"

#: ../glade/gbwidgets/gbtextview.c:139
msgid "Space Inside:"
msgstr "Прастора ў нутры:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr "Піксэляў прасторы між загорнутымі радкамі ў параграфе"

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr "Левы водступ:"

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr "Шырыня левага водступа ў піксэлях"

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr "Правы водступ:"

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr "Шырыня правага водступа ў піксэлях"

#: ../glade/gbwidgets/gbtextview.c:149
msgid "Indent:"
msgstr "Водступ:"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr "Колькасьць піксэляў для водступу абзац"

#: ../glade/gbwidgets/gbtextview.c:463
msgid "Text View"
msgstr "Прагляд тэкста"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "Ці зьяўляецца кнопка-пераключальнік першапачаткова уключанай"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "Кнопка-пераключальнік"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
#, fuzzy
msgid "Toolbar Toggle Button"
msgstr "Кнопка-пераключальнік"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "Новая панель інструмэнта"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "Колькасьць элемэнтаў:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "Колькасьць элемэнтаў на панэлі інструмэнта"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "Арыентацыя панэлі інструмэнта"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "Стыль:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "Стыль панэлі інструмэнта"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "Падказкі:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "Ці уключаны падказкі"

#: ../glade/gbwidgets/gbtoolbar.c:277
#, fuzzy
msgid "Show Arrow:"
msgstr "Адлюстроўваць мяжу:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""

#: ../glade/gbwidgets/gbtoolbar.c:427
#, fuzzy
msgid "If the item should be the same size as other homogeneous items"
msgstr "Ці мусяць нашчадкі быць аднаго памера"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "Уставіць элемэнт перад гэтым"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "Уставіць элемэнт пасьля гэтага"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "Панель інструмэнта"

#: ../glade/gbwidgets/gbtoolbutton.c:586
#, fuzzy
msgid "Toolbar Button"
msgstr "Кнопка-пераключальнік"

#: ../glade/gbwidgets/gbtoolitem.c:201
#, fuzzy
msgid "Toolbar Item"
msgstr "Панель інструмэнта"

#: ../glade/gbwidgets/gbtreeview.c:71
msgid "Column 1"
msgstr "Слупок 1"

#: ../glade/gbwidgets/gbtreeview.c:79
msgid "Column 2"
msgstr "Слупок 2"

#: ../glade/gbwidgets/gbtreeview.c:87
#, fuzzy
msgid "Column 3"
msgstr "Слупок 1"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:114
msgid "Headers Visible:"
msgstr "Бачнасьць загалоўкаў:"

#: ../glade/gbwidgets/gbtreeview.c:115
msgid "If the column header buttons are shown"
msgstr "Ці адлюстроўваць загалоўкі кнопак слупкоў"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr "Падказкі правілаў:"

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""
"Калі рухавік тэмы мусіць адлюстроўваць падказкі ў альтэрнатыўных колерах"

#: ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr "Пераўпарадкаваны:"

#: ../glade/gbwidgets/gbtreeview.c:119
msgid "If the view is reorderable"
msgstr "Калі выгляд пераўпарадкоўваецца"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr "Уключыць пошук:"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr "Ці можа карыстальнік інтэрактыўна шукаць слупкі"

#: ../glade/gbwidgets/gbtreeview.c:123
#, fuzzy
msgid "Fixed Height Mode:"
msgstr "Маштаб па вышыні:"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:125
#, fuzzy
msgid "Hover Selection:"
msgstr "Выбар колера"

#: ../glade/gbwidgets/gbtreeview.c:126
#, fuzzy
msgid "Whether the selection should follow the pointer"
msgstr "Режим выбора дерева"

#: ../glade/gbwidgets/gbtreeview.c:127
#, fuzzy
msgid "Hover Expand:"
msgstr "X Пашырэньне:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr "Прагляд сьпіса ці дрэва"

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "Новы вэртыкальны бокс"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "Вэртыкальны бокс"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "Новы вэртыкальны бокс з кнопкамі"

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "Вэртыкальны бокс з кнопкамі"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "Тып ценю акна прагляду"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "Акно прагляду"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "Вэртыкальныя панэлі"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "Вэртыкальная лінарка"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "Вэртыкальная шкала"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "Вэртыкальная паласа пракруткі"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "Вэртыкальны дзельнік"

#: ../glade/gbwidgets/gbwindow.c:242
msgid "The title of the window"
msgstr "Загаловак акна"

#: ../glade/gbwidgets/gbwindow.c:245
msgid "The type of the window"
msgstr "Тып акна"

#: ../glade/gbwidgets/gbwindow.c:249
#, fuzzy
msgid "Type Hint:"
msgstr "Тып:"

#: ../glade/gbwidgets/gbwindow.c:250
msgid "Tells the window manager how to treat the window"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:255
msgid "The initial position of the window"
msgstr "Першапачатковая пазыцыя акна"

#: ../glade/gbwidgets/gbwindow.c:259 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "Мадальны:"

#: ../glade/gbwidgets/gbwindow.c:259
msgid "If the window is modal"
msgstr "Калі акно мадальнае"

#: ../glade/gbwidgets/gbwindow.c:264
msgid "Default Width:"
msgstr "Дапомная шырыня:"

#: ../glade/gbwidgets/gbwindow.c:265
msgid "The default width of the window"
msgstr "Дапомная шырыня акна"

#: ../glade/gbwidgets/gbwindow.c:269
msgid "Default Height:"
msgstr "Дапомная вышыня:"

#: ../glade/gbwidgets/gbwindow.c:270
msgid "The default height of the window"
msgstr "Дапомная вышыня акна"

#: ../glade/gbwidgets/gbwindow.c:276
msgid "Resizable:"
msgstr "Зьмена памера:"

#: ../glade/gbwidgets/gbwindow.c:277
msgid "If the window can be resized"
msgstr "Ці можа акно зьмяняць памер"

#: ../glade/gbwidgets/gbwindow.c:284
msgid "If the window can be shrunk"
msgstr "Ці можа акно сьціскацца"

#: ../glade/gbwidgets/gbwindow.c:285
msgid "Grow:"
msgstr "Рост:"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be enlarged"
msgstr "Ці можа акно пашырацца"

#: ../glade/gbwidgets/gbwindow.c:291
msgid "Auto-Destroy:"
msgstr "Аўта-зьнішчэньне:"

#: ../glade/gbwidgets/gbwindow.c:292
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "Ці зьнішчаецца акно калі зьнішчаецца бацькоўскае акно"

#: ../glade/gbwidgets/gbwindow.c:296
msgid "The icon for this window"
msgstr "Заначка для гэтага акна"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "Role:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:303
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:306
#, fuzzy
msgid "Decorated:"
msgstr "А_суджанае"

#: ../glade/gbwidgets/gbwindow.c:307
#, fuzzy
msgid "If the window should be decorated by the window manager"
msgstr "Калі патрабуецца вызначаць стасунак памераў па элемэнту-нашчадку"

#: ../glade/gbwidgets/gbwindow.c:310
msgid "Skip Taskbar:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:311
#, fuzzy
msgid "If the window should not appear in the task bar"
msgstr "Калі ў акна ёсьць радок стану"

#: ../glade/gbwidgets/gbwindow.c:314
msgid "Skip Pager:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:315
#, fuzzy
msgid "If the window should not appear in the pager"
msgstr "Калі на індыкатары выкананьня мусіць адлюстроўвацца тэкст"

#: ../glade/gbwidgets/gbwindow.c:318
#, fuzzy
msgid "Gravity:"
msgstr "Стыль экранае сеткі:"

#: ../glade/gbwidgets/gbwindow.c:319
msgid "The reference point to use when the window coordinates are set"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "Focus On Map:"
msgstr "Мэта засяроджаньня:"

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "If the window should receive the input focus when it is mapped"
msgstr "Калі патрабуецца вызначаць стасунак памераў па элемэнту-нашчадку"

#: ../glade/gbwidgets/gbwindow.c:1198
msgid "Window"
msgstr "Акно"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:74
msgid "Error"
msgstr "Памылка"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "Сыстэмная памылка"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "Памылка адкрыцьця файла"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "Памылка чытаньня файла"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "Памылка запісу файла"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "Памылковы каталёг"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "Няправільнае значэньне"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "Памылковая канструкцыя XML"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "Адсутнічае пачатковы тэг"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "Адсутнічае заключны тэг"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "Адсутнічаюць сымбальныя даньні"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "Памылковы ідэнтыфікатар клясы"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "Невядомая кляса"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "Памылковы кампанэнт"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "Нечаканы канец файла"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "Невядомы код памылкі"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr "Кантралюецца"

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr "Кантралёр для"

#: ../glade/glade_atk.c:122
msgid "Label For"
msgstr "Метка для"

#: ../glade/glade_atk.c:123
msgid "Labelled By"
msgstr "Памечана"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr "Удзельнік"

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr "Вузлавы нашчадак"

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr ""

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr ""

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr ""

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr ""

#: ../glade/glade_atk.c:130
#, fuzzy
msgid "Embedded By"
msgstr "Памечана"

#: ../glade/glade_atk.c:131
#, fuzzy
msgid "Popup For"
msgstr "Мэню якое выпадае"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr ""

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr "Стасунак: %s"

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "Віджэт"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:772
#: ../glade/property.c:776
msgid "Name:"
msgstr "Назоў:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr "Назва віджэта якая перадаецца дапаможным тэхналёгіям"

#: ../glade/glade_atk.c:640
msgid "Description:"
msgstr "Апісаньне:"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr "Апісаньне віджэта якое перадаецца дапаможным тэхналёгіям"

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr "Загаловак табліцы:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr "Загаловак табліцы які перадаецца дапаможным тэхналёгіям"

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr "Выбярыце віджэты з гэтым стасункам"

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr "Клікнуць"

#: ../glade/glade_atk.c:762
msgid "Press"
msgstr "Націснуць"

#: ../glade/glade_atk.c:763
msgid "Release"
msgstr "Адпусьціць"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr "Пазначце аісаньне дзеяньня для перадачы дапаможным тэхналёгіям"

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "Буфер абмену"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "Вы мусіце выбраць віджэт для ўстаўкі"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "Вы ня можаце устаўляць у вокны ці дыялёгі."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"Вы ня можаце ўстаўляць у выбраны элемэнт, таму што\n"
"ён аўтаматычна ствараецца ягоным бацькам."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr "Толькі элемэнты мэню могуць быць устаўлены ў мэню ці на панель мэню."

#: ../glade/glade_clipboard.c:427
msgid "Only buttons can be pasted into a dialog action area."
msgstr "Толькі кнопкі могуць быць устаўлены ў вобласьць дзеяньня дыялёга."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "Толькі віджэт GnomeDockItem можа быць устаўлены ў GnomeDock."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "Толькі віджэты GnomeDockItem могуць быць устаўленыя на GnomeDockItem."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "Выбачайце, устаўка праз GnomeDockItem яшчэ ня рэалізавана."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "Віджэт GnomeDockItem можа быць устаўлены толькі ў GnomeDock."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:632
msgid "_New"
msgstr "_Новы"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "Стварыць новы файл"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
msgid "_Gnome"
msgstr "_Gnome"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
msgid "Dep_recated"
msgstr "А_суджанае"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
msgid "GTK+ _Basic"
msgstr "GTK+ _Асноўнае"

#: ../glade/glade_gtk12lib.c:247
msgid "GTK+ _Additional"
msgstr "GTK+ _Дадатковае"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "Выбар гарачай клявішы"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "Клявішы"

#: ../glade/glade_menu_editor.c:394
msgid "Menu Editor"
msgstr "Рэдактар мэню"

#: ../glade/glade_menu_editor.c:411
msgid "Type"
msgstr "Тып"

#: ../glade/glade_menu_editor.c:412
msgid "Accelerator"
msgstr "Паскаральнік"

#: ../glade/glade_menu_editor.c:413
msgid "Name"
msgstr "Назва"

#: ../glade/glade_menu_editor.c:414 ../glade/property.c:1498
msgid "Handler"
msgstr "Апрацоўшчык"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:102
msgid "Active"
msgstr "Актыўны"

#: ../glade/glade_menu_editor.c:416
msgid "Group"
msgstr "Група"

#: ../glade/glade_menu_editor.c:417
msgid "Icon"
msgstr "Значка"

#: ../glade/glade_menu_editor.c:458
msgid "Move the item and its children up one place in the list"
msgstr "Перамясьціць элемэнт са сваімі пашчадкамі на 1 мейсца вышэй у сьпісе"

#: ../glade/glade_menu_editor.c:470
msgid "Move the item and its children down one place in the list"
msgstr "Перамясьціць элемэнт са сваімі пашчадкамі на 1 мейсца ніжэй у сьпісе"

#: ../glade/glade_menu_editor.c:482
msgid "Move the item and its children up one level"
msgstr "Перамясьціць элемэнт са сваімі нашчадкамі на ўзровень вышэй"

#: ../glade/glade_menu_editor.c:494
msgid "Move the item and its children down one level"
msgstr "Перамясьціць элемэнт са сваімі нашчадкамі на ўзровень ніжэй"

#: ../glade/glade_menu_editor.c:524
msgid "The stock item to use."
msgstr "Убудаваны элемэнт GNOME для выкарыстаньня."

#: ../glade/glade_menu_editor.c:527 ../glade/glade_menu_editor.c:642
msgid "Stock Item:"
msgstr "Убудаваны элемэнт:"

#: ../glade/glade_menu_editor.c:640
msgid "The stock Gnome item to use."
msgstr "Убудаваны элемэнт GNOME для выкарыстаньня."

#: ../glade/glade_menu_editor.c:745
msgid "The text of the menu item, or empty for separators."
msgstr ""

#: ../glade/glade_menu_editor.c:769 ../glade/property.c:777
msgid "The name of the widget"
msgstr "Назва гэтага віджэта"

#: ../glade/glade_menu_editor.c:790
msgid "The function to be called when the item is selected"
msgstr "Функцыя, якая выклікаецца калі элемэнт будзе выбраны"

#: ../glade/glade_menu_editor.c:792 ../glade/property.c:1546
msgid "Handler:"
msgstr "Апрацоўшчык:"

#: ../glade/glade_menu_editor.c:811
msgid "An optional icon to show on the left of the menu item."
msgstr ""
"Дадатковая значка, якая адлюстроўваецца з левага боку ад элемэнта мэню."

#: ../glade/glade_menu_editor.c:934
msgid "The tip to show when the mouse is over the item"
msgstr ""
"Адлюстроўваць падказку, калі курсор мышы затрымліваецца па-над элемэнтам"

#: ../glade/glade_menu_editor.c:936 ../glade/property.c:824
msgid "Tooltip:"
msgstr "Падказка:"

#: ../glade/glade_menu_editor.c:957
msgid "_Add"
msgstr "_Дадаць"

#: ../glade/glade_menu_editor.c:962
msgid "Add a new item below the selected item."
msgstr "Дадаць элемэнт ніжэй за абраны."

#: ../glade/glade_menu_editor.c:967
msgid "Add _Child"
msgstr "Дадаць _нашчадка"

#: ../glade/glade_menu_editor.c:972
msgid "Add a new child item below the selected item."
msgstr "Дадаць элемэнт-нашчадак ніжэй за абраны."

#: ../glade/glade_menu_editor.c:978
msgid "Add _Separator"
msgstr "Дадаць _дзельнік"

#: ../glade/glade_menu_editor.c:983
msgid "Add a separator below the selected item."
msgstr "Дадаць дзельнік пад выбраным дакумэнтам."

#: ../glade/glade_menu_editor.c:988 ../glade/glade_project_window.c:239
msgid "_Delete"
msgstr "_Выдаліць"

#: ../glade/glade_menu_editor.c:993
msgid "Delete the current item"
msgstr "Выдаліць бягучы элемэнт"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:999
msgid "Item Type:"
msgstr "Тып элемэнта:"

#: ../glade/glade_menu_editor.c:1015
msgid "If the item is initially on."
msgstr "Элемэнт будзе актыўны адпачатку."

#: ../glade/glade_menu_editor.c:1017
msgid "Active:"
msgstr "Актыўны:"

#: ../glade/glade_menu_editor.c:1022 ../glade/glade_menu_editor.c:1632
#: ../glade/property.c:2215 ../glade/property.c:2225
msgid "No"
msgstr "Не"

#: ../glade/glade_menu_editor.c:1036
msgid "The radio menu item's group"
msgstr "Кнопка выбару першапачаткова ўключана"

#: ../glade/glade_menu_editor.c:1053 ../glade/glade_menu_editor.c:2406
#: ../glade/glade_menu_editor.c:2546
msgid "Radio"
msgstr "Выбар"

#: ../glade/glade_menu_editor.c:1060 ../glade/glade_menu_editor.c:2404
#: ../glade/glade_menu_editor.c:2544
msgid "Check"
msgstr "Адзначэньне"

#: ../glade/glade_menu_editor.c:1067 ../glade/property.c:102
msgid "Normal"
msgstr "Нармальны"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1076
msgid "Accelerator:"
msgstr "Паскаральнік:"

#: ../glade/glade_menu_editor.c:1113 ../glade/property.c:1681
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1118 ../glade/property.c:1684
msgid "Shift"
msgstr "Shift"

#: ../glade/glade_menu_editor.c:1123 ../glade/property.c:1687
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1128 ../glade/property.c:1694
msgid "Key:"
msgstr "Клявіша:"

#: ../glade/glade_menu_editor.c:1134 ../glade/property.c:1673
msgid "Modifiers:"
msgstr "Мадыфікатары:"

#: ../glade/glade_menu_editor.c:1632 ../glade/glade_menu_editor.c:2411
#: ../glade/glade_menu_editor.c:2554 ../glade/property.c:2215
msgid "Yes"
msgstr "Так"

#: ../glade/glade_menu_editor.c:2002
msgid "Select icon"
msgstr "Выбярыце значку"

#: ../glade/glade_menu_editor.c:2345 ../glade/glade_menu_editor.c:2706
msgid "separator"
msgstr "дзельнік"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3624 ../glade/glade_project_window.c:366
#: ../glade/property.c:5109
msgid "New"
msgstr "Новы"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "Выбарнік"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Каталёг праекта ня ўсталяваны.\n"
"Калі ласка, усталюйце яго карыстаючыся дыялёгам \"Парамэтры праекта\".\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Каталёг зыходных тэкстаў ня ўсталяваны.\n"
"Калі ласка, усталюйце яго выкарыстоўваючы дыялёг \"Парамэтры праекта\".\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"Няправільны каталёг зыходных тэкстаў:\n"
"\n"
"Каталёг зыходных тэкстаў мусіць быць каталёгам праекта\n"
"ці падкаталёгам у каталёзе праекта.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Каталёг карцінак ня ўсталяваны.\n"
"Калі ласка, усталюйце яго карыстаючыся дыялёгам \"Парамэтры праекта\".\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "Выбачайце, генераваньне зыходнага кода для %s яшчэ ня рэалізавана"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""

#: ../glade/glade_project.c:521
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"Памылка запуску glade-- для генераваньня зыходнага кода C++.\n"
"Праверце што ў вас усталяваны glade-- і што ён даступны."

#: ../glade/glade_project.c:548
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"Памылка запуску gate з мэтай стварэння зыходнага коду Ada95.\n"
"Праверце, што ў вас усталявана gate, што шлях да яе прысутнічае\n"
"ў пераменнай асяродзьдзя PATH. Затым паспрабуйце выканаць\n"
"\"glade2perl <файл_праекта.glade>\" у тэрмінале."

#: ../glade/glade_project.c:571
#, fuzzy
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"Памылка запуску glade2perl з мэтай стварэння зыходнага коду Perl.\n"
"Праверце, што ў вас усталяваны glade2perl, што ён прысутнічае ў\n"
"пераменнай асяродзьдзя PATH. Затым паспрабуйце выканаць\n"
"\"glade2perl <файл_праекта.glade>\" у тэрмінале."

#: ../glade/glade_project.c:594
#, fuzzy
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"Памылка запуску eglade з мэтай стварэння зыходнага коду Eiffel.\n"
"Праверце, што ў вас усталяваны eglade, што ён прысутнічае ў\n"
"пераменнай асяродзьдзя PATH. Затым паспрабуйце выканаць\n"
"\"eglade <файл_праекта.glade>\" у тэрмінале."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"Каталёг карцінак ня усталяваны.\n"
"Калі ласка, усталюйце яго карыстаючыся дыалёгам \"Парамэтры праекта\".\n"

#: ../glade/glade_project.c:1772
msgid "Error writing project XML file\n"
msgstr "Памылка запісу XML файла\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:382
#: ../glade/glade_project_window.c:889
msgid "Project Options"
msgstr "Парамэтры праекта"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "Асноўнае"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "Базавыя парамэтры:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "Каталёг праекта"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "Каталёг праекта:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "Прагляд..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "Назва бягучага праекта"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "Назва праекта:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "Назва праграмы"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "Файл праекта"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "Файл праекта:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "Падкаталёгі:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "Каталёг для захаваньня створанага зыходнага кода"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "Каталёг зых. кода:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "Каталёг для захаваньня карцінак"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "Каталёг карцінак:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "Ліцэнзыя, якая дадаецца ў пачатак створаных файлаў"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "Мова:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "Уключыць падтрымку Gnome"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "Ці мусіць быць пабудавана дастасаваньне Gnome"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "Уключыць падтрымку Gnome-DB"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Калі мусіць быць створана дастасаваньне Gnome DB"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "Парамэтры C"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "Асноўныя парамэтры:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Падтрымка Gettext"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "Ці будуць пазначацца радкі для перакладу gettext"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "Усталяваць назвы віджэтаў"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "Ці ўсталёўваць назвы віджэтаў у зыходны код"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "Рэзэрвовая копія файлаў зыходных тэкстаў:"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "Ці рабіць копію старых файлаў зыходніка"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Падтрымка даведкі Gnome"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "Ці мусіць быць уключана падтрымка для даведкі Gnome"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "Парамэтры вываду файлаў:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "Вывад файла main.c"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr "Калі файл вываду main.c утрымлівае функцыю main() і яшчэ ня існуе"

#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "Вывад функцыі падтрымкі"

#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "Калі патрабуецца вывадзіць функцыі падтрымкі"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Вывад файлаў для пабудовы"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Калі патрабуецца вывадзіць файлы для пабудовы зыходнага тэкста, улічваючы "
"Makefile.am і configure.in, калі яны яшчэ ня існуюць"

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "Функцыі стварэньня інтэрфэйса:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "Файл у якім запісаны функцыі стварэньня інтэрфэйса"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "Файл зыходных тэкстаў:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr "Файл, у якім запісаны аб'явы функцыяў для стварэньня інтэрфэйса"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "Файл загалоўкаў:"

#: ../glade/glade_project_options.c:594
#, fuzzy
msgid "Source file for interface creation functions"
msgstr "Функцыі стварэньня інтэрфэйса:"

#: ../glade/glade_project_options.c:595
#, fuzzy
msgid "Header file for interface creation functions"
msgstr "Функцыі стварэньня інтэрфэйса:"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "Апрацоўшчыкі сыгналаў і адваротныя функцыі:"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr ""
"Файл, у якім запісаны пустыя загалоўкі апрацоўшчыкаў падзеяў і адваротных "
"функцыяў"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr ""
"Файл, у якім запісаны аб'явы апрацоўшчыкаў падзеяў і адваротны функцыяў"

#: ../glade/glade_project_options.c:640
#, fuzzy
msgid "Source file for signal handler and callback functions"
msgstr "Апрацоўшчыкі сыгналаў і адваротныя функцыі:"

#: ../glade/glade_project_options.c:641
#, fuzzy
msgid "Header file for signal handler and callback functions"
msgstr ""
"Файл, у якім запісаны пустыя загалоўкі апрацоўшчыкаў падзеяў і адваротных "
"функцыяў"

#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "Функцыі падтрымкі:"

#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "Файл, у якім запісаны функцыі падтрымкі"

#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "Файл, у якім запісаны аб'явы функцыяў падтрымкі"

#: ../glade/glade_project_options.c:686
#, fuzzy
msgid "Source file for support functions"
msgstr "Функцыі падтрымкі:"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr ""

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "Парамэтры LibGlade"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "Радкі для перакладу:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "Захаваць радкі для перакладу"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"Калі радкі для перакладу захаваныя ў асобны файл зыходных тэкстаў C, каб "
"уключыць пераклад інтэрфэйсаў,які загружаецца libglade"

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "Файл зыходнага тэкста C для захаваньня ў ім усіх радкоў для перакладу"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "Файл:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "Выбраць каталёг праекта"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "Вам трэба ўсталяваць парамэтар \"Файл радкоў для перакладу\""

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "Вам трэба ўсталяваць парамэтар \"Каталёг праекта\""

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "Вам трэба ўсталяваць парамэтар \"Файл праекта\""

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "Вам трэба ўсталяваць парамэтар \"Назва праекта\""

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "Вам трэба ўсталяваць парамэтар \"Назва праграмы\""

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "Вам трэба ўсталяваць парамэтар \"Каталёг зыходных тэкстаў\""

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "Вам трэба ўсталяваць парамэтар \"Каталёг карцінак\""

#: ../glade/glade_project_window.c:184
#, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"Немагчыма адлюстраваць файл: %s.\n"
"\n"
"Памылка: %s"

#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:634
msgid "Create a new project"
msgstr "Стварыць новы праект"

#: ../glade/glade_project_window.c:216 ../glade/glade_project_window.c:654
#: ../glade/glade_project_window.c:905
msgid "_Build"
msgstr "_Пабудаваць"

#: ../glade/glade_project_window.c:217 ../glade/glade_project_window.c:665
msgid "Output the project source code"
msgstr "Згенераваць зыходны код праекта"

#: ../glade/glade_project_window.c:223 ../glade/glade_project_window.c:668
msgid "Op_tions..."
msgstr "_Парамэтры..."

#: ../glade/glade_project_window.c:224 ../glade/glade_project_window.c:677
msgid "Edit the project options"
msgstr "Рэдагаваньне парамэтраў праекта"

#: ../glade/glade_project_window.c:239 ../glade/glade_project_window.c:716
msgid "Delete the selected widget"
msgstr "Выдаліць выбраны віджэт"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:727
msgid "Show _Palette"
msgstr "Адлюстраваць палітру"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:732
msgid "Show the palette of widgets"
msgstr "Адлюстраваць палітру віджэта"

#: ../glade/glade_project_window.c:263 ../glade/glade_project_window.c:737
msgid "Show Property _Editor"
msgstr "Адлюстраваць _рэдактар уласьцівасьцяў"

#: ../glade/glade_project_window.c:264 ../glade/glade_project_window.c:743
msgid "Show the property editor"
msgstr "Адлюстраваць рэдактар уласьцівасьцяў"

#: ../glade/glade_project_window.c:270 ../glade/glade_project_window.c:747
msgid "Show Widget _Tree"
msgstr "Адлюстраваць _дрэва віджэта"

#: ../glade/glade_project_window.c:271 ../glade/glade_project_window.c:753
#: ../glade/main.c:82
msgid "Show the widget tree"
msgstr "Адлюстраваць дрэва віджэта"

#: ../glade/glade_project_window.c:277 ../glade/glade_project_window.c:757
msgid "Show _Clipboard"
msgstr "Адлюстраваць буфер абмена"

#: ../glade/glade_project_window.c:278 ../glade/glade_project_window.c:763
#: ../glade/main.c:86
msgid "Show the clipboard"
msgstr "Адлюстраваць зьмест буфера абмена"

#: ../glade/glade_project_window.c:296
msgid "Show _Grid"
msgstr "Адлюстраваць _сетку"

#: ../glade/glade_project_window.c:297 ../glade/glade_project_window.c:799
msgid "Show the grid (in fixed containers only)"
msgstr "Адлюстраваць сетку (толькі ў кантэйнэрох з фіксацыяй)"

#: ../glade/glade_project_window.c:303
msgid "_Snap to Grid"
msgstr "_Раўнаць па сетцы"

#: ../glade/glade_project_window.c:304
msgid "Snap widgets to the grid"
msgstr "Прывязаць віджэты да сеткі"

#: ../glade/glade_project_window.c:310 ../glade/glade_project_window.c:771
msgid "Show _Widget Tooltips"
msgstr "Адлюстраваць падказкі _віджэта"

#: ../glade/glade_project_window.c:311 ../glade/glade_project_window.c:779
msgid "Show the tooltips of created widgets"
msgstr "Адлюстраваць падказкі створаных віджэтаў"

#: ../glade/glade_project_window.c:320 ../glade/glade_project_window.c:802
msgid "Set Grid _Options..."
msgstr "Усталяваць парамэтры _сеткі..."

#: ../glade/glade_project_window.c:321
msgid "Set the grid style and spacing"
msgstr "Усталяваць стыль і інтэрвалы сеткі"

#: ../glade/glade_project_window.c:327 ../glade/glade_project_window.c:823
msgid "Set Snap O_ptions..."
msgstr "Усталяваць _парамэтры прывязкі..."

#: ../glade/glade_project_window.c:328
msgid "Set options for snapping to the grid"
msgstr "Усталяваць парамэтры прывязкі да сеткі"

#: ../glade/glade_project_window.c:340
msgid "_FAQ"
msgstr "_FAQ"

#: ../glade/glade_project_window.c:341
msgid "View the Glade FAQ"
msgstr "З Glade FAQ"

#. create File menu
#: ../glade/glade_project_window.c:355 ../glade/glade_project_window.c:625
msgid "_Project"
msgstr "_Праект"

#: ../glade/glade_project_window.c:366 ../glade/glade_project_window.c:872
#: ../glade/glade_project_window.c:1049
msgid "New Project"
msgstr "Новы праект"

#: ../glade/glade_project_window.c:371
msgid "Open"
msgstr "Адчыніць"

#: ../glade/glade_project_window.c:371 ../glade/glade_project_window.c:877
#: ../glade/glade_project_window.c:1110
msgid "Open Project"
msgstr "Адчыніць праект"

#: ../glade/glade_project_window.c:376
msgid "Save"
msgstr "Захаваць"

#: ../glade/glade_project_window.c:376 ../glade/glade_project_window.c:881
#: ../glade/glade_project_window.c:1475
msgid "Save Project"
msgstr "Захаваць праект"

#: ../glade/glade_project_window.c:382
msgid "Options"
msgstr "Парамэтры"

#: ../glade/glade_project_window.c:387
msgid "Build"
msgstr "Пабудаваць"

#: ../glade/glade_project_window.c:387
msgid "Build the Source Code"
msgstr "Згенераваць зыходны код"

#: ../glade/glade_project_window.c:638
msgid "Open an existing project"
msgstr "Адчыніць існуючы праект"

#: ../glade/glade_project_window.c:642
msgid "Save project"
msgstr "Захаваць праект"

#: ../glade/glade_project_window.c:687
msgid "Quit Glade"
msgstr "Выйсьці з Glade"

#: ../glade/glade_project_window.c:701
msgid "Cut the selected widget to the clipboard"
msgstr "Выразаць вылучаны віджэт у буфер абмену"

#: ../glade/glade_project_window.c:706
msgid "Copy the selected widget to the clipboard"
msgstr "Капіяваць вылучаны віджэт у буфер абмену"

#: ../glade/glade_project_window.c:711
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "Уставіць віджэт з буферу замест вылучанага віджэта"

#: ../glade/glade_project_window.c:783
msgid "_Grid"
msgstr "_Сетка"

#: ../glade/glade_project_window.c:791
msgid "_Show Grid"
msgstr "Ад_люстраваць сетку"

#: ../glade/glade_project_window.c:808
msgid "Set the spacing between grid lines"
msgstr "Усталяваць прамежак між лініямі сеткі"

#: ../glade/glade_project_window.c:811
msgid "S_nap to Grid"
msgstr "Пры_вязка да сеткі"

#: ../glade/glade_project_window.c:819
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "Прывязываць віджэты да сеткі (толькі ў кантэйнэрох з фіксацыяй)"

#: ../glade/glade_project_window.c:829
msgid "Set which parts of a widget snap to the grid"
msgstr "Усталяваць якія часткі віджэту будуць прывязывацца да сеткі"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:854
msgid "_About..."
msgstr "_Пра праграму..."

#: ../glade/glade_project_window.c:895
msgid "Optio_ns"
msgstr "_Парамэтры"

#: ../glade/glade_project_window.c:899
msgid "Write Source Code"
msgstr "Згенераваць зыходны код"

#: ../glade/glade_project_window.c:986 ../glade/glade_project_window.c:1691
#: ../glade/glade_project_window.c:1980
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:993
msgid "Are you sure you want to create a new project?"
msgstr "Вы сапраўды жадаеце стварыць новы праект?"

#: ../glade/glade_project_window.c:1053
msgid "New _GTK+ Project"
msgstr "Новы праект _GTK+"

#: ../glade/glade_project_window.c:1054
msgid "New G_NOME Project"
msgstr "Новы праект G_NOME"

#: ../glade/glade_project_window.c:1057
msgid "Which type of project do you want to create?"
msgstr "Які тып праекту вы жадаеце стварыць?"

#: ../glade/glade_project_window.c:1091
msgid "New project created."
msgstr "Новы праект распачаты."

#: ../glade/glade_project_window.c:1181
msgid "Project opened."
msgstr "Праект адчынены."

#: ../glade/glade_project_window.c:1195
msgid "Error opening project."
msgstr "Памылка адкрыцьця праекта."

#: ../glade/glade_project_window.c:1259
msgid "Errors opening project file"
msgstr "Памылка адкрыцьця файла праекта"

#: ../glade/glade_project_window.c:1265
msgid " errors opening project file:"
msgstr " памылкі адкрыцьця файла праекта:"

#: ../glade/glade_project_window.c:1338
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""
"Няма адкрытага праекта.\n"
"Стварыце новы праект з дапамогай пунктаў мэню Праект/Новае."

#: ../glade/glade_project_window.c:1542
msgid "Error saving project"
msgstr "Памылка захаваньня праекта."

#: ../glade/glade_project_window.c:1544
msgid "Error saving project."
msgstr "Памылка захаваньня праекта."

#: ../glade/glade_project_window.c:1550
msgid "Project saved."
msgstr "Праект захаваны."

#: ../glade/glade_project_window.c:1620
msgid "Errors writing source code"
msgstr "Памылка запісу зыходнага кода"

#: ../glade/glade_project_window.c:1622
msgid "Error writing source."
msgstr "Памылка запісу зыходнага кода."

#: ../glade/glade_project_window.c:1628
msgid "Source code written."
msgstr "Зыходны код запісаны."

#: ../glade/glade_project_window.c:1659
msgid "System error message:"
msgstr "Сыстэмнае паведамленьне пра памылку:"

#: ../glade/glade_project_window.c:1698
msgid "Are you sure you want to quit?"
msgstr "Вы сапраўды жадаеце выйсьці?"

#: ../glade/glade_project_window.c:1982 ../glade/glade_project_window.c:2042
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "(C) 1998-2002 Damon Chaplin"

#: ../glade/glade_project_window.c:1983 ../glade/glade_project_window.c:2041
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr ""
"Glade - сродак стварэньня графічнага інтэрфэйса карыстальніка для GTK+ і "
"GNOME."

#: ../glade/glade_project_window.c:2012
msgid "About Glade"
msgstr "Выйсьці з Glade"

#: ../glade/glade_project_window.c:2097
msgid "<untitled>"
msgstr "<бяз назвы>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "Праглядальнік базы даньняў"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "Камбінаванае мэню абмежаваных даньняў"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr ""

#: ../glade/gnome-db/gnomedbconnectsel.c:147
msgid "Connection Selector"
msgstr "Выбар злучэньня"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "Канфігуратар DSN"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr "Майстар канфігураваньня DSN"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:178
#, fuzzy
msgid "GnomeDbEditor"
msgstr "Рэдагаваньне даты Gnome"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "Прагляд памылак базы даньняў"

#: ../glade/gnome-db/gnomedberrordlg.c:218
msgid "Database error dialog"
msgstr "Дыялёг памылкі базы даньняў"

#: ../glade/gnome-db/gnomedbform.c:147
msgid "Form"
msgstr "Форма"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr ""

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr ""

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "Сетка абмежаваных даньняў"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "Сьпіс абмежаваных даньняў"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "Віджэт рэгістрацыі ў базе даньняў"

#: ../glade/gnome-db/gnomedblogindlg.c:76
msgid "Login"
msgstr "Рэгістрацыя"

#: ../glade/gnome-db/gnomedblogindlg.c:219
msgid "Database login dialog"
msgstr "Дыялёг рэгістрацыі ў базе даньняў"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
msgid "Provider Selector"
msgstr "Выбар пастаўшчыка"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr ""

#: ../glade/gnome-db/gnomedbsourcesel.c:147
msgid "Data Source Selector"
msgstr "Выбар крыніцы даньняў"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
msgid "Table Editor "
msgstr "Рэдактар табліцаў"

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "Дазволіць руханьне:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "Ці дапушчальныя плаваючыя элемэнты, якія ўбудоўваюцца"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "Дадаць стужку для элемэнтаў, якія ўбудоўваюцца, зверху"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "Дадаць стужку для элемэнтаў, якія ўбудоўваюцца, знізу"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "Дадаць стужку для элемэнтаў,якія ўбудоўваюцца , злева"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "Дадаць стужку для элемэнтаў, якія ўбудоўваюцца, зправа"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "Дадаць плаваючы элемэнт, які ўбудоўваецца"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Gnome Dock"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "Фіксаваны:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "Ці зафіксавана пазыцыя элемэнту, які ўбудоўваецца"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "Эксклюзыўны:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "Ці зьяўляецца элемэнт, які ўбудоўваецца, эксклюзыўным у сваёй стужцы"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "Ніколі ня рушыць:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "Ці можа элемэнт, які ўбудоўваецца, рушыць у сваім прыватным акне"

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "Ніколі па вэртыкалі:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "Ці можа элемэнт, які ўбудоўваецца, быць вэртыкальным"

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "Ніколі па гарызанталі:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "Ці можа элемэнт, які ўбудоўваецца, быць гарызантальным"

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "Тып ценю вакол элемэнту, які ўбудоўваецца"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "Арыентацыя плаваючага элемэнту, які ўбудоўваецца"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "Дадаць элемэнт, які ўбудоўваецца, перад"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "Дадаць элемэнт, які ўбудоўваецца, пасьля"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Элемэнт Gnome, які ўбудоўваецца"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr ""
"Дадатковая інфармацыя, як апісаньне пакету й яго хатняя старонка ў Інтэрнэце"

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Дыялёг Gnome \"Пра праграму\""

#: ../glade/gnome/gnomeapp.c:170
msgid "New File"
msgstr "Новы файл"

#: ../glade/gnome/gnomeapp.c:172
msgid "Open File"
msgstr "Адчыніць файл"

#: ../glade/gnome/gnomeapp.c:174
msgid "Save File"
msgstr "Захаваць файл"

#: ../glade/gnome/gnomeapp.c:203
msgid "Status Bar:"
msgstr "Радок стану:"

#: ../glade/gnome/gnomeapp.c:204
msgid "If the window has a status bar"
msgstr "Калі ў акна ёсьць радок стану"

#: ../glade/gnome/gnomeapp.c:205
msgid "Store Config:"
msgstr "Захоўваць канфігурацыю:"

#: ../glade/gnome/gnomeapp.c:206
msgid "If the layout is saved and restored automatically"
msgstr "Калі пазыцыя вокнаў аўтаматычна захоўваецца й аднаўляецца"

#: ../glade/gnome/gnomeapp.c:442
msgid "Gnome Application Window"
msgstr "Акно дастасаваньня Gnome"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "Паведамленьне аб стане."

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "Выкананьне:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "Калі дастасаваньне мае радок індыкатару выкананьня"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "Стан:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr ""
"Калі дастасаваньне мае радок для ўводу карыстальніка й паведамленьняў аб "
"стане"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Радок дастасаваньня Gnome"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "Згладжанае:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr "Ці патрабуецца згладжаньне тэксту і графікі"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "Мінімальная каардыната x"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "Мінімальная каардыната y"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2:"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "Максымальная каардыната x"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "Максымальная каардыната y"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "Піксэляў на адзінку:"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "Колькасьць піксэляў , якія адпавядаюць, адной адзінцы"

#: ../glade/gnome/gnomecanvas.c:239
msgid "GnomeCanvas"
msgstr "GnomeCanvas"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "Зьмяшаньне:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "Калі мусіце выкарыстоўваць зьмяшаньне для большай дакладнасьці"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "Выбар колеру"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Выбар колеру ў Gnome"

#: ../glade/gnome/gnomecontrol.c:160
msgid "Couldn't create the Bonobo control"
msgstr "Немагчыма стварыць элемэнт кіраваньня Bonobo"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr "Новы элемэнт кіраваньня Bonobo"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr "Выбярыце элемэнт кіраваньня Bonobo"

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr "OAFIID"

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3896
msgid "Description"
msgstr "Апісаньне"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr "Элемэнт кіраваньня Bonobo"

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "Адлюстроўваць час:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "Калі час адлюстроўваецца таксама як і дата"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "24-х гадзінны фармат:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "Калі час адлюстроўваецца ў 24-х гадзінным фармаце"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "Найменшы час:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "Найменшы час, які адлюстроўваецца ў дыялёге"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "Найвялікі час:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "Найвялікі час, які адлюстроўваецца ў дыялёге"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "Рэдагаваньне даты Gnome"

#: ../glade/gnome/gnomedialog.c:152 ../glade/gnome/gnomemessagebox.c:189
msgid "Auto Close:"
msgstr "Аўтазакрыцьцё:"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "If the dialog closes when any button is clicked"
msgstr "Ці зачыняецца дыялёг калі націснута любая кнопка"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "Hide on Close:"
msgstr "Хаваць пад час закрыцьця:"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "Ці будзе дыялёг хавацца пад час закрыцьця замест зьнішчэньня"

#: ../glade/gnome/gnomedialog.c:341
msgid "Gnome Dialog Box"
msgstr "Дыялёгавае акно Gnome"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "Новы памочнік GNOME"

#: ../glade/gnome/gnomedruid.c:190
msgid "Show Help"
msgstr "Адлюстраваць дапамогу"

#: ../glade/gnome/gnomedruid.c:190
msgid "Display the help button."
msgstr "Адлюстраваць кнопку дапамогі"

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "Дадаць стартавую старонку"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "Дадаць апошнюю старонку"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "Памочнік"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "Загаловак старонкі"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "Асноўны тэкст старонкі, якую прадастаўляе памочнік."

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "Колер загалоўка:"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "Колер тэксту загалоўка"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "Колер тэксту:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "Колер асноўнага тэксту"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "Колер тла старонкі"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "Колер тла лягатыпу:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "Колер тла вакол лягатыпу"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "Колер тэкставага акна:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "Колер тла асноўнай вобласьці тэксту"

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
msgid "Logo Image:"
msgstr "Відарыс лягатыпу:"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "Лягатып в правым верхнім куце старонкі"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr "Бакавы вадзяны знак:"

#: ../glade/gnome/gnomedruidpageedge.c:111
msgid "The main image to display on the side of the page."
msgstr "Асноўны відарыс які адлюстроўваецца з боку старонкі"

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr "Верхні вадзяны знак:"

#: ../glade/gnome/gnomedruidpageedge.c:113
msgid "The watermark to display at the top of the page."
msgstr "Асноўны відарыс, які адлюстроўваецца, злева на старонцы."

#: ../glade/gnome/gnomedruidpageedge.c:522
msgid "Druid Start or Finish Page"
msgstr "Стартавая й апошняя старонкі памочніку"

#: ../glade/gnome/gnomedruidpagestandard.c:89
msgid "Contents Back. Color:"
msgstr "Колер тла зьместу:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
msgid "The background color around the title"
msgstr "Колер тла вакол загалоўку"

#: ../glade/gnome/gnomedruidpagestandard.c:98
msgid "The image to display along the top of the page"
msgstr "Відарыс які адлюстроўваецца ў самым версе старонкі"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "Стандартная старонка памочніку"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "ID гісторыі:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "Ідэнтыфікатар, пад якім будзе захаваны радок гісторыі ўводу"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "Макс. захаванае:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr ""
"Максымальная колькасьць запісаў гісторыі ўводу,якія могуць быць захаваныя"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Поле ўводу Gnome"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "Загаловак дыялёгу выбару файла"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "Каталёг:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "Калі каталёг патрэбны раней за файл"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "Калі дыялёг выбару файла мусіць быць мадальным"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Увод файлу Gnome"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "Тэкст, які адлюстроўваецца для прагляду ў дыялёге выбару шрыфту"

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "Рэжым:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "Што адлюстроўваць на кнопцы выбару шрыфту"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "Памер шрыфту які выкарыстоўваецца на кнопцы выбару шрыфту"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Выбар шрыфту ў Gnome"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "URL адлюстроўваеца калі націснута кнопка"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "Тэкст які адлюстроўваецца на кнопцы"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Кнопка HRef-спасылкі Gnome"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Значка Gnome"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "Рэжым вылучэньня"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "Шырыня значкі:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "Шырыня кожнай значкі"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "Колькасьць піксэляў між радкамі значак"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "Колькасьць піксэляў між слупкамі значак"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "Мяжа значкі:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "Колькасьць піксэляў вакол значкі (ня выкарыстоўваецца?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "Інтэрвал тэксту:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "Колькасьць піксэляў між тэкстам і значкай"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "Тэкст можна рэдагаваць:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "Ці можа тэкст пад значкай рэдагавацца карыстальнікам"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "Статычны тэкст:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr "Калі тэкст пад значкай статычны, ён ня будзе капіявацца GnomeIconList"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "Сьпіс значак"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "Выбар значкі"

#: ../glade/gnome/gnomemessagebox.c:174
msgid "Message Type:"
msgstr "Тып паведамленьня:"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "The type of the message box"
msgstr "Тып акна паведамленьня"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "Message:"
msgstr "Паведамленьне:"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "The message to display"
msgstr "Паведамленьне для адлюстраваньня"

#: ../glade/gnome/gnomemessagebox.c:498
msgid "Gnome Message Box"
msgstr "Акно паведамленьня Gnome"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "Файл які утрымлівае карцінку"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "Маштабаваная:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "Ці маштабуецца карцінка"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "Маштаб па шырыні:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "Шырыня для маштабаваньня відарысу"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "Маштаб па вышыні:"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "Вышыня для маштабаваньня відарысу"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Карцінка Gnome"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "Папярэдні прагляд:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "Ці адлюстроўваецца маленькі відарыс для прагляду"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmap:Entry"

#: ../glade/gnome/gnomepropertybox.c:112
msgid "New GnomePropertyBox"
msgstr "Новае акно уласьцівасьцяў Gnome"

#: ../glade/gnome/gnomepropertybox.c:365
msgid "Property Dialog Box"
msgstr "Дыялёгавае акно уласьцівасьцяў"

#: ../glade/main.c:70
msgid "Write the source code and exit"
msgstr "Запісаць зыходны код і выйсьці"

#: ../glade/main.c:74
msgid "Start with the palette hidden"
msgstr "Пачаць са схаванай палітрай"

#: ../glade/main.c:78
msgid "Start with the property editor hidden"
msgstr "Пачаць са схаваным рэдактарам уласьцівасьцяў"

#: ../glade/main.c:436
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade: для парамэтраў '-w' ці '--write-source' мусіць быць усталяваны XML-"
"файл\n"

#: ../glade/main.c:450
msgid "glade: Error loading XML file.\n"
msgstr "glade: Памылка загрузкі XML-файла.\n"

#: ../glade/main.c:457
msgid "glade: Error writing source.\n"
msgstr "glade: Памылка запісу зыходнага кода.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "Палітра"

#: ../glade/property.c:73
msgid "private"
msgstr "прыватнае"

#: ../glade/property.c:73
msgid "protected"
msgstr "абароненае"

#: ../glade/property.c:73
msgid "public"
msgstr "публічнае"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "Падсьветлены"

#: ../glade/property.c:103
msgid "Selected"
msgstr "Вылучаны"

#: ../glade/property.c:103
msgid "Insens"
msgstr "Нячулы"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "Калі акно патрабуецца перарысаваць"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "Пад час руху мышы"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "Падказкі перамяшчэньня мышы"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "Рух мышы зь любой націснутай кнопкай"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "Рух мышы з 1 націснутай кнопкай"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "Рух мышы з 2-ма націснутымі кнопкамі"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "Рух мышы з 3-ма націснутымі кнопкамі"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "Любая націснутая кнопка мышы"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "Любое адпусканьне кнопкі мышы"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "Націск якой-небудзь клявішы"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "Адпусканьне якой-небудзь клявішы"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "Пад час уваходзе курсора мышы ў акно"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "Калі курсор мышы пакідае акно"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "Пад час зьмены засяроджаньня акна"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "Любая зьмена ў структуры акна"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "Зьмена ўва уласьцівасьцях X Windows"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "Любая зьмена ў бачнасьці (visibility) акна"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "Для курсору ў праграмах знаёмых з XInput"

#: ../glade/property.c:596
msgid "Properties"
msgstr "Уласьцівасьці"

#: ../glade/property.c:620
msgid "Packing"
msgstr "Упакоўка"

#: ../glade/property.c:625
msgid "Common"
msgstr "Агульныя"

#: ../glade/property.c:631
msgid "Style"
msgstr "Стыль"

#: ../glade/property.c:637 ../glade/property.c:4640
msgid "Signals"
msgstr "Сыгналы"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "Уласьцівасьці: "

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "Уласьцівасьці: <няма>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "Кляса:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "Кляса элемэнту кіраваньня"

#: ../glade/property.c:813
msgid "Width:"
msgstr "Шырыня:"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""
"Запытаная шырыня віджэта (звычайна выкарыстоўваецца для усталёўкі "
"мінімальнае шырыні)"

#: ../glade/property.c:816
msgid "Height:"
msgstr "Вышыня:"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""
"Запытаная вышыня віджэта (звычайна выкарыстоўваецца для усталёўкі "
"мінімальнае вышыні)"

#: ../glade/property.c:820
msgid "Visible:"
msgstr "Бачнасьць:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "Ці патрабуецца першапачатковая бачнасьць віджэту"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "Чуласьць:"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "Ці рэагуе віджэт на ўвод"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr ""
"Падказка адлюстроўваецца калі курсор мышы затрымліваецца па-над элемэнтам"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "Можа быць дапомным:"

#: ../glade/property.c:828
msgid "If the widget can be the default action in a dialog"
msgstr "Элемэнт можа быць дапомным у дыялёге"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "Мае дапомнасьць:"

#: ../glade/property.c:830
msgid "If the widget is the default action in the dialog"
msgstr "Калі віджэт - дапомнае дзеяньне ў дыялёге"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "Можа засяроджвацца:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "Ці можа віджэт атрымліваць засяроджаньне"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "Мае засяроджаньне:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "Ці можа віджэт атрымліваць увод пасьля засяроджаньня"

#: ../glade/property.c:836
msgid "Events:"
msgstr "Падзеі:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "Падзеі X, якія прыймаюцца віджэтам"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "Пашыр.падзеі:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "Рэжым з выкарыстаньнем пашыраных падзеяў X Windows"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "Гарачыя клявішы:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "Вызначыць сыгнал, які выдаецца пад час націску клявішы"

#: ../glade/property.c:845
msgid "Edit..."
msgstr "Рэдагаваньне..."

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "Распаўсюдзіць:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr ""
"Усталяваць у TRUE, калі патрабуецца распаўсюдзіць стыль элемэнту на яго "
"нашчадкаў"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "Названы стыль:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "Назва стылю, які можна падзяляць  між некалькімі віджэтамі"

#: ../glade/property.c:872
msgid "Font:"
msgstr "Шрыфт:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "Шрыфт тэксту ў віджэце"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "Капіяваць усё"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "Пярэдні плян:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "Тло:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "База:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "Асноўны колер"

#: ../glade/property.c:928
msgid "Background color"
msgstr "Колер тла"

#: ../glade/property.c:928
msgid "Text color"
msgstr "Колер тэксту:"

#: ../glade/property.c:929
msgid "Base color"
msgstr "Базавы колер"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "Карцінка тла:"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "Карцінка, якая выкарыстоўваецца як тло віджэту"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "Назва файлу, куды пісаць код"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "Публічны:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "Віджэт дадаецца да структуры кампанэнты як элемэнт"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "Падзяліць клясу:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "Уставіць паддрэва гэтага віджэту ў асобную клясу"

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "Іншы файл:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "Уставіць віджэт у іншы файл"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "Бачнасьць:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr "Бачнасьць віджэту. Публічныя віджэты экспартуюцца у глябальную мапу."

#: ../glade/property.c:1126
msgid "You need to select a color or background to copy"
msgstr "Вам трэба выбраць колер ці тло для капіяваньня"

#: ../glade/property.c:1145
msgid "Invalid selection in on_style_copy()"
msgstr "Няверны выбар у/для функцыі on_style_copy()"

#: ../glade/property.c:1187
msgid "You need to copy a color or background pixmap first"
msgstr "Вы мусіце спачатку скапіяваць колер ці карцінку тла"

#: ../glade/property.c:1193
msgid "You need to select a color to paste into"
msgstr "Вы мусіце выбраць колер для устаўкі"

#: ../glade/property.c:1203
msgid "You need to select a background pixmap to paste into"
msgstr "Вы мусіце выбраць карцінку тла для ўстаўкі"

#: ../glade/property.c:1455
msgid "Couldn't create pixmap from file\n"
msgstr "Немагчыма стварыць карцінку з файла\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1497
msgid "Signal"
msgstr "Сыгнал"

#: ../glade/property.c:1499
msgid "Data"
msgstr "Даньні"

#: ../glade/property.c:1500
msgid "After"
msgstr "Пасьля"

#: ../glade/property.c:1501
msgid "Object"
msgstr "Аб'ект"

#: ../glade/property.c:1532 ../glade/property.c:1696
msgid "Signal:"
msgstr "Сыгнал:"

#: ../glade/property.c:1533
msgid "The signal to add a handler for"
msgstr "Сыгнал, для якога дадаецца апрацоўшчык"

#: ../glade/property.c:1547
msgid "The function to handle the signal"
msgstr "Функцыя, якая апрацоўвае сыгнал"

#: ../glade/property.c:1550
msgid "Data:"
msgstr "Даньні:"

#: ../glade/property.c:1551
msgid "The data passed to the handler"
msgstr "Даньні, якія перадаюцца функцыі-апрацоўшчыку"

#: ../glade/property.c:1552
msgid "Object:"
msgstr "Аб'ект:"

#: ../glade/property.c:1553
msgid "The object which receives the signal"
msgstr "Аб'ект, які прыймае сыгнал"

#: ../glade/property.c:1554
msgid "After:"
msgstr "Пасьля:"

#: ../glade/property.c:1555
msgid "If the handler runs after the class function"
msgstr "Апрацоўшчык запускаецца пасьля функцыі клясы"

#: ../glade/property.c:1568
msgid "Add"
msgstr "Дадаць"

#: ../glade/property.c:1574
msgid "Update"
msgstr "Абнавіць"

#: ../glade/property.c:1586
msgid "Clear"
msgstr "Ачысьціць"

#: ../glade/property.c:1636
msgid "Accelerators"
msgstr "Паскаральнікі"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1649
msgid "Mod"
msgstr "Рэжым"

#: ../glade/property.c:1650
msgid "Key"
msgstr "Клявіша"

#: ../glade/property.c:1651
msgid "Signal to emit"
msgstr "Сыгнал для ўзбуджэньня"

#: ../glade/property.c:1695
msgid "The accelerator key"
msgstr "Гарачая клявіша"

#: ../glade/property.c:1697
msgid "The signal to emit when the accelerator is pressed"
msgstr "Сыгнал, які ўзбуджаецца пад час націску гарачай клявішы"

#: ../glade/property.c:1846
msgid "Edit Text Property"
msgstr ""

#: ../glade/property.c:1884
msgid "<b>_Text:</b>"
msgstr ""

#: ../glade/property.c:1894
#, fuzzy
msgid "T_ranslatable"
msgstr "Радкі для перакладу:"

#: ../glade/property.c:1898
msgid "Has Context _Prefix"
msgstr ""

#: ../glade/property.c:1924
msgid "<b>Co_mments For Translators:</b>"
msgstr ""

#: ../glade/property.c:3886
msgid "Select X Events"
msgstr "Выбярыце падзею X"

#: ../glade/property.c:3895
msgid "Event Mask"
msgstr "Маска падзеяў"

#: ../glade/property.c:4025 ../glade/property.c:4074
msgid "You need to set the accelerator key"
msgstr "Вам трэба выбраць гарачую клявішу"

#: ../glade/property.c:4032 ../glade/property.c:4081
msgid "You need to set the signal to emit"
msgstr "Вам трэба выбраць сыгнал для ўзбуджэньня"

#: ../glade/property.c:4308 ../glade/property.c:4364
msgid "You need to set the signal name"
msgstr "Вам трэба усталяваць назву сыгналу"

#: ../glade/property.c:4315 ../glade/property.c:4371
msgid "You need to set the handler for the signal"
msgstr "Вам трэба усталяваць апрацоўшчык сыгналу"

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4574
#, c-format
msgid "%s signals"
msgstr "Сыгналы %s"

#: ../glade/property.c:4631
msgid "Select Signal"
msgstr "Выбар сыгналу"

#: ../glade/property.c:4827
msgid "Value:"
msgstr "Значэньне:"

#: ../glade/property.c:4827
msgid "Min:"
msgstr "Мін.:"

#: ../glade/property.c:4827
msgid "Step Inc:"
msgstr "Пав. крок:"

#: ../glade/property.c:4828
msgid "Page Inc:"
msgstr "Стар. пав.:"

#: ../glade/property.c:4828
msgid "Page Size:"
msgstr "Памер стар.:"

#: ../glade/property.c:4830
msgid "H Value:"
msgstr "Г знач.:"

#: ../glade/property.c:4830
msgid "H Min:"
msgstr "Г мін.:"

#: ../glade/property.c:4830
msgid "H Max:"
msgstr "Г макс.:"

#: ../glade/property.c:4830
msgid "H Step Inc:"
msgstr "Г пав. крок:"

#: ../glade/property.c:4831
msgid "H Page Inc:"
msgstr "Г стар. пав.:"

#: ../glade/property.c:4831
msgid "H Page Size:"
msgstr "Г памер старонкі:"

#: ../glade/property.c:4833
msgid "V Value:"
msgstr "У значэньне:"

#: ../glade/property.c:4833
msgid "V Min:"
msgstr "У мін.:"

#: ../glade/property.c:4833
msgid "V Max:"
msgstr "У макс.:"

#: ../glade/property.c:4833
msgid "V Step Inc:"
msgstr "У пав. крок:"

#: ../glade/property.c:4834
msgid "V Page Inc:"
msgstr "У стар. пав.:"

#: ../glade/property.c:4834
msgid "V Page Size:"
msgstr "У памер старонкі:"

#: ../glade/property.c:4837
msgid "The initial value"
msgstr "Пачатковае значэньне"

#: ../glade/property.c:4838
msgid "The minimum value"
msgstr "Мінімальнае значэньне"

#: ../glade/property.c:4839
msgid "The maximum value"
msgstr "Максымальнае значэньне"

#: ../glade/property.c:4840
msgid "The step increment"
msgstr "Крок павялічэньня"

#: ../glade/property.c:4841
msgid "The page increment"
msgstr "Павялічэньне па старонках"

#: ../glade/property.c:4842
msgid "The page size"
msgstr "Памер старонкі"

#: ../glade/property.c:4997
msgid "The requested font is not available."
msgstr "Запытаны шрыфт недаступны"

#: ../glade/property.c:5046
msgid "Select Named Style"
msgstr "Выбар названага стылю"

#: ../glade/property.c:5057
msgid "Styles"
msgstr "Стылі"

#: ../glade/property.c:5116
msgid "Rename"
msgstr "Зьмяніць назву"

#: ../glade/property.c:5144
msgid "Cancel"
msgstr "Адмяніць"

#: ../glade/property.c:5264
msgid "New Style:"
msgstr "Новы стыль:"

#: ../glade/property.c:5278 ../glade/property.c:5399
msgid "Invalid style name"
msgstr "Няправільнае імя/назва стылю"

#: ../glade/property.c:5286 ../glade/property.c:5409
msgid "That style name is already in use"
msgstr "Гэтае імя стылю ўжо дзесьці выкарыстоўваецца"

#: ../glade/property.c:5384
msgid "Rename Style To:"
msgstr "Зьмяніць назву стылю як:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"Немагчыма зьмяніць назву файлу:\n"
"  %s\n"
"в:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"Немагчыма стварыць файл:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "Памылка запісу XML файлу\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"Памылковае імя файла зыходнага тэкста інтэрфэйсу: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"Памылковае імя файла загалоўкаў інтэрфэйсу: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"Памылковае імя файла зыходнага тэксту адваротных выклікаў: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"Памылковае імя файла загалоўкаў адваротных выклікаў: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"Памылковае імя файла зыходнага тэксту падтрымкі: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"Памылковае імя файла загалоўкаў падтрымкі: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"Ня атрымалася дадаць да файлу:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"Памылка запісу файла:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr "Назва файла мусіць быць усталявана ў дыялёге \"Парамэтры праекту\"."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"Назва файла мусіць быць простым адносным імем.\n"
"Выкарыстоўвайце дыялёг \"Парамэтры праекту\", каб усталяваць яго."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "Дрэва віджэтаў"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "Віджэт ня адшуканы (у box)"

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "Віджэт ня адшуканы ў табліцы"

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "Віджэт ня адшуканы у кантэйнэры з фіксацыяй"

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "Віджэт ня адшуканы (у packer)"

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"Ня атрымалася атрымаць доступ да файлу:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"Ня атрымалася адчыніць файл:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"Памылка пад час чытаньня файлу:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"Ня атрымалася стварыць каталёг:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"Няма доступу да каталёгу:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"Няправільны каталёг:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Праекты"

#: ../glade/utils.c:1628
msgid "project"
msgstr "праект"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"Немагчыма адчыніць каталёг:\n"
"  %s\n"
