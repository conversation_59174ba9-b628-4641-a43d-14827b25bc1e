/*  Gtk+ User Interface Builder
 *  Copyright (C) 1999  <PERSON>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2 of the License, or
 *  (at your option) any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a copy of the GNU General Public License
 *  along with this program; if not, write to the Free Software
 *  Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 */

#include <config.h>

#include <gnome.h>
#include "../gb.h"

/* Include the 21x21 icon pixmap for this widget, to be used in the palette */
#include "../graphics/gnome-appbar.xpm"

/*
 * This is the GbWidget struct for this widget (see ../gbwidget.h).
 * It is initialized in the init() function at the end of this file
 */
static GbWidget gbwidget;

static gchar *HasProgress = "GnomeApp::has_progress";
static gchar *HasStatus = "GnomeApp::has_status";


/******
 * NOTE: To use these functions you need to uncomment them AND add a pointer
 * to the function in the GbWidget struct at the end of this file.
 ******/

/*
 * Creates a new GtkWidget of class GnomeAppBar, performing any specialized
 * initialization needed for the widget to work correctly in this environment.
 * If a dialog box is used to initialize the widget, return NULL from this
 * function, and call data->callback with your new widget when it is done.
 * If the widget needs a special destroy handler, add a signal here.
 */
static GtkWidget*
gb_gnome_appbar_new (GbWidgetNewData *data)
{
  GtkWidget *new_widget;

  new_widget = gnome_appbar_new (TRUE, TRUE, GNOME_PREFERENCES_NEVER);

  gnome_appbar_set_status (GNOME_APPBAR (new_widget), _("Status Message."));

  return new_widget;
}



/*
 * Creates the components needed to edit the extra properties of this widget.
 */
static void
gb_gnome_appbar_create_properties (GtkWidget * widget, GbWidgetCreateArgData * data)
{
  property_add_bool (HasProgress, _("Progress:"),
		     _("If the app bar has a progress indicator"));
  property_add_bool (HasStatus, _("Status:"),
		     _("If the app bar has an area for status messages and user input"));
}



/*
 * Gets the properties of the widget. This is used for both displaying the
 * properties in the property editor, and also for saving the properties.
 */
static void
gb_gnome_appbar_get_properties (GtkWidget *widget, GbWidgetGetArgData * data)
{
  gb_widget_output_bool (data, HasProgress,
			 GTK_WIDGET_VISIBLE (gnome_appbar_get_progress (GNOME_APPBAR (widget))));
  gb_widget_output_bool (data, HasStatus,
			 GTK_WIDGET_VISIBLE (gnome_appbar_get_status (GNOME_APPBAR (widget))));
}



/*
 * Sets the properties of the widget. This is used for both applying the
 * properties changed in the property editor, and also for loading.
 */
static void
gb_gnome_appbar_set_properties (GtkWidget * widget, GbWidgetSetArgData * data)
{
  gboolean has_progress, has_status;

  has_progress = gb_widget_input_bool (data, HasProgress);
  if (data->apply)
    {
      if (has_progress)
	gtk_widget_show (GTK_WIDGET (gnome_appbar_get_progress (GNOME_APPBAR (widget))));
      else
	gtk_widget_hide (GTK_WIDGET (gnome_appbar_get_progress (GNOME_APPBAR (widget))));
    }

  has_status = gb_widget_input_bool (data, HasStatus);
  if (data->apply)
    {
      if (has_status)
	gtk_widget_show (gnome_appbar_get_status (GNOME_APPBAR (widget)));
      else
	gtk_widget_hide (gnome_appbar_get_status (GNOME_APPBAR (widget)));

      /* We need to clear the selection or redrawing is messed up. */
      editor_clear_selection (NULL);
    }
}



/*
 * Adds menu items to a context menu which is just about to appear!
 * Add commands to aid in editing a GnomeAppBar, with signals pointing to
 * other functions in this file.
 */
/*
static void
gb_gnome_appbar_create_popup_menu (GtkWidget * widget, GbWidgetCreateMenuData * data)
{

}
*/



/*
 * Writes the source code needed to create this widget.
 * You have to output everything necessary to create the widget here, though
 * there are some convenience functions to help.
 */
static void
gb_gnome_appbar_write_source (GtkWidget * widget, GbWidgetWriteSourceData * data)
{
  if (data->create_widget)
    {
      source_add (data,
		  "  %s = gnome_appbar_new (%s, %s, GNOME_PREFERENCES_NEVER);\n",
		  data->wname,
		  GTK_WIDGET_VISIBLE (gnome_appbar_get_progress (GNOME_APPBAR (widget)))
		  ? "TRUE" : "FALSE",
		  GTK_WIDGET_VISIBLE (gnome_appbar_get_status (GNOME_APPBAR (widget)))
		  ? "TRUE" : "FALSE");
    }

  gb_widget_write_standard_source (widget, data);

  /* If the GnomeAppBar is in a GnomeApp, the code to install the menu hints
     is output in gnomedockitem.c. */
}



/*
 * Initializes the GbWidget structure.
 * I've placed this at the end of the file so we don't have to include
 * declarations of all the functions.
 */
GbWidget*
gb_gnome_appbar_init ()
{
  /* Initialise the GTK type */
  volatile GtkType type;
  type = gnome_appbar_get_type();

  /* Initialize the GbWidget structure */
  gb_widget_init_struct(&gbwidget);

  /* Fill in the pixmap struct & tooltip */
  gbwidget.pixmap_struct = gnome_appbar_xpm;
  gbwidget.tooltip = _("Gnome Application Bar");

  /* Fill in any functions that this GbWidget has */
  gbwidget.gb_widget_new		= gb_gnome_appbar_new;
  gbwidget.gb_widget_create_properties	= gb_gnome_appbar_create_properties;
  gbwidget.gb_widget_get_properties	= gb_gnome_appbar_get_properties;
  gbwidget.gb_widget_set_properties	= gb_gnome_appbar_set_properties;
  gbwidget.gb_widget_write_source	= gb_gnome_appbar_write_source;
/*
  gbwidget.gb_widget_create_popup_menu	= gb_gnome_appbar_create_popup_menu;
*/

  return &gbwidget;
}

