💛💙

Help file for GTK version of Deadbeef Player (Linux/*BS<PERSON> and friends)

* LINKS

    main website: https://deadbeef.sourceforge.io
    wiki: https://github.com/DeaDBeeF-Player/deadbeef/wiki
    development website: https://github.com/DeaDBeeF-Player/deadbeef
    info for contributors: https://github.com/DeaDBeeF-Player/deadbeef/blob/master/CONTRIBUTING.md

* CUESHEETS

    To use cuesheets, just open/add the cue files, select audio + cue files in the file dialog, or add whole folders with audio and cue files to playlist.

    Adding a single audio file would not load a cuesheet, unless it's embedded in the file.

* CONFIGURATION

    Most settings can be configured through the Edit->Preferences dialog.

    All configuration is stored in the $HOME/.config/deadbeef folder.
    Individual settings can be tweaked in the $HOME/.config/deadbeef/config text file.

    You should exit the player before editing this file, or your changes will be overwritten.

* ALBUM ART DISPLAY

    To display album art in the playlist, you need to perform the following steps:

    1. Add a new column, and choose "Album Art" column type
    2. Right click on the playlist column headers, and in the context menu select "Group by" submenu. Click "Artist/Date/Album", or another option which you prefer.

    Another option is to use the album art widget in design mode (View -> Design mode)

* OSD NOTIFICATIONS

    You can enable/configure them in "OSD Notify" plugin options.
    The plugin requires notification daemon to be installed and enabled.

