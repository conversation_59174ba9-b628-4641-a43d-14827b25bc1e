# Glade Korean messages
# Copyright (C) 2000 Chang<PERSON><PERSON> Ryu
# <PERSON><PERSON><PERSON> Ryu <<EMAIL>>, 2000.
#
#
# 원칙:
#
# Glade는 GTK+ 개발자가 사용하는 도구로서, Glade 사용자는 이미 GTK+에 관한
# 지식이 있을 것이라고 가정한다.  무리한 GTK+ 위젯 이름의 번역은 삼가할
# 것이다.
#
msgid ""
msgstr ""
"Project-Id-Version: glade 0.5.9\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2005-08-26 13:38+0200\n"
"PO-Revision-Date: 2000-09-20 07:16+0900\n"
"Last-Translator: Chang<PERSON><PERSON> Ryu <<EMAIL>>\n"
"Language-Team: Korean <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../glade-2.desktop.in.h:1
msgid "Design user interfaces"
msgstr ""

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr ""

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "그리드 설정"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "가로 간격:"

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "세로 간격:"

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "그리드 스타일:"

#: ../glade/editor.c:396
msgid "Dots"
msgstr "점"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "선"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "스냅 설정"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "가로 스냅:"

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "왼쪽으로 나란히"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "오른쪽으로 나란히"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "세로 스냅"

#: ../glade/editor.c:532
msgid "Top"
msgstr "맨 위"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "맨 아래"

#: ../glade/editor.c:741
#, fuzzy
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "GnomeDockItem 위젯은 다만 GnomeDock에 붙여 넣을 수 있습니다."

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "Gtk스크롤윈도우 위젯을 삽입할 수 없습니다."

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "Gtk보기창 위젯을 삽입할 수 없습니다."

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "새로운 위젯을 추가할 수 없습니다."

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"선택한 위치에 위젯을 추가할 수 없습니다.\n"
"\n"
"제시: GTK+에서는 위젯을 배열하기 위해 콘테이너를 씁니다.\n"
"이미 있는 위젯을 삭제하고 대신에 박스 또는 \n"
"테이블 콘테이너를 써 보십시오.\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "위젯을 삭제할 수 없습니다."

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "위젯이 삭제되지 않습니다."

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr ""
"이 위젯은 부모위젯의 일부로 자동적으로 생성된 것이기에 삭제할 수 없습니다."

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "테두리 두께:"

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "콘테이너 주위 변두리의 너비"

#: ../glade/gbwidget.c:1745
msgid "Select"
msgstr "선택"

#: ../glade/gbwidget.c:1767
msgid "Remove Scrolled Window"
msgstr "스크롤된 윈도우의 삭제"

#: ../glade/gbwidget.c:1776
msgid "Add Scrolled Window"
msgstr "스크롤된 윈도우의 추가"

#: ../glade/gbwidget.c:1797
msgid "Remove Alignment"
msgstr "Alignment 삭제"

#: ../glade/gbwidget.c:1805
msgid "Add Alignment"
msgstr "Alignment 추가"

#: ../glade/gbwidget.c:1820
msgid "Remove Event Box"
msgstr "이벤트박스 삭제"

#: ../glade/gbwidget.c:1828
msgid "Add Event Box"
msgstr "이벤트박스 추가"

#: ../glade/gbwidget.c:1838
msgid "Redisplay"
msgstr "새로고침"

#: ../glade/gbwidget.c:1849
msgid "Cut"
msgstr "잘라내기"

#: ../glade/gbwidget.c:1856 ../glade/property.c:892 ../glade/property.c:5135
msgid "Copy"
msgstr "복사"

#: ../glade/gbwidget.c:1865 ../glade/property.c:904
msgid "Paste"
msgstr "붙여넣기"

#: ../glade/gbwidget.c:1877 ../glade/property.c:1580 ../glade/property.c:5126
msgid "Delete"
msgstr "지우기"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2403 ../glade/gbwidget.c:2472
msgid "N/A"
msgstr "미동작"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3202
msgid "replacing child of container - not implemented yet\n"
msgstr "콘테이너의 자식을 바꾸는 중 -아직 구현되지 않았음\n"

#: ../glade/gbwidget.c:3430
msgid "Couldn't insert GtkAlignment widget."
msgstr "GtkAlignment 위젯을 삽입할 수 없습니다."

#: ../glade/gbwidget.c:3470
msgid "Couldn't remove GtkAlignment widget."
msgstr "GtkAlignment 위젯을 삭제할 수 없습니다."

#: ../glade/gbwidget.c:3494
msgid "Couldn't insert GtkEventBox widget."
msgstr "GtkEventBox 위젯을 삽입할 수 없습니다."

#: ../glade/gbwidget.c:3533
msgid "Couldn't remove GtkEventBox widget."
msgstr "GtkEventBox 위젯을 삭제할 수 없습니다."

#: ../glade/gbwidget.c:3568
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "GtkScrolledWindow 위젯을 삽입할 수 없습니다."

#: ../glade/gbwidget.c:3607
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "GtkScrolledWindow 위젯을 삭제할 수 없습니다."

#: ../glade/gbwidget.c:3721
msgid "Remove Label"
msgstr "라벨의 삭제"

#: ../glade/gbwidgets/gbaboutdialog.c:78
#, fuzzy
msgid "Application Name"
msgstr "그놈 애플리케이션 바"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "로고:"

#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "로고로 쓰이는 픽스맵"

#: ../glade/gbwidgets/gbaboutdialog.c:104 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "프로그램명:"

#: ../glade/gbwidgets/gbaboutdialog.c:104
#, fuzzy
msgid "The name of the application"
msgstr "위젯의 명칭"

#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "해설:"

#: ../glade/gbwidgets/gbaboutdialog.c:105
#, fuzzy
msgid "Additional information, such as a description of the application"
msgstr "패키지 설명, 웹의 홈페이지등의 추가정보 "

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "저작권:"

#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "저작권 명시"

#: ../glade/gbwidgets/gbaboutdialog.c:108
msgid "Website URL:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:108
#, fuzzy
msgid "The URL of the application's website"
msgstr "그놈 애플리케이션을 만들 지"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "Website Label:"
msgstr "라벨:"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "The label to display for the link to the website"
msgstr "페이지의 왼쪽에 표시되는 메인 이미지"

#: ../glade/gbwidgets/gbaboutdialog.c:111 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "라이센스:"

#: ../glade/gbwidgets/gbaboutdialog.c:111
#, fuzzy
msgid "The license details of the application"
msgstr "버튼의 들어가기 스타일"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "저자:"

#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "패키지의 저자, 매 줄마다 기입"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
#, fuzzy
msgid "Documenters:"
msgstr "해설:"

#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
#, fuzzy
msgid "The documenters of the package, one on each line"
msgstr "패키지의 저자, 매 줄마다 기입"

#: ../glade/gbwidgets/gbaboutdialog.c:115
msgid "Artists:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:115
#, fuzzy
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr "패키지의 저자, 매 줄마다 기입"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
#, fuzzy
msgid "Translators:"
msgstr "번역할 수 있는 스트링:"

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:559
#, fuzzy
msgid "About Dialog"
msgstr "그놈 프로그램 정보 다이어로그"

#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "가속기의 라벨"

#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:180 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "X 조정:"

#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "자식위젯의 가로방향의 조정"

#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:183 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Y 조정:"

#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "자식위젯의 새로방향의 조정"

#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "X 스케일:"

#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "자식위젯의 가로방향의 스케일"

#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Y 스케일:"

#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "자식위젯의 새로방향의 스케일"

#: ../glade/gbwidgets/gbalignment.c:85
#, fuzzy
msgid "Top Padding:"
msgstr "가로 패딩:"

#: ../glade/gbwidgets/gbalignment.c:86
#, fuzzy
msgid "Space to put above the child widget"
msgstr "자식위젯의 가로방향의 스케일"

#: ../glade/gbwidgets/gbalignment.c:89
#, fuzzy
msgid "Bottom Padding:"
msgstr "가로 패딩:"

#: ../glade/gbwidgets/gbalignment.c:90
#, fuzzy
msgid "Space to put below the child widget"
msgstr "자식위젯의 가로방향의 스케일"

#: ../glade/gbwidgets/gbalignment.c:93
#, fuzzy
msgid "Left Padding:"
msgstr "가로 패딩:"

#: ../glade/gbwidgets/gbalignment.c:94
#, fuzzy
msgid "Space to put to the left of the child widget"
msgstr "자식위젯의 가로방향의 스케일"

#: ../glade/gbwidgets/gbalignment.c:97
#, fuzzy
msgid "Right Padding:"
msgstr "가로 패딩:"

#: ../glade/gbwidgets/gbalignment.c:98
#, fuzzy
msgid "Space to put to the right of the child widget"
msgstr "자식위젯의 가로방향의 조정"

#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "조정"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "방향:"

#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "화살표의 방향"

#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "그림자:"

#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "화살의 그림자의 종류"

#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "화살의 가로방향의 조정"

#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "화살의 새로방향의 조정"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186
msgid "X Pad:"
msgstr "X 패드:"

#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "가로방향의 패딩"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188
msgid "Y Pad:"
msgstr "Y 패드:"

#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "세로 패딩"

#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "화살표"

#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "X 방향의 라벨의 조정:"

#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
#, fuzzy
msgid "The horizontal alignment of the frame's label widget"
msgstr "프레임라벨의 가로방향의 조정"

#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
#, fuzzy
msgid "Label Y Align:"
msgstr "X 방향의 라벨의 조정:"

#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
#, fuzzy
msgid "The vertical alignment of the frame's label widget"
msgstr "입력라벨의 세로방향의 조정"

#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "프레임그림자의 유형"

#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "프레임의 자식의 가로방향의 조정"

#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "비율:"

#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "프레임의 자식의 종횡비율"

#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "자식에 따름:"

#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "종횡비가 자식에 의해 결정되는가요?"

#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "모양 프레임"

#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "스톡 버튼:"

#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
#, fuzzy
msgid "The stock button to use"
msgstr "그놈 스톡 버튼의 사용"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:747
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "라벨:"

#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "표시할 텍스트"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:295
#: ../glade/glade_menu_editor.c:813
msgid "Icon:"
msgstr "아이콘:"

#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
#, fuzzy
msgid "The icon to display"
msgstr "표시할 픽스맵"

#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "버튼 들어가기:"

#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "버튼의 들어가기 스타일"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
#, fuzzy
msgid "If the button grabs focus when it is clicked"
msgstr "다이어로그가 임의의 버튼이 눌리는것으로 자동적으로 닫기우는가요?"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "버튼"

#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "머리글:"

#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "맨 위에 년과 달을 표시할까요?"

#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "요일명:"

#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "요일명을 표시할까요?"

#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "고정된 달:"

#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "년과 달이 변경할 수 없게끔 할까요?"

#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "주일 번호:"

#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "주일 번호를 표시할까요?"

#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "월요일부터 시작:"

#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "주일의 시작을 월요일로 할까요?"

#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "달력"

#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "배경색:"

#: ../glade/gbwidgets/gbcellview.c:64
#, fuzzy
msgid "The background color"
msgstr "배경 색깔"

#: ../glade/gbwidgets/gbcellview.c:192
#, fuzzy
msgid "Cell View"
msgstr "텍스트 보기:"

#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "최초부터 온:"

#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "확인버튼이 최초로부터 온이였는가요 ?"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
#, fuzzy
msgid "Inconsistent:"
msgstr "아이콘 목록"

#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
#, fuzzy
msgid "If the button is shown in an inconsistent state"
msgstr "날짜와 마찬가지로 시간을 표기할까요?"

#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "지시물:"

#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "지시물을 언제나 그려져 있게 할까요?"

#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "확인버튼"

#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "체크메뉴아이템이 최초부터 온 인가요?"

#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "메뉴아이템을 체크하기"

#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "새 열 리스트"

#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "열수:"

#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:127 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "선택모드:"

#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "열 리스트의 선택모드"

#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "제목 보이기:"

#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "열의 제목을 보일까요?"

#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "열 리스트의 테두리의 그림자의 유형"

#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "열 리스트"

#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "알파사용:"

#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "알파채널을 사용했나요?"

#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:85
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:242
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "제목:"

#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "색깔선택다이어로그의 제목"

#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
#, fuzzy
msgid "Pick a Color"
msgstr "색깔의 선택"

#: ../glade/gbwidgets/gbcolorbutton.c:211
#, fuzzy
msgid "Color Chooser Button"
msgstr "확인버튼"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr ""

#: ../glade/gbwidgets/gbcolorselection.c:63
#, fuzzy
msgid "If the opacity control is shown"
msgstr "요일명을 표시할까요?"

#: ../glade/gbwidgets/gbcolorselection.c:64
#, fuzzy
msgid "Palette:"
msgstr "팔렛트"

#: ../glade/gbwidgets/gbcolorselection.c:65
#, fuzzy
msgid "If the palette is shown"
msgstr "스케일수치를 보일까요?"

#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "색깔 선택"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:70
msgid "Select Color"
msgstr "색깔 선택하기"

#: ../glade/gbwidgets/gbcolorselectiondialog.c:315 ../glade/property.c:1275
msgid "Color Selection Dialog"
msgstr "색깔선택 다이어로그"

#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "리스트중의 수치:"

#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "수치가 리스트중에 있지 않으면 안되는가요?"

#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "빈수치 허용:"

#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr "리스트의 수치가 설정되여 있을 때 빈 수치를 허용하는가요?"

#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "대소 구별:"

#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "대문자.소문자를 구별하여 검색할까요?"

#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "화살표의 사용:"

#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "화살표로 수치의 변경이 될수 있도록 할까요?"

#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "언제나 사용:"

#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "리스트에 수치가 없어도 화살표가 움직이는가요?"

#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "아이템:"

#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "콤보 리스트의 아이템 ,한줄에 하나씩"

#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "콤보 박스"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
#, fuzzy
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "다이어로그가 임의의 버튼이 눌리는것으로 자동적으로 닫기우는가요?"

#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
#, fuzzy
msgid "Has Frame:"
msgstr "프레임"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr ""

#: ../glade/gbwidgets/gbcomboboxentry.c:302
#, fuzzy
msgid "Combo Box Entry"
msgstr "콤보 박스"

#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "새 열 트리"

#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "열 트리의 선택모드"

#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "열 트리의 테두리 그림자의 유형"

#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "열 트리"

#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:245
msgid "Type:"
msgstr "유형:"

#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "곡선의 유형"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "X방향최소치:"

#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "가로방향의 최소치 "

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "X 방향최대치:"

#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "가로방향의 최대치"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Y 방향최소치:"

#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "새로방향의 최소치"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Y 방향최대치:"

#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "새로방향의 최대치"

#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "곡선"

#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "생성함수:"

#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "위젯을 생성하는 함수"

#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "스트링1:"

#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "함수에 넘어가는 첫번째의 스트링인자"

#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "스트링2: "

#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "함수에 넘어가는 두번째의 스트링인자"

#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Int1:"

#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "함수에 넘어가는 첫번째의 Int인자"

#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Int2:"

#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "함수에 넘어가는 두번째의 Int인자"

#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "사용자 위젯"

#: ../glade/gbwidgets/gbdialog.c:292
#, fuzzy
msgid "New dialog"
msgstr "새 그림"

#: ../glade/gbwidgets/gbdialog.c:304
#, fuzzy
msgid "Cancel, OK"
msgstr "취소"

#: ../glade/gbwidgets/gbdialog.c:313 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1316 ../glade/property.c:5156
msgid "OK"
msgstr "확인"

#: ../glade/gbwidgets/gbdialog.c:322
msgid "Cancel, Apply, OK"
msgstr ""

#: ../glade/gbwidgets/gbdialog.c:331
msgid "Close"
msgstr "닫기"

#: ../glade/gbwidgets/gbdialog.c:340
msgid "_Standard Button Layout:"
msgstr ""

#: ../glade/gbwidgets/gbdialog.c:349
#, fuzzy
msgid "_Number of Buttons:"
msgstr "행수:"

#: ../glade/gbwidgets/gbdialog.c:366
#, fuzzy
msgid "Show Help Button"
msgstr "스핀 버튼"

#: ../glade/gbwidgets/gbdialog.c:397
#, fuzzy
msgid "Has Separator:"
msgstr "분리기"

#: ../glade/gbwidgets/gbdialog.c:398
#, fuzzy
msgid "If the dialog has a horizontal separator above the buttons"
msgstr "버튼의 가로방향의 패딩"

#: ../glade/gbwidgets/gbdialog.c:605
msgid "Dialog"
msgstr "다이어로그"

#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "그림구역"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "편집가능:"

#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "텍스트를 편집할 수 있을까요?"

#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "텍스트 보기:"

#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr "입력한 텍스트를 전시할까요?"

#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "최대길이:"

#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "텍스트의 최대 길이"

#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "텍스트:"

#: ../glade/gbwidgets/gbentry.c:102
#, fuzzy
msgid "If the entry has a frame around it"
msgstr "애플리케이션 바가 프로그레스 지시부를 갖고 있는가요?"

#: ../glade/gbwidgets/gbentry.c:103
#, fuzzy
msgid "Invisible Char:"
msgstr "보이는 것:"

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""

#: ../glade/gbwidgets/gbentry.c:104
#, fuzzy
msgid "Activates Default:"
msgstr "디폴트설정:"

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr ""

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr ""

#: ../glade/gbwidgets/gbentry.c:105
#, fuzzy
msgid "The number of characters to leave space for in the entry"
msgstr "테이블의 열수"

#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "텍스트 엔트리"

#: ../glade/gbwidgets/gbeventbox.c:65
#, fuzzy
msgid "Visible Window:"
msgstr "보이는 것:"

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr ""

#: ../glade/gbwidgets/gbeventbox.c:66
#, fuzzy
msgid "Above Child:"
msgstr "자식에 따름:"

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr ""

#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "이벤트박스"

#: ../glade/gbwidgets/gbexpander.c:54
#, fuzzy
msgid "Initially Expanded:"
msgstr "최초부터 온:"

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr ""

#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "간격:"

#: ../glade/gbwidgets/gbexpander.c:58
#, fuzzy
msgid "Space to put between the label and the child"
msgstr "텍스트와 아이콘 사이의 픽셀 수"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
#, fuzzy
msgid "Add Label Widget"
msgstr "Alignment 추가"

#: ../glade/gbwidgets/gbexpander.c:228
#, fuzzy
msgid "Expander"
msgstr "확장:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#, fuzzy
msgid "The window title of the file chooser dialog"
msgstr "파일선택 다이어로그의 제목"

#: ../glade/gbwidgets/gbfilechooserbutton.c:87
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:156
#: ../glade/gnome/gnomefileentry.c:109
#, fuzzy
msgid "Action:"
msgstr "방향:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:157
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:90
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
msgid "Local Only:"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:160
msgid "Whether the selected files should be limited to local files"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
#, fuzzy
msgid "Show Hidden:"
msgstr "시간 보이기:"

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether the hidden files and folders should be displayed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gblabel.c:200
msgid "Width in Chars:"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#, fuzzy
msgid "The width of the button in characters"
msgstr "배열구역의 너비"

#: ../glade/gbwidgets/gbfilechooserbutton.c:283
#, fuzzy
msgid "File Chooser Button"
msgstr "확인버튼"

#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
#, fuzzy
msgid "Select Multiple:"
msgstr "파일선택"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether to allow multiple files to be selected"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserwidget.c:260
#, fuzzy
msgid "File Chooser"
msgstr "제목 색깔:"

#: ../glade/gbwidgets/gbfilechooserdialog.c:421
#, fuzzy
msgid "File Chooser Dialog"
msgstr "파일선택 다이어로그"

#: ../glade/gbwidgets/gbfileselection.c:71 ../glade/property.c:1365
msgid "Select File"
msgstr "파일선택"

#: ../glade/gbwidgets/gbfileselection.c:113
msgid "File Ops.:"
msgstr "파일 조작:"

#: ../glade/gbwidgets/gbfileselection.c:114
msgid "If the file operation buttons are shown"
msgstr "파일 조작버튼을 보이게 할까요?"

#: ../glade/gbwidgets/gbfileselection.c:292
msgid "File Selection Dialog"
msgstr "파일선택 다이어로그"

#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
#, fuzzy
msgid "X:"
msgstr "X1:"

#: ../glade/gbwidgets/gbfixed.c:140
#, fuzzy
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "테이블내의 위젯의 웃머리"

#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
#, fuzzy
msgid "Y:"
msgstr "Y1:"

#: ../glade/gbwidgets/gbfixed.c:143
#, fuzzy
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "테이블내의 위젯의 웃머리"

#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "고정된 위치"

#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "글꼴 선택 다이어로그의 제목"

#: ../glade/gbwidgets/gbfontbutton.c:70
#, fuzzy
msgid "Show Style:"
msgstr "제목 보이기:"

#: ../glade/gbwidgets/gbfontbutton.c:71
#, fuzzy
msgid "If the font style is shown as part of the font information"
msgstr "글꼴 정보에 사이즈가 포함되는가요?"

#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "크기를 보이기:"

#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "글꼴 정보에 사이즈가 포함되는가요?"

#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "글꼴 사용:"

#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr "글꼴 정보를 표시할 때 선택된 글꼴이 사용되는가요?"

#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "사이즈 사용:"

#: ../glade/gbwidgets/gbfontbutton.c:77
#, fuzzy
msgid "if the selected font size is used when displaying the font information"
msgstr "글꼴 정보를 표시할 때 선택된 글꼴이 사용되는가요?"

#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "글꼴 선택"

#: ../glade/gbwidgets/gbfontbutton.c:268
#, fuzzy
msgid "Font Chooser Button"
msgstr "확인버튼"

#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "미리보기 텍스트:"

#: ../glade/gbwidgets/gbfontselection.c:64
#, fuzzy
msgid "The preview text to display"
msgstr "표시할 텍스트"

#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "글꼴 선택"

#: ../glade/gbwidgets/gbfontselectiondialog.c:69
msgid "Select Font"
msgstr "글꼴 선택하기"

#: ../glade/gbwidgets/gbfontselectiondialog.c:300
msgid "Font Selection Dialog"
msgstr "글꼴 선택 다이어로그"

#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "프레임"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "초기의 유형:"

#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "최초의 곡선의 유형"

#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "감마 곡선"

#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "핸들 박스의 주위그림자의 유형"

#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "핸들의 위치:"

#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "핸들을 표시하는 위치"

#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "스냅 단면:"

#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "스냅할 핸들박스의 단면"

#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "핸들 박스"

#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "새로운 가로박스"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "크기:"

#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "박스중의 위젯수"

#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "균일화:"

#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "자식들의 크기를 똑같게 할까요?"

#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "자식과 자식들간의 간격"

#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "어느 자식도 삭제할 수 없습니다."

#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:254
msgid "Position:"
msgstr "위치:"

#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "위젯과 형제의 상대위치"

#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "패딩:"

#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "위젯의 패딩"

#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "확장:"

#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "위젯이 자동적으로 확장합니다."

#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "구역을 채우기:"

#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "위젯이 분할구역을 채우게 하기 "

#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "첫머리부터 싸기:"

#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "위젯은 박스의 첫머리로부터 싸게 된다"

#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "앞에 삽입"

#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "뒤에 삽입"

#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "가로박스"

#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "새로운 가로버튼박스 "

#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "버튼수"

#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "배열:"

#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "버튼의 배열 유형"

#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "버튼간의 간격"

#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "가로 버튼 박스"

#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "분할위치"

#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:283
msgid "Shrink:"
msgstr "축소:"

#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "위젯을 압축할 수 있을가요?"

#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "사이즈변경:"

#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "위젯에 사이즈변경 시킬까요?"

#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "가로 페인"

#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "단위:"

#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "자의 단위"

#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "최소치:"

#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "자의 최소치"

#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "최대치:"

#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "자의 최대치"

#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "자의 현재 위치"

#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4827
msgid "Max:"
msgstr "최대:"

#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "자의 최대치"

#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "가로자"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "수치를 보이기:"

#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "스케일수치를 보일까요?"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "소수의 자리수:"

#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "소수점아래 몇자리까지 표할까요?"

#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "수치의 위치:"

#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "수치를 표시하는 위치 "

#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "폴리시:"

#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "스케일의 업데이트 폴리시"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
#, fuzzy
msgid "Inverted:"
msgstr "변환"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
#, fuzzy
msgid "If the range values are inverted"
msgstr "수치가 리스트중에 있지 않으면 안되는가요?"

#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "가로 스케일"

#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "스크롤바의 업데이트 폴리시"

#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "가로 스크롤바"

#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "가로 분리기"

#: ../glade/gbwidgets/gbiconview.c:106
#, fuzzy, c-format
msgid "Icon %i"
msgstr "아이콘 목록"

#: ../glade/gbwidgets/gbiconview.c:128
#, fuzzy
msgid "The selection mode of the icon view"
msgstr "열 트리의 선택모드"

#: ../glade/gbwidgets/gbiconview.c:130 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "방향:"

#: ../glade/gbwidgets/gbiconview.c:131
#, fuzzy
msgid "The orientation of the icons"
msgstr "프로그레스바의 뻗는 방향"

#: ../glade/gbwidgets/gbiconview.c:287
#, fuzzy
msgid "Icon View"
msgstr "크기를 보이기:"

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:299
#, fuzzy
msgid "Named Icon:"
msgstr "아이콘:"

#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:300
#, fuzzy
msgid "The named icon to use"
msgstr "스톡 GnomeItem을 사용합니다."

#: ../glade/gbwidgets/gbimage.c:112
#, fuzzy
msgid "Icon Size:"
msgstr "크기를 보이기:"

#: ../glade/gbwidgets/gbimage.c:113
#, fuzzy
msgid "The stock icon size"
msgstr "스톡 GnomeItem을 사용합니다."

#: ../glade/gbwidgets/gbimage.c:115
#, fuzzy
msgid "Pixel Size:"
msgstr "페이지 크기:"

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""

#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "가로방향의 조정"

#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "새로방향의 조정"

#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "그림"

#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "부당한 스톡 메뉴아이템 "

#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "픽스맵나름의 메뉴아이템"

#: ../glade/gbwidgets/gbinputdialog.c:256
msgid "Input Dialog"
msgstr "입력 다이어로그"

#: ../glade/gbwidgets/gblabel.c:169
#, fuzzy
msgid "Use Underline:"
msgstr "밑줄"

#: ../glade/gbwidgets/gblabel.c:170
msgid "If the text includes an underlined access key"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:171
#, fuzzy
msgid "Use Markup:"
msgstr "Heap의 사용:"

#: ../glade/gbwidgets/gblabel.c:172
msgid "If the text includes pango markup"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:173
msgid "Justify:"
msgstr "배치:"

#: ../glade/gbwidgets/gblabel.c:174
msgid "The justification of the lines of the label"
msgstr "라벨의 선에 나란히"

#: ../glade/gbwidgets/gblabel.c:176
msgid "Wrap Text:"
msgstr "텍스트 행바꾸기:"

#: ../glade/gbwidgets/gblabel.c:177
msgid "If the text is wrapped to fit within the width of the label"
msgstr "라벨의 너비에 맞게끔 텍스트를 행바꾸기를 할까요?"

#: ../glade/gbwidgets/gblabel.c:178
#, fuzzy
msgid "Selectable:"
msgstr "선택됐음"

#: ../glade/gbwidgets/gblabel.c:179
#, fuzzy
msgid "If the label text can be selected with the mouse"
msgstr "아이콘 텍스트를 편집할 수 있는 지"

#: ../glade/gbwidgets/gblabel.c:181
msgid "The horizontal alignment of the entire label"
msgstr "입력라벨의 가로방향의 조정"

#: ../glade/gbwidgets/gblabel.c:184
msgid "The vertical alignment of the entire label"
msgstr "입력라벨의 세로방향의 조정"

#: ../glade/gbwidgets/gblabel.c:190
msgid "Focus Target:"
msgstr "초점대상:"

#: ../glade/gbwidgets/gblabel.c:191
#, fuzzy
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr "밑선이 끄인 가속기 키가 눌리웠을 적에 키보드 초점을 설정하는 위젯"

#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:197 ../glade/gbwidgets/gbprogressbar.c:146
#, fuzzy
msgid "Ellipsize:"
msgstr "독점:"

#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:201
#, fuzzy
msgid "The width of the label in characters"
msgstr "배열구역의 너비"

#: ../glade/gbwidgets/gblabel.c:203
#, fuzzy
msgid "Single Line Mode:"
msgstr "선택모드:"

#: ../glade/gbwidgets/gblabel.c:204
msgid "If the label is only given enough height for a single line"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:205
msgid "Angle:"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:206
#, fuzzy
msgid "The angle of the label text"
msgstr "텍스트의 최대 길이"

#: ../glade/gbwidgets/gblabel.c:332 ../glade/gbwidgets/gblabel.c:347
#: ../glade/gbwidgets/gblabel.c:614
msgid "Auto"
msgstr "자동"

#: ../glade/gbwidgets/gblabel.c:870 ../glade/glade_menu_editor.c:410
msgid "Label"
msgstr "라벨"

#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "구역 너비:"

#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "배열구역의 너비"

#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "구역 높이:"

#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "배열구역의 높이"

#: ../glade/gbwidgets/gblayout.c:222
#, fuzzy
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "테이블내의 위젯의 웃머리"

#: ../glade/gbwidgets/gblayout.c:225
#, fuzzy
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "테이블내의 위젯의 웃머리"

#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "배열"

#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "리스트의 선택모드"

#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "리스트"

#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "리스트 항목"

#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "튀여나오기 메뉴"

#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:190
#, fuzzy
msgid "_File"
msgstr "파일"

#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:198 ../glade/glade_project_window.c:691
#, fuzzy
msgid "_Edit"
msgstr "편집"

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:204 ../glade/glade_project_window.c:720
#, fuzzy
msgid "_View"
msgstr "보기"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:206 ../glade/glade_project_window.c:833
#, fuzzy
msgid "_Help"
msgstr "도움말"

#: ../glade/gbwidgets/gbmenubar.c:207
#, fuzzy
msgid "_About"
msgstr "프로그램 정보"

#: ../glade/gbwidgets/gbmenubar.c:268 ../glade/gbwidgets/gbmenubar.c:346
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "메뉴 편집..."

#: ../glade/gbwidgets/gbmenubar.c:442
msgid "Menu Bar"
msgstr "메뉴바"

#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "메뉴 아이템"

#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
#, fuzzy
msgid "Show Horizontal:"
msgstr "가로의 불허:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
#, fuzzy
msgid "If the item is visible when the toolbar is horizontal"
msgstr "도크아이템에서 가로방향으로 되는것을 허가하지 않습니다."

#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
#, fuzzy
msgid "Show Vertical:"
msgstr "수치를 보이기:"

#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
#, fuzzy
msgid "If the item is visible when the toolbar is vertical"
msgstr "도크아이템에서 세로방향으로 되는것을 허가하지 않습니다."

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:255
#, fuzzy
msgid "Toolbar Button with Menu"
msgstr "토글 버튼"

#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "새 노트북"

#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:124
msgid "Number of pages:"
msgstr "페이지번호:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "탭 표시:"

#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "노트북의 탭을 보이게 할까요?"

#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "테두리 보이기:"

#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "탭이 표시되어 있지 않을 때 노트북의 테두리가 보이게 할까요?"

#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "탭 위치:"

#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "노트북 탭의 위치"

#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "스크롤가능:"

#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "노트북 탭이 스크롤 가능할까요?"

#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "가로 탭 테두리:"

#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "노트북 탭의 가로방향의 테두리의 크기"

#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "탭 세로 테두리:"

#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "노트북 탭의 새로방향의 테두리의 크기"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "튀여나오기 보이기:"

#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "튀여나오기메뉴가 쓸수 있을가?"

#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "페이지수:"

#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "노트북의 페이지수"

#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "전 페이지"

#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "다음 페이지"

#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "페이지 삭제"

#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "다음페이지에 이동"

#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "전페이지에 이동"

#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "뒤에 페이지를 삽입 "

#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "앞에 페이지를 삽입"

#: ../glade/gbwidgets/gbnotebook.c:670
#, fuzzy
msgid "The page's position in the list of pages"
msgstr "자의 현재 위치"

#: ../glade/gbwidgets/gbnotebook.c:673
#, fuzzy
msgid "Set True to let the tab expand"
msgstr "위젯이 자동적으로 확장합니다."

#: ../glade/gbwidgets/gbnotebook.c:675
#, fuzzy
msgid "Set True to let the tab fill its allocated area"
msgstr "위젯이 분할구역을 채우게 하기 "

#: ../glade/gbwidgets/gbnotebook.c:677
#, fuzzy
msgid "Set True to pack the tab at the start of the notebook"
msgstr "위젯은 박스의 첫머리로부터 싸게 된다"

#: ../glade/gbwidgets/gbnotebook.c:678
#, fuzzy
msgid "Menu Label:"
msgstr "라벨:"

#: ../glade/gbwidgets/gbnotebook.c:679
#, fuzzy
msgid "The text to display in the popup menu"
msgstr "버튼에 표시되는 텍스트"

#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "노트북"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr ""

#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "메뉴 선택"

#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "색깔:"

#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "미리보기가 칼라인가 그레이스케일인가"

#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "미리보기가 분할구역을 만끽하게 확장하는가요?"

#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "미리보기"

#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "프로그레스바의 뻗는 방향"

#: ../glade/gbwidgets/gbprogressbar.c:137
#, fuzzy
msgid "Fraction:"
msgstr "방향:"

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr ""

#: ../glade/gbwidgets/gbprogressbar.c:140
#, fuzzy
msgid "Pulse Step:"
msgstr "Heap의 사용:"

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""

#: ../glade/gbwidgets/gbprogressbar.c:144
#, fuzzy
msgid "The text to display over the progress bar"
msgstr "프로그레스바의 스타일"

#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "텍스트 보이기:"

#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "프로그레스바의 안에 텍스트를 표기할까요?"

#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "동작 모드:"

#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "프로그레스바의 동작을 자동차의 앞면과 같이 할까요?"

#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "텍스트의 가로방향의 조정"

#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "텍스트의 세로방향의 조정"

#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "프로그레스바"

#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "라디오 버튼이 최초로부터 온이 였었는가요?"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1038
msgid "Group:"
msgstr "그룹:"

#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr ""
"라디오버튼의 그룹(디폴트에서는 전부 라디오버튼은 같은 부모를 갖고 있다)"

#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
#, fuzzy
msgid "New Group"
msgstr "새그룹:"

#: ../glade/gbwidgets/gbradiobutton.c:463
msgid "Radio Button"
msgstr "라디오 버튼"

#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "라디오메뉴아이템이 최초로부터 온이 였었는가요?"

#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr ""
"라디오메뉴아이템의 그룹(디폴트에서는 전부 라디오 메뉴 아이템은 같은 부모를 갖"
"고 있다.)"

#: ../glade/gbwidgets/gbradiomenuitem.c:386
msgid "Radio Menu Item"
msgstr "라디오 메뉴 아이템"

#: ../glade/gbwidgets/gbradiotoolbutton.c:142
#, fuzzy
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr ""
"라디오버튼의 그룹(디폴트에서는 전부 라디오버튼은 같은 부모를 갖고 있다)"

#: ../glade/gbwidgets/gbradiotoolbutton.c:528
#, fuzzy
msgid "Toolbar Radio Button"
msgstr "라디오 버튼"

#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "가로 폴리시:"

#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "가로 스크롤바가 표시될 적에"

#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "세로 폴리시:"

#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "세로 스크롤바가 보이자 할 때"

#: ../glade/gbwidgets/gbscrolledwindow.c:137
#, fuzzy
msgid "Window Pos:"
msgstr "윈도우"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
#, fuzzy
msgid "Where the child window is located with respect to the scrollbars"
msgstr "형제들에 대한 자식위젯의 위치"

#: ../glade/gbwidgets/gbscrolledwindow.c:140
#, fuzzy
msgid "Shadow Type:"
msgstr "그림자:"

#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "세로스크롤바의 업데이트폴리시"

#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "스크롤된 윈도우"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:204
#, fuzzy
msgid "Toolbar Separator Item"
msgstr "가로 분리기"

#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "증가비율:"

#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr "스핀버튼의 증가비율,페이지증가와 함께 쓰입니다."

#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "표기되는 수자의 소수점아래의 자리수"

#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "수치:"

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr ""

#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "업데이트 폴리시:"

#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "value_changed시그널가 발행될 때"

#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "스냅:"

#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "스냅증가량의 배수로 수치가 스냅될까요?"

#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "전환:"

#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "양단에서 수치를 돌렸는가요?"

#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "스핀 버튼"

#: ../glade/gbwidgets/gbstatusbar.c:64
#, fuzzy
msgid "Resize Grip:"
msgstr "사이즈변경:"

#: ../glade/gbwidgets/gbstatusbar.c:64
#, fuzzy
msgid "If the status bar has a resize grip to resize the window"
msgstr "애플리케이션 바가 프로그레스 지시부를 갖고 있는가요?"

#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "상태바"

#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "새 테이블"

#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "행수:"

#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "행:"

#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "테이블의 행수"

#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "열:"

#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "테이블의 열수"

#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "자식들을 전부 똑같은 크기로 할까요?"

#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "행 간격:"

#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "각 행 사이의 간격"

#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "열 간격:"

#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "각 열 사이의 간격"

#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "Cell가로위치:"

#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "테이블내에서의 위젯의 왼변의 장소"

#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "Cell 세로위치:"

#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "테이블내의 위젯의 웃머리"

#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "점유하는 열:"

#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "테이블에서 위젯이 몇열을 점하는가요?"

#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "점유하는 행:"

#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "테이블에서 위젯이 몇행을 점하는가요?"

#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "가로 패딩:"

#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "세로 패딩:"

#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "X 방향의 확장:"

#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "위젯을 가로방향으로 확장되게 합니다."

#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Y 방향의 확장:"

#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "위젯을 세로방향으로 확장되게 합니다."

#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "X 방향의 축소:"

#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "위젯을 가로방향으로 축소되게 합니다."

#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Y 방향의 축소:"

#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "위젯을 세로방향으로 축소되게 합니다."

#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "X 방향의 채움:"

#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr "위젯은 분할구역을 가로방향으로 채우도록 합니다."

#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Y 방향의 채움:"

#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "위젯은 분할구역을 세로방향으로 채우도록 합니다."

#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "행을 앞에 삽입"

#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "행을 뒤에 삽입"

#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "열을 앞에 삽입"

#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "열을 뒤에 삽입"

#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "행을 삭제"

#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "열을 삭제"

#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "테이블"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "중간에 나란히"

#: ../glade/gbwidgets/gbtextview.c:52
#, fuzzy
msgid "Fill"
msgstr "구역을 채우기:"

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:542 ../glade/glade_menu_editor.c:829
#: ../glade/glade_menu_editor.c:1344 ../glade/glade_menu_editor.c:2251
#: ../glade/property.c:2431
msgid "None"
msgstr "아니오"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:117
#, fuzzy
msgid "Cursor Visible:"
msgstr "보이는 것:"

#: ../glade/gbwidgets/gbtextview.c:118
#, fuzzy
msgid "If the cursor is visible"
msgstr "위젯이 최초로부터 보이는가요?"

#: ../glade/gbwidgets/gbtextview.c:119
#, fuzzy
msgid "Overwrite:"
msgstr "변환"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:122
#, fuzzy
msgid "If tab characters can be entered"
msgstr "텍스트를 편집할 수 있을까요?"

#: ../glade/gbwidgets/gbtextview.c:126
#, fuzzy
msgid "Justification:"
msgstr "배치:"

#: ../glade/gbwidgets/gbtextview.c:127
#, fuzzy
msgid "The justification of the text"
msgstr "라벨의 선에 나란히"

#: ../glade/gbwidgets/gbtextview.c:129
#, fuzzy
msgid "Wrapping:"
msgstr "전환:"

#: ../glade/gbwidgets/gbtextview.c:130
#, fuzzy
msgid "The wrapping of the text"
msgstr "텍스트의 최대 길이"

#: ../glade/gbwidgets/gbtextview.c:133
#, fuzzy
msgid "Space Above:"
msgstr "공간크기:"

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:136
#, fuzzy
msgid "Space Below:"
msgstr "공간크기:"

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:139
#, fuzzy
msgid "Space Inside:"
msgstr "공간크기:"

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:149
#, fuzzy
msgid "Indent:"
msgstr "찾아보기"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:463
#, fuzzy
msgid "Text View"
msgstr "텍스트 보기:"

#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "토글버튼이 최초로부터 온이 였는가요?"

#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "토글 버튼"

#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
#, fuzzy
msgid "Toolbar Toggle Button"
msgstr "토글 버튼"

#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "새 툴바"

#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "아이템수:"

#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "툴바의 아이템수"

#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "툴바의 방향"

#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "스타일:"

#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "툴바 스타일"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "툴팁:"

#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "툴팁을 쓸수 있을가요?"

#: ../glade/gbwidgets/gbtoolbar.c:277
#, fuzzy
msgid "Show Arrow:"
msgstr "테두리 보이기:"

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""

#: ../glade/gbwidgets/gbtoolbar.c:427
#, fuzzy
msgid "If the item should be the same size as other homogeneous items"
msgstr "자식들의 크기를 똑같게 할까요?"

#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "아이템을 앞에 삽입"

#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "아이템을 뒤에 삽입"

#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "툴바"

#: ../glade/gbwidgets/gbtoolbutton.c:586
#, fuzzy
msgid "Toolbar Button"
msgstr "토글 버튼"

#: ../glade/gbwidgets/gbtoolitem.c:201
#, fuzzy
msgid "Toolbar Item"
msgstr "툴바"

#: ../glade/gbwidgets/gbtreeview.c:71
#, fuzzy
msgid "Column 1"
msgstr "열:"

#: ../glade/gbwidgets/gbtreeview.c:79
#, fuzzy
msgid "Column 2"
msgstr "열:"

#: ../glade/gbwidgets/gbtreeview.c:87
#, fuzzy
msgid "Column 3"
msgstr "열:"

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:114
#, fuzzy
msgid "Headers Visible:"
msgstr "헤더 파일:"

#: ../glade/gbwidgets/gbtreeview.c:115
#, fuzzy
msgid "If the column header buttons are shown"
msgstr "열의 제목을 보일까요?"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:118
#, fuzzy
msgid "Reorderable:"
msgstr "역재생:"

#: ../glade/gbwidgets/gbtreeview.c:119
#, fuzzy
msgid "If the view is reorderable"
msgstr "미리보기가 칼라인가 그레이스케일인가"

#: ../glade/gbwidgets/gbtreeview.c:120
#, fuzzy
msgid "Enable Search:"
msgstr "찾기"

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:123
#, fuzzy
msgid "Fixed Height Mode:"
msgstr "스케일할 높이"

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:125
#, fuzzy
msgid "Hover Selection:"
msgstr "색깔 선택"

#: ../glade/gbwidgets/gbtreeview.c:126
#, fuzzy
msgid "Whether the selection should follow the pointer"
msgstr "트리의 선택모드"

#: ../glade/gbwidgets/gbtreeview.c:127
#, fuzzy
msgid "Hover Expand:"
msgstr "X 방향의 확장:"

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr ""

#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "새로운 세로 박스"

#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "세로박스"

#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "새로운 세로 버튼 박스 "

#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "세로 버튼 박스"

#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "보기창의 그림자의 유형"

#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "보기창"

#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "세로 페인"

#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "세로 자"

#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "세로 스케일"

#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "세로 스크롤바"

#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "세로 분리기"

#: ../glade/gbwidgets/gbwindow.c:242
msgid "The title of the window"
msgstr "윈도우의 제목"

#: ../glade/gbwidgets/gbwindow.c:245
msgid "The type of the window"
msgstr "윈도우의 유형"

#: ../glade/gbwidgets/gbwindow.c:249
#, fuzzy
msgid "Type Hint:"
msgstr "유형:"

#: ../glade/gbwidgets/gbwindow.c:250
msgid "Tells the window manager how to treat the window"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:255
msgid "The initial position of the window"
msgstr "윈도우의 초기위치"

#: ../glade/gbwidgets/gbwindow.c:259 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "모달:"

#: ../glade/gbwidgets/gbwindow.c:259
msgid "If the window is modal"
msgstr "윈도우가 모달인지요?"

#: ../glade/gbwidgets/gbwindow.c:264
msgid "Default Width:"
msgstr "표준너비:"

#: ../glade/gbwidgets/gbwindow.c:265
msgid "The default width of the window"
msgstr "디폴트에서의 윈도우너비"

#: ../glade/gbwidgets/gbwindow.c:269
msgid "Default Height:"
msgstr "표준높이:"

#: ../glade/gbwidgets/gbwindow.c:270
msgid "The default height of the window"
msgstr "윈도우의 높이 기본값"

#: ../glade/gbwidgets/gbwindow.c:276
#, fuzzy
msgid "Resizable:"
msgstr "사이즈변경:"

#: ../glade/gbwidgets/gbwindow.c:277
#, fuzzy
msgid "If the window can be resized"
msgstr "윈도우를 확대할 수 있는가요?"

#: ../glade/gbwidgets/gbwindow.c:284
msgid "If the window can be shrunk"
msgstr "윈도우를 축소할 수 있을까요?"

#: ../glade/gbwidgets/gbwindow.c:285
msgid "Grow:"
msgstr "확대:"

#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be enlarged"
msgstr "윈도우를 확대할 수 있는가요?"

#: ../glade/gbwidgets/gbwindow.c:291
msgid "Auto-Destroy:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:292
#, fuzzy
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "다이어로그를 닫을적에 파괴될 대신에 감추기를 쓰겠나요?"

#: ../glade/gbwidgets/gbwindow.c:296
#, fuzzy
msgid "The icon for this window"
msgstr "윈도우의 제목"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "Role:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:303
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:306
#, fuzzy
msgid "Decorated:"
msgstr "protected"

#: ../glade/gbwidgets/gbwindow.c:307
#, fuzzy
msgid "If the window should be decorated by the window manager"
msgstr "종횡비가 자식에 의해 결정되는가요?"

#: ../glade/gbwidgets/gbwindow.c:310
msgid "Skip Taskbar:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:311
#, fuzzy
msgid "If the window should not appear in the task bar"
msgstr "윈도우가 상태바를 갖고 있는가요?"

#: ../glade/gbwidgets/gbwindow.c:314
msgid "Skip Pager:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:315
#, fuzzy
msgid "If the window should not appear in the pager"
msgstr "프로그레스바의 안에 텍스트를 표기할까요?"

#: ../glade/gbwidgets/gbwindow.c:318
#, fuzzy
msgid "Gravity:"
msgstr "그리드 스타일:"

#: ../glade/gbwidgets/gbwindow.c:319
msgid "The reference point to use when the window coordinates are set"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "Focus On Map:"
msgstr "초점대상:"

#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "If the window should receive the input focus when it is mapped"
msgstr "종횡비가 자식에 의해 결정되는가요?"

#: ../glade/gbwidgets/gbwindow.c:1198
msgid "Window"
msgstr "윈도우"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:74
msgid "Error"
msgstr "에러"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "시스템 에러"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "파일 열기 에러"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "파일 읽기 에러"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "파일 쓰기 에러"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "부당한 디렉토리"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "부당한 값"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "부당한 XML 존재"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "스타트 택 없음"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "End택을 기대함"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "캐럭터 데이타 없읍"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "종류 id가 없음"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "알려지지않은 클래스 "

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "부당한 부분"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "파일이 도중에 종료됩니다."

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "알 수 없는 에러 번호"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr ""

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr ""

#: ../glade/glade_atk.c:122
#, fuzzy
msgid "Label For"
msgstr "라벨"

#: ../glade/glade_atk.c:123
#, fuzzy
msgid "Labelled By"
msgstr "라벨"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr ""

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr ""

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr ""

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr ""

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr ""

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr ""

#: ../glade/glade_atk.c:130
#, fuzzy
msgid "Embedded By"
msgstr "라벨"

#: ../glade/glade_atk.c:131
#, fuzzy
msgid "Popup For"
msgstr "튀여나오기 메뉴"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr ""

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr ""

#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "위젯"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:772
#: ../glade/property.c:776
msgid "Name:"
msgstr "명칭:"

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr ""

#: ../glade/glade_atk.c:640
#, fuzzy
msgid "Description:"
msgstr "설명"

#: ../glade/glade_atk.c:641
#, fuzzy
msgid "The description of the widget to pass to assistive technologies"
msgstr "형제들에 대한 자식위젯의 위치"

#: ../glade/glade_atk.c:643
#, fuzzy
msgid "Table Caption:"
msgstr "일반 옵션:"

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr ""

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr ""

#: ../glade/glade_atk.c:761
#, fuzzy
msgid "Click"
msgstr "시계"

#: ../glade/glade_atk.c:762
#, fuzzy
msgid "Press"
msgstr "프로그레쓰:"

#: ../glade/glade_atk.c:763
#, fuzzy
msgid "Release"
msgstr "역재생:"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr ""

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "클립보드"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "붙일 위젯을 선택할 필요가 있습니다."

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "윈도우 또는 다이어로그에는 붙일수 없습니다."

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"선택한 위젯을 붙일수 없습니다.\n"
"부모위젯에 의해 자동적으로 생성되기 때문에."

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr "오직 메뉴항목만이 메뉴혹은 메뉴바에 붙일수 있습니다."

#: ../glade/glade_clipboard.c:427
#, fuzzy
msgid "Only buttons can be pasted into a dialog action area."
msgstr "오직 버튼만이 GnomeDialog action area에 붙일수 있습니다."

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "오직 GnomeDockItem 위젯만 GnomeDock에 붙여 넣을 수 있습니다."

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "오직 GnomeDockItem 위젯만이 GnomeDockItem에 붙여 넣을 수 있습니다."

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "미안합니다 - GnomeDockItem에서 붙여 넣기는 아직 구현되지 않았습니다."

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "GnomeDockItem 위젯은 다만 GnomeDock에 붙여 넣을 수 있습니다."

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:632
msgid "_New"
msgstr "새로 만들기(_N)"

#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "새 파일 생성"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
#, fuzzy
msgid "_Gnome"
msgstr "그놈:"

#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
#, fuzzy
msgid "Dep_recated"
msgstr "protected"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
#, fuzzy
msgid "GTK+ _Basic"
msgstr "GTK+ 기초"

#: ../glade/glade_gtk12lib.c:247
#, fuzzy
msgid "GTK+ _Additional"
msgstr "GTK+ 추가"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "가속기 키 선택"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "키"

#: ../glade/glade_menu_editor.c:394
msgid "Menu Editor"
msgstr "메뉴 편집기"

#: ../glade/glade_menu_editor.c:411
msgid "Type"
msgstr "유형"

#: ../glade/glade_menu_editor.c:412
msgid "Accelerator"
msgstr "가속기"

#: ../glade/glade_menu_editor.c:413
msgid "Name"
msgstr "이름"

#: ../glade/glade_menu_editor.c:414 ../glade/property.c:1498
msgid "Handler"
msgstr "핸들"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:102
msgid "Active"
msgstr "활성화"

#: ../glade/glade_menu_editor.c:416
msgid "Group"
msgstr "그룹"

#: ../glade/glade_menu_editor.c:417
msgid "Icon"
msgstr "아이콘"

#: ../glade/glade_menu_editor.c:458
msgid "Move the item and its children up one place in the list"
msgstr "아이템을 옮기고 그 자식들도 리스트 위로 올립니다."

#: ../glade/glade_menu_editor.c:470
msgid "Move the item and its children down one place in the list"
msgstr "아이템을 옮기고 그 자식들을 리스트 아래로 내립니다."

#: ../glade/glade_menu_editor.c:482
msgid "Move the item and its children up one level"
msgstr "아이템을 옮기고 그 자식들도 한충 올립니다."

#: ../glade/glade_menu_editor.c:494
msgid "Move the item and its children down one level"
msgstr "아이템을 옮기고 그 자식들을 한충 내립니다."

#: ../glade/glade_menu_editor.c:524
#, fuzzy
msgid "The stock item to use."
msgstr "스톡 GnomeItem을 사용합니다."

#: ../glade/glade_menu_editor.c:527 ../glade/glade_menu_editor.c:642
msgid "Stock Item:"
msgstr "스톡 아이템:"

#: ../glade/glade_menu_editor.c:640
msgid "The stock Gnome item to use."
msgstr "스톡 GnomeItem을 사용합니다."

#: ../glade/glade_menu_editor.c:745
msgid "The text of the menu item, or empty for separators."
msgstr ""

#: ../glade/glade_menu_editor.c:769 ../glade/property.c:777
msgid "The name of the widget"
msgstr "위젯의 명칭"

#: ../glade/glade_menu_editor.c:790
msgid "The function to be called when the item is selected"
msgstr "아이템을 선택하기만 하면 함수를 불러낼수 있습니다."

#: ../glade/glade_menu_editor.c:792 ../glade/property.c:1546
msgid "Handler:"
msgstr "핸들:"

#: ../glade/glade_menu_editor.c:811
msgid "An optional icon to show on the left of the menu item."
msgstr "메뉴아이템의 왼쪽에 있는 선택할 수 있는 아이콘."

#: ../glade/glade_menu_editor.c:934
msgid "The tip to show when the mouse is over the item"
msgstr "마우스커서가 아이템위에 지나기만 하면 나타나는 팁."

#: ../glade/glade_menu_editor.c:936 ../glade/property.c:824
msgid "Tooltip:"
msgstr "툴팁:"

#: ../glade/glade_menu_editor.c:957
#, fuzzy
msgid "_Add"
msgstr "추가"

#: ../glade/glade_menu_editor.c:962
msgid "Add a new item below the selected item."
msgstr "선택한 아이템의 아래에 새로운 아이템을 추가합니다."

#: ../glade/glade_menu_editor.c:967
msgid "Add _Child"
msgstr ""

#: ../glade/glade_menu_editor.c:972
#, fuzzy
msgid "Add a new child item below the selected item."
msgstr "선택한 아이템의 아래에 새로운 아이템을 추가합니다."

#: ../glade/glade_menu_editor.c:978
#, fuzzy
msgid "Add _Separator"
msgstr "분리기 추가"

#: ../glade/glade_menu_editor.c:983
msgid "Add a separator below the selected item."
msgstr "선택한 아이템의 아래에 분리기를 추가합니다."

#: ../glade/glade_menu_editor.c:988 ../glade/glade_project_window.c:239
#, fuzzy
msgid "_Delete"
msgstr "지우기"

#: ../glade/glade_menu_editor.c:993
msgid "Delete the current item"
msgstr "현재의 아이템을 삭제합니다."

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:999
msgid "Item Type:"
msgstr "아이템 유형:"

#: ../glade/glade_menu_editor.c:1015
msgid "If the item is initially on."
msgstr "아이템이 초기로부터 on되어 있는 지"

#: ../glade/glade_menu_editor.c:1017
msgid "Active:"
msgstr "활성화:"

#: ../glade/glade_menu_editor.c:1022 ../glade/glade_menu_editor.c:1632
#: ../glade/property.c:2215 ../glade/property.c:2225
msgid "No"
msgstr "아니오"

#: ../glade/glade_menu_editor.c:1036
#, fuzzy
msgid "The radio menu item's group"
msgstr "라디오메뉴아이템이 최초로부터 온이 였었는가요?"

#: ../glade/glade_menu_editor.c:1053 ../glade/glade_menu_editor.c:2406
#: ../glade/glade_menu_editor.c:2546
msgid "Radio"
msgstr "라디오"

#: ../glade/glade_menu_editor.c:1060 ../glade/glade_menu_editor.c:2404
#: ../glade/glade_menu_editor.c:2544
msgid "Check"
msgstr "체크"

#: ../glade/glade_menu_editor.c:1067 ../glade/property.c:102
msgid "Normal"
msgstr "표준"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1076
msgid "Accelerator:"
msgstr "가속기:"

#: ../glade/glade_menu_editor.c:1113 ../glade/property.c:1681
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1118 ../glade/property.c:1684
msgid "Shift"
msgstr "Shift"

#: ../glade/glade_menu_editor.c:1123 ../glade/property.c:1687
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1128 ../glade/property.c:1694
msgid "Key:"
msgstr "키:"

#: ../glade/glade_menu_editor.c:1134 ../glade/property.c:1673
msgid "Modifiers:"
msgstr "수정:"

#: ../glade/glade_menu_editor.c:1632 ../glade/glade_menu_editor.c:2411
#: ../glade/glade_menu_editor.c:2554 ../glade/property.c:2215
msgid "Yes"
msgstr "예"

#: ../glade/glade_menu_editor.c:2002
msgid "Select icon"
msgstr "아이콘 선택"

#: ../glade/glade_menu_editor.c:2345 ../glade/glade_menu_editor.c:2706
msgid "separator"
msgstr "분리기"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3624 ../glade/glade_project_window.c:366
#: ../glade/property.c:5109
msgid "New"
msgstr "새..."

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "선택자"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"프로젝트 디렉토리는 설정되여 있지 않습니다.\n"
"프로젝트 옵션 다이어로그로 설정하여 주십시오.\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"소스디렉토리는 설정되여 있지 않습니다 .\n"
"프로젝트 옵션 다이어로그로 설정하여 주십시오.\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"부정확한 소스 디렉토리:\n"
"\n"
"소스디렉토리는 반드시 프로젝트디렉토리 \n"
"혹은 그 아래디렉토리여야 합니다.\n"

#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"픽스맵디렉토리는 설정되여 있지 않습니다.\n"
"프로젝트 옵션 다이어로그로 설정하여 주십시오.\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "미안합니다 - %s의 소스의 출력은 아직 구현되지 않았습니다."

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""

#: ../glade/glade_project.c:521
#, fuzzy
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"C++ 소스코드 생성을 위한 glade--실행시 에러가 발생.\n"
"glade--가 설치되여 있는지 PATH가 통하여 있는지 체크하십시오."

#: ../glade/glade_project.c:548
#, fuzzy
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"C++ 소스코드 생성을 위한 glade--실행시 에러가 발생.\n"
"glade--가 설치되여 있는지 PATH가 통하여 있는지 체크하십시오."

#: ../glade/glade_project.c:571
#, fuzzy
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"C++ 소스코드 생성을 위한 glade--실행시 에러가 발생.\n"
"glade--가 설치되여 있는지 PATH가 통하여 있는지 체크하십시오."

#: ../glade/glade_project.c:594
#, fuzzy
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"C++ 소스코드 생성을 위한 glade--실행시 에러가 발생.\n"
"glade--가 설치되여 있는지 PATH가 통하여 있는지 체크하십시오."

#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"픽스맵 디렉토리가 설정되여 있지 않습니다.\n"
"프로젝트 옵션 다이어로그로 설정하여 놓으십시오.\n"

#: ../glade/glade_project.c:1772
#, fuzzy
msgid "Error writing project XML file\n"
msgstr "XML파일을 쓰는 과정 에러가 발생\n"

#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:382
#: ../glade/glade_project_window.c:889
msgid "Project Options"
msgstr "프로젝트 옵션"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "일반"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "기본 설정:"

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "프로젝트 디렉토리"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "프로젝트 디렉토리:"

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "브라우즈..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "현재 프로젝트 명칭"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "프로젝트명:"

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "프로그램 명칭"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "프로젝트파일"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "프로젝트파일:"

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "서브디렉토리:"

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "소스코드를 저장할 디렉토리"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "소스 디렉토리:"

#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "픽스맵을 저장하는 디렉토리"

#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "픽스맵의 디렉토리:"

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "소스를 만들 때 쓰이는 라이센스"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "언어:"

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "그놈:"

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "그놈 지원함"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "그놈 애플리케이션을 만들 지"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "그놈 DB 지원함"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "Gnome DB 애플리케이션을 건립할까요?"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "C 옵션"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "일반 옵션:"

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Gettext 지원"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "gettext에 의한 번역을 할 수 있게끔 스트링을 마크할까요?"

#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "위젯 명칭 설정"

#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "소스에 위젯 이름을 설정할까요?"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "소스파일백업"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "과거 소스의 복사본을 생성할까요?"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "그놈 도움말 지원"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "그놈 도움말 시스템에 대한 지원이 포함될 지"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "파일출력 옵션:"

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "main.c 파일 출력"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr "아직 존재하지 않는다면 main()함수를 포함한 main.c파일을 출력합니다."

# (FIXME)
#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "서포트 함수 출력"

# (FIXME)
#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "서포트 함수를 출력할까요?"

#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "Build 파일 출력"

#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"Makefile.am 과 configure.in을 포함한 소스코드를 컴파일을 하기 위한 파일을 출"
"력합니다."

#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "인터페이스 생성 함수:"

#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "인터페이스를 만드는 함수를 출력할 수 있는 파일"

#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "소스 파일이름:"

#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr "인터페이스를 생성하는 함수의 선언이 씌여져 있는 파일"

#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "헤더 파일:"

#: ../glade/glade_project_options.c:594
#, fuzzy
msgid "Source file for interface creation functions"
msgstr "인터페이스 생성 함수:"

#: ../glade/glade_project_options.c:595
#, fuzzy
msgid "Header file for interface creation functions"
msgstr "인터페이스 생성 함수:"

#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "시그널 핸들러와 콜백함수:"

#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr "빈 시그널핸들러와 콜백함수가 씌여있는 파일"

#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr "시그널핸들러와 콜백함수의 선언이 씌여있는 파일"

#: ../glade/glade_project_options.c:640
#, fuzzy
msgid "Source file for signal handler and callback functions"
msgstr "시그널 핸들러와 콜백함수:"

#: ../glade/glade_project_options.c:641
#, fuzzy
msgid "Header file for signal handler and callback functions"
msgstr "빈 시그널핸들러와 콜백함수가 씌여있는 파일"

# (FIXME)
#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "서포트 함수:"

# (FIXME)
#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "서포트 함수가 씌여있는 파일"

# (FIXME)
#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "서포트 함수의 선언이 씌여있는 파일"

# (FIXME)
#: ../glade/glade_project_options.c:686
#, fuzzy
msgid "Source file for support functions"
msgstr "서포트 함수:"

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr ""

#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "LibGlade 옵션"

#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "번역할 수 있는 스트링:"

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""

#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "번역할 수 있는 스트링의 저장"

#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"libglade에 의해 적재된 매개면의 번역을 가능케하기 위해 분할된 C의 소스파일에 "
"번역할 수 있는 스트링을 저장합니다."

#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "전부의 번역을 할 수 있는 스트링을 저장하는 C의 소스파일"

#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "파일:"

#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "프로젝트 디렉토리 선택"

#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "번역할 수 있는 스트링의 파일을 설정할 필요가 있습니다."

#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "프로젝트 디렉토리를 설정할 필요가 있습니다."

#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "프로젝트 파일을 설정할 필요가 있습니다."

#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "프로젝트 이름을 설정할 필요가 있습니다."

#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "프로그램 이름을 설정할 필요가 있습니다."

#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "소스디렉토리를 설정할 필요가 있습니다."

#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "픽스맵 디렉토리를 설정할 필요가 있습니다."

#: ../glade/glade_project_window.c:184
#, fuzzy, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"파일의 이름을 바꿀수 없습니다:\n"
"  %s\n"
"변경후:\n"
"  %s\n"

#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:634
msgid "Create a new project"
msgstr "새 프로젝트를 생성"

#: ../glade/glade_project_window.c:216 ../glade/glade_project_window.c:654
#: ../glade/glade_project_window.c:905
#, fuzzy
msgid "_Build"
msgstr "빌드"

#: ../glade/glade_project_window.c:217 ../glade/glade_project_window.c:665
msgid "Output the project source code"
msgstr "프로젝트의 소스코드를 출력함"

#: ../glade/glade_project_window.c:223 ../glade/glade_project_window.c:668
#, fuzzy
msgid "Op_tions..."
msgstr "옵션"

#: ../glade/glade_project_window.c:224 ../glade/glade_project_window.c:677
msgid "Edit the project options"
msgstr "프로젝트 옵션 편집"

#: ../glade/glade_project_window.c:239 ../glade/glade_project_window.c:716
#, fuzzy
msgid "Delete the selected widget"
msgstr "선택한 위젯의 속성을 보이기"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:727
msgid "Show _Palette"
msgstr "팔렛트를 보이기(_P)"

#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:732
msgid "Show the palette of widgets"
msgstr "위젯의 팔렛트 보이기"

#: ../glade/glade_project_window.c:263 ../glade/glade_project_window.c:737
msgid "Show Property _Editor"
msgstr "속성 에디터 보이기(_E)"

#: ../glade/glade_project_window.c:264 ../glade/glade_project_window.c:743
msgid "Show the property editor"
msgstr "속성 에디터를 보이기"

#: ../glade/glade_project_window.c:270 ../glade/glade_project_window.c:747
msgid "Show Widget _Tree"
msgstr "위젯 트리를 보이기(_T)"

#: ../glade/glade_project_window.c:271 ../glade/glade_project_window.c:753
#: ../glade/main.c:82
msgid "Show the widget tree"
msgstr "위젯 트리를 보이기"

#: ../glade/glade_project_window.c:277 ../glade/glade_project_window.c:757
msgid "Show _Clipboard"
msgstr "클립보드 보이기(_C)"

#: ../glade/glade_project_window.c:278 ../glade/glade_project_window.c:763
#: ../glade/main.c:86
msgid "Show the clipboard"
msgstr "클립보드 보이기"

#: ../glade/glade_project_window.c:296
msgid "Show _Grid"
msgstr "그리드 보이기(_G)"

#: ../glade/glade_project_window.c:297 ../glade/glade_project_window.c:799
msgid "Show the grid (in fixed containers only)"
msgstr "그리드 보이기(고정 콘테이너만이)"

#: ../glade/glade_project_window.c:303
msgid "_Snap to Grid"
msgstr "그리드으로 붙잡기(_S)"

#: ../glade/glade_project_window.c:304
msgid "Snap widgets to the grid"
msgstr "위젯을 그리드으로 붙잡기"

#: ../glade/glade_project_window.c:310 ../glade/glade_project_window.c:771
msgid "Show _Widget Tooltips"
msgstr "위젯 툴팁을 보이기(_W)"

#: ../glade/glade_project_window.c:311 ../glade/glade_project_window.c:779
msgid "Show the tooltips of created widgets"
msgstr "생성한 위젯의 툴팁을 보이기"

#: ../glade/glade_project_window.c:320 ../glade/glade_project_window.c:802
msgid "Set Grid _Options..."
msgstr "그리드 옵션 설정..."

#: ../glade/glade_project_window.c:321
msgid "Set the grid style and spacing"
msgstr "그리드의 유형과 간격의 설정"

#: ../glade/glade_project_window.c:327 ../glade/glade_project_window.c:823
msgid "Set Snap O_ptions..."
msgstr "스냅 옵션 설정(_P)..."

#: ../glade/glade_project_window.c:328
msgid "Set options for snapping to the grid"
msgstr "그리드으로의 스냅의 옵션설정"

#: ../glade/glade_project_window.c:340
msgid "_FAQ"
msgstr ""

#: ../glade/glade_project_window.c:341
msgid "View the Glade FAQ"
msgstr ""

#. create File menu
#: ../glade/glade_project_window.c:355 ../glade/glade_project_window.c:625
#, fuzzy
msgid "_Project"
msgstr "Projects"

#: ../glade/glade_project_window.c:366 ../glade/glade_project_window.c:872
#: ../glade/glade_project_window.c:1049
msgid "New Project"
msgstr "새 프로젝트"

#: ../glade/glade_project_window.c:371
msgid "Open"
msgstr "열기"

#: ../glade/glade_project_window.c:371 ../glade/glade_project_window.c:877
#: ../glade/glade_project_window.c:1110
msgid "Open Project"
msgstr "프로젝트를 열기"

#: ../glade/glade_project_window.c:376
msgid "Save"
msgstr "저장"

#: ../glade/glade_project_window.c:376 ../glade/glade_project_window.c:881
#: ../glade/glade_project_window.c:1475
msgid "Save Project"
msgstr "프로젝트저장"

#: ../glade/glade_project_window.c:382
msgid "Options"
msgstr "옵션"

#: ../glade/glade_project_window.c:387
msgid "Build"
msgstr "빌드"

#: ../glade/glade_project_window.c:387
msgid "Build the Source Code"
msgstr "소스코드를 빌드하기"

#: ../glade/glade_project_window.c:638
msgid "Open an existing project"
msgstr "기존 프로젝트 열기"

#: ../glade/glade_project_window.c:642
msgid "Save project"
msgstr "프로젝트 저장"

#: ../glade/glade_project_window.c:687
msgid "Quit Glade"
msgstr "Glade의 종료"

#: ../glade/glade_project_window.c:701
#, fuzzy
msgid "Cut the selected widget to the clipboard"
msgstr "붙일 위젯을 선택할 필요가 있습니다."

#: ../glade/glade_project_window.c:706
#, fuzzy
msgid "Copy the selected widget to the clipboard"
msgstr "붙일 위젯을 선택할 필요가 있습니다."

#: ../glade/glade_project_window.c:711
#, fuzzy
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "선택한 위젯의 속성을 보이기"

#: ../glade/glade_project_window.c:783
#, fuzzy
msgid "_Grid"
msgstr "그리드"

#: ../glade/glade_project_window.c:791
#, fuzzy
msgid "_Show Grid"
msgstr "그리드 보이기"

#: ../glade/glade_project_window.c:808
msgid "Set the spacing between grid lines"
msgstr "그리드의 줄간격을 설정"

#: ../glade/glade_project_window.c:811
#, fuzzy
msgid "S_nap to Grid"
msgstr "그리드 붙잡기"

#: ../glade/glade_project_window.c:819
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "그리드로 위젯을 붙잡기(고정 콘테이너만이)"

#: ../glade/glade_project_window.c:829
msgid "Set which parts of a widget snap to the grid"
msgstr "위젯의 어느부분을 그리드으로 붙잡을것인가를 설정"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:854
#, fuzzy
msgid "_About..."
msgstr "GLADE에 관하여..."

#: ../glade/glade_project_window.c:895
#, fuzzy
msgid "Optio_ns"
msgstr "옵션"

#: ../glade/glade_project_window.c:899
msgid "Write Source Code"
msgstr "소스코드 쓰기"

#: ../glade/glade_project_window.c:986 ../glade/glade_project_window.c:1691
#: ../glade/glade_project_window.c:1980
msgid "Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:993
msgid "Are you sure you want to create a new project?"
msgstr "새로운 프로젝트를 생성해도 되겠습니까?"

#: ../glade/glade_project_window.c:1053
#, fuzzy
msgid "New _GTK+ Project"
msgstr "새 프로젝트"

#: ../glade/glade_project_window.c:1054
#, fuzzy
msgid "New G_NOME Project"
msgstr "새 프로젝트"

#: ../glade/glade_project_window.c:1057
msgid "Which type of project do you want to create?"
msgstr ""

#: ../glade/glade_project_window.c:1091
msgid "New project created."
msgstr "새 프로젝트 생성됨"

#: ../glade/glade_project_window.c:1181
msgid "Project opened."
msgstr "프로젝트 열렸음"

#: ../glade/glade_project_window.c:1195
msgid "Error opening project."
msgstr "프로젝트를 여는 중 에러가 생김"

#: ../glade/glade_project_window.c:1259
msgid "Errors opening project file"
msgstr "프로젝트파일을 여는 중 에러가 생김"

#: ../glade/glade_project_window.c:1265
msgid " errors opening project file:"
msgstr "프로젝트파일을 여는 중 에러가 생김:"

#: ../glade/glade_project_window.c:1338
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""

#: ../glade/glade_project_window.c:1542
msgid "Error saving project"
msgstr "프로젝트저장중 에러발생 "

#: ../glade/glade_project_window.c:1544
msgid "Error saving project."
msgstr "프로젝트저장중 에러발생"

#: ../glade/glade_project_window.c:1550
msgid "Project saved."
msgstr "프로젝트 저장됨"

#: ../glade/glade_project_window.c:1620
msgid "Errors writing source code"
msgstr "소스코드를 쓸 때 에러발생"

#: ../glade/glade_project_window.c:1622
msgid "Error writing source."
msgstr "소스를 쓸 때 에러발생"

#: ../glade/glade_project_window.c:1628
msgid "Source code written."
msgstr "소스코드 씌여졌음"

#: ../glade/glade_project_window.c:1659
msgid "System error message:"
msgstr "시스템중 에러 정보:"

#: ../glade/glade_project_window.c:1698
msgid "Are you sure you want to quit?"
msgstr "정말 끝낼 것입니까?"

#: ../glade/glade_project_window.c:1982 ../glade/glade_project_window.c:2042
#, fuzzy
msgid "(C) 1998-2002 Damon Chaplin"
msgstr "Copyright 1998 Damon Chaplin"

#: ../glade/glade_project_window.c:1983 ../glade/glade_project_window.c:2041
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr ""

#: ../glade/glade_project_window.c:2012
#, fuzzy
msgid "About Glade"
msgstr "Glade의 종료"

#: ../glade/glade_project_window.c:2097
msgid "<untitled>"
msgstr "<이름없음>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "데이타베이스 브라우저"

#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "데이타-바운드 콤보"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr ""

#: ../glade/gnome-db/gnomedbconnectsel.c:147
#, fuzzy
msgid "Connection Selector"
msgstr "글꼴 선택"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr ""

#: ../glade/gnome-db/gnomedbdsndruid.c:147
msgid "DSN Config Druid"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:178
#, fuzzy
msgid "GnomeDbEditor"
msgstr "GnomeDateEdit"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "데이타베이스 애러 보기"

#: ../glade/gnome-db/gnomedberrordlg.c:218
msgid "Database error dialog"
msgstr "데이타베이스 애러 다이어로그"

#: ../glade/gnome-db/gnomedbform.c:147
#, fuzzy
msgid "Form"
msgstr "포맷:"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr ""

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr ""

#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "데이타-바운드 그리드"

#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "데이타-바운드 리스트"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "데이타베이스 로그인 위젯"

#: ../glade/gnome-db/gnomedblogindlg.c:76
msgid "Login"
msgstr "로그인"

#: ../glade/gnome-db/gnomedblogindlg.c:219
msgid "Database login dialog"
msgstr "데이타베이스 로그인 다이어로그"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
#, fuzzy
msgid "Provider Selector"
msgstr "그놈 용지 선택"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr ""

#: ../glade/gnome-db/gnomedbsourcesel.c:147
#, fuzzy
msgid "Data Source Selector"
msgstr "소스 디렉토리:"

#: ../glade/gnome-db/gnomedbtableeditor.c:133
#, fuzzy
msgid "Table Editor "
msgstr "메뉴 편집기"

#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "플로팅허가:"

#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "플로팅 도크 아이템을 허가하는가요?"

#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "윗머리에 도크를 추가"

#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "아래끝에 도크를 추가"

#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "왼쪽에 도크를 추가"

#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "오른쪽에 도크를 추가"

#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "플로팅 도크를 추가"

#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "그놈 도크"

#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "잠금:"

#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "도크아이템의 장소를 고정할까요?"

#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "독점:"

#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "도크에 있어서 유일한 도크아이템인가요?"

#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "플로팅 불허:"

#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "도크아이템에서 플로팅아이템으로 되는것을 허가하지 않습니다."

#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "세로의 불허:"

#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "도크아이템에서 세로방향으로 되는것을 허가하지 않습니다."

#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "가로의 불허:"

#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "도크아이템에서 가로방향으로 되는것을 허가하지 않습니다."

#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "도크아이템주위그림자의 유형"

#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "플로팅도크아이템의 방향"

#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "앞에 도크아이템을 추가"

#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "뒤에 도크아이템을 추가"

#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "그놈 도크 아이템"

#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr "패키지 설명, 웹의 홈페이지등의 추가정보 "

#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "그놈 프로그램 정보 다이어로그"

#: ../glade/gnome/gnomeapp.c:170
msgid "New File"
msgstr "새 파일"

#: ../glade/gnome/gnomeapp.c:172
msgid "Open File"
msgstr "파일열기"

#: ../glade/gnome/gnomeapp.c:174
msgid "Save File"
msgstr "파일저장"

#: ../glade/gnome/gnomeapp.c:203
msgid "Status Bar:"
msgstr "상태바:"

#: ../glade/gnome/gnomeapp.c:204
msgid "If the window has a status bar"
msgstr "윈도우가 상태바를 갖고 있는가요?"

#: ../glade/gnome/gnomeapp.c:205
msgid "Store Config:"
msgstr "설정저장:"

#: ../glade/gnome/gnomeapp.c:206
msgid "If the layout is saved and restored automatically"
msgstr "자동적으로 배열이 저장되는가요?"

#: ../glade/gnome/gnomeapp.c:442
msgid "Gnome Application Window"
msgstr "그놈 애플리케이션 윈도우"

#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "상황 정보"

#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "프로그레쓰:"

#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "애플리케이션 바가 프로그레스 지시부를 갖고 있는가요?"

#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "상태:"

#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr "애플리케이션 바가 상태정보와 사용자입력을 위한 구역을 갖고 있는가요?"

#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "그놈 애플리케이션 바"

#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "안티 알리아스:"

#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr "텍스트와 그래픽의 변을 미끈히 하기 위해 캔바스에 안티 알리아스를 할 지"

#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1:"

#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "x 좌표의 최소치"

#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1:"

#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "y축의 최소치"

#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2:"

#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "x 좌표의 최대치"

#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2:"

#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "y 좌표의 최대치"

#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "픽셀수/매단위:"

#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "한 단위에 해당되는 픽셀 수"

#: ../glade/gnome/gnomecanvas.c:239
msgid "GnomeCanvas"
msgstr "GnomeCanvas"

#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "디더링:"

#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "보다 정밀하도록 샘플에 디더링을 사용하는지"

#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "색깔의 선택"

#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "그놈 색깔 선택기"

#: ../glade/gnome/gnomecontrol.c:160
#, fuzzy
msgid "Couldn't create the Bonobo control"
msgstr ""
"파일을 생성할 수 없습니다:\n"
"  %s\n"

#: ../glade/gnome/gnomecontrol.c:249
#, fuzzy
msgid "New Bonobo Control"
msgstr "새 노트북"

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr ""

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr ""

#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3896
msgid "Description"
msgstr "설명"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr ""

#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "시간 보이기:"

#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "날짜와 마찬가지로 시간을 표기할까요?"

#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "24시간 형식:"

#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "24시간 형식중 시간을 보일까요?"

#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "최저한시간:"

#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "튀여나오기로 표시될 최저한시간"

#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "최대한시간:"

#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "튀여나오기로 표시될 최대한 시간"

#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "GnomeDateEdit"

#: ../glade/gnome/gnomedialog.c:152 ../glade/gnome/gnomemessagebox.c:189
msgid "Auto Close:"
msgstr "자동적으로 닫기기:"

#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "If the dialog closes when any button is clicked"
msgstr "다이어로그가 임의의 버튼이 눌리는것으로 자동적으로 닫기우는가요?"

#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "Hide on Close:"
msgstr "닫을 때 감추기:"

#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "다이어로그를 닫을적에 파괴될 대신에 감추기를 쓰겠나요?"

#: ../glade/gnome/gnomedialog.c:341
msgid "Gnome Dialog Box"
msgstr "그놈 다이어로그 박스"

#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "새 그놈 드뤼드"

#: ../glade/gnome/gnomedruid.c:190
#, fuzzy
msgid "Show Help"
msgstr "텍스트 보이기:"

#: ../glade/gnome/gnomedruid.c:190
#, fuzzy
msgid "Display the help button."
msgstr "버튼간의 간격"

#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "시작 페이지 추가"

#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "마지막 페이지 추가"

#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "드뤼드"

#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "페이지의 제목"

#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "사람들을 드뤼드에 인도하는 주 텍스트 페이지."

#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "제목 색깔:"

#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "제목 텍스트의 색깔"

#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "텍스트 색깔:"

#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "주 텍스트의 색깔"

#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "페이지의 배경색"

#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "로고 배경색:"

#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "로고 주위의 배경색"

#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "텍스트 박스 색깔:"

#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "주 텍스트 구역의 배경색 "

#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
#, fuzzy
msgid "Logo Image:"
msgstr "그림"

#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "페이지의 오른쪽 위에 표시되는 로고"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr ""

#: ../glade/gnome/gnomedruidpageedge.c:111
#, fuzzy
msgid "The main image to display on the side of the page."
msgstr "페이지의 왼쪽에 표시되는 메인 이미지"

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr ""

#: ../glade/gnome/gnomedruidpageedge.c:113
#, fuzzy
msgid "The watermark to display at the top of the page."
msgstr "페이지의 왼쪽에 표시되는 메인 이미지"

#: ../glade/gnome/gnomedruidpageedge.c:522
#, fuzzy
msgid "Druid Start or Finish Page"
msgstr "드뤼드 마지막 페이지"

#: ../glade/gnome/gnomedruidpagestandard.c:89
#, fuzzy
msgid "Contents Back. Color:"
msgstr "로고 배경색:"

#: ../glade/gnome/gnomedruidpagestandard.c:90
#, fuzzy
msgid "The background color around the title"
msgstr "로고 주위의 배경색"

#: ../glade/gnome/gnomedruidpagestandard.c:98
#, fuzzy
msgid "The image to display along the top of the page"
msgstr "페이지의 왼쪽에 표시되는 메인 이미지"

#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "드뤼드 표준 페이지"

#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "기록 ID:"

#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "기록을 저장할 ID"

#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "저장최대:"

#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "저장되는 기록의 최대수"

#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "그놈 엔트리"

#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "파일선택 다이어로그의 제목"

#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "디렉토리:"

#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "파일이 아니라 디렉토리가 필요되는가요?"

#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "선택다이어로그가 모달인가요?"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "그놈 파일 엔트리"

#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "글꼴 선택 다이어로그에 미리보기텍스트를 표시합니다."

#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "모드:"

#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "글꼴 선택 버튼이 무엇을 표시하는가요?"

#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "글꼴 선택 버튼에 쓰이는 글꼴의 크기"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "그놈 글꼴 선택기"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL:"

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "버튼을 누르면 표시할 URL"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "버튼에 표시되는 텍스트"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "그놈 HRef 링크 버튼"

#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "그놈 아이콘 엔트리"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "선택 모드"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "아이콘 너비:"

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "각 아이콘의 너비"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "각 아이콘 줄들 사이의 픽셀수"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "각 아이콘 열들 사이의 픽셀수"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "아이콘 테두리:"

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "아이콘 주변의 픽셀 수 (사용안됨?)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "텍스트 간격:"

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "텍스트와 아이콘 사이의 픽셀 수"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "텍스트 편집가능:"

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "아이콘 텍스트를 편집할 수 있는 지"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "텍스트 고정:"

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr ""
"아이콘 텍스트가 고정되었는지, 이 경우에 GnomeIconList에서 복사하지 못합니다"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "아이콘 목록"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "아이콘 선택"

#: ../glade/gnome/gnomemessagebox.c:174
msgid "Message Type:"
msgstr "정보 유형:"

#: ../glade/gnome/gnomemessagebox.c:175
msgid "The type of the message box"
msgstr "정보 박스의 유형"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "Message:"
msgstr "정보:"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "The message to display"
msgstr "표시되는 정보"

#: ../glade/gnome/gnomemessagebox.c:498
msgid "Gnome Message Box"
msgstr "그놈 메세지 박스"

#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "픽스맵 파일이름"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "확대축소:"

#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "픽스맵은 확대축소할 수 있는가요?"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "스케일할 너비:"

#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "픽스맵의 너비를 얼마로 스케일할 지"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "스케일할 높이"

#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "픽스맵의 높이를 얼마로 스케일할 지"

#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "그놈 픽스맵"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "미리보기:"

#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "픽스맵의 작은 미리보기를 표시할 지"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmapEntry"

#: ../glade/gnome/gnomepropertybox.c:112
msgid "New GnomePropertyBox"
msgstr "새 GnomePropertyBox"

#: ../glade/gnome/gnomepropertybox.c:365
msgid "Property Dialog Box"
msgstr "등록정보 대화상자"

#: ../glade/main.c:70
msgid "Write the source code and exit"
msgstr "소스코드 쓰기 와 종료"

#: ../glade/main.c:74
#, fuzzy
msgid "Start with the palette hidden"
msgstr "위젯의 팔렛트 보이기"

#: ../glade/main.c:78
#, fuzzy
msgid "Start with the property editor hidden"
msgstr "속성 에디터를 보이기"

#: ../glade/main.c:436
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr ""
"glade:XML파일은 반드시 '-w' 혹은 '--write-source' 옵션을 위해 설정돼야 합니"
"다.\n"

#: ../glade/main.c:450
msgid "glade: Error loading XML file.\n"
msgstr "glade: XML파일 적재중 에러가 발생.\n"

#: ../glade/main.c:457
msgid "glade: Error writing source.\n"
msgstr "glade:소스를 쓰는 과정에 에러가 발생.\n"

#: ../glade/palette.c:60
msgid "Palette"
msgstr "팔렛트"

#: ../glade/property.c:73
msgid "private"
msgstr "private"

#: ../glade/property.c:73
msgid "protected"
msgstr "protected"

#: ../glade/property.c:73
msgid "public"
msgstr "public"

#: ../glade/property.c:102
msgid "Prelight"
msgstr "프리라이트"

#: ../glade/property.c:103
msgid "Selected"
msgstr "선택됐음"

#: ../glade/property.c:103
msgid "Insens"
msgstr "무반응"

#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "윈도우 다시그림이 필요할 때"

#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "마우스를 움직일 때"

#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "마우스 이동 힌트"

#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "임의의 버튼을 누르면서 마우스를 움직였을 때"

#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "버튼1을 누르면서 마우스를 움직였을 때"

#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "버튼2을 누르면서 마우스를 움직였을 때"

#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "버튼3을 누르면서 마우스를 움직였을 때"

#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "임의의 마우스버튼을 눌렀을 때"

#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "임의의 마우스버튼을 놓았을 때"

#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "임의의 키를 눌렀을 때"

#: ../glade/property.c:477
msgid "Any key released"
msgstr "임의의 키를 놓았을 때"

#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "마우스커서가 윈도우안에 들어갔을 때"

#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "마우스커서가 윈도우에서 나갈 때"

#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "입력 초점에서 변경이 있을 때"

#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "윈도우 구성에서 변경이 있을 때"

#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "X Windows 소유물에서 변경이 있을 때"

#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "보이기에서 변화가 있을 때"

#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "XInput에 대한 프로그램의 커서를 위하여"

#: ../glade/property.c:596
msgid "Properties"
msgstr "등록정보"

#: ../glade/property.c:620
#, fuzzy
msgid "Packing"
msgstr "간격:"

#: ../glade/property.c:625
#, fuzzy
msgid "Common"
msgstr "해설:"

#: ../glade/property.c:631
msgid "Style"
msgstr "스타일"

#: ../glade/property.c:637 ../glade/property.c:4640
msgid "Signals"
msgstr "시그널"

#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "소유물:"

#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "소유물:<없음>"

#: ../glade/property.c:778
msgid "Class:"
msgstr "클래스:"

#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "위젯의 클래스"

#: ../glade/property.c:813
msgid "Width:"
msgstr "너비:"

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""

#: ../glade/property.c:816
msgid "Height:"
msgstr "높이:"

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""

#: ../glade/property.c:820
msgid "Visible:"
msgstr "보이는 것:"

#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "위젯이 최초로부터 보이는가요?"

#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "반응가능"

#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "위젯이 입력에 반응하는가요?"

#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "마우스 커서가 위젯의 위에 왔을 때 현시되는 툴팁"

#: ../glade/property.c:827
msgid "Can Default:"
msgstr "디폴트 가능:"

#: ../glade/property.c:828
#, fuzzy
msgid "If the widget can be the default action in a dialog"
msgstr "위젯이 디폴트 할 수 있는가?"

#: ../glade/property.c:829
msgid "Has Default:"
msgstr "디폴트설정:"

#: ../glade/property.c:830
#, fuzzy
msgid "If the widget is the default action in the dialog"
msgstr "위젯이 디폴트인가요?"

#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "초점가능:"

#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "위젯이 입력초점을 받을 수 있는 지"

#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "초점설정:"

#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "위젯에 입력초점이 맞는가요?"

#: ../glade/property.c:836
msgid "Events:"
msgstr "이벤트:"

#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "위젯이 받아 들이는 X이벤트"

#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "확장 이벤트:"

#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "X 확장 이벤트 모드"

#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "가속기:"

#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "키가 눌리웠을 적에 발행하는 시그널의 설정"

#: ../glade/property.c:845
#, fuzzy
msgid "Edit..."
msgstr "편집"

#: ../glade/property.c:867
msgid "Propagate:"
msgstr "계승:"

#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "위젯의 자식에 스타일을 계승시킴"

#: ../glade/property.c:869
msgid "Named Style:"
msgstr "스타일 이름:"

#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "몇개의 위젯에서 공유되여 있는 스타일의 이름"

#: ../glade/property.c:872
msgid "Font:"
msgstr "글꼴:"

#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "위젯의 소유의 텍스트에 사용되는 글꼴"

#: ../glade/property.c:898
msgid "Copy All"
msgstr "전부 복사"

#: ../glade/property.c:926
msgid "Foreground:"
msgstr "앞면:"

#: ../glade/property.c:926
msgid "Background:"
msgstr "배경:"

#: ../glade/property.c:926
msgid "Base:"
msgstr "기본:"

#: ../glade/property.c:928
msgid "Foreground color"
msgstr "앞면 색깔"

#: ../glade/property.c:928
msgid "Background color"
msgstr "배경 색깔"

#: ../glade/property.c:928
msgid "Text color"
msgstr "텍스트 색깔 "

#: ../glade/property.c:929
msgid "Base color"
msgstr "기본바탕 색깔"

#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "배경 픽스맵:"

#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "위젯의 배경으로 사용되는 그래픽"

#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "소스를 써넣는 디렉토리"

#: ../glade/property.c:1000
msgid "Public:"
msgstr "전체:"

#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "위젯이 조성부분의 데이타구성에 추가되는가요?"

#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "다른 종류:"

#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "이 위젯의 삽트리를 다른 종류에 써넣기 "

#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "별도 파일:"

#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "이 위젯을 다른 소스파일에 써넣기"

#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "공개/보호:"

#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr "위젯의 공개/보호/비공개.공개 위젯은 글로발매프에 씌여져 있습니다."

#: ../glade/property.c:1126
msgid "You need to select a color or background to copy"
msgstr "복사할 색깔 혹은 배경을 선택할 필요가 있습니다."

#: ../glade/property.c:1145
msgid "Invalid selection in on_style_copy()"
msgstr "on_style_copy()에서의 선택 착오가 있습니다."

#: ../glade/property.c:1187
msgid "You need to copy a color or background pixmap first"
msgstr "최초에 색깔 또는 배경 픽스맵을 복사할 필요가 있습니다."

#: ../glade/property.c:1193
msgid "You need to select a color to paste into"
msgstr "붙일 색깔을 선택할 필요가 있습니다."

#: ../glade/property.c:1203
msgid "You need to select a background pixmap to paste into"
msgstr "붙일 배경 픽스맵을 선택할 필요가 있습니다."

#: ../glade/property.c:1455
msgid "Couldn't create pixmap from file\n"
msgstr "파일에서 픽스맵을 생성할 수 없습니다.\n"

#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1497
msgid "Signal"
msgstr "시그널 "

#: ../glade/property.c:1499
msgid "Data"
msgstr "데이타"

#: ../glade/property.c:1500
msgid "After"
msgstr "에프터"

#: ../glade/property.c:1501
msgid "Object"
msgstr "오브젝트"

#: ../glade/property.c:1532 ../glade/property.c:1696
msgid "Signal:"
msgstr "시그널:"

#: ../glade/property.c:1533
msgid "The signal to add a handler for"
msgstr "핸들을 추가할 시그널"

#: ../glade/property.c:1547
msgid "The function to handle the signal"
msgstr "시그널를 취급하는 함수"

#: ../glade/property.c:1550
msgid "Data:"
msgstr "데이타:"

#: ../glade/property.c:1551
msgid "The data passed to the handler"
msgstr "핸들에 넘어갈 데이타"

#: ../glade/property.c:1552
msgid "Object:"
msgstr "오브젝트:"

#: ../glade/property.c:1553
msgid "The object which receives the signal"
msgstr "시그널를 받아 들이는 오브젝트"

#: ../glade/property.c:1554
msgid "After:"
msgstr "애프터:"

#: ../glade/property.c:1555
msgid "If the handler runs after the class function"
msgstr "클래스 함수 후에 핸들러가 실행되는 지"

#: ../glade/property.c:1568
msgid "Add"
msgstr "추가"

#: ../glade/property.c:1574
msgid "Update"
msgstr "업데이트"

#: ../glade/property.c:1586
msgid "Clear"
msgstr "깨끗이"

#: ../glade/property.c:1636
msgid "Accelerators"
msgstr "가속기"

#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1649
msgid "Mod"
msgstr "Mod"

#: ../glade/property.c:1650
msgid "Key"
msgstr "키"

#: ../glade/property.c:1651
msgid "Signal to emit"
msgstr "발행할 시그널"

#: ../glade/property.c:1695
msgid "The accelerator key"
msgstr "가속기 키"

#: ../glade/property.c:1697
msgid "The signal to emit when the accelerator is pressed"
msgstr "가속기가 눌리웠을적에 발행될 시그널"

#: ../glade/property.c:1846
msgid "Edit Text Property"
msgstr ""

#: ../glade/property.c:1884
msgid "<b>_Text:</b>"
msgstr ""

#: ../glade/property.c:1894
#, fuzzy
msgid "T_ranslatable"
msgstr "번역할 수 있는 스트링:"

#: ../glade/property.c:1898
msgid "Has Context _Prefix"
msgstr ""

#: ../glade/property.c:1924
msgid "<b>Co_mments For Translators:</b>"
msgstr ""

#: ../glade/property.c:3886
msgid "Select X Events"
msgstr "X이벤트 선택"

#: ../glade/property.c:3895
msgid "Event Mask"
msgstr "이벤트 마스크"

#: ../glade/property.c:4025 ../glade/property.c:4074
msgid "You need to set the accelerator key"
msgstr "가속기키를 설정할 필요가 있습니다."

#: ../glade/property.c:4032 ../glade/property.c:4081
msgid "You need to set the signal to emit"
msgstr "발생할 시그널를 설정할 필요가 있습니다."

#: ../glade/property.c:4308 ../glade/property.c:4364
msgid "You need to set the signal name"
msgstr "시그널명을 설정할 필요가 있습니다."

#: ../glade/property.c:4315 ../glade/property.c:4371
msgid "You need to set the handler for the signal"
msgstr "시그널에 핸들을 설정할 필요가 있습니다."

#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4574
#, c-format
msgid "%s signals"
msgstr "%s시그널"

#: ../glade/property.c:4631
msgid "Select Signal"
msgstr "시그널선택"

#: ../glade/property.c:4827
msgid "Value:"
msgstr "수치:"

#: ../glade/property.c:4827
msgid "Min:"
msgstr "최소:"

#: ../glade/property.c:4827
msgid "Step Inc:"
msgstr "스텝 증폭:"

#: ../glade/property.c:4828
msgid "Page Inc:"
msgstr "페이지 증폭:"

#: ../glade/property.c:4828
msgid "Page Size:"
msgstr "페이지 크기:"

#: ../glade/property.c:4830
msgid "H Value:"
msgstr "가로 치:"

#: ../glade/property.c:4830
msgid "H Min:"
msgstr "가로 최소치:"

#: ../glade/property.c:4830
msgid "H Max:"
msgstr "가로 최대치:"

#: ../glade/property.c:4830
msgid "H Step Inc:"
msgstr "가로 스텝 증폭:"

#: ../glade/property.c:4831
msgid "H Page Inc:"
msgstr "가로 페이지 증폭:"

#: ../glade/property.c:4831
msgid "H Page Size:"
msgstr "가로 페이지 크기:"

#: ../glade/property.c:4833
msgid "V Value:"
msgstr "세로 치:"

#: ../glade/property.c:4833
msgid "V Min:"
msgstr "세로 최소치:"

#: ../glade/property.c:4833
msgid "V Max:"
msgstr "세로 최대치:"

#: ../glade/property.c:4833
msgid "V Step Inc:"
msgstr "세로 스텝 증폭:"

#: ../glade/property.c:4834
msgid "V Page Inc:"
msgstr "세로 페이지 증폭:"

#: ../glade/property.c:4834
msgid "V Page Size:"
msgstr "세로 페이지 크기:"

#: ../glade/property.c:4837
msgid "The initial value"
msgstr "초기치"

#: ../glade/property.c:4838
msgid "The minimum value"
msgstr "최소수치"

#: ../glade/property.c:4839
msgid "The maximum value"
msgstr "최대수치"

#: ../glade/property.c:4840
msgid "The step increment"
msgstr "스텝의 증가치 "

#: ../glade/property.c:4841
msgid "The page increment"
msgstr "페이지의 증가치"

#: ../glade/property.c:4842
msgid "The page size"
msgstr "페이지의 크기"

#: ../glade/property.c:4997
msgid "The requested font is not available."
msgstr "글꼴을 사용할 수 없습니다."

#: ../glade/property.c:5046
msgid "Select Named Style"
msgstr "스타일 이름 선택"

#: ../glade/property.c:5057
msgid "Styles"
msgstr "스타일"

#: ../glade/property.c:5116
msgid "Rename"
msgstr "다시 이름짓기"

#: ../glade/property.c:5144
msgid "Cancel"
msgstr "취소"

#: ../glade/property.c:5264
msgid "New Style:"
msgstr "새 스타일:"

#: ../glade/property.c:5278 ../glade/property.c:5399
msgid "Invalid style name"
msgstr "부당한 스타일 이름"

#: ../glade/property.c:5286 ../glade/property.c:5409
msgid "That style name is already in use"
msgstr "그 스타일 이름은 이미 사용되고 있습니다."

#: ../glade/property.c:5384
msgid "Rename Style To:"
msgstr "스타일 이름 변경:"

#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"파일의 이름을 바꿀수 없습니다:\n"
"  %s\n"
"변경후:\n"
"  %s\n"

#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"파일을 생성할 수 없습니다:\n"
"  %s\n"

#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "XML파일을 쓰는 과정 에러가 발생\n"

#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Glade에 의해 생성된 번역 스트링입니다.\n"
" * 이 파일을 귀하의 프로젝트의 POTFILES.in에 가해 주십시오.\n"
" * 귀하의 애플리케이션의 일부로 컴파일하지 말아 주십시오.\n"
" */\n"
"\n"

#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"부당한 매개면 소스 파일이름: %s\n"
"%s\n"

#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"부당한 매개면 헤더 파일이름: %s\n"
"%s\n"

#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"부당한 콜백 소스 파일이름: %s\n"
"%s\n"

#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"부당한 콜백 헤더 파일이름: %s\n"
"%s\n"

#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"부당한 서포트 소스 파일이름: %s\n"
"%s\n"

#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"부당한 서포트 헤더 파일이름: %s\n"
"%s\n"

#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"파일에 추가할 수 없습니다:\n"
"  %s\n"

#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"파일을 쓰는 과정 에러:\n"
"  %s\n"

#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr "파일이름이 프로젝트설정다이어로그에 설정되지 않으면 안됩니다."

#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"파일이름은 단순한 상대파일이름이 아니면 안됩니다.\n"
"프로젝트설정다이어로그로 설정하십시오."

#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "위젯트리"

#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "박스에 위젯이 보이지 않습니다."

#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "테이블에 위젯이 보이지 않습니다."

#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "고정 콘테이너에 위젯이 보이지 않습니다."

#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "팩커에 위젯이 없습니다."

#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"파일에 도달할 수 없습니다:\n"
"  %s\n"

#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"파일을 열수 없습니다:\n"
"  %s\n"

#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"파일을 읽는중 에러가 발생:\n"
"  %s\n"

#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"디렉토리를 생성할 수 없습니다:\n"
"  %s\n"

#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"디렉토리에 도달할 수 없습니다:\n"
"  %s\n"

#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"부당한 디렉토리:\n"
"  %s\n"

#: ../glade/utils.c:1611
msgid "Projects"
msgstr "Projects"

#: ../glade/utils.c:1628
msgid "project"
msgstr "project"

#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"디렉토리를 열수 없습니다:\n"
"  %s\n"
