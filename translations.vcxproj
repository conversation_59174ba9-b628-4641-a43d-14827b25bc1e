﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="debug Windows|Win32">
      <Configuration>debug Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="release Windows|Win32">
      <Configuration>release Windows</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E7938205-D3F6-11AA-7C7C-4A6068BEDD2C}</ProjectGuid>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>translations</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <PlatformToolset>ClangCL</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
    <OutDir>bin\debug\plugins\</OutDir>
    <IntDir>obj\Windows\debug\translations\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
    <OutDir>bin\release\plugins\</OutDir>
    <IntDir>obj\Windows\release\translations\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='debug Windows|Win32'">
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='release Windows|Win32'">
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="po\be.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/be.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\bg.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/bg.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\bn.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/bn.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ca.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ca.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\cs.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/cs.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\da.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/da.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\de.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/de.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\el.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/el.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\en_GB.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/en_GB.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\es.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/es.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\et.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/et.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\eu.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/eu.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\fa.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/fa.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\fi.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/fi.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\fr.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/fr.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\gl.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/gl.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\he.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/he.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\hr.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/hr.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\hu.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/hu.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\id.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/id.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ie.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ie.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\it.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/it.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ja.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ja.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ka.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ka.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\kk.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/kk.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\km.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/km.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ko.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ko.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\lg.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/lg.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\lt.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/lt.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\lv.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/lv.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\nb.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/nb.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ne.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ne.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\nl.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/nl.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\pl.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/pl.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\pt.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/pt.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\pt_BR.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/pt_BR.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ro.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ro.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\si_LK.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/si_LK.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\sk.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/sk.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\sl.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/sl.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\sr.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/sr.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\<EMAIL>">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/<EMAIL></Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\sv.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/sv.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\te.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/te.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\tr.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/tr.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\ug.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/ug.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\uk.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/uk.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\vi.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/vi.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\zh_CN.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/zh_CN.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
    <CustomBuild Include="po\zh_TW.po">
      <FileType>Document</FileType>
      <Command>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Command>
      <Outputs>po/zh_TW.gmo</Outputs>
      <Message>msgfmt -o "%(RootDir)%(Directory)/%(Filename).gmo" "%(Identity)"</Message>
    </CustomBuild>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>