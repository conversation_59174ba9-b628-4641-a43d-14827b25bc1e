{\rtf1\ansi\ansicpg1252\cocoartf2706
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica-Bold;\f1\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\paperw11900\paperh16840\margl1440\margr1440\vieww13440\viewh7800\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural\partightenfactor0

\f0\b\fs24 \cf0 Credits
\f1\b0 \
\
Copyright \'a9 2009-2023  <PERSON><PERSON><PERSON><PERSON> and contributors\
https://deadbeef.sourceforge.io\
\
Some of the most significant contributors in alphabetical order:\
\
    Alex <PERSON>dul <<EMAIL>>\
    <PERSON><PERSON> <<EMAIL>>\
    <PERSON> <<EMAIL>>\
    Christian Boxd\'f6rfer <<EMAIL>>\
    <PERSON> <<EMAIL>>\
    Derreck <<EMAIL>>\
    Ian Nartowicz\
    Igor Murzov <<EMAIL>>\
    Igor Rudchenko <<EMAIL>>\
    Jakub Wasylk\'f3w <<EMAIL>>\
    Jan D. Behrens <<EMAIL>>\
    Kryksyh <<EMAIL>>\
    Martin Panter\
    Nicolai Syvertsen <<EMAIL>>\
    Serghey Kotlyarov <<EMAIL>>\
    Sofias <<EMAIL>>\
    starws <<EMAIL>>\
    Stas Akimushkin <<EMAIL>>\
    Themaister <<EMAIL>>\
    Veniamin Gvozdikov <<EMAIL>>\
    Viktor Semykin <<EMAIL>>\
\
If you think your name is missing here, or needs to be removed, please send an email to Oleksiy Yakovenko <<EMAIL>>\
\
Special Thanks To:\
\
    James Lee\
    Jan Marguc\
    Olga Belozerova\
\
\
The bundled 3rd party libraries:\
\
\
Independent implementation of MD5 (RFC 1321).\
Copyright \'a9 1999, 2000, 2002 Aladdin Enterprises.  All rights reserved.\
\
Fast FTOI routines from libvorbis\
Copyright \'a9 2002-2008 Xiph.org Foundation\
\
The UTF-8 code is based on Basic UTF-8 manipulation routines by Jeff Bezanson\
Copyright \'a9 Jeff Bezanson\
placed in the public domain Fall 2005\
\
\
For information on the libraries used by the plugins -- please see the specific plugin's copyright information.\
}