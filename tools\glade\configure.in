dnl Process this file with autoconf to produce a configure script.

AC_INIT([Glad<PERSON>],[2.12.2-ddb],[http://deadbeef.sf.net],[glade])

AC_CONFIG_SRCDIR([glade/gbwidget.c])

AM_INIT_AUTOMAKE([1.9 foreign no-dist-gzip dist-bzip2])
AM_MAINTAINER_MODE
AC_CONFIG_HEADERS([config.h])

AC_ISC_POSIX
AC_PROG_CC

AM_PROG_LIBTOOL
AM_PROG_CC_C_O

AC_PROG_INSTALL
AC_PROG_MAKE_SET

AC_CHECK_HEADERS(os2.h)

AC_PATH_XTRA

dnl ***************************************************************************
dnl pkg-config - check for gtk
dnl ***************************************************************************

glade_modules=""
gtk_modules="libxml-2.0 >= 2.4.1 gtk+-2.0 >= 2.8.0"

PKG_CHECK_MODULES(GLADE_GTK, [$gtk_modules])

glade_modules="$glade_modules $gtk_modules"


dnl ***************************************************************************
dnl Final pkgconfig run: this runs with all of the modules we need
dnl ***************************************************************************
PKG_CHECK_MODULES(GLADE, [$glade_modules])
GLADE_CFLAGS="$GLADE_CFLAGS $X_CFLAGS"
GLADE_LIBS="$GLADE_LIBS $X_LIBS $X_PRE_LIBS -lX11 $X_EXTRA_LIBS"
AC_SUBST(GLADE_CFLAGS)
AC_SUBST(GLADE_LIBS)


dnl ***************************************************************************
dnl Deprecation flags.
dnl ***************************************************************************

GLADE_DEPRECATION_CFLAGS=
dnl GLADE_DEPRECATION_CFLAGS="-DG_DISABLE_DEPRECATED -DGDK_DISABLE_DEPRECATED -DGTK_DISABLE_DEPRECATED -DGNOME_DISABLE_DEPRECATED -Wall -Werror"
AC_SUBST(GLADE_DEPRECATION_CFLAGS)


dnl ***************************************************************************
dnl Gettext stuff.
dnl ***************************************************************************

GETTEXT_PACKAGE=glade-2.0
AC_SUBST(GETTEXT_PACKAGE)
AC_DEFINE_UNQUOTED(GETTEXT_PACKAGE,"$GETTEXT_PACKAGE", [Gettext package.])

AM_GLIB_GNU_GETTEXT
IT_PROG_INTLTOOL([0.35.0])

dnl ***************************************************************************
dnl Debugging - defines GLADE_DEBUG if --enable-debug option is used.
dnl ***************************************************************************

AC_ARG_ENABLE(debug,
[  --enable-debug          turn on debugging [default=no]],
	AC_DEFINE(GLADE_DEBUG, 1, [Define to 1 to enable debugging code.]),)


dnl ***************************************************************************
dnl Output the Makefiles etc.
dnl ***************************************************************************

AC_OUTPUT([
glade.spec
Makefile
glade/Makefile
glade/gbwidgets/Makefile
glade/data/Makefile
glade/data/gtk/Makefile
omf-install/Makefile
])

dnl doc/it/Makefile

echo "

Configuration:

	Source code location:	${srcdir}
	Compiler:		${CC} 
"
