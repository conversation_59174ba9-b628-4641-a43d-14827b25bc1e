//
//  GuiPreferencesWindowController.h
//  DeaDBeeF
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2/26/20.
//  Copyright © 2020 <PERSON><PERSON><PERSON><PERSON>. All rights reserved.
//

#import <Cocoa/Cocoa.h>

#define DEFAULT_TITLEBAR_PLAYING_VALUE "DeaDBeeF"
#define DEFAULT_TITLEBAR_SUBTITLE_PLAYING_VALUE "%artist% - %title%"
#define DEFAULT_TITLEBAR_STOPPED_VALUE "DeaDBeeF"
#define DEFAULT_TITLEBAR_SUBTITLE_STOPPED_VALUE "Stopped"

NS_ASSUME_NONNULL_BEGIN

@interface GuiPreferencesWindowController : NSViewController

@end

NS_ASSUME_NONNULL_END
