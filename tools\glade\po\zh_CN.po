# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR Free Software Foundation, Inc.
# <PERSON> <<EMAIL>>
#
msgid ""
msgstr ""
"Project-Id-Version: glade\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2005-08-26 13:38+0200\n"
"PO-Revision-Date: 2002-05-30 03:33\n"
"Last-Translator: Sun G11n <<EMAIL>>\n"
"Language-Team: Sun G11n <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: ../glade-2.desktop.in.h:1
msgid "Design user interfaces"
msgstr ""

#: ../glade-2.desktop.in.h:2
msgid "Glade Interface Designer"
msgstr ""

#: ../glade/editor.c:343
msgid "Grid Options"
msgstr "网格选项"

#: ../glade/editor.c:357
msgid "Horizontal Spacing:"
msgstr "水平间隔："

#: ../glade/editor.c:372
msgid "Vertical Spacing:"
msgstr "垂直间隔："

#: ../glade/editor.c:390
msgid "Grid Style:"
msgstr "网格风格："

#: ../glade/editor.c:396
msgid "Dots"
msgstr "点"

#: ../glade/editor.c:405
msgid "Lines"
msgstr "线"

#: ../glade/editor.c:487
msgid "Snap Options"
msgstr "定位选项"

#. Horizontal snapping
#: ../glade/editor.c:502
msgid "Horizontal Snapping:"
msgstr "水平定位："

#: ../glade/editor.c:508 ../glade/gbwidgets/gbtextview.c:49
msgid "Left"
msgstr "左"

#: ../glade/editor.c:517 ../glade/gbwidgets/gbtextview.c:50
msgid "Right"
msgstr "右"

#. Vertical snapping
#: ../glade/editor.c:526
msgid "Vertical Snapping:"
msgstr "垂直定位："

#: ../glade/editor.c:532
msgid "Top"
msgstr "上"

#: ../glade/editor.c:540
msgid "Bottom"
msgstr "下"

#: ../glade/editor.c:741
#, fuzzy
msgid "GtkToolItem widgets can only be added to a GtkToolbar."
msgstr "GnomeDockItem 部件只能被粘贴到 GnomeDock 中。"

#: ../glade/editor.c:758
msgid "Couldn't insert a GtkScrolledWindow widget."
msgstr "无法插入一个 GtkScrolledWindow 部件。"

#: ../glade/editor.c:805
msgid "Couldn't insert a GtkViewport widget."
msgstr "无法插入一个 GtkViewport 部件。"

#: ../glade/editor.c:832
msgid "Couldn't add new widget."
msgstr "无法添加新部件。"

#: ../glade/editor.c:1230
msgid ""
"You can't add a widget at the selected position.\n"
"\n"
"Tip: GTK+ uses containers to lay out widgets.\n"
"Try deleting the existing widget and using\n"
"a box or table container instead.\n"
msgstr ""
"您无法在选定的位置添加部件。\n"
"\n"
"提示：GTK+ 使用容器来进行部件布局。您可以试试\n"
"将存在的部件删除，再使用方框或表容器。\n"

#: ../glade/editor.c:3517
msgid "Couldn't delete widget."
msgstr "无法删除部件。"

#: ../glade/editor.c:3541 ../glade/editor.c:3545
msgid "The widget can't be deleted"
msgstr "部件不能被删除"

#: ../glade/editor.c:3572
msgid ""
"The widget is created automatically as part of the parent widget, and it "
"can't be deleted."
msgstr "该部件自动创建作为父部件的一部分，不能被删除。"

#: ../glade/gbwidget.c:697
msgid "Border Width:"
msgstr "边界宽度："

#: ../glade/gbwidget.c:698
msgid "The width of the border around the container"
msgstr "容器边界的宽度"

#: ../glade/gbwidget.c:1745
msgid "Select"
msgstr "选择"

#: ../glade/gbwidget.c:1767
msgid "Remove Scrolled Window"
msgstr "删除卷动窗口"

#: ../glade/gbwidget.c:1776
msgid "Add Scrolled Window"
msgstr "添加卷动窗口"

#: ../glade/gbwidget.c:1797
msgid "Remove Alignment"
msgstr "删除对齐"

#: ../glade/gbwidget.c:1805
msgid "Add Alignment"
msgstr "添加对齐"

#: ../glade/gbwidget.c:1820
msgid "Remove Event Box"
msgstr "删除事件框"

#: ../glade/gbwidget.c:1828
msgid "Add Event Box"
msgstr "添加事件框"

#: ../glade/gbwidget.c:1838
msgid "Redisplay"
msgstr "重新显示"

#: ../glade/gbwidget.c:1849
msgid "Cut"
msgstr "剪切"

#: ../glade/gbwidget.c:1856 ../glade/property.c:892 ../glade/property.c:5135
msgid "Copy"
msgstr "复制"

#: ../glade/gbwidget.c:1865 ../glade/property.c:904
msgid "Paste"
msgstr "粘贴"

#: ../glade/gbwidget.c:1877 ../glade/property.c:1580 ../glade/property.c:5126
msgid "Delete"
msgstr "删除"

#. N/A stands for 'Not Applicable'. It is used when a standard widget
#. property does not apply to the current widget. e.g. widgets without
#. windows can't use the Events property. This appears in the property
#. editor and so should be a short abbreviation.
#: ../glade/gbwidget.c:2403 ../glade/gbwidget.c:2472
msgid "N/A"
msgstr "不可用"

#. General code for container - has to remove all children and add back
#. NOTE: this may not work for specialised containers.
#. NOTE: need to ref widgets?
#: ../glade/gbwidget.c:3202
msgid "replacing child of container - not implemented yet\n"
msgstr "替换容器的子部件 - 没有实现\n"

#: ../glade/gbwidget.c:3430
msgid "Couldn't insert GtkAlignment widget."
msgstr "无法插入 GtkAlignment 部件。"

#: ../glade/gbwidget.c:3470
msgid "Couldn't remove GtkAlignment widget."
msgstr "无法删除 GtkAlignment 部件。"

#: ../glade/gbwidget.c:3494
msgid "Couldn't insert GtkEventBox widget."
msgstr "无法插入 GtkEventBox 部件。"

#: ../glade/gbwidget.c:3533
msgid "Couldn't remove GtkEventBox widget."
msgstr "无法删除 GtkEventBox 部件。"

#: ../glade/gbwidget.c:3568
msgid "Couldn't insert GtkScrolledWindow widget."
msgstr "无法插入 GtkScrolledWindow 部件。"

#: ../glade/gbwidget.c:3607
msgid "Couldn't remove GtkScrolledWindow widget."
msgstr "无法删除 GtkScrolledWindow 部件。"

#: ../glade/gbwidget.c:3721
msgid "Remove Label"
msgstr "删除标签"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:78
#, fuzzy
msgid "Application Name"
msgstr "Gnome 应用程序栏"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "Logo:"
msgstr "徽标："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:102 ../glade/gnome/gnomeabout.c:137
msgid "The pixmap to use as the logo"
msgstr "用作徽标的像素图"

#: ../glade/gbwidgets/gbaboutdialog.c:104 ../glade/glade_project_options.c:260
msgid "Program Name:"
msgstr "程序名："

#: ../glade/gbwidgets/gbaboutdialog.c:104
#, fuzzy
msgid "The name of the application"
msgstr "部件名"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:105 ../glade/gnome/gnomeabout.c:139
msgid "Comments:"
msgstr "备注："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:105
#, fuzzy
msgid "Additional information, such as a description of the application"
msgstr "附加信息，例如软件包的说明及其在网上的主页"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "Copyright:"
msgstr "版权："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:106 ../glade/gnome/gnomeabout.c:138
msgid "The copyright notice"
msgstr "版权声明"

#: ../glade/gbwidgets/gbaboutdialog.c:108
msgid "Website URL:"
msgstr ""

#: ../glade/gbwidgets/gbaboutdialog.c:108
#, fuzzy
msgid "The URL of the application's website"
msgstr "是否要编写 Gnome 应用程序"

#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "Website Label:"
msgstr "标签："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:109
#, fuzzy
msgid "The label to display for the link to the website"
msgstr "显示在页面右上方的徽标"

#: ../glade/gbwidgets/gbaboutdialog.c:111 ../glade/glade_project_options.c:365
msgid "License:"
msgstr "授权："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:111
#, fuzzy
msgid "The license details of the application"
msgstr "按钮的释放样式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "Authors:"
msgstr "作者："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:113 ../glade/gnome/gnomeabout.c:141
msgid "The authors of the package, one on each line"
msgstr "软件包的作者，每行一位"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
#, fuzzy
msgid "Documenters:"
msgstr "备注："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:114 ../glade/gnome/gnomeabout.c:142
#, fuzzy
msgid "The documenters of the package, one on each line"
msgstr "软件包的作者，每行一位"

#: ../glade/gbwidgets/gbaboutdialog.c:115
msgid "Artists:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:115
#, fuzzy
msgid ""
"The people who have created the artwork for the package, one on each line"
msgstr "软件包的作者，每行一位"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
#, fuzzy
msgid "Translators:"
msgstr "可翻译字符串："

#: ../glade/gbwidgets/gbaboutdialog.c:116 ../glade/gnome/gnomeabout.c:143
msgid ""
"The translators of the package. This should normally be left empty so that "
"translators can add their names in the po files"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaboutdialog.c:559
#, fuzzy
msgid "About Dialog"
msgstr "Gnome 关于对话框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaccellabel.c:200
msgid "Label with Accelerator"
msgstr "带有加速键的标签"

# SUN NEW TRANSLATION
#. The GtkProgress interface is deprecated now, and GtkProgressBar doesn't
#. have functions to set these, so I suppose we shouldn't support them.
#: ../glade/gbwidgets/gbalignment.c:71 ../glade/gbwidgets/gbarrow.c:89
#: ../glade/gbwidgets/gbaspectframe.c:130 ../glade/gbwidgets/gbimage.c:119
#: ../glade/gbwidgets/gblabel.c:180 ../glade/gbwidgets/gbprogressbar.c:162
msgid "X Align:"
msgstr "X 对齐："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:72
msgid "The horizontal alignment of the child widget"
msgstr "水平对齐子构件"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:74 ../glade/gbwidgets/gbarrow.c:92
#: ../glade/gbwidgets/gbaspectframe.c:133 ../glade/gbwidgets/gbimage.c:122
#: ../glade/gbwidgets/gblabel.c:183 ../glade/gbwidgets/gbprogressbar.c:165
msgid "Y Align:"
msgstr "Y 对齐："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:75
msgid "The vertical alignment of the child widget"
msgstr "垂直对齐子构件"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:77
msgid "X Scale:"
msgstr "X 比例尺："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:78
msgid "The horizontal scale of the child widget"
msgstr "子构件的水平比例尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:80
msgid "Y Scale:"
msgstr "Y 比例尺："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:81
msgid "The vertical scale of the child widget"
msgstr "子构件的垂直比例尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:85
#, fuzzy
msgid "Top Padding:"
msgstr "水平填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:86
#, fuzzy
msgid "Space to put above the child widget"
msgstr "子构件的水平比例尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:89
#, fuzzy
msgid "Bottom Padding:"
msgstr "水平填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:90
#, fuzzy
msgid "Space to put below the child widget"
msgstr "子构件的水平比例尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:93
#, fuzzy
msgid "Left Padding:"
msgstr "水平填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:94
#, fuzzy
msgid "Space to put to the left of the child widget"
msgstr "子构件的水平比例尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:97
#, fuzzy
msgid "Right Padding:"
msgstr "水平填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:98
#, fuzzy
msgid "Space to put to the right of the child widget"
msgstr "水平对齐子构件"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbalignment.c:255
msgid "Alignment"
msgstr "对齐"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:85
msgid "Direction:"
msgstr "方向："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:85
msgid "The direction of the arrow"
msgstr "箭头方向"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:87 ../glade/gbwidgets/gbaspectframe.c:128
#: ../glade/gbwidgets/gbclist.c:247 ../glade/gbwidgets/gbctree.c:253
#: ../glade/gbwidgets/gbframe.c:123 ../glade/gbwidgets/gbhandlebox.c:109
#: ../glade/gbwidgets/gbviewport.c:104 ../glade/gnome/bonobodockitem.c:176
msgid "Shadow:"
msgstr "阴影："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:87
msgid "The shadow type of the arrow"
msgstr "箭头的阴影类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:90
msgid "The horizontal alignment of the arrow"
msgstr "水平对齐箭头"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:93
msgid "The vertical alignment of the arrow"
msgstr "垂直对齐箭头"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186
msgid "X Pad:"
msgstr "X 填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:95 ../glade/gbwidgets/gbimage.c:125
#: ../glade/gbwidgets/gblabel.c:186 ../glade/gbwidgets/gbtable.c:382
msgid "The horizontal padding"
msgstr "水平填充"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188
msgid "Y Pad:"
msgstr "Y 填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:97 ../glade/gbwidgets/gbimage.c:127
#: ../glade/gbwidgets/gblabel.c:188 ../glade/gbwidgets/gbtable.c:385
msgid "The vertical padding"
msgstr "垂直填充"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbarrow.c:299
msgid "Arrow"
msgstr "箭头"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:122 ../glade/gbwidgets/gbframe.c:117
msgid "Label X Align:"
msgstr "标签 X 对齐："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:123 ../glade/gbwidgets/gbframe.c:118
#, fuzzy
msgid "The horizontal alignment of the frame's label widget"
msgstr "水平对齐帧标签"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:125 ../glade/gbwidgets/gbframe.c:120
#, fuzzy
msgid "Label Y Align:"
msgstr "标签 X 对齐："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:126 ../glade/gbwidgets/gbframe.c:121
#, fuzzy
msgid "The vertical alignment of the frame's label widget"
msgstr "垂直对齐整个标签"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:128 ../glade/gbwidgets/gbframe.c:123
msgid "The type of shadow of the frame"
msgstr "帧阴影的类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:131
#: ../glade/gbwidgets/gbaspectframe.c:134
msgid "The horizontal alignment of the frame's child"
msgstr "水平对齐帧的子帧"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:136
msgid "Ratio:"
msgstr "比例："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:137
msgid "The aspect ratio of the frame's child"
msgstr "该帧的子帧的长宽比"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:138
msgid "Obey Child:"
msgstr "遵从子项："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:139
msgid "If the aspect ratio should be determined by the child"
msgstr "长宽比是否由子项决定"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbaspectframe.c:319
msgid "Aspect Frame"
msgstr "帧外观"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:118 ../glade/gbwidgets/gbcheckbutton.c:85
#: ../glade/gbwidgets/gbmenutoolbutton.c:85
#: ../glade/gbwidgets/gbradiobutton.c:126
#: ../glade/gbwidgets/gbradiotoolbutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:88
#: ../glade/gbwidgets/gbtoggletoolbutton.c:90
#: ../glade/gbwidgets/gbtoolbutton.c:104
msgid "Stock Button:"
msgstr "库按钮："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:119 ../glade/gbwidgets/gbcheckbutton.c:86
#: ../glade/gbwidgets/gbmenutoolbutton.c:86
#: ../glade/gbwidgets/gbradiobutton.c:127
#: ../glade/gbwidgets/gbradiotoolbutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:89
#: ../glade/gbwidgets/gbtoggletoolbutton.c:91
#: ../glade/gbwidgets/gbtoolbutton.c:105
#, fuzzy
msgid "The stock button to use"
msgstr "要使用的库 Gnome 项目。"

#. For now we don't support editing the menuitem properties in the property
#. editor. The menu editor has to be used instead.
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/glade_menu_editor.c:747
#: ../glade/gnome/gnomehref.c:68
msgid "Label:"
msgstr "标签："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:121 ../glade/gbwidgets/gbcheckbutton.c:88
#: ../glade/gbwidgets/gbcheckmenuitem.c:72 ../glade/gbwidgets/gbentry.c:100
#: ../glade/gbwidgets/gbimagemenuitem.c:92 ../glade/gbwidgets/gblabel.c:168
#: ../glade/gbwidgets/gblistitem.c:73 ../glade/gbwidgets/gbmenuitem.c:87
#: ../glade/gbwidgets/gbmenutoolbutton.c:88
#: ../glade/gbwidgets/gbradiobutton.c:129
#: ../glade/gbwidgets/gbradiomenuitem.c:103
#: ../glade/gbwidgets/gbradiotoolbutton.c:136
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gbwidgets/gbtogglebutton.c:91
#: ../glade/gbwidgets/gbtoggletoolbutton.c:93
#: ../glade/gbwidgets/gbtoolbutton.c:107 ../glade/gnome-db/gnomedbeditor.c:64
msgid "The text to display"
msgstr "要显示的文件"

#: ../glade/gbwidgets/gbbutton.c:122 ../glade/gbwidgets/gbcheckbutton.c:89
#: ../glade/gbwidgets/gbimage.c:107 ../glade/gbwidgets/gbmenutoolbutton.c:89
#: ../glade/gbwidgets/gbradiobutton.c:130
#: ../glade/gbwidgets/gbradiotoolbutton.c:137
#: ../glade/gbwidgets/gbtogglebutton.c:92
#: ../glade/gbwidgets/gbtoggletoolbutton.c:94
#: ../glade/gbwidgets/gbtoolbutton.c:108 ../glade/gbwidgets/gbwindow.c:295
#: ../glade/glade_menu_editor.c:813
msgid "Icon:"
msgstr "图标："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:123 ../glade/gbwidgets/gbcheckbutton.c:90
#: ../glade/gbwidgets/gbimage.c:108 ../glade/gbwidgets/gbmenutoolbutton.c:90
#: ../glade/gbwidgets/gbradiobutton.c:131
#: ../glade/gbwidgets/gbradiotoolbutton.c:138
#: ../glade/gbwidgets/gbtogglebutton.c:93
#: ../glade/gbwidgets/gbtoggletoolbutton.c:95
#: ../glade/gbwidgets/gbtoolbutton.c:109
#, fuzzy
msgid "The icon to display"
msgstr "要显示的文件"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:125 ../glade/gbwidgets/gbcheckbutton.c:92
#: ../glade/gbwidgets/gbradiobutton.c:133
#: ../glade/gbwidgets/gbtogglebutton.c:95
msgid "Button Relief:"
msgstr "按钮释放："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:126 ../glade/gbwidgets/gbcheckbutton.c:93
#: ../glade/gbwidgets/gbradiobutton.c:134
#: ../glade/gbwidgets/gbtogglebutton.c:96
msgid "The relief style of the button"
msgstr "按钮的释放样式"

#: ../glade/gbwidgets/gbbutton.c:131
msgid "Response ID:"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:132
msgid ""
"The response code returned when the button is pressed. Select one of the "
"standard responses or enter a positive integer value"
msgstr ""

#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbcombobox.c:83
#: ../glade/gbwidgets/gbcomboboxentry.c:82
#: ../glade/gbwidgets/gbfontbutton.c:78 ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
msgid "Focus On Click:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:137 ../glade/gbwidgets/gbcheckbutton.c:102
#: ../glade/gbwidgets/gbcolorbutton.c:70 ../glade/gbwidgets/gbfontbutton.c:78
#: ../glade/gbwidgets/gbradiobutton.c:148
#: ../glade/gbwidgets/gbtogglebutton.c:103
#: ../glade/gnome/gnomecolorpicker.c:76 ../glade/gnome/gnomefontpicker.c:109
#: ../glade/gnome/gnomehref.c:70
#, fuzzy
msgid "If the button grabs focus when it is clicked"
msgstr "是否在单击任意按钮时关闭对话框"

#: ../glade/gbwidgets/gbbutton.c:1502
msgid "Remove Button Contents"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbbutton.c:1600
msgid "Button"
msgstr "按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:73
msgid "Heading:"
msgstr "标题："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:74
msgid "If the month and year should be shown at the top"
msgstr "月份和年份是否显示在顶部"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:75
msgid "Day Names:"
msgstr "日期名称："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:76
msgid "If the day names should be shown"
msgstr "是否显示日期名称"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:77
msgid "Fixed Month:"
msgstr "固定月份："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:78
msgid "If the month and year shouldn't be changeable"
msgstr "月份和年份是否应为不可更改"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:79
msgid "Week Numbers:"
msgstr "周数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:80
msgid "If the number of the week should be shown"
msgstr "是否显示周数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:81 ../glade/gnome/gnomedateedit.c:74
msgid "Monday First:"
msgstr "周一开始："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:82 ../glade/gnome/gnomedateedit.c:75
msgid "If the week should start on Monday"
msgstr "周是否从周一开始"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcalendar.c:266
msgid "Calendar"
msgstr "日历"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcellview.c:63 ../glade/gnome/gnomedruidpageedge.c:102
#: ../glade/gnome/gnomedruidpagestandard.c:91
msgid "Back. Color:"
msgstr "背景色："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcellview.c:64
#, fuzzy
msgid "The background color"
msgstr "背景色"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcellview.c:192
#, fuzzy
msgid "Cell View"
msgstr "文本可见："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckbutton.c:96
#: ../glade/gbwidgets/gbcheckmenuitem.c:73
#: ../glade/gbwidgets/gbradiobutton.c:137
#: ../glade/gbwidgets/gbradiomenuitem.c:104
#: ../glade/gbwidgets/gbradiotoolbutton.c:147
#: ../glade/gbwidgets/gbtogglebutton.c:99
#: ../glade/gbwidgets/gbtoggletoolbutton.c:97
msgid "Initially On:"
msgstr "初始打开："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckbutton.c:97
msgid "If the check button is initially on"
msgstr "选中按钮最初是否处于打开状态"

#: ../glade/gbwidgets/gbcheckbutton.c:98
#: ../glade/gbwidgets/gbradiobutton.c:139
#: ../glade/gbwidgets/gbtogglebutton.c:101
#, fuzzy
msgid "Inconsistent:"
msgstr "图标列表"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckbutton.c:99
#: ../glade/gbwidgets/gbradiobutton.c:140
#: ../glade/gbwidgets/gbtogglebutton.c:102
#, fuzzy
msgid "If the button is shown in an inconsistent state"
msgstr "是否显示日期及时间"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckbutton.c:100
#: ../glade/gbwidgets/gbradiobutton.c:141
msgid "Indicator:"
msgstr "指示器："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckbutton.c:101
#: ../glade/gbwidgets/gbradiobutton.c:142
msgid "If the indicator is always drawn"
msgstr "指示器是否始终处于关闭状态"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckbutton.c:211
msgid "Check Button"
msgstr "选中按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckmenuitem.c:74
msgid "If the check menu item is initially on"
msgstr "选中菜单项最初是否处于打开状态"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcheckmenuitem.c:203
msgid "Check Menu Item"
msgstr "选中菜单项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbclist.c:141
msgid "New columned list"
msgstr "新柱状列表"

# SUN NEW TRANSLATION
#. Columns label & entry
#: ../glade/gbwidgets/gbclist.c:152 ../glade/gbwidgets/gbctree.c:157
#: ../glade/gbwidgets/gbhbox.c:110 ../glade/gbwidgets/gbhbuttonbox.c:132
#: ../glade/gbwidgets/gbtable.c:165
msgid "Number of columns:"
msgstr "列数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbclist.c:242 ../glade/gbwidgets/gbctree.c:248
#: ../glade/gbwidgets/gbiconview.c:127 ../glade/gbwidgets/gblist.c:77
#: ../glade/gnome/gnomeiconlist.c:174
msgid "Select Mode:"
msgstr "选择模式："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbclist.c:243
msgid "The selection mode of the columned list"
msgstr "柱状列表的选择模式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbclist.c:245 ../glade/gbwidgets/gbctree.c:251
msgid "Show Titles:"
msgstr "显示标题："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbclist.c:246 ../glade/gbwidgets/gbctree.c:252
msgid "If the column titles are shown"
msgstr "是否显示列标题"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbclist.c:248
msgid "The type of shadow of the columned list's border"
msgstr "柱状列表边框的阴影类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbclist.c:594
msgid "Columned List"
msgstr "柱状列表"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorbutton.c:65 ../glade/gnome/gnomecolorpicker.c:70
msgid "Use Alpha:"
msgstr "使用 Alpha："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorbutton.c:66 ../glade/gnome/gnomecolorpicker.c:71
msgid "If the alpha channel should be used"
msgstr "是否使用 Alpha 通道"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorbutton.c:68
#: ../glade/gbwidgets/gbfilechooserbutton.c:85
#: ../glade/gbwidgets/gbfontbutton.c:68 ../glade/gbwidgets/gbwindow.c:242
#: ../glade/gnome/gnomecolorpicker.c:73 ../glade/gnome/gnomedruidpageedge.c:93
#: ../glade/gnome/gnomedruidpagestandard.c:85
#: ../glade/gnome/gnomefileentry.c:101 ../glade/gnome/gnomefontpicker.c:95
#: ../glade/gnome/gnomeiconentry.c:72 ../glade/gnome/gnomepixmapentry.c:82
msgid "Title:"
msgstr "标题："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorbutton.c:69 ../glade/gnome/gnomecolorpicker.c:74
msgid "The title of the color selection dialog"
msgstr "颜色选择对话框的标题"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorbutton.c:91
#: ../glade/gbwidgets/gbcolorbutton.c:119
#: ../glade/gbwidgets/gbcolorbutton.c:162
#, fuzzy
msgid "Pick a Color"
msgstr "选择颜色"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorbutton.c:211
#, fuzzy
msgid "Color Chooser Button"
msgstr "选中按钮"

#: ../glade/gbwidgets/gbcolorselection.c:62
msgid "Opacity Control:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorselection.c:63
#, fuzzy
msgid "If the opacity control is shown"
msgstr "是否显示日期名称"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorselection.c:64
#, fuzzy
msgid "Palette:"
msgstr "调色板"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorselection.c:65
#, fuzzy
msgid "If the palette is shown"
msgstr "是否显示比例尺的值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorselection.c:173
msgid "Color Selection"
msgstr "颜色选择"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorselectiondialog.c:70
msgid "Select Color"
msgstr "选择颜色"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcolorselectiondialog.c:315 ../glade/property.c:1275
msgid "Color Selection Dialog"
msgstr "“颜色选择”对话框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:105
msgid "Value In List:"
msgstr "列表中的值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:106
msgid "If the value must be in the list"
msgstr "值是否必须在列表中"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:107
msgid "OK If Empty:"
msgstr "如果为空可否接受："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:108
msgid "If an empty value is acceptable, when 'Value In List' is set"
msgstr "当设置“列表中的值”时，是否可以接受空值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:109
msgid "Case Sensitive:"
msgstr "区分大小写："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:110
msgid "If the searching is case sensitive"
msgstr "搜索是否区分大小写"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:111
msgid "Use Arrows:"
msgstr "使用箭头："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:112
msgid "If arrows can be used to change the value"
msgstr "箭头是否可以用于更改值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:113
msgid "Use Always:"
msgstr "始终使用："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:114
msgid "If arrows work even if the value is not in the list"
msgstr "即使当值不在列表中时，箭头是否依然起作用"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:115 ../glade/gbwidgets/gbcombobox.c:78
#: ../glade/gbwidgets/gbcomboboxentry.c:75
msgid "Items:"
msgstr "项："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:116 ../glade/gbwidgets/gbcombobox.c:79
#: ../glade/gbwidgets/gbcomboboxentry.c:76
msgid "The items in the combo list, one per line"
msgstr "组合列表中的项，每行一个"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombo.c:425 ../glade/gbwidgets/gbcombobox.c:289
msgid "Combo Box"
msgstr "组合框"

#: ../glade/gbwidgets/gbcombobox.c:81 ../glade/gbwidgets/gbcomboboxentry.c:78
msgid "Add Tearoffs:"
msgstr ""

#: ../glade/gbwidgets/gbcombobox.c:82 ../glade/gbwidgets/gbcomboboxentry.c:79
msgid "Whether dropdowns should have a tearoff menu item"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcombobox.c:84 ../glade/gbwidgets/gbcomboboxentry.c:83
#, fuzzy
msgid "Whether the combo box grabs focus when it is clicked"
msgstr "是否在单击任意按钮时关闭对话框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcomboboxentry.c:80 ../glade/gbwidgets/gbentry.c:102
#, fuzzy
msgid "Has Frame:"
msgstr "帧"

#: ../glade/gbwidgets/gbcomboboxentry.c:81
msgid "Whether the combo box draws a frame around the child"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcomboboxentry.c:302
#, fuzzy
msgid "Combo Box Entry"
msgstr "组合框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbctree.c:146
msgid "New columned tree"
msgstr "新柱状树"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbctree.c:249
msgid "The selection mode of the columned tree"
msgstr "柱状树的选择模式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbctree.c:254
msgid "The type of shadow of the columned tree's border"
msgstr "柱状树边框的阴影类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbctree.c:538
msgid "Columned Tree"
msgstr "柱状树"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:85 ../glade/gbwidgets/gbwindow.c:245
msgid "Type:"
msgstr "类型："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:85
msgid "The type of the curve"
msgstr "曲线类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "X Min:"
msgstr "X 最小值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:87 ../glade/gbwidgets/gbgammacurve.c:91
msgid "The minimum horizontal value"
msgstr "水平最小值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "X Max:"
msgstr "X 最大值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:88 ../glade/gbwidgets/gbgammacurve.c:92
msgid "The maximum horizontal value"
msgstr "水平最大值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "Y Min:"
msgstr "Y 最小值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:89 ../glade/gbwidgets/gbgammacurve.c:93
msgid "The minimum vertical value"
msgstr "垂直最小值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "Y Max:"
msgstr "Y 最大值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:90 ../glade/gbwidgets/gbgammacurve.c:94
msgid "The maximum vertical value"
msgstr "垂直最大值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcurve.c:250
msgid "Curve"
msgstr "曲线"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:154
msgid "Creation Function:"
msgstr "创建函数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:155
msgid "The function which creates the widget"
msgstr "创建构件的函数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:157
msgid "String1:"
msgstr "String1："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:158
msgid "The first string argument to pass to the function"
msgstr "传递给函数的第一个字符串参数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:159
msgid "String2:"
msgstr "String2："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:160
msgid "The second string argument to pass to the function"
msgstr "传递给函数的第二个字符串参数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:161
msgid "Int1:"
msgstr "Int1："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:162
msgid "The first integer argument to pass to the function"
msgstr "传递给函数的第一个整型参数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:163
msgid "Int2:"
msgstr "Int2："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:164
msgid "The second integer argument to pass to the function"
msgstr "传递给函数的第二个整型参数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbcustom.c:380
msgid "Custom Widget"
msgstr "定制构件"

#: ../glade/gbwidgets/gbdialog.c:292
#, fuzzy
msgid "New dialog"
msgstr "新邮件"

#: ../glade/gbwidgets/gbdialog.c:304
#, fuzzy
msgid "Cancel, OK"
msgstr "取消"

#: ../glade/gbwidgets/gbdialog.c:313 ../glade/glade.c:367
#: ../glade/glade_project_window.c:1316 ../glade/property.c:5156
msgid "OK"
msgstr "确定"

#: ../glade/gbwidgets/gbdialog.c:322
msgid "Cancel, Apply, OK"
msgstr ""

#: ../glade/gbwidgets/gbdialog.c:331
msgid "Close"
msgstr "关闭"

#: ../glade/gbwidgets/gbdialog.c:340
msgid "_Standard Button Layout:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbdialog.c:349
#, fuzzy
msgid "_Number of Buttons:"
msgstr "行数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbdialog.c:366
#, fuzzy
msgid "Show Help Button"
msgstr "旋转按钮"

#: ../glade/gbwidgets/gbdialog.c:397
#, fuzzy
msgid "Has Separator:"
msgstr "分隔符"

#: ../glade/gbwidgets/gbdialog.c:398
msgid "If the dialog has a horizontal separator above the buttons"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbdialog.c:605
msgid "Dialog"
msgstr "对话框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbdrawingarea.c:146
msgid "Drawing Area"
msgstr "绘制区"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:115
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "Editable:"
msgstr "可编辑："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:94 ../glade/gbwidgets/gbtextview.c:116
#: ../glade/gnome-db/gnomedbeditor.c:62
msgid "If the text can be edited"
msgstr "文本是否可以编辑"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:95
msgid "Text Visible:"
msgstr "文本可见："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:96
msgid ""
"If the text entered by the user will be shown. When turned off, the text "
"typed in is displayed as asterix characters, which is useful for entering "
"passwords"
msgstr ""
"是否显示用户输入的文本。当关闭时，键入的文本将显示为星号，这可用于输入密码"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:97
msgid "Max Length:"
msgstr "最大长度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:98
msgid "The maximum length of the text"
msgstr "文本的最大长度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:100 ../glade/gbwidgets/gbprogressbar.c:143
#: ../glade/gbwidgets/gbtextview.c:124 ../glade/gnome-db/gnomedbeditor.c:64
#: ../glade/gnome-db/gnomedbgraybar.c:59
#: ../glade/gnome/gnomedruidpageedge.c:95 ../glade/property.c:926
msgid "Text:"
msgstr "文本："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:102
#, fuzzy
msgid "If the entry has a frame around it"
msgstr "应用程序栏是否有进度指示器"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:103
#, fuzzy
msgid "Invisible Char:"
msgstr "可见："

#: ../glade/gbwidgets/gbentry.c:103
msgid ""
"The character to use if the text should not visible, e.g. when entering "
"passwords"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:104
#, fuzzy
msgid "Activates Default:"
msgstr "有默认值："

#: ../glade/gbwidgets/gbentry.c:104
msgid "If the default widget in the window is activated when Enter is pressed"
msgstr ""

#: ../glade/gbwidgets/gbentry.c:105
msgid "Width In Chars:"
msgstr ""

#: ../glade/gbwidgets/gbentry.c:105
msgid "The number of characters to leave space for in the entry"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbentry.c:318
msgid "Text Entry"
msgstr "文本输入"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbeventbox.c:65
#, fuzzy
msgid "Visible Window:"
msgstr "可见："

#: ../glade/gbwidgets/gbeventbox.c:65
msgid "If the event box uses a visible window"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbeventbox.c:66
#, fuzzy
msgid "Above Child:"
msgstr "遵从子项："

#: ../glade/gbwidgets/gbeventbox.c:66
msgid "If the event box window is above the child widget's window"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbeventbox.c:167
msgid "Event Box"
msgstr "事件框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbexpander.c:54
#, fuzzy
msgid "Initially Expanded:"
msgstr "初始打开："

#: ../glade/gbwidgets/gbexpander.c:55
msgid "Whether the expander is initially opened to reveal the child widget"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbexpander.c:57 ../glade/gbwidgets/gbhbox.c:175
#: ../glade/gbwidgets/gbhbuttonbox.c:199 ../glade/gbwidgets/gbvbox.c:160
msgid "Spacing:"
msgstr "间距："

#: ../glade/gbwidgets/gbexpander.c:58
#, fuzzy
msgid "Space to put between the label and the child"
msgstr "在文字和图标直接的间距(像素数)"

#: ../glade/gbwidgets/gbexpander.c:105 ../glade/gbwidgets/gbframe.c:225
#, fuzzy
msgid "Add Label Widget"
msgstr "添加对齐"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbexpander.c:228
#, fuzzy
msgid "Expander"
msgstr "扩展："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserbutton.c:86
#, fuzzy
msgid "The window title of the file chooser dialog"
msgstr "“文件选择”对话框的标题"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserbutton.c:87
#: ../glade/gbwidgets/gbfilechooserwidget.c:86
#: ../glade/gbwidgets/gbfilechooserdialog.c:156
#: ../glade/gnome/gnomefileentry.c:109
#, fuzzy
msgid "Action:"
msgstr "方向："

#: ../glade/gbwidgets/gbfilechooserbutton.c:88
#: ../glade/gbwidgets/gbfilechooserwidget.c:87
#: ../glade/gbwidgets/gbfilechooserdialog.c:157
#: ../glade/gnome/gnomefileentry.c:110
msgid "The type of file operation being performed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:90
#: ../glade/gbwidgets/gbfilechooserwidget.c:89
#: ../glade/gbwidgets/gbfilechooserdialog.c:159
msgid "Local Only:"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:91
#: ../glade/gbwidgets/gbfilechooserwidget.c:90
#: ../glade/gbwidgets/gbfilechooserdialog.c:160
msgid "Whether the selected files should be limited to local files"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserbutton.c:92
#: ../glade/gbwidgets/gbfilechooserwidget.c:93
#: ../glade/gbwidgets/gbfilechooserdialog.c:163
#, fuzzy
msgid "Show Hidden:"
msgstr "显示时间："

#: ../glade/gbwidgets/gbfilechooserbutton.c:93
#: ../glade/gbwidgets/gbfilechooserwidget.c:94
#: ../glade/gbwidgets/gbfilechooserdialog.c:164
msgid "Whether the hidden files and folders should be displayed"
msgstr ""

#: ../glade/gbwidgets/gbfilechooserbutton.c:94
#: ../glade/gbwidgets/gblabel.c:200
msgid "Width in Chars:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserbutton.c:95
#, fuzzy
msgid "The width of the button in characters"
msgstr "布局区域的宽度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserbutton.c:283
#, fuzzy
msgid "File Chooser Button"
msgstr "选中按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserwidget.c:91
#: ../glade/gbwidgets/gbfilechooserdialog.c:161
#, fuzzy
msgid "Select Multiple:"
msgstr "选择文件"

#: ../glade/gbwidgets/gbfilechooserwidget.c:92
#: ../glade/gbwidgets/gbfilechooserdialog.c:162
msgid "Whether to allow multiple files to be selected"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserwidget.c:260
#, fuzzy
msgid "File Chooser"
msgstr "标题颜色："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfilechooserdialog.c:421
#, fuzzy
msgid "File Chooser Dialog"
msgstr "“文件选择”对话框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfileselection.c:71 ../glade/property.c:1365
msgid "Select File"
msgstr "选择文件"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfileselection.c:113
msgid "File Ops.:"
msgstr "文件操作："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfileselection.c:114
msgid "If the file operation buttons are shown"
msgstr "是否显示文件操作按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfileselection.c:292
msgid "File Selection Dialog"
msgstr "“文件选择”对话框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfixed.c:139 ../glade/gbwidgets/gblayout.c:221
msgid "X:"
msgstr "X："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfixed.c:140
#, fuzzy
msgid "The X coordinate of the widget in the GtkFixed"
msgstr "表中构件的上边缘"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfixed.c:142 ../glade/gbwidgets/gblayout.c:224
msgid "Y:"
msgstr "Y："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfixed.c:143
#, fuzzy
msgid "The Y coordinate of the widget in the GtkFixed"
msgstr "表中构件的上边缘"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfixed.c:228
msgid "Fixed Positions"
msgstr "固定位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:69 ../glade/gnome/gnomefontpicker.c:96
msgid "The title of the font selection dialog"
msgstr "“字体选择”对话框的标题"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:70
#, fuzzy
msgid "Show Style:"
msgstr "显示标题："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:71
#, fuzzy
msgid "If the font style is shown as part of the font information"
msgstr "是否将字体大小作为字体信息的一部分显示"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:72 ../glade/gnome/gnomefontpicker.c:102
msgid "Show Size:"
msgstr "显示大小："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:73 ../glade/gnome/gnomefontpicker.c:103
msgid "If the font size is shown as part of the font information"
msgstr "是否将字体大小作为字体信息的一部分显示"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:74 ../glade/gnome/gnomefontpicker.c:104
msgid "Use Font:"
msgstr "使用字体："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:75 ../glade/gnome/gnomefontpicker.c:105
msgid "If the selected font is used when displaying the font information"
msgstr "在显示字体信息时是否使用选定字体"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:76 ../glade/gnome/gnomefontpicker.c:106
msgid "Use Size:"
msgstr "使用大小："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:77
#, fuzzy
msgid "if the selected font size is used when displaying the font information"
msgstr "在显示字体信息时是否使用选定字体"

# SUN CHANGED MESSAGE
#: ../glade/gbwidgets/gbfontbutton.c:97 ../glade/gbwidgets/gbfontbutton.c:133
#: ../glade/gbwidgets/gbfontbutton.c:191 ../glade/gnome/gnomefontpicker.c:128
#: ../glade/gnome/gnomefontpicker.c:199 ../glade/gnome/gnomefontpicker.c:301
msgid "Pick a Font"
msgstr "选取字体"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontbutton.c:268
#, fuzzy
msgid "Font Chooser Button"
msgstr "选中按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontselection.c:64 ../glade/gnome/gnomefontpicker.c:97
msgid "Preview Text:"
msgstr "预览文本："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontselection.c:64
#, fuzzy
msgid "The preview text to display"
msgstr "要显示的文件"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontselection.c:170
msgid "Font Selection"
msgstr "字体选择"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontselectiondialog.c:69
msgid "Select Font"
msgstr "选择字体"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbfontselectiondialog.c:300
msgid "Font Selection Dialog"
msgstr "“字体选择”对话框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbframe.c:360
msgid "Frame"
msgstr "帧"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "Initial Type:"
msgstr "初始类型："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbgammacurve.c:88
msgid "The initial type of the curve"
msgstr "曲线的初始类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbgammacurve.c:256
msgid "Gamma Curve"
msgstr "Gamma 曲线"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhandlebox.c:110
msgid "The type of shadow around the handle box"
msgstr "手柄框周围的阴影类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhandlebox.c:113
msgid "Handle Pos:"
msgstr "手柄位置："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhandlebox.c:114
msgid "The position of the handle"
msgstr "手柄的位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhandlebox.c:116
msgid "Snap Edge:"
msgstr "对齐边缘："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhandlebox.c:117
msgid "The edge of the handle box which snaps into position"
msgstr "对齐位置的手柄框边缘"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhandlebox.c:304
msgid "Handle Box"
msgstr "手柄框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:99
msgid "New horizontal box"
msgstr "新水平框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbhbuttonbox.c:194
#: ../glade/gbwidgets/gbtoolbar.c:267 ../glade/gbwidgets/gbvbox.c:156
msgid "Size:"
msgstr "大小："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:171 ../glade/gbwidgets/gbvbox.c:156
msgid "The number of widgets in the box"
msgstr "框中的构件数量"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:173 ../glade/gbwidgets/gbtable.c:243
#: ../glade/gbwidgets/gbtoolbar.c:426 ../glade/gbwidgets/gbvbox.c:158
msgid "Homogeneous:"
msgstr "单一："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:174 ../glade/gbwidgets/gbvbox.c:159
msgid "If the children should be the same size"
msgstr "子项的大小是否应相同"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:175 ../glade/gbwidgets/gbvbox.c:160
msgid "The space between each child"
msgstr "每个子项之间的间隔"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:312
msgid "Can't delete any children."
msgstr "无法删除任何子项。"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:327 ../glade/gbwidgets/gbhpaned.c:73
#: ../glade/gbwidgets/gbhruler.c:89 ../glade/gbwidgets/gbnotebook.c:669
#: ../glade/gbwidgets/gbvpaned.c:69 ../glade/gbwidgets/gbvruler.c:89
#: ../glade/gbwidgets/gbwindow.c:254
msgid "Position:"
msgstr "位置："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:328
msgid "The widget's position relative to its siblings"
msgstr "构件相对于其同级的位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:330
msgid "Padding:"
msgstr "填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:331
msgid "The widget's padding"
msgstr "构件的填充"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:333 ../glade/gbwidgets/gbnotebook.c:672
#: ../glade/gbwidgets/gbpreview.c:65 ../glade/gbwidgets/gbtoolbar.c:424
msgid "Expand:"
msgstr "扩展："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:334 ../glade/gbwidgets/gbtoolbar.c:425
msgid "Set True to let the widget expand"
msgstr "设置为 True 使构件可以扩展"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:335 ../glade/gbwidgets/gbnotebook.c:674
msgid "Fill:"
msgstr "填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:336
msgid "Set True to let the widget fill its allocated area"
msgstr "设置为 True 使构件可以填充它的分配区域"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:337 ../glade/gbwidgets/gbnotebook.c:676
msgid "Pack Start:"
msgstr "打包开始位置："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:338
msgid "Set True to pack the widget at the start of the box"
msgstr "设置为 True 在框的开始处对构件打包"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:455
msgid "Insert Before"
msgstr "在以下对象之前插入"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:461
msgid "Insert After"
msgstr "在以下对象之后插入"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbox.c:571
msgid "Horizontal Box"
msgstr "水平框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbuttonbox.c:120
msgid "New horizontal button box"
msgstr "新水平按钮框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbuttonbox.c:194
msgid "The number of buttons"
msgstr "按钮数量"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbuttonbox.c:196
msgid "Layout:"
msgstr "布局："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbuttonbox.c:197
msgid "The layout style of the buttons"
msgstr "按钮的布局样式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbuttonbox.c:199
msgid "The space between the buttons"
msgstr "按钮之间的间隔"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhbuttonbox.c:414
msgid "Horizontal Button Box"
msgstr "水平按钮框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhpaned.c:74 ../glade/gbwidgets/gbvpaned.c:70
msgid "The position of the divider"
msgstr "分隔符的位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhpaned.c:186 ../glade/gbwidgets/gbwindow.c:283
msgid "Shrink:"
msgstr "收缩："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhpaned.c:187
msgid "Set True to let the widget shrink"
msgstr "设置为 True 使构件可以收缩"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhpaned.c:188
msgid "Resize:"
msgstr "改变大小："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhpaned.c:189
msgid "Set True to let the widget resize"
msgstr "设置为 True 使构件可以改变大小"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhpaned.c:315
msgid "Horizontal Panes"
msgstr "水平窗格"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:82 ../glade/gbwidgets/gbvruler.c:82
msgid "Metric:"
msgstr "度量："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:83 ../glade/gbwidgets/gbvruler.c:83
msgid "The units of the ruler"
msgstr "标尺的单位"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:85 ../glade/gbwidgets/gbvruler.c:85
msgid "Lower Value:"
msgstr "较低值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:86 ../glade/gbwidgets/gbvruler.c:86
#: ../glade/gbwidgets/gbvruler.c:88
msgid "The low value of the ruler"
msgstr "标尺的低值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:87 ../glade/gbwidgets/gbvruler.c:87
msgid "Upper Value:"
msgstr "较高值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:88
msgid "The high value of the ruler"
msgstr "标尺的高值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:90 ../glade/gbwidgets/gbvruler.c:90
msgid "The current position on the ruler"
msgstr "标尺上的当前位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:91 ../glade/gbwidgets/gbvruler.c:91
#: ../glade/property.c:4827
msgid "Max:"
msgstr "最大值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:92 ../glade/gbwidgets/gbvruler.c:92
msgid "The maximum value of the ruler"
msgstr "标尺的最大值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhruler.c:247
msgid "Horizontal Ruler"
msgstr "水平标尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "Show Value:"
msgstr "显示值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:107 ../glade/gbwidgets/gbvscale.c:108
msgid "If the scale's value is shown"
msgstr "是否显示比例尺的值"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbspinbutton.c:93
#: ../glade/gbwidgets/gbvscale.c:109
msgid "Digits:"
msgstr "数字："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:108 ../glade/gbwidgets/gbvscale.c:109
msgid "The number of digits to show"
msgstr "要显示的数字位数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:110 ../glade/gbwidgets/gbvscale.c:111
msgid "Value Pos:"
msgstr "值位置："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:111 ../glade/gbwidgets/gbvscale.c:112
msgid "The position of the value"
msgstr "值的位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:113 ../glade/gbwidgets/gbhscrollbar.c:87
#: ../glade/gbwidgets/gbvscale.c:114 ../glade/gbwidgets/gbvscrollbar.c:87
msgid "Policy:"
msgstr "策略："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:114 ../glade/gbwidgets/gbvscale.c:115
msgid "The update policy of the scale"
msgstr "比例尺的更新策略"

#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
#, fuzzy
msgid "Inverted:"
msgstr "转换"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:116 ../glade/gbwidgets/gbhscrollbar.c:90
#: ../glade/gbwidgets/gbvscale.c:117 ../glade/gbwidgets/gbvscrollbar.c:90
#, fuzzy
msgid "If the range values are inverted"
msgstr "值是否必须在列表中"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscale.c:319
msgid "Horizontal Scale"
msgstr "水平比例尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscrollbar.c:88 ../glade/gbwidgets/gbvscrollbar.c:88
msgid "The update policy of the scrollbar"
msgstr "滚动条的更新策略"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhscrollbar.c:237
msgid "Horizontal Scrollbar"
msgstr "水平滚动条"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbhseparator.c:144
msgid "Horizonal Separator"
msgstr "水平分隔符"

#: ../glade/gbwidgets/gbiconview.c:106
#, fuzzy, c-format
msgid "Icon %i"
msgstr "图标列表"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbiconview.c:128
#, fuzzy
msgid "The selection mode of the icon view"
msgstr "柱状树的选择模式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbiconview.c:130 ../glade/gbwidgets/gbprogressbar.c:134
#: ../glade/gbwidgets/gbtoolbar.c:270 ../glade/gnome/bonobodockitem.c:179
msgid "Orientation:"
msgstr "方向："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbiconview.c:131
#, fuzzy
msgid "The orientation of the icons"
msgstr "进度栏内容的方向"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbiconview.c:287
#, fuzzy
msgid "Icon View"
msgstr "显示大小："

#: ../glade/gbwidgets/gbimage.c:110 ../glade/gbwidgets/gbwindow.c:299
#, fuzzy
msgid "Named Icon:"
msgstr "图标："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimage.c:111 ../glade/gbwidgets/gbwindow.c:300
#, fuzzy
msgid "The named icon to use"
msgstr "要使用的库 Gnome 项目。"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimage.c:112
#, fuzzy
msgid "Icon Size:"
msgstr "显示大小："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimage.c:113
#, fuzzy
msgid "The stock icon size"
msgstr "要使用的库 Gnome 项目。"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimage.c:115
#, fuzzy
msgid "Pixel Size:"
msgstr "页面大小："

#: ../glade/gbwidgets/gbimage.c:116
msgid ""
"The size of the named icon in pixels, or -1 to use the Icon Size property"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimage.c:120
msgid "The horizontal alignment"
msgstr "水平对齐"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimage.c:123
msgid "The vertical alignment"
msgstr "垂直对齐"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimage.c:648
msgid "Image"
msgstr "图像"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimagemenuitem.c:255
#: ../glade/gbwidgets/gbmenuitem.c:228
msgid "Invalid stock menu item"
msgstr "无效的库菜单项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbimagemenuitem.c:471
msgid "Menu item with a pixmap"
msgstr "带像素图的菜单项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbinputdialog.c:256
msgid "Input Dialog"
msgstr "输入对话框"

#: ../glade/gbwidgets/gblabel.c:169
#, fuzzy
msgid "Use Underline:"
msgstr "下划线"

#: ../glade/gbwidgets/gblabel.c:170
msgid "If the text includes an underlined access key"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:171
#, fuzzy
msgid "Use Markup:"
msgstr "使用堆："

#: ../glade/gbwidgets/gblabel.c:172
msgid "If the text includes pango markup"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:173
msgid "Justify:"
msgstr "调整："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:174
msgid "The justification of the lines of the label"
msgstr "调整标签中的各行"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:176
msgid "Wrap Text:"
msgstr "回绕文本："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:177
msgid "If the text is wrapped to fit within the width of the label"
msgstr "是否回绕文本以适合标签的宽度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:178
#, fuzzy
msgid "Selectable:"
msgstr "选定"

#: ../glade/gbwidgets/gblabel.c:179
#, fuzzy
msgid "If the label text can be selected with the mouse"
msgstr "图标文字是否可编辑"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:181
msgid "The horizontal alignment of the entire label"
msgstr "水平对齐整个标签"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:184
msgid "The vertical alignment of the entire label"
msgstr "垂直对齐整个标签"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:190
msgid "Focus Target:"
msgstr "聚焦目标："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:191
#, fuzzy
msgid ""
"The widget to set the keyboard focus to when the underlined access key is "
"used"
msgstr "当使用带下划线的加速键时，要在其上设置键盘焦点的构件"

# SUN NEW TRANSLATION
#. gtk_combo_set_value_in_list (GTK_COMBO (combo), TRUE, TRUE);
#: ../glade/gbwidgets/gblabel.c:197 ../glade/gbwidgets/gbprogressbar.c:146
#, fuzzy
msgid "Ellipsize:"
msgstr "独占："

#: ../glade/gbwidgets/gblabel.c:198 ../glade/gbwidgets/gbprogressbar.c:147
msgid "How to ellipsize the string"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:201
#, fuzzy
msgid "The width of the label in characters"
msgstr "布局区域的宽度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:203
#, fuzzy
msgid "Single Line Mode:"
msgstr "选择模式："

#: ../glade/gbwidgets/gblabel.c:204
msgid "If the label is only given enough height for a single line"
msgstr ""

#: ../glade/gbwidgets/gblabel.c:205
msgid "Angle:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:206
#, fuzzy
msgid "The angle of the label text"
msgstr "文本的最大长度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblabel.c:332 ../glade/gbwidgets/gblabel.c:347
#: ../glade/gbwidgets/gblabel.c:614
msgid "Auto"
msgstr "自动"

#: ../glade/gbwidgets/gblabel.c:870 ../glade/glade_menu_editor.c:410
msgid "Label"
msgstr "标签"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblayout.c:96
msgid "Area Width:"
msgstr "区域宽度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblayout.c:97
msgid "The width of the layout area"
msgstr "布局区域的宽度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblayout.c:99
msgid "Area Height:"
msgstr "区域高度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblayout.c:100
msgid "The height of the layout area"
msgstr "布局区域的高度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblayout.c:222
#, fuzzy
msgid "The X coordinate of the widget in the GtkLayout"
msgstr "表中构件的上边缘"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblayout.c:225
#, fuzzy
msgid "The Y coordinate of the widget in the GtkLayout"
msgstr "表中构件的上边缘"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblayout.c:380
msgid "Layout"
msgstr "布局"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblist.c:78
msgid "The selection mode of the list"
msgstr "列表的选择模式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblist.c:192
msgid "List"
msgstr "列表"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gblistitem.c:171
msgid "List Item"
msgstr "列表项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenu.c:198
msgid "Popup Menu"
msgstr "弹出菜单"

# SUN NEW TRANSLATION
#. FIXME: I'm not sure if we should translate the non-stock labels or not.
#: ../glade/gbwidgets/gbmenubar.c:190
#, fuzzy
msgid "_File"
msgstr "文件："

# SUN NEW TRANSLATION
#. Create Edit menu
#: ../glade/gbwidgets/gbmenubar.c:198 ../glade/glade_project_window.c:691
#, fuzzy
msgid "_Edit"
msgstr "编辑..."

#. Create View menu
#: ../glade/gbwidgets/gbmenubar.c:204 ../glade/glade_project_window.c:720
#, fuzzy
msgid "_View"
msgstr "新建(_N)"

#. Create Help menu
#: ../glade/gbwidgets/gbmenubar.c:206 ../glade/glade_project_window.c:833
#, fuzzy
msgid "_Help"
msgstr "帮助"

#: ../glade/gbwidgets/gbmenubar.c:207
#, fuzzy
msgid "_About"
msgstr "关于"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenubar.c:268 ../glade/gbwidgets/gbmenubar.c:346
#: ../glade/gbwidgets/gboptionmenu.c:139
msgid "Edit Menus..."
msgstr "编辑菜单..."

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenubar.c:442
msgid "Menu Bar"
msgstr "菜单栏"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenuitem.c:379
msgid "Menu Item"
msgstr "菜单项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenutoolbutton.c:92
#: ../glade/gbwidgets/gbradiotoolbutton.c:150
#: ../glade/gbwidgets/gbseparatortoolitem.c:67
#: ../glade/gbwidgets/gbtoggletoolbutton.c:99
#: ../glade/gbwidgets/gbtoolbutton.c:111 ../glade/gbwidgets/gbtoolitem.c:65
#, fuzzy
msgid "Show Horizontal:"
msgstr "从不水平："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenutoolbutton.c:93
#: ../glade/gbwidgets/gbradiotoolbutton.c:151
#: ../glade/gbwidgets/gbseparatortoolitem.c:68
#: ../glade/gbwidgets/gbtoggletoolbutton.c:100
#: ../glade/gbwidgets/gbtoolbutton.c:112 ../glade/gbwidgets/gbtoolitem.c:66
#, fuzzy
msgid "If the item is visible when the toolbar is horizontal"
msgstr "是否从不允许停靠项处于水平状态"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenutoolbutton.c:94
#: ../glade/gbwidgets/gbradiotoolbutton.c:152
#: ../glade/gbwidgets/gbseparatortoolitem.c:69
#: ../glade/gbwidgets/gbtoggletoolbutton.c:101
#: ../glade/gbwidgets/gbtoolbutton.c:113 ../glade/gbwidgets/gbtoolitem.c:67
#, fuzzy
msgid "Show Vertical:"
msgstr "显示值："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenutoolbutton.c:95
#: ../glade/gbwidgets/gbradiotoolbutton.c:153
#: ../glade/gbwidgets/gbseparatortoolitem.c:70
#: ../glade/gbwidgets/gbtoggletoolbutton.c:102
#: ../glade/gbwidgets/gbtoolbutton.c:114 ../glade/gbwidgets/gbtoolitem.c:68
#, fuzzy
msgid "If the item is visible when the toolbar is vertical"
msgstr "是否从不允许停靠项处于垂直状态"

#: ../glade/gbwidgets/gbmenutoolbutton.c:96
#: ../glade/gbwidgets/gbradiotoolbutton.c:154
#: ../glade/gbwidgets/gbtoggletoolbutton.c:103
#: ../glade/gbwidgets/gbtoolbutton.c:115 ../glade/gbwidgets/gbtoolitem.c:69
msgid "Is Important:"
msgstr ""

#: ../glade/gbwidgets/gbmenutoolbutton.c:97
#: ../glade/gbwidgets/gbradiotoolbutton.c:155
#: ../glade/gbwidgets/gbtoggletoolbutton.c:104
#: ../glade/gbwidgets/gbtoolbutton.c:116 ../glade/gbwidgets/gbtoolitem.c:70
msgid ""
"If the item's text should be shown when the toolbar's mode is "
"GTK_TOOLBAR_BOTH_HORIZ"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbmenutoolbutton.c:255
#, fuzzy
msgid "Toolbar Button with Menu"
msgstr "切换按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:191
msgid "New notebook"
msgstr "新建笔记本"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:202 ../glade/gnome/gnomepropertybox.c:124
msgid "Number of pages:"
msgstr "页数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:274
msgid "Show Tabs:"
msgstr "显示缩进键："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:274
msgid "If the notebook tabs are shown"
msgstr "是否显示笔记本缩进键"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:275
msgid "Show Border:"
msgstr "显示边框："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:276
msgid "If the notebook border is shown, when the tabs are not shown"
msgstr "在不显示缩进键时，是否显示笔记本边框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:277
msgid "Tab Pos:"
msgstr "缩进键位置："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:278
msgid "The position of the notebook tabs"
msgstr "笔记本缩进键的位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:280
msgid "Scrollable:"
msgstr "可滚动："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:281
msgid "If the notebook tabs are scrollable"
msgstr "笔记本缩进键是否可滚动"

# SUN NEW TRANSLATION
#. These seem to be deprecated.
#: ../glade/gbwidgets/gbnotebook.c:284
msgid "Tab Horz. Border:"
msgstr "缩进键水平边框："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:285
msgid "The size of the notebook tabs' horizontal border"
msgstr "笔记本缩进键的水平边框的大小"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:287
msgid "Tab Vert. Border:"
msgstr "缩进键垂直边框："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:288
msgid "The size of the notebook tabs' vertical border"
msgstr "笔记本缩进键的垂直边框的大小"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:291
msgid "Show Popup:"
msgstr "显示弹出菜单："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:291
msgid "If the popup menu is enabled"
msgstr "是否启用弹出菜单"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:292 ../glade/gnome/gnomedruid.c:102
msgid "Number of Pages:"
msgstr "页数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:293
msgid "The number of notebook pages"
msgstr "笔记本页数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:540
msgid "Previous Page"
msgstr "上一页"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:548
msgid "Next Page"
msgstr "下一页"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:556
msgid "Delete Page"
msgstr "删除页"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:562
msgid "Switch Next"
msgstr "切换下一个"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:570
msgid "Switch Previous"
msgstr "切换上一个"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:578 ../glade/gnome/gnomedruid.c:298
msgid "Insert Page After"
msgstr "在以下对象之后插入页面"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:586 ../glade/gnome/gnomedruid.c:285
msgid "Insert Page Before"
msgstr "在以下对象之前插入页面"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:670
#, fuzzy
msgid "The page's position in the list of pages"
msgstr "标尺上的当前位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:673
#, fuzzy
msgid "Set True to let the tab expand"
msgstr "设置为 True 使构件可以扩展"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:675
#, fuzzy
msgid "Set True to let the tab fill its allocated area"
msgstr "设置为 True 使构件可以填充它的分配区域"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:677
#, fuzzy
msgid "Set True to pack the tab at the start of the notebook"
msgstr "设置为 True 在框的开始处对构件打包"

#: ../glade/gbwidgets/gbnotebook.c:678
#, fuzzy
msgid "Menu Label:"
msgstr "标签："

#: ../glade/gbwidgets/gbnotebook.c:679
#, fuzzy
msgid "The text to display in the popup menu"
msgstr "显示在按钮上的文字"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbnotebook.c:937
msgid "Notebook"
msgstr "笔记本"

#: ../glade/gbwidgets/gboptionmenu.c:230
#, c-format
msgid "Cannot add a %s to a GtkOptionMenu."
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gboptionmenu.c:270
msgid "Option Menu"
msgstr "选项菜单"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbpreview.c:63
msgid "Color:"
msgstr "颜色："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbpreview.c:64
msgid "If the preview is color or grayscale"
msgstr "预览是彩色图像还是灰度级图像"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbpreview.c:66
msgid "If the preview expands to fill its allocated area"
msgstr "是否扩展预览以填充它的分配区域"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbpreview.c:162
msgid "Preview"
msgstr "预览"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbprogressbar.c:135
msgid "The orientation of the progress bar's contents"
msgstr "进度栏内容的方向"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbprogressbar.c:137
#, fuzzy
msgid "Fraction:"
msgstr "方向："

#: ../glade/gbwidgets/gbprogressbar.c:138
msgid "The fraction of work that has been completed"
msgstr ""

#: ../glade/gbwidgets/gbprogressbar.c:140
msgid "Pulse Step:"
msgstr ""

#: ../glade/gbwidgets/gbprogressbar.c:141
msgid ""
"The fraction of the progress bar length to move the bouncing block when "
"pulsed"
msgstr ""

#: ../glade/gbwidgets/gbprogressbar.c:144
#, fuzzy
msgid "The text to display over the progress bar"
msgstr "显示在按钮上的文字"

# SUN NEW TRANSLATION
#. ShowText is implicit now, if the Text property is set to anything.
#: ../glade/gbwidgets/gbprogressbar.c:152
msgid "Show Text:"
msgstr "显示文本："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbprogressbar.c:153
msgid "If the text should be shown in the progress bar"
msgstr "是否在进度栏中显示文本"

# SUN NEW TRANSLATION
#. ActivityMode is deprecated and implicit now. The app just calls
#. gtk_progress_bar_pulse() and it automatically goes into activity mode.
#: ../glade/gbwidgets/gbprogressbar.c:157
msgid "Activity Mode:"
msgstr "活动模式："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbprogressbar.c:158
msgid "If the progress bar should act like the front of Kit's car"
msgstr "进度栏是否应像 Kit 的汽车的前部"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbprogressbar.c:163
msgid "The horizontal alignment of the text"
msgstr "水平对齐文本"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbprogressbar.c:166
msgid "The vertical alignment of the text"
msgstr "垂直对齐文本"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbprogressbar.c:421
msgid "Progress Bar"
msgstr "进度栏"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiobutton.c:138
#: ../glade/gbwidgets/gbradiotoolbutton.c:148
msgid "If the radio button is initially on"
msgstr "单选按钮初始是否打开"

#: ../glade/gbwidgets/gbradiobutton.c:143
#: ../glade/gbwidgets/gbradiomenuitem.c:106
#: ../glade/gbwidgets/gbradiotoolbutton.c:141
#: ../glade/glade_menu_editor.c:1038
msgid "Group:"
msgstr "编组："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiobutton.c:144
msgid ""
"The radio button group (the default is all radio buttons with the same "
"parent)"
msgstr "单选按钮组（默认情况下是具有相同父级的所有单选按钮）"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiobutton.c:189
#: ../glade/gbwidgets/gbradiobutton.c:350
#: ../glade/gbwidgets/gbradiotoolbutton.c:233
#: ../glade/gbwidgets/gbradiotoolbutton.c:322
#, fuzzy
msgid "New Group"
msgstr "新建组："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiobutton.c:463
msgid "Radio Button"
msgstr "单选按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiomenuitem.c:105
msgid "If the radio menu item is initially on"
msgstr "单选菜单项初始是否打开"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiomenuitem.c:107
msgid ""
"The radio menu item group (the default is all radio menu items with the same "
"parent)"
msgstr "单选菜单项组（默认情况下具有相同父级的所有单选菜单项）"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiomenuitem.c:386
msgid "Radio Menu Item"
msgstr "单选菜单项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiotoolbutton.c:142
#, fuzzy
msgid ""
"The radio tool button group (the default is all radio tool buttons in the "
"toolbar)"
msgstr "单选按钮组（默认情况下是具有相同父级的所有单选按钮）"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbradiotoolbutton.c:528
#, fuzzy
msgid "Toolbar Radio Button"
msgstr "单选按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:131
msgid "H Policy:"
msgstr "水平策略："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:132
msgid "When the horizontal scrollbar will be shown"
msgstr "显示水平滚动条时"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:134
msgid "V Policy:"
msgstr "垂直策略："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:135
msgid "When the vertical scrollbar will be shown"
msgstr "显示垂直滚动条时"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:137
#, fuzzy
msgid "Window Pos:"
msgstr "窗口"

#: ../glade/gbwidgets/gbscrolledwindow.c:138
msgid "Where the child window is located with respect to the scrollbars"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:140
#, fuzzy
msgid "Shadow Type:"
msgstr "阴影："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:141
msgid "The update policy of the vertical scrollbar"
msgstr "垂直滚动条的更新策略"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbscrolledwindow.c:353
msgid "Scrolled Window"
msgstr "滚动窗口"

#: ../glade/gbwidgets/gbseparatormenuitem.c:153
msgid "Separator for Menus"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:65
msgid "Draw:"
msgstr ""

#: ../glade/gbwidgets/gbseparatortoolitem.c:66
msgid "If the separator is drawn, or just blank"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbseparatortoolitem.c:204
#, fuzzy
msgid "Toolbar Separator Item"
msgstr "水平分隔符"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:91
msgid "Climb Rate:"
msgstr "倾斜度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:92
msgid ""
"The climb rate of the spinbutton, used in conjunction with the Page Increment"
msgstr "旋转按钮的倾斜度，与页面增量配合使用"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:94
msgid "The number of decimal digits to show"
msgstr "显示的小数位数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:96
msgid "Numeric:"
msgstr "数字："

#: ../glade/gbwidgets/gbspinbutton.c:97
msgid "If only numeric entry is allowed"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:98
msgid "Update Policy:"
msgstr "更新策略："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:99
msgid "When value_changed signals are emitted"
msgstr "发出值更改信号时"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:101
msgid "Snap:"
msgstr "对齐："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:102
msgid "If the value is snapped to multiples of the step increment"
msgstr "值是否与多个步进增量对齐"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:103
msgid "Wrap:"
msgstr "回绕："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:104
msgid "If the value is wrapped at the limits"
msgstr "值是否在达到限制时回绕"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbspinbutton.c:284
msgid "Spin Button"
msgstr "旋转按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbstatusbar.c:64
#, fuzzy
msgid "Resize Grip:"
msgstr "改变大小："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbstatusbar.c:64
#, fuzzy
msgid "If the status bar has a resize grip to resize the window"
msgstr "应用程序栏是否有进度指示器"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbstatusbar.c:156
msgid "Status Bar"
msgstr "状态栏"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:137
msgid "New table"
msgstr "新建表"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:149 ../glade/gbwidgets/gbvbox.c:95
#: ../glade/gbwidgets/gbvbuttonbox.c:123
msgid "Number of rows:"
msgstr "行数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:237
msgid "Rows:"
msgstr "行："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:238
msgid "The number of rows in the table"
msgstr "表中的行数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:240
msgid "Columns:"
msgstr "列："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:241
msgid "The number of columns in the table"
msgstr "表中的列数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:244
msgid "If the children should all be the same size"
msgstr "所有子项的大小是否都应相同"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:245 ../glade/gnome/gnomeiconlist.c:180
msgid "Row Spacing:"
msgstr "行间距："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:246
msgid "The space between each row"
msgstr "每行之间的间距"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:248 ../glade/gnome/gnomeiconlist.c:183
msgid "Col Spacing:"
msgstr "列间距："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:249
msgid "The space between each column"
msgstr "每列之间的间距"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:368
msgid "Cell X:"
msgstr "单元格 X："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:369
msgid "The left edge of the widget in the table"
msgstr "表中构件的左边缘"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:371
msgid "Cell Y:"
msgstr "单元格 Y："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:372
msgid "The top edge of the widget in the table"
msgstr "表中构件的上边缘"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:375
msgid "Col Span:"
msgstr "列跨度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:376
msgid "The number of columns spanned by the widget in the table"
msgstr "表中构件跨越的列数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:378
msgid "Row Span:"
msgstr "行跨度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:379
msgid "The number of rows spanned by the widget in the table"
msgstr "表中构件跨越的行数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:381
msgid "H Padding:"
msgstr "水平填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:384
msgid "V Padding:"
msgstr "垂直填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:387
msgid "X Expand:"
msgstr "X 扩展："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:388
msgid "Set True to let the widget expand horizontally"
msgstr "设置为 True 使构件可以水平扩展"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:389
msgid "Y Expand:"
msgstr "Y 扩展："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:390
msgid "Set True to let the widget expand vertically"
msgstr "设置为 True 使构件可以垂直扩展"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:391
msgid "X Shrink:"
msgstr "X 收缩："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:392
msgid "Set True to let the widget shrink horizontally"
msgstr "设置为 True 使构件可以水平收缩"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:393
msgid "Y Shrink:"
msgstr "Y 收缩："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:394
msgid "Set True to let the widget shrink vertically"
msgstr "设置为 True 使构件可以垂直收缩"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:395
msgid "X Fill:"
msgstr "X 填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:396
msgid "Set True to let the widget fill its horizontal allocated area"
msgstr "设置为 True 使构件可以填充它的水平分配区域"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:397
msgid "Y Fill:"
msgstr "Y 填充："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:398
msgid "Set True to let the widget fill its vertical allocated area"
msgstr "设置为 True 使构件可以填充它的垂直分配区域"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:667
msgid "Insert Row Before"
msgstr "在以下对象之前插入行"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:674
msgid "Insert Row After"
msgstr "在以下对象之后插入行"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:681
msgid "Insert Column Before"
msgstr "在以下对象之前插入列"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:688
msgid "Insert Column After"
msgstr "在以下对象之后插入列"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:695
msgid "Delete Row"
msgstr "删除行"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:701
msgid "Delete Column"
msgstr "删除列"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtable.c:1208
msgid "Table"
msgstr "表"

#: ../glade/gbwidgets/gbtextview.c:51
msgid "Center"
msgstr "中心"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:52
#, fuzzy
msgid "Fill"
msgstr "填充："

#. Add a "None" item first, so it is easy to reset the pixmap.
#. If the user selects the 'None' item from the combo, we reset the
#. text to "" and return. This callback will be called again.
#. Add a 'None' item at the top of the list.
#: ../glade/gbwidgets/gbtextview.c:71 ../glade/glade_gnome.c:112
#: ../glade/glade_menu_editor.c:542 ../glade/glade_menu_editor.c:829
#: ../glade/glade_menu_editor.c:1344 ../glade/glade_menu_editor.c:2251
#: ../glade/property.c:2431
msgid "None"
msgstr "无"

#: ../glade/gbwidgets/gbtextview.c:72
msgid "Character"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:73
msgid "Word"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:117
#, fuzzy
msgid "Cursor Visible:"
msgstr "可见："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:118
#, fuzzy
msgid "If the cursor is visible"
msgstr "构件最初是否可见"

#: ../glade/gbwidgets/gbtextview.c:119
#, fuzzy
msgid "Overwrite:"
msgstr "转换"

#: ../glade/gbwidgets/gbtextview.c:120
msgid "If entered text overwrites the existing text"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:121
msgid "Accepts Tab:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:122
#, fuzzy
msgid "If tab characters can be entered"
msgstr "文本是否可以编辑"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:126
#, fuzzy
msgid "Justification:"
msgstr "调整："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:127
#, fuzzy
msgid "The justification of the text"
msgstr "调整标签中的各行"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:129
#, fuzzy
msgid "Wrapping:"
msgstr "回绕："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:130
#, fuzzy
msgid "The wrapping of the text"
msgstr "文本的最大长度"

#: ../glade/gbwidgets/gbtextview.c:133
msgid "Space Above:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:134
msgid "Pixels of blank space above paragraphs"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:136
msgid "Space Below:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:137
msgid "Pixels of blank space below paragraphs"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:139
#, fuzzy
msgid "Space Inside:"
msgstr "敏感："

#: ../glade/gbwidgets/gbtextview.c:140
msgid "Pixels of blank space between wrapped lines in a paragraph"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:143
msgid "Left Margin:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:144
msgid "Width of the left margin in pixels"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:146
msgid "Right Margin:"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:147
msgid "Width of the right margin in pixels"
msgstr ""

#: ../glade/gbwidgets/gbtextview.c:149
#, fuzzy
msgid "Indent:"
msgstr "索引"

#: ../glade/gbwidgets/gbtextview.c:150
msgid "Amount of pixels to indent paragraphs"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtextview.c:463
#, fuzzy
msgid "Text View"
msgstr "文本可见："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtogglebutton.c:100
#: ../glade/gbwidgets/gbtoggletoolbutton.c:98
msgid "If the toggle button is initially on"
msgstr "切换按钮最初是否处于打开状态"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtogglebutton.c:199
msgid "Toggle Button"
msgstr "切换按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoggletoolbutton.c:297
#, fuzzy
msgid "Toolbar Toggle Button"
msgstr "切换按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:191
msgid "New toolbar"
msgstr "新建工具栏"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:202
msgid "Number of items:"
msgstr "项数："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:268
msgid "The number of items in the toolbar"
msgstr "工具栏中的项数"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:271
msgid "The toolbar orientation"
msgstr "工具栏方向"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:273
msgid "Style:"
msgstr "样式："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:274
msgid "The toolbar style"
msgstr "工具栏样式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "Tooltips:"
msgstr "工具提示："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:276
msgid "If tooltips are enabled"
msgstr "是否启用工具提示"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:277
#, fuzzy
msgid "Show Arrow:"
msgstr "显示边框："

#: ../glade/gbwidgets/gbtoolbar.c:277
msgid "If an arrow should be shown to popup a menu if the toolbar doesn't fit"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:427
#, fuzzy
msgid "If the item should be the same size as other homogeneous items"
msgstr "子项的大小是否应相同"

# SUN NEW TRANSLATION
#. Commands for inserting new items.
#: ../glade/gbwidgets/gbtoolbar.c:506
msgid "Insert Item Before"
msgstr "在以下对象之前插入项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:513
msgid "Insert Item After"
msgstr "在以下对象之后插入项"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbar.c:680
msgid "Toolbar"
msgstr "工具栏"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolbutton.c:586
#, fuzzy
msgid "Toolbar Button"
msgstr "切换按钮"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtoolitem.c:201
#, fuzzy
msgid "Toolbar Item"
msgstr "工具栏"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:71
#, fuzzy
msgid "Column 1"
msgstr "列："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:79
#, fuzzy
msgid "Column 2"
msgstr "列："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:87
#, fuzzy
msgid "Column 3"
msgstr "列："

#: ../glade/gbwidgets/gbtreeview.c:97
#, c-format
msgid "Row %i"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:114
#, fuzzy
msgid "Headers Visible:"
msgstr "头文件："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:115
#, fuzzy
msgid "If the column header buttons are shown"
msgstr "是否显示列标题"

#: ../glade/gbwidgets/gbtreeview.c:116
msgid "Rules Hint:"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:117
msgid ""
"If a hint is set so the theme engine should draw rows in alternating colors"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:118
msgid "Reorderable:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:119
#, fuzzy
msgid "If the view is reorderable"
msgstr "预览是彩色图像还是灰度级图像"

#: ../glade/gbwidgets/gbtreeview.c:120
msgid "Enable Search:"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:121
msgid "If the user can search through columns interactively"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:123
#, fuzzy
msgid "Fixed Height Mode:"
msgstr "缩放高度："

#: ../glade/gbwidgets/gbtreeview.c:124
msgid "Sets all rows to the same height to improve performance"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:125
#, fuzzy
msgid "Hover Selection:"
msgstr "颜色选择"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:126
#, fuzzy
msgid "Whether the selection should follow the pointer"
msgstr "列表的选择模式"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbtreeview.c:127
#, fuzzy
msgid "Hover Expand:"
msgstr "X 扩展："

#: ../glade/gbwidgets/gbtreeview.c:128
msgid ""
"Whether rows should be expanded or collapsed when the pointer moves over them"
msgstr ""

#: ../glade/gbwidgets/gbtreeview.c:317
msgid "List or Tree View"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvbox.c:84
msgid "New vertical box"
msgstr "新垂直框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvbox.c:245
msgid "Vertical Box"
msgstr "垂直框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvbuttonbox.c:111
msgid "New vertical button box"
msgstr "新垂直按钮框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvbuttonbox.c:344
msgid "Vertical Button Box"
msgstr "垂直按钮框"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbviewport.c:104
msgid "The type of shadow of the viewport"
msgstr "视见区的阴影类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbviewport.c:240
msgid "Viewport"
msgstr "视见区"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvpaned.c:192
msgid "Vertical Panes"
msgstr "垂直窗格"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvruler.c:247
msgid "Vertical Ruler"
msgstr "垂直标尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvscale.c:319
msgid "Vertical Scale"
msgstr "垂直比例尺"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvscrollbar.c:236
msgid "Vertical Scrollbar"
msgstr "垂直滚动条"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbvseparator.c:144
msgid "Vertical Separator"
msgstr "垂直分隔符"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:242
msgid "The title of the window"
msgstr "窗口的标题"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:245
msgid "The type of the window"
msgstr "窗口的类型"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:249
#, fuzzy
msgid "Type Hint:"
msgstr "类型："

#: ../glade/gbwidgets/gbwindow.c:250
msgid "Tells the window manager how to treat the window"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:255
msgid "The initial position of the window"
msgstr "窗口的初始位置"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:259 ../glade/gnome/gnomefileentry.c:105
#: ../glade/gnome/gnomepixmapentry.c:84
msgid "Modal:"
msgstr "模态："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:259
msgid "If the window is modal"
msgstr "窗口是否为模态"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:264
msgid "Default Width:"
msgstr "默认宽度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:265
msgid "The default width of the window"
msgstr "窗口的默认宽度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:269
msgid "Default Height:"
msgstr "默认高度："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:270
msgid "The default height of the window"
msgstr "窗口的默认高度"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:276
#, fuzzy
msgid "Resizable:"
msgstr "改变大小："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:277
#, fuzzy
msgid "If the window can be resized"
msgstr "是否可以放大窗口"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:284
msgid "If the window can be shrunk"
msgstr "是否可以收缩窗口"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:285
msgid "Grow:"
msgstr "增大："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:286
msgid "If the window can be enlarged"
msgstr "是否可以放大窗口"

#: ../glade/gbwidgets/gbwindow.c:291
msgid "Auto-Destroy:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:292
#, fuzzy
msgid "If the window is destroyed when its transient parent is destroyed"
msgstr "是否在关闭对话框时隐藏它，而不摧毁它"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:296
#, fuzzy
msgid "The icon for this window"
msgstr "窗口的标题"

#: ../glade/gbwidgets/gbwindow.c:303
msgid "Role:"
msgstr ""

#: ../glade/gbwidgets/gbwindow.c:303
msgid "A unique identifier for the window to be used when restoring a session"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:306
#, fuzzy
msgid "Decorated:"
msgstr "被保护"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:307
#, fuzzy
msgid "If the window should be decorated by the window manager"
msgstr "长宽比是否由子项决定"

#: ../glade/gbwidgets/gbwindow.c:310
msgid "Skip Taskbar:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:311
#, fuzzy
msgid "If the window should not appear in the task bar"
msgstr "窗口是否有状态栏"

#: ../glade/gbwidgets/gbwindow.c:314
msgid "Skip Pager:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:315
#, fuzzy
msgid "If the window should not appear in the pager"
msgstr "是否在进度栏中显示文本"

#: ../glade/gbwidgets/gbwindow.c:318
#, fuzzy
msgid "Gravity:"
msgstr "网格风格："

#: ../glade/gbwidgets/gbwindow.c:319
msgid "The reference point to use when the window coordinates are set"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "Focus On Map:"
msgstr "聚焦目标："

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:323
#, fuzzy
msgid "If the window should receive the input focus when it is mapped"
msgstr "长宽比是否由子项决定"

# SUN NEW TRANSLATION
#: ../glade/gbwidgets/gbwindow.c:1198
msgid "Window"
msgstr "窗口"

#: ../glade/glade.c:369 ../glade/gnome-db/gnomedberrordlg.c:74
msgid "Error"
msgstr "错误"

#: ../glade/glade.c:372
msgid "System Error"
msgstr "系统错误"

#: ../glade/glade.c:376
msgid "Error opening file"
msgstr "打开文件时发生错误"

#: ../glade/glade.c:378
msgid "Error reading file"
msgstr "读取文件时发生错误"

#: ../glade/glade.c:380
msgid "Error writing file"
msgstr "写入文件时发生错误"

#: ../glade/glade.c:383
msgid "Invalid directory"
msgstr "无效的目录"

#: ../glade/glade.c:387
msgid "Invalid value"
msgstr "无效的值"

#: ../glade/glade.c:389
msgid "Invalid XML entity"
msgstr "无效的 XML 实体"

#: ../glade/glade.c:391
msgid "Start tag expected"
msgstr "需要开始标记"

#: ../glade/glade.c:393
msgid "End tag expected"
msgstr "需要结束标记"

#: ../glade/glade.c:395
msgid "Character data expected"
msgstr "需要字符数据"

#: ../glade/glade.c:397
msgid "Class id missing"
msgstr "需要类ID"

#: ../glade/glade.c:399
msgid "Class unknown"
msgstr "未知类"

#: ../glade/glade.c:401
msgid "Invalid component"
msgstr "无效的部件"

#: ../glade/glade.c:403
msgid "Unexpected end of file"
msgstr "非正常文件结束"

#: ../glade/glade.c:406
msgid "Unknown error code"
msgstr "未知错误码"

#: ../glade/glade_atk.c:120
msgid "Controlled By"
msgstr ""

#: ../glade/glade_atk.c:121
msgid "Controller For"
msgstr ""

#: ../glade/glade_atk.c:122
#, fuzzy
msgid "Label For"
msgstr "标签"

#: ../glade/glade_atk.c:123
#, fuzzy
msgid "Labelled By"
msgstr "标签"

#: ../glade/glade_atk.c:124
msgid "Member Of"
msgstr ""

#: ../glade/glade_atk.c:125
msgid "Node Child Of"
msgstr ""

#: ../glade/glade_atk.c:126
msgid "Flows To"
msgstr ""

#: ../glade/glade_atk.c:127
msgid "Flows From"
msgstr ""

#: ../glade/glade_atk.c:128
msgid "Subwindow Of"
msgstr ""

#: ../glade/glade_atk.c:129
msgid "Embeds"
msgstr ""

#: ../glade/glade_atk.c:130
#, fuzzy
msgid "Embedded By"
msgstr "标签"

# SUN NEW TRANSLATION
#: ../glade/glade_atk.c:131
#, fuzzy
msgid "Popup For"
msgstr "弹出菜单"

#: ../glade/glade_atk.c:132
msgid "Parent Window Of"
msgstr ""

#. I don't think we should set the transient parent as the dialog could be
#. left open if desired.
#: ../glade/glade_atk.c:331
#, c-format
msgid "Relationship: %s"
msgstr ""

# SUN NEW TRANSLATION
#. Create the pages of the main notebook
#. NOTE: If you add/remove pages you need to change the GB_PAGE_SIGNALS
#. value at the top of this file
#: ../glade/glade_atk.c:375 ../glade/property.c:615
msgid "Widget"
msgstr "构件"

#: ../glade/glade_atk.c:638 ../glade/glade_menu_editor.c:772
#: ../glade/property.c:776
msgid "Name:"
msgstr "名字："

#: ../glade/glade_atk.c:639
msgid "The name of the widget to pass to assistive technologies"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/glade_atk.c:640
#, fuzzy
msgid "Description:"
msgstr "说明"

#: ../glade/glade_atk.c:641
msgid "The description of the widget to pass to assistive technologies"
msgstr ""

#: ../glade/glade_atk.c:643
msgid "Table Caption:"
msgstr ""

#: ../glade/glade_atk.c:644
msgid "The table caption to pass to assistive technologies"
msgstr ""

#: ../glade/glade_atk.c:681
msgid "Select the widgets with this relationship"
msgstr ""

#: ../glade/glade_atk.c:761
msgid "Click"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/glade_atk.c:762
#, fuzzy
msgid "Press"
msgstr "进度："

#: ../glade/glade_atk.c:763
#, fuzzy
msgid "Release"
msgstr "删除"

#: ../glade/glade_atk.c:822
msgid "Enter the description of the action to pass to assistive technologies"
msgstr ""

#: ../glade/glade_clipboard.c:118
msgid "Clipboard"
msgstr "剪贴板"

#: ../glade/glade_clipboard.c:351
msgid "You need to select a widget to paste into"
msgstr "您需要选择一个部件将数据粘贴入"

#: ../glade/glade_clipboard.c:376
msgid "You can't paste into windows or dialogs."
msgstr "您无法粘贴到窗口或对话中。"

#: ../glade/glade_clipboard.c:399
msgid ""
"You can't paste into the selected widget, since\n"
"it is created automatically by its parent."
msgstr ""
"您无法粘贴到选中的部件中，因为它由父部件\n"
"自动创建。"

#: ../glade/glade_clipboard.c:408 ../glade/glade_clipboard.c:416
msgid "Only menu items can be pasted into a menu or menu bar."
msgstr "只有菜单项能够被粘贴到菜单或菜单栏中。"

#: ../glade/glade_clipboard.c:427
#, fuzzy
msgid "Only buttons can be pasted into a dialog action area."
msgstr "只有按钮能被粘贴到 GnomeDialog 动作区。"

#: ../glade/glade_clipboard.c:437
msgid "Only GnomeDockItem widgets can be pasted into a GnomeDock."
msgstr "只有 GnomeDockItem 部件能被粘贴到 GnomeDock 中。"

#: ../glade/glade_clipboard.c:446
msgid "Only GnomeDockItem widgets can be pasted over a GnomeDockItem."
msgstr "只有 GnomeDOckItem 部件能被粘贴到 GnomeDockItem 之上。"

#: ../glade/glade_clipboard.c:449
msgid "Sorry - pasting over a GnomeDockItem is not implemented yet."
msgstr "抱歉 - 粘贴到 GnomeDockItem 之上还没有实现。"

#: ../glade/glade_clipboard.c:457
msgid "GnomeDockItem widgets can only be pasted into a GnomeDock."
msgstr "GnomeDockItem 部件只能被粘贴到 GnomeDock 中。"

#. 3 - see GladeStockMenuItemNew above.
#: ../glade/glade_gnome.c:121 ../glade/glade_gnome.c:874
#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:632
msgid "_New"
msgstr "新建(_N)"

# SUN NEW TRANSLATION
#: ../glade/glade_gnome.c:874
msgid "Create a new file"
msgstr "创建新文件"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gnomelib.c:116
#, fuzzy
msgid "_Gnome"
msgstr "Gnome"

# SUN NEW TRANSLATION
#: ../glade/glade_gnomelib.c:117 ../glade/glade_gtk12lib.c:248
#: ../glade/glade_palette.c:315
#, fuzzy
msgid "Dep_recated"
msgstr "被保护"

#. Note that glade_palette_set_show_gnome_widgets() has some of these
#. strings hard-coded now, so keep up-to-date.
#: ../glade/glade_gtk12lib.c:246
#, fuzzy
msgid "GTK+ _Basic"
msgstr "GTK+ 基本"

#: ../glade/glade_gtk12lib.c:247
#, fuzzy
msgid "GTK+ _Additional"
msgstr "GTK+ 附加"

#: ../glade/glade_keys_dialog.c:94
msgid "Select Accelerator Key"
msgstr "选择加速键"

#: ../glade/glade_keys_dialog.c:97
msgid "Keys"
msgstr "键"

#: ../glade/glade_menu_editor.c:394
msgid "Menu Editor"
msgstr "菜单编辑器"

#: ../glade/glade_menu_editor.c:411
msgid "Type"
msgstr "类型"

#: ../glade/glade_menu_editor.c:412
msgid "Accelerator"
msgstr "加速键"

#: ../glade/glade_menu_editor.c:413
msgid "Name"
msgstr "名字"

#: ../glade/glade_menu_editor.c:414 ../glade/property.c:1498
msgid "Handler"
msgstr "处理"

#: ../glade/glade_menu_editor.c:415 ../glade/property.c:102
msgid "Active"
msgstr "活动"

#: ../glade/glade_menu_editor.c:416
msgid "Group"
msgstr "分组"

#: ../glade/glade_menu_editor.c:417
msgid "Icon"
msgstr "图标"

#: ../glade/glade_menu_editor.c:458
msgid "Move the item and its children up one place in the list"
msgstr "在列表中将项目及其孩子向上移动一个位置"

#: ../glade/glade_menu_editor.c:470
msgid "Move the item and its children down one place in the list"
msgstr "在列表中将项目及其孩子向下移动一个位置"

#: ../glade/glade_menu_editor.c:482
msgid "Move the item and its children up one level"
msgstr "将项目及其孩子向上移动一个级别"

#: ../glade/glade_menu_editor.c:494
msgid "Move the item and its children down one level"
msgstr "将项目及其孩子向下移动一个级别"

# SUN NEW TRANSLATION
#: ../glade/glade_menu_editor.c:524
#, fuzzy
msgid "The stock item to use."
msgstr "要使用的库 Gnome 项目。"

# SUN NEW TRANSLATION
#: ../glade/glade_menu_editor.c:527 ../glade/glade_menu_editor.c:642
msgid "Stock Item:"
msgstr "库项目："

# SUN NEW TRANSLATION
#: ../glade/glade_menu_editor.c:640
msgid "The stock Gnome item to use."
msgstr "要使用的库 Gnome 项目。"

#: ../glade/glade_menu_editor.c:745
msgid "The text of the menu item, or empty for separators."
msgstr ""

#: ../glade/glade_menu_editor.c:769 ../glade/property.c:777
msgid "The name of the widget"
msgstr "部件名"

#: ../glade/glade_menu_editor.c:790
msgid "The function to be called when the item is selected"
msgstr "当项目被选中时调用的函数"

#: ../glade/glade_menu_editor.c:792 ../glade/property.c:1546
msgid "Handler:"
msgstr "处理："

#: ../glade/glade_menu_editor.c:811
msgid "An optional icon to show on the left of the menu item."
msgstr "可选的在菜单项左面显示的图标。"

#: ../glade/glade_menu_editor.c:934
msgid "The tip to show when the mouse is over the item"
msgstr "当鼠标位于项目之上时显示的提示"

#: ../glade/glade_menu_editor.c:936 ../glade/property.c:824
msgid "Tooltip:"
msgstr "工具提示："

#: ../glade/glade_menu_editor.c:957
#, fuzzy
msgid "_Add"
msgstr "添加"

#: ../glade/glade_menu_editor.c:962
msgid "Add a new item below the selected item."
msgstr "在选中的项目下添加一个新项目。"

#: ../glade/glade_menu_editor.c:967
msgid "Add _Child"
msgstr ""

#: ../glade/glade_menu_editor.c:972
#, fuzzy
msgid "Add a new child item below the selected item."
msgstr "在选中的项目下添加一个新项目。"

#: ../glade/glade_menu_editor.c:978
#, fuzzy
msgid "Add _Separator"
msgstr "添加分隔符"

#: ../glade/glade_menu_editor.c:983
msgid "Add a separator below the selected item."
msgstr "在选中的项目下添加一个分隔符。"

# SUN NEW TRANSLATION
#: ../glade/glade_menu_editor.c:988 ../glade/glade_project_window.c:239
msgid "_Delete"
msgstr "删除(_D)"

#: ../glade/glade_menu_editor.c:993
msgid "Delete the current item"
msgstr "删除当前项目"

#. Type radio options and toggle options.
#: ../glade/glade_menu_editor.c:999
msgid "Item Type:"
msgstr "项目类型："

#: ../glade/glade_menu_editor.c:1015
msgid "If the item is initially on."
msgstr "是否项目初始是打开的。"

#: ../glade/glade_menu_editor.c:1017
msgid "Active:"
msgstr "活动："

#: ../glade/glade_menu_editor.c:1022 ../glade/glade_menu_editor.c:1632
#: ../glade/property.c:2215 ../glade/property.c:2225
msgid "No"
msgstr "否"

# SUN NEW TRANSLATION
#: ../glade/glade_menu_editor.c:1036
#, fuzzy
msgid "The radio menu item's group"
msgstr "单选菜单项初始是否打开"

#: ../glade/glade_menu_editor.c:1053 ../glade/glade_menu_editor.c:2406
#: ../glade/glade_menu_editor.c:2546
msgid "Radio"
msgstr "收音机"

#: ../glade/glade_menu_editor.c:1060 ../glade/glade_menu_editor.c:2404
#: ../glade/glade_menu_editor.c:2544
msgid "Check"
msgstr "核对"

#: ../glade/glade_menu_editor.c:1067 ../glade/property.c:102
msgid "Normal"
msgstr "普通"

#. Accelerator key options.
#: ../glade/glade_menu_editor.c:1076
msgid "Accelerator:"
msgstr "加速键："

#: ../glade/glade_menu_editor.c:1113 ../glade/property.c:1681
msgid "Ctrl"
msgstr "Ctrl"

#: ../glade/glade_menu_editor.c:1118 ../glade/property.c:1684
msgid "Shift"
msgstr "Shift"

#: ../glade/glade_menu_editor.c:1123 ../glade/property.c:1687
msgid "Alt"
msgstr "Alt"

#: ../glade/glade_menu_editor.c:1128 ../glade/property.c:1694
msgid "Key:"
msgstr "键："

#: ../glade/glade_menu_editor.c:1134 ../glade/property.c:1673
msgid "Modifiers:"
msgstr "修饰键："

#: ../glade/glade_menu_editor.c:1632 ../glade/glade_menu_editor.c:2411
#: ../glade/glade_menu_editor.c:2554 ../glade/property.c:2215
msgid "Yes"
msgstr "是"

#: ../glade/glade_menu_editor.c:2002
msgid "Select icon"
msgstr "选择图标"

#: ../glade/glade_menu_editor.c:2345 ../glade/glade_menu_editor.c:2706
msgid "separator"
msgstr "分隔符"

#. Add the special 'New' item to create a new group.
#: ../glade/glade_menu_editor.c:3624 ../glade/glade_project_window.c:366
#: ../glade/property.c:5109
msgid "New"
msgstr "新建"

#: ../glade/glade_palette.c:194 ../glade/glade_palette.c:196
#: ../glade/glade_palette.c:412
msgid "Selector"
msgstr "选择器"

#: ../glade/glade_project.c:385
msgid ""
"The project directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"没有设置项目目录。\n"
"请使用项目选项对话来设置它。\n"

#: ../glade/glade_project.c:392
msgid ""
"The source directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"没有设置源目录。\n"
"请使用项目选项对话来设置它。\n"

#: ../glade/glade_project.c:402
msgid ""
"Invalid source directory:\n"
"\n"
"The source directory must be the project directory\n"
"or a subdirectory of the project directory.\n"
msgstr ""
"无效的源目录：\n"
"\n"
"源目录必须是项目目录或项目目录的子目录。\n"

# SUN CHANGED MESSAGE
#: ../glade/glade_project.c:410
msgid ""
"The pixmaps directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"没有设置像素图目录。\n"
"请使用项目选项对话来设置它。\n"

#: ../glade/glade_project.c:438
#, c-format
msgid "Sorry - generating source for %s is not implemented yet"
msgstr "抱歉 - 生成 %s 的源代码的功能还没有实现"

#: ../glade/glade_project.c:509
msgid ""
"Your project uses deprecated widgets that Gtkmm-2\n"
"does not support.  Check your project for these\n"
"widgets, and use their replacements."
msgstr ""

#: ../glade/glade_project.c:521
#, fuzzy
msgid ""
"Error running glade-- to generate the C++ source code.\n"
"Check that you have glade-- installed and that it is in your PATH.\n"
"Then try running 'glade-- <project_file.glade>' in a terminal."
msgstr ""
"运行 glade-- 生成 C++ 源代码时发生错误。\n"
"请检查您已安装了 glade-- 并且它在您的命令搜索路径中。"

#: ../glade/glade_project.c:548
#, fuzzy
msgid ""
"Error running gate to generate the Ada95 source code.\n"
"Check that you have gate installed and that it is in your PATH.\n"
"Then try running 'gate <project_file.glade>' in a terminal."
msgstr ""
"运行 glade-- 生成 C++ 源代码时发生错误。\n"
"请检查您已安装了 glade-- 并且它在您的命令搜索路径中。"

#: ../glade/glade_project.c:571
#, fuzzy
msgid ""
"Error running glade2perl to generate the Perl source code.\n"
"Check that you have glade2perl installed and that it is in your PATH.\n"
"Then try running 'glade2perl <project_file.glade>' in a terminal."
msgstr ""
"运行 glade-- 生成 C++ 源代码时发生错误。\n"
"请检查您已安装了 glade-- 并且它在您的命令搜索路径中。"

#: ../glade/glade_project.c:594
#, fuzzy
msgid ""
"Error running eglade to generate the Eiffel source code.\n"
"Check that you have eglade installed and that it is in your PATH.\n"
"Then try running 'eglade <project_file.glade>' in a terminal."
msgstr ""
"运行 glade-- 生成 C++ 源代码时发生错误。\n"
"请检查您已安装了 glade-- 并且它在您的命令搜索路径中。"

# SUN NEW TRANSLATION
#: ../glade/glade_project.c:954
msgid ""
"The pixmap directory is not set.\n"
"Please set it using the Project Options dialog.\n"
msgstr ""
"没有设置像素图目录。\n"
"请使用“项目选项”对话框设置它。\n"

# SUN NEW TRANSLATION
#: ../glade/glade_project.c:1772
#, fuzzy
msgid "Error writing project XML file\n"
msgstr "编写 XML 文件时出错\n"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:157 ../glade/glade_project_window.c:382
#: ../glade/glade_project_window.c:889
msgid "Project Options"
msgstr "项目选项"

#.
#. * General Page.
#.
#: ../glade/glade_project_options.c:174
msgid "General"
msgstr "常规"

#: ../glade/glade_project_options.c:183
msgid "Basic Options:"
msgstr "基本选项："

#: ../glade/glade_project_options.c:201
msgid "The project directory"
msgstr "工程目录"

#: ../glade/glade_project_options.c:203
msgid "Project Directory:"
msgstr "工程目录："

#: ../glade/glade_project_options.c:221
msgid "Browse..."
msgstr "浏览..."

#: ../glade/glade_project_options.c:236
msgid "The name of the current project"
msgstr "当前工程的名字"

#: ../glade/glade_project_options.c:238
msgid "Project Name:"
msgstr "工程名："

#: ../glade/glade_project_options.c:258
msgid "The name of the program"
msgstr "程序名"

#: ../glade/glade_project_options.c:281
msgid "The project file"
msgstr "工程文件"

#: ../glade/glade_project_options.c:283
msgid "Project File:"
msgstr "工程文件："

#. Project Source Directory.
#: ../glade/glade_project_options.c:299
msgid "Subdirectories:"
msgstr "子目录："

#: ../glade/glade_project_options.c:316
msgid "The directory to save generated source code"
msgstr "保存生成的源代码的目录"

#: ../glade/glade_project_options.c:319
msgid "Source Directory:"
msgstr "源代码目录："

# SUN CHANGED MESSAGE
#: ../glade/glade_project_options.c:338
msgid "The directory to store pixmaps"
msgstr "保存像素图的目录"

# SUN CHANGED MESSAGE
#: ../glade/glade_project_options.c:341
msgid "Pixmaps Directory:"
msgstr "像素图目录："

#: ../glade/glade_project_options.c:363
msgid "The license which is added at the top of generated files"
msgstr "在生成的文件的顶部添加的授权"

#. Source Language.
#: ../glade/glade_project_options.c:385
msgid "Language:"
msgstr "语言："

#: ../glade/glade_project_options.c:416
msgid "Gnome:"
msgstr "Gnome："

#: ../glade/glade_project_options.c:424
msgid "Enable Gnome Support"
msgstr "启用 Gnome 支持"

#: ../glade/glade_project_options.c:430
msgid "If a Gnome application is to be built"
msgstr "是否要编写 Gnome 应用程序"

#: ../glade/glade_project_options.c:433
msgid "Enable Gnome DB Support"
msgstr "启用 Gnome DB 支持"

#: ../glade/glade_project_options.c:437
msgid "If a Gnome DB application is to be built"
msgstr "是否要编写 Gnome DB 应用程序"

#.
#. * C Options Page.
#.
#: ../glade/glade_project_options.c:447
msgid "C Options"
msgstr "C 选项"

#: ../glade/glade_project_options.c:456
msgid "<b>Note:</b> for large applications the use of libglade is recommended."
msgstr ""

#: ../glade/glade_project_options.c:468
msgid "General Options:"
msgstr "常规选项："

#. Gettext Support.
#: ../glade/glade_project_options.c:478
msgid "Gettext Support"
msgstr "Gettext 支持"

#: ../glade/glade_project_options.c:483
msgid "If strings are marked for translation by gettext"
msgstr "是否将字符串标记让 gettext 进行翻译"

# SUN NEW TRANSLATION
#. Setting widget names.
#: ../glade/glade_project_options.c:487
msgid "Set Widget Names"
msgstr "设置构件名称"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:492
msgid "If widget names are set in the source code"
msgstr "构件名称是否在源代码中进行设置"

#. Backing up source files.
#: ../glade/glade_project_options.c:496
msgid "Backup Source Files"
msgstr "备份源文件"

#: ../glade/glade_project_options.c:501
msgid "If copies of old source files are made"
msgstr "是否保留旧的源代码的复件"

#. Gnome Help System support.
#: ../glade/glade_project_options.c:505
msgid "Gnome Help Support"
msgstr "Gnome 帮助支持"

#: ../glade/glade_project_options.c:512
msgid "If support for the Gnome Help system should be included"
msgstr "是否包括 Gnome 帮助系统的支持"

#: ../glade/glade_project_options.c:515
msgid "File Output Options:"
msgstr "文件输出选项："

#. Outputting main file.
#: ../glade/glade_project_options.c:525
msgid "Output main.c File"
msgstr "输出 main.c 文件"

#: ../glade/glade_project_options.c:530
msgid ""
"If a main.c file is output containing a main() function, if it doesn't "
"already exist"
msgstr "如果不存在 main.c，是否输出包含 main() 函数的 main.c 文件"

# SUN NEW TRANSLATION
#. Outputting support files.
#: ../glade/glade_project_options.c:534
msgid "Output Support Functions"
msgstr "输出支持函数"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:539
msgid "If the support functions are output"
msgstr "是否输出支持函数"

# SUN NEW TRANSLATION
#. Outputting build files.
#: ../glade/glade_project_options.c:543
msgid "Output Build Files"
msgstr "输出联编文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:548
msgid ""
"If files for building the source code are output, including Makefile.am and "
"configure.in, if they don't already exist"
msgstr ""
"如果用于联编源代码的文件不存在，包括 Makefile.am 和 configure.in，是否输出这"
"些文件"

# SUN NEW TRANSLATION
#. Main source file.
#: ../glade/glade_project_options.c:552
msgid "Interface Creation Functions:"
msgstr "界面创建函数："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:564
msgid "The file in which the functions to create the interface are written"
msgstr "编写用于创建界面的函数的文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:566 ../glade/glade_project_options.c:612
#: ../glade/glade_project_options.c:658 ../glade/property.c:998
msgid "Source File:"
msgstr "源文件："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:581
msgid ""
"The file in which the declarations of the functions to create the interface "
"are written"
msgstr "编写用于创建界面的函数声明的文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:583 ../glade/glade_project_options.c:629
#: ../glade/glade_project_options.c:675
msgid "Header File:"
msgstr "头文件："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:594
#, fuzzy
msgid "Source file for interface creation functions"
msgstr "界面创建函数："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:595
#, fuzzy
msgid "Header file for interface creation functions"
msgstr "界面创建函数："

# SUN NEW TRANSLATION
#. Handler source file.
#: ../glade/glade_project_options.c:598
msgid "Signal Handler & Callback Functions:"
msgstr "信号处理程序和回调函数："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:610
msgid ""
"The file in which the empty signal handler and callback functions are written"
msgstr "编写空的信号处理程序和回调函数的文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:627
msgid ""
"The file in which the declarations of the signal handler and callback "
"functions are written"
msgstr "编写信号处理程序和回调函数的声明的文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:640
#, fuzzy
msgid "Source file for signal handler and callback functions"
msgstr "信号处理程序和回调函数："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:641
#, fuzzy
msgid "Header file for signal handler and callback functions"
msgstr "编写空的信号处理程序和回调函数的文件"

# SUN NEW TRANSLATION
#. Support source file.
#: ../glade/glade_project_options.c:644
msgid "Support Functions:"
msgstr "支持函数："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:656
msgid "The file in which the support functions are written"
msgstr "编写支持函数的文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:673
msgid "The file in which the declarations of the support functions are written"
msgstr "编写支持函数的声明的文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:686
#, fuzzy
msgid "Source file for support functions"
msgstr "支持函数："

#: ../glade/glade_project_options.c:687
msgid "Header file for support functions"
msgstr ""

# SUN NEW TRANSLATION
#.
#. * libglade Options Page.
#.
#: ../glade/glade_project_options.c:693
msgid "LibGlade Options"
msgstr "LibGlade 选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:702
msgid "Translatable Strings:"
msgstr "可翻译字符串："

#: ../glade/glade_project_options.c:716
msgid "<b>Note:</b> this option is deprecated - use intltool instead."
msgstr ""

# SUN NEW TRANSLATION
#. Output translatable strings.
#: ../glade/glade_project_options.c:726
msgid "Save Translatable Strings"
msgstr "保存可翻译字符串"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:731
msgid ""
"If translatable strings are saved in a separate C source file, to enable "
"translation of interfaces loaded by libglade"
msgstr ""
"是否将可翻译字符串保存在一个单独的 C 语言源文件中，以便可以翻译由 libglade 载"
"入的界面"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:741
msgid "The C source file to save all translatable strings in"
msgstr "要在其中保存所有可翻译字符串的 C 语言源文件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:743 ../glade/gnome/gnomepixmap.c:79
msgid "File:"
msgstr "文件："

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1202
msgid "Select the Project Directory"
msgstr "选择项目目录"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1392 ../glade/glade_project_options.c:1402
#: ../glade/glade_project_options.c:1412
msgid "You need to set the Translatable Strings File option"
msgstr "需要设置“可翻译字符串文件”选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1396 ../glade/glade_project_options.c:1406
msgid "You need to set the Project Directory option"
msgstr "需要设置“项目目录”选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1398 ../glade/glade_project_options.c:1408
msgid "You need to set the Project File option"
msgstr "需要设置“项目文件”选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1414
msgid "You need to set the Project Name option"
msgstr "需要设置“项目名称”选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1416
msgid "You need to set the Program Name option"
msgstr "需要设置“程序名称”选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1419
msgid "You need to set the Source Directory option"
msgstr "需要设置“源目录”选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_options.c:1422
msgid "You need to set the Pixmaps Directory option"
msgstr "需要设置“像素图目录”选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:184
#, fuzzy, c-format
msgid ""
"Couldn't show help file: %s.\n"
"\n"
"Error: %s"
msgstr ""
"无法将文件：\n"
"  %s\n"
"重命名为：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:208 ../glade/glade_project_window.c:634
msgid "Create a new project"
msgstr "创建新项目"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:216 ../glade/glade_project_window.c:654
#: ../glade/glade_project_window.c:905
#, fuzzy
msgid "_Build"
msgstr "联编"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:217 ../glade/glade_project_window.c:665
msgid "Output the project source code"
msgstr "输出项目源代码"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:223 ../glade/glade_project_window.c:668
#, fuzzy
msgid "Op_tions..."
msgstr "选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:224 ../glade/glade_project_window.c:677
msgid "Edit the project options"
msgstr "编辑项目选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:239 ../glade/glade_project_window.c:716
msgid "Delete the selected widget"
msgstr "删除选定构件"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:727
msgid "Show _Palette"
msgstr "显示调色板(_P)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:257 ../glade/glade_project_window.c:732
msgid "Show the palette of widgets"
msgstr "显示构件的调色板"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:263 ../glade/glade_project_window.c:737
msgid "Show Property _Editor"
msgstr "显示属性编辑器(_E)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:264 ../glade/glade_project_window.c:743
msgid "Show the property editor"
msgstr "显示属性编辑器"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:270 ../glade/glade_project_window.c:747
msgid "Show Widget _Tree"
msgstr "显示构件树(_T)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:271 ../glade/glade_project_window.c:753
#: ../glade/main.c:82
msgid "Show the widget tree"
msgstr "显示构件树"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:277 ../glade/glade_project_window.c:757
msgid "Show _Clipboard"
msgstr "显示剪贴板(_C)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:278 ../glade/glade_project_window.c:763
#: ../glade/main.c:86
msgid "Show the clipboard"
msgstr "显示剪贴板"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:296
msgid "Show _Grid"
msgstr "显示网格(_G)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:297 ../glade/glade_project_window.c:799
msgid "Show the grid (in fixed containers only)"
msgstr "显示网格（仅限固定容器中）"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:303
msgid "_Snap to Grid"
msgstr "对齐网格(_S)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:304
msgid "Snap widgets to the grid"
msgstr "将构件与网格对齐"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:310 ../glade/glade_project_window.c:771
msgid "Show _Widget Tooltips"
msgstr "显示构件工具提示(_W)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:311 ../glade/glade_project_window.c:779
msgid "Show the tooltips of created widgets"
msgstr "显示已创建构件的工具提示"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:320 ../glade/glade_project_window.c:802
msgid "Set Grid _Options..."
msgstr "设置网格选项(_O)..."

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:321
msgid "Set the grid style and spacing"
msgstr "设置网格样式和间距"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:327 ../glade/glade_project_window.c:823
msgid "Set Snap O_ptions..."
msgstr "设置对齐选项(_P)..."

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:328
msgid "Set options for snapping to the grid"
msgstr "设置对齐网格的选项"

#: ../glade/glade_project_window.c:340
msgid "_FAQ"
msgstr ""

#: ../glade/glade_project_window.c:341
msgid "View the Glade FAQ"
msgstr ""

# SUN NEW TRANSLATION
#. create File menu
#: ../glade/glade_project_window.c:355 ../glade/glade_project_window.c:625
#, fuzzy
msgid "_Project"
msgstr "项目"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:366 ../glade/glade_project_window.c:872
#: ../glade/glade_project_window.c:1049
msgid "New Project"
msgstr "新项目"

#: ../glade/glade_project_window.c:371
msgid "Open"
msgstr "打开"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:371 ../glade/glade_project_window.c:877
#: ../glade/glade_project_window.c:1110
msgid "Open Project"
msgstr "打开项目"

#: ../glade/glade_project_window.c:376
msgid "Save"
msgstr "保存"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:376 ../glade/glade_project_window.c:881
#: ../glade/glade_project_window.c:1475
msgid "Save Project"
msgstr "保存项目"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:382
msgid "Options"
msgstr "选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:387
msgid "Build"
msgstr "联编"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:387
msgid "Build the Source Code"
msgstr "联编源代码"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:638
msgid "Open an existing project"
msgstr "打开现有项目"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:642
msgid "Save project"
msgstr "保存项目"

#: ../glade/glade_project_window.c:687
#, fuzzy
msgid "Quit Glade"
msgstr "Glade"

#: ../glade/glade_project_window.c:701
#, fuzzy
msgid "Cut the selected widget to the clipboard"
msgstr "您需要选择一个部件将数据粘贴入"

#: ../glade/glade_project_window.c:706
#, fuzzy
msgid "Copy the selected widget to the clipboard"
msgstr "您需要选择一个部件将数据粘贴入"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:711
#, fuzzy
msgid "Paste the widget from the clipboard over the selected widget"
msgstr "显示选定构件的属性"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:783
#, fuzzy
msgid "_Grid"
msgstr "显示网格(_G)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:791
#, fuzzy
msgid "_Show Grid"
msgstr "显示网格(_G)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:808
msgid "Set the spacing between grid lines"
msgstr "设置网格线之间的间距"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:811
#, fuzzy
msgid "S_nap to Grid"
msgstr "对齐网格(_S)"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:819
msgid "Snap widgets to the grid (in fixed containers only)"
msgstr "将构件与网格对齐（仅限固定容器中）"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:829
msgid "Set which parts of a widget snap to the grid"
msgstr "设置构件中的哪些部件与网格对齐"

#. Don't show these yet as we have no help pages.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Contents"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new_with_mnemonic (_("_Index"));
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#. menuitem = gtk_menu_item_new ();
#. gtk_container_add (GTK_CONTAINER (menu), menuitem);
#. gtk_widget_show (menuitem);
#.
#: ../glade/glade_project_window.c:854
#, fuzzy
msgid "_About..."
msgstr "关于"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:895
#, fuzzy
msgid "Optio_ns"
msgstr "选项"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:899
msgid "Write Source Code"
msgstr "编写源代码"

#: ../glade/glade_project_window.c:986 ../glade/glade_project_window.c:1691
#: ../glade/glade_project_window.c:1980
msgid "Glade"
msgstr "Glade"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:993
msgid "Are you sure you want to create a new project?"
msgstr "您确定要创建新项目？"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1053
#, fuzzy
msgid "New _GTK+ Project"
msgstr "新项目"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1054
#, fuzzy
msgid "New G_NOME Project"
msgstr "新项目"

#: ../glade/glade_project_window.c:1057
msgid "Which type of project do you want to create?"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1091
msgid "New project created."
msgstr "新项目已创建。"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1181
msgid "Project opened."
msgstr "项目已打开。"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1195
msgid "Error opening project."
msgstr "打开项目时出错。"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1259
msgid "Errors opening project file"
msgstr "打开项目文件时出错"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1265
msgid " errors opening project file:"
msgstr " 打开项目文件时出错："

#: ../glade/glade_project_window.c:1338
msgid ""
"There is no project currently open.\n"
"Create a new project with the Project/New command."
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1542
msgid "Error saving project"
msgstr "保存项目时出错"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1544
msgid "Error saving project."
msgstr "保存项目时出错。"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1550
msgid "Project saved."
msgstr "项目已保存。"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1620
msgid "Errors writing source code"
msgstr "编写源代码时出错"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1622
msgid "Error writing source."
msgstr "编写源代码时出错。"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1628
msgid "Source code written."
msgstr "源代码已编写。"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1659
msgid "System error message:"
msgstr "系统错误消息："

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:1698
#, fuzzy
msgid "Are you sure you want to quit?"
msgstr "您确定要创建新项目？"

#: ../glade/glade_project_window.c:1982 ../glade/glade_project_window.c:2042
msgid "(C) 1998-2002 Damon Chaplin"
msgstr ""

#: ../glade/glade_project_window.c:1983 ../glade/glade_project_window.c:2041
msgid "Glade is a User Interface Builder for GTK+ and GNOME."
msgstr ""

#: ../glade/glade_project_window.c:2012
#, fuzzy
msgid "About Glade"
msgstr "Glade"

# SUN NEW TRANSLATION
#: ../glade/glade_project_window.c:2097
msgid "<untitled>"
msgstr "<未命名>"

#: ../glade/gnome-db/gnomedbbrowser.c:135
msgid "Database Browser"
msgstr "数据库浏览器"

# SUN NEW TRANSLATION
#: ../glade/gnome-db/gnomedbcombo.c:124
msgid "Data-bound combo"
msgstr "数据绑定组合"

#: ../glade/gnome-db/gnomedbconnectprop.c:86
msgid "GnomeDbConnectionProperties"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome-db/gnomedbconnectsel.c:147
#, fuzzy
msgid "Connection Selector"
msgstr "字体选择"

#: ../glade/gnome-db/gnomedbdsnconfig.c:136
msgid "DSN Configurator"
msgstr "DSN配置器"

#: ../glade/gnome-db/gnomedbdsndruid.c:147
#, fuzzy
msgid "DSN Config Druid"
msgstr "DSN配置器"

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "Highlight text:"
msgstr ""

#: ../glade/gnome-db/gnomedbeditor.c:63
msgid "If selected, text will be highlighted inside the widget"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome-db/gnomedbeditor.c:178
#, fuzzy
msgid "GnomeDbEditor"
msgstr "GnomeDateEdit"

#: ../glade/gnome-db/gnomedberror.c:136
msgid "Database error viewer"
msgstr "数据库错误查看器"

#: ../glade/gnome-db/gnomedberrordlg.c:218
msgid "Database error dialog"
msgstr "数据库错误对话"

# SUN NEW TRANSLATION
#: ../glade/gnome-db/gnomedbform.c:147
#, fuzzy
msgid "Form"
msgstr "帧"

#: ../glade/gnome-db/gnomedbgraybar.c:59
msgid "Text inside the gray bar"
msgstr ""

#: ../glade/gnome-db/gnomedbgraybar.c:138
msgid "Gray Bar"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome-db/gnomedbgrid.c:132
msgid "Data-bound grid"
msgstr "数据绑定网格"

# SUN NEW TRANSLATION
#: ../glade/gnome-db/gnomedblist.c:136
msgid "Data-bound list"
msgstr "数据绑定列表"

#: ../glade/gnome-db/gnomedblogin.c:136
msgid "Database login widget"
msgstr "数据库登录部件"

#: ../glade/gnome-db/gnomedblogindlg.c:76
msgid "Login"
msgstr "登录"

#: ../glade/gnome-db/gnomedblogindlg.c:219
msgid "Database login dialog"
msgstr "数据库登录对话"

#: ../glade/gnome-db/gnomedbprovidersel.c:147
#, fuzzy
msgid "Provider Selector"
msgstr "Gnome 纸张选择器"

#: ../glade/gnome-db/gnomedbquerybuilder.c:86
msgid "GnomeDbQueryBuilder"
msgstr ""

#: ../glade/gnome-db/gnomedbsourcesel.c:147
#, fuzzy
msgid "Data Source Selector"
msgstr "源代码目录："

# SUN CHANGED MESSAGE
#: ../glade/gnome-db/gnomedbtableeditor.c:133
#, fuzzy
msgid "Table Editor "
msgstr "表编辑器"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:231
msgid "Allow Floating:"
msgstr "允许浮动："

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:232
msgid "If floating dock items are allowed"
msgstr "是否允许浮动停靠项"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:278
msgid "Add dock band on top"
msgstr "在顶部添加停靠带"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:285
msgid "Add dock band on bottom"
msgstr "在底部添加停靠带"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:292
msgid "Add dock band on left"
msgstr "在左侧添加停靠带"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:299
msgid "Add dock band on right"
msgstr "在右侧添加停靠带"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:306
msgid "Add floating dock item"
msgstr "添加浮动停靠项"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodock.c:495
msgid "Gnome Dock"
msgstr "Gnome 停靠"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:165
msgid "Locked:"
msgstr "锁定："

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:166
msgid "If the dock item is locked in position"
msgstr "停靠项是否锁定位置"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:167
msgid "Exclusive:"
msgstr "独占："

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:168
msgid "If the dock item is always the only item in its band"
msgstr "停靠项是否始终是该停靠带内的唯一项"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:169
msgid "Never Floating:"
msgstr "从不浮动："

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:170
msgid "If the dock item is never allowed to float in its own window"
msgstr "是否从不允许停靠项在它自己的窗口内浮动"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:171
msgid "Never Vertical:"
msgstr "从不垂直："

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:172
msgid "If the dock item is never allowed to be vertical"
msgstr "是否从不允许停靠项处于垂直状态"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:173
msgid "Never Horizontal:"
msgstr "从不水平："

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:174
msgid "If the dock item is never allowed to be horizontal"
msgstr "是否从不允许停靠项处于水平状态"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:177
msgid "The type of shadow around the dock item"
msgstr "停靠项周围的阴影类型"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:180
msgid "The orientation of a floating dock item"
msgstr "浮动停靠项的方向"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:428
msgid "Add dock item before"
msgstr "在以下对象之前添加停靠项"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:435
msgid "Add dock item after"
msgstr "在以下对象之后添加停靠项"

# SUN NEW TRANSLATION
#: ../glade/gnome/bonobodockitem.c:771
msgid "Gnome Dock Item"
msgstr "Gnome 停靠项"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeabout.c:139
msgid ""
"Additional information, such as a description of the package and its home "
"page on the web"
msgstr "附加信息，例如软件包的说明及其在网上的主页"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeabout.c:539
msgid "Gnome About Dialog"
msgstr "Gnome 关于对话框"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:170
msgid "New File"
msgstr "新建文件"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:172
msgid "Open File"
msgstr "打开文件"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:174
msgid "Save File"
msgstr "保存文件"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:203
msgid "Status Bar:"
msgstr "状态栏："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:204
msgid "If the window has a status bar"
msgstr "窗口是否有状态栏"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:205
msgid "Store Config:"
msgstr "库配置："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:206
msgid "If the layout is saved and restored automatically"
msgstr "是否自动保存并恢复布局"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeapp.c:442
msgid "Gnome Application Window"
msgstr "Gnome 应用程序窗口"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeappbar.c:56
msgid "Status Message."
msgstr "状态消息。"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeappbar.c:69
msgid "Progress:"
msgstr "进度："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeappbar.c:70
msgid "If the app bar has a progress indicator"
msgstr "应用程序栏是否有进度指示器"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeappbar.c:71
msgid "Status:"
msgstr "状态："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeappbar.c:72
msgid "If the app bar has an area for status messages and user input"
msgstr "应用程序栏是否有用于显示状态消息和用户输入的区域"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeappbar.c:184
msgid "Gnome Application Bar"
msgstr "Gnome 应用程序栏"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:68
msgid "Anti-Aliased:"
msgstr "平滑锯齿："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:69
msgid "If the canvas is anti-aliased, to smooth the edges of text and graphics"
msgstr "画布是否能平滑锯齿，以便平滑文本和图形的边缘"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:70
msgid "X1:"
msgstr "X1："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:70
msgid "The minimum x coordinate"
msgstr "X 坐标的最小值"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:71
msgid "Y1:"
msgstr "Y1："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:71
msgid "The minimum y coordinate"
msgstr "Y 坐标的最小值"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:72
msgid "X2:"
msgstr "X2："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:72
msgid "The maximum x coordinate"
msgstr "X 坐标的最大值"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:73
msgid "Y2:"
msgstr "Y2："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:73
msgid "The maximum y coordinate"
msgstr "Y 坐标的最大值"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:75
msgid "Pixels Per Unit:"
msgstr "每单位像素数："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:76
msgid "The number of pixels corresponding to one unit"
msgstr "对应于一个单位的像素数"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecanvas.c:239
msgid "GnomeCanvas"
msgstr "GnomeCanvas"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecolorpicker.c:68
msgid "Dither:"
msgstr "抖动："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecolorpicker.c:69
msgid "If the sample should use dithering to be more accurate"
msgstr "取样是否使用抖动以使结果更精确"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecolorpicker.c:160
msgid "Pick a color"
msgstr "选择颜色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecolorpicker.c:219
msgid "Gnome Color Picker"
msgstr "Gnome 颜色选择器"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecontrol.c:160
#, fuzzy
msgid "Couldn't create the Bonobo control"
msgstr ""
"无法创建文件：\n"
"  %s\n"

#: ../glade/gnome/gnomecontrol.c:249
msgid "New Bonobo Control"
msgstr ""

#: ../glade/gnome/gnomecontrol.c:262
msgid "Select a Bonobo Control"
msgstr ""

#: ../glade/gnome/gnomecontrol.c:290
msgid "OAFIID"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomecontrol.c:295 ../glade/property.c:3896
msgid "Description"
msgstr "说明"

#: ../glade/gnome/gnomecontrol.c:339
msgid "Bonobo Control"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:70
msgid "Show Time:"
msgstr "显示时间："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:71
msgid "If the time is shown as well as the date"
msgstr "是否显示日期及时间"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:72
msgid "24 Hour Format:"
msgstr "24 小时格式："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:73
msgid "If the time is shown in 24-hour format"
msgstr "是否以 24 小时格式显示时间"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:76
msgid "Lower Hour:"
msgstr "较低小时："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:77
msgid "The lowest hour to show in the popup"
msgstr "在弹出菜单中显示的最低小时"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:79
msgid "Upper Hour:"
msgstr "较高小时："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:80
msgid "The highest hour to show in the popup"
msgstr "在弹出菜单中显示的最高小时"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedateedit.c:298
msgid "GnomeDateEdit"
msgstr "GnomeDateEdit"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedialog.c:152 ../glade/gnome/gnomemessagebox.c:189
msgid "Auto Close:"
msgstr "自动关闭："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedialog.c:153 ../glade/gnome/gnomemessagebox.c:190
msgid "If the dialog closes when any button is clicked"
msgstr "是否在单击任意按钮时关闭对话框"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedialog.c:154 ../glade/gnome/gnomemessagebox.c:191
msgid "Hide on Close:"
msgstr "关闭时隐藏："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedialog.c:155 ../glade/gnome/gnomemessagebox.c:192
msgid "If the dialog is hidden when it is closed, instead of being destroyed"
msgstr "是否在关闭对话框时隐藏它，而不摧毁它"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedialog.c:341
msgid "Gnome Dialog Box"
msgstr "Gnome 对话框"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruid.c:91
msgid "New Gnome Druid"
msgstr "新建 Gnome 专家"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruid.c:190
#, fuzzy
msgid "Show Help"
msgstr "显示文本："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruid.c:190
#, fuzzy
msgid "Display the help button."
msgstr "按钮之间的间隔"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruid.c:255
msgid "Add Start Page"
msgstr "添加开始页面"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruid.c:270
msgid "Add Finish Page"
msgstr "添加完成页面"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruid.c:485
msgid "Druid"
msgstr "专家"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:94
#: ../glade/gnome/gnomedruidpagestandard.c:86
msgid "The title of the page"
msgstr "页面标题"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:96
msgid "The main text of the page, introducing people to the druid."
msgstr "该页面的主要文本，向专家介绍人员。"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:98
#: ../glade/gnome/gnomedruidpagestandard.c:87
msgid "Title Color:"
msgstr "标题颜色："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:99
#: ../glade/gnome/gnomedruidpagestandard.c:88
msgid "The color of the title text"
msgstr "标题文本的颜色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:100
msgid "Text Color:"
msgstr "文本颜色："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:101
msgid "The color of the main text"
msgstr "主要文本的颜色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:103
#: ../glade/gnome/gnomedruidpagestandard.c:92
msgid "The background color of the page"
msgstr "页面的背景色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:104
#: ../glade/gnome/gnomedruidpagestandard.c:93
msgid "Logo Back. Color:"
msgstr "徽标背景色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:105
#: ../glade/gnome/gnomedruidpagestandard.c:94
msgid "The background color around the logo"
msgstr "徽标周围的背景色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:106
msgid "Text Box Color:"
msgstr "文本框颜色:"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:107
msgid "The background color of the main text area"
msgstr "主文本区域的背景色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:108
#: ../glade/gnome/gnomedruidpagestandard.c:95
#, fuzzy
msgid "Logo Image:"
msgstr "图像"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:109
#: ../glade/gnome/gnomedruidpagestandard.c:96
msgid "The logo to display in the top-right of the page"
msgstr "显示在页面右上方的徽标"

#: ../glade/gnome/gnomedruidpageedge.c:110
msgid "Side Watermark:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:111
#, fuzzy
msgid "The main image to display on the side of the page."
msgstr "显示在页面右上方的徽标"

#: ../glade/gnome/gnomedruidpageedge.c:112
#: ../glade/gnome/gnomedruidpagestandard.c:97
msgid "Top Watermark:"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:113
#, fuzzy
msgid "The watermark to display at the top of the page."
msgstr "显示在页面右上方的徽标"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpageedge.c:522
#, fuzzy
msgid "Druid Start or Finish Page"
msgstr "专家标准页面"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpagestandard.c:89
#, fuzzy
msgid "Contents Back. Color:"
msgstr "徽标背景色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpagestandard.c:90
#, fuzzy
msgid "The background color around the title"
msgstr "徽标周围的背景色"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpagestandard.c:98
#, fuzzy
msgid "The image to display along the top of the page"
msgstr "显示在页面右上方的徽标"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomedruidpagestandard.c:447
msgid "Druid Standard Page"
msgstr "专家标准页面"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeentry.c:71 ../glade/gnome/gnomefileentry.c:96
#: ../glade/gnome/gnomeiconentry.c:74 ../glade/gnome/gnomepixmapentry.c:77
msgid "History ID:"
msgstr "历史 ID："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeentry.c:72 ../glade/gnome/gnomefileentry.c:97
#: ../glade/gnome/gnomeiconentry.c:75 ../glade/gnome/gnomepixmapentry.c:78
msgid "The ID to save the history entries under"
msgstr "用于保存历史项的 ID"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeentry.c:73 ../glade/gnome/gnomefileentry.c:98
#: ../glade/gnome/gnomeiconentry.c:76 ../glade/gnome/gnomepixmapentry.c:79
msgid "Max Saved:"
msgstr "最大保存数量："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeentry.c:74 ../glade/gnome/gnomefileentry.c:99
#: ../glade/gnome/gnomeiconentry.c:77 ../glade/gnome/gnomepixmapentry.c:80
msgid "The maximum number of history entries saved"
msgstr "保存的历史项的最大数量"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomeentry.c:210
msgid "Gnome Entry"
msgstr "Gnome 输入"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefileentry.c:102 ../glade/gnome/gnomeiconentry.c:73
#: ../glade/gnome/gnomepixmapentry.c:83
msgid "The title of the file selection dialog"
msgstr "“文件选择”对话框的标题"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefileentry.c:103
msgid "Directory:"
msgstr "目录："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefileentry.c:104
msgid "If a directory is needed rather than a file"
msgstr "是否需要目录，而不是文件"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefileentry.c:106 ../glade/gnome/gnomepixmapentry.c:85
msgid "If the file selection dialog should be modal"
msgstr "“文件选择”对话框是否为模态"

#: ../glade/gnome/gnomefileentry.c:107 ../glade/gnome/gnomepixmapentry.c:86
msgid "Use FileChooser:"
msgstr ""

#: ../glade/gnome/gnomefileentry.c:108 ../glade/gnome/gnomepixmapentry.c:87
msgid "Use the new GtkFileChooser widget instead of GtkFileSelection"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefileentry.c:367
msgid "Gnome File Entry"
msgstr "Gnome 文件输入"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefontpicker.c:98
msgid "The preview text to show in the font selection dialog"
msgstr "在“字体选择”对话框中显示的预览文本"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefontpicker.c:99
msgid "Mode:"
msgstr "模式："

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefontpicker.c:100
msgid "What to display in the font picker button"
msgstr "“字体选择器”按钮中显示的内容"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomefontpicker.c:107
msgid "The size of the font to use in the font picker button"
msgstr "在“字体选择器”按钮中使用的字体大小"

#: ../glade/gnome/gnomefontpicker.c:392
msgid "Gnome Font Picker"
msgstr "Gnome 字体选择器"

#: ../glade/gnome/gnomehref.c:66
msgid "URL:"
msgstr "URL："

#: ../glade/gnome/gnomehref.c:67
msgid "The URL to display when the button is clicked"
msgstr "当按钮被点击时显示的 URL"

#: ../glade/gnome/gnomehref.c:69
msgid "The text to display in the button"
msgstr "显示在按钮上的文字"

#: ../glade/gnome/gnomehref.c:206
msgid "Gnome HRef Link Button"
msgstr "Gnome 超文本链接按钮"

# SUN CHANGED MESSAGE
#: ../glade/gnome/gnomeiconentry.c:208
msgid "Gnome Icon Entry"
msgstr "Gnome 图标输入"

#: ../glade/gnome/gnomeiconlist.c:175
msgid "The selection mode"
msgstr "选择模式"

#: ../glade/gnome/gnomeiconlist.c:177
msgid "Icon Width:"
msgstr "图标宽度："

#: ../glade/gnome/gnomeiconlist.c:178
msgid "The width of each icon"
msgstr "每个图标的宽度"

#: ../glade/gnome/gnomeiconlist.c:181
msgid "The number of pixels between rows of icons"
msgstr "图标行之间的间距(像素数)"

#: ../glade/gnome/gnomeiconlist.c:184
msgid "The number of pixels between columns of icons"
msgstr "图标列之间的间距(像素数)"

#: ../glade/gnome/gnomeiconlist.c:187
msgid "Icon Border:"
msgstr "图标边界："

#: ../glade/gnome/gnomeiconlist.c:188
msgid "The number of pixels around icons (unused?)"
msgstr "图标周围的像素(未用？)"

#: ../glade/gnome/gnomeiconlist.c:191
msgid "Text Spacing:"
msgstr "文字间距："

#: ../glade/gnome/gnomeiconlist.c:192
msgid "The number of pixels between the text and the icon"
msgstr "在文字和图标直接的间距(像素数)"

#: ../glade/gnome/gnomeiconlist.c:194
msgid "Text Editable:"
msgstr "可编辑文字："

#: ../glade/gnome/gnomeiconlist.c:195
msgid "If the icon text can be edited by the user"
msgstr "图标文字是否可编辑"

#: ../glade/gnome/gnomeiconlist.c:196
msgid "Text Static:"
msgstr "静态文字："

#: ../glade/gnome/gnomeiconlist.c:197
msgid ""
"If the icon text is static, in which case it will not be copied by the "
"GnomeIconList"
msgstr "图标文字是否静态，如果是静态的，GnomeIconList 将不会复制它"

#: ../glade/gnome/gnomeiconlist.c:461
msgid "Icon List"
msgstr "图标列表"

#: ../glade/gnome/gnomeiconselection.c:154
msgid "Icon Selection"
msgstr "选择图标"

#: ../glade/gnome/gnomemessagebox.c:174
msgid "Message Type:"
msgstr "消息类型："

#: ../glade/gnome/gnomemessagebox.c:175
msgid "The type of the message box"
msgstr "消息框类型"

#: ../glade/gnome/gnomemessagebox.c:177
msgid "Message:"
msgstr "消息："

#: ../glade/gnome/gnomemessagebox.c:177
msgid "The message to display"
msgstr "要显示的消息"

#: ../glade/gnome/gnomemessagebox.c:498
msgid "Gnome Message Box"
msgstr "Gnome 消息框"

# SUN NEW TRANSLATION
#: ../glade/gnome/gnomepixmap.c:79
msgid "The pixmap filename"
msgstr "像素图文件名"

#: ../glade/gnome/gnomepixmap.c:80
msgid "Scaled:"
msgstr "缩放："

# SUN CHANGED MESSAGE
#: ../glade/gnome/gnomepixmap.c:80
msgid "If the pixmap is scaled"
msgstr "是否缩放像素图"

#: ../glade/gnome/gnomepixmap.c:81
msgid "Scaled Width:"
msgstr "缩放宽度："

# SUN CHANGED MESSAGE
#: ../glade/gnome/gnomepixmap.c:82
msgid "The width to scale the pixmap to"
msgstr "像素图缩放后宽度"

#: ../glade/gnome/gnomepixmap.c:84
msgid "Scaled Height:"
msgstr "缩放高度："

# SUN CHANGED MESSAGE
#: ../glade/gnome/gnomepixmap.c:85
msgid "The height to scale the pixmap to"
msgstr "像素图缩放后高度"

# SUN CHANGED MESSAGE
#: ../glade/gnome/gnomepixmap.c:346
msgid "Gnome Pixmap"
msgstr "Gnome 像素图"

#: ../glade/gnome/gnomepixmapentry.c:75
msgid "Preview:"
msgstr "预览："

# SUN CHANGED MESSAGE
#: ../glade/gnome/gnomepixmapentry.c:76
msgid "If a small preview of the pixmap is displayed"
msgstr "是否显示像素图的预览"

#: ../glade/gnome/gnomepixmapentry.c:303
msgid "GnomePixmapEntry"
msgstr "GnomePixmapEntry"

#: ../glade/gnome/gnomepropertybox.c:112
msgid "New GnomePropertyBox"
msgstr "新建 GnomePropertyBox"

#: ../glade/gnome/gnomepropertybox.c:365
msgid "Property Dialog Box"
msgstr "属性对话框"

# SUN NEW TRANSLATION
#: ../glade/main.c:70
msgid "Write the source code and exit"
msgstr "编写源代码并退出"

# SUN NEW TRANSLATION
#: ../glade/main.c:74
#, fuzzy
msgid "Start with the palette hidden"
msgstr "显示构件的调色板"

# SUN NEW TRANSLATION
#: ../glade/main.c:78
#, fuzzy
msgid "Start with the property editor hidden"
msgstr "显示属性编辑器"

# SUN NEW TRANSLATION
#: ../glade/main.c:436
msgid ""
"glade: The XML file must be set for the '-w' or '--write-source' option.\n"
msgstr "glade：必须为“-w”或“--write-source”选项设置 XML 文件。\n"

# SUN NEW TRANSLATION
#: ../glade/main.c:450
msgid "glade: Error loading XML file.\n"
msgstr "glade：载入 XML 文件时出错。\n"

# SUN NEW TRANSLATION
#: ../glade/main.c:457
msgid "glade: Error writing source.\n"
msgstr "glade：编写源代码时出错。\n"

# SUN NEW TRANSLATION
#: ../glade/palette.c:60
msgid "Palette"
msgstr "调色板"

# SUN NEW TRANSLATION
#: ../glade/property.c:73
msgid "private"
msgstr "私有"

# SUN NEW TRANSLATION
#: ../glade/property.c:73
msgid "protected"
msgstr "被保护"

# SUN NEW TRANSLATION
#: ../glade/property.c:73
msgid "public"
msgstr "公共"

# SUN NEW TRANSLATION
#: ../glade/property.c:102
msgid "Prelight"
msgstr "预先亮起"

# SUN NEW TRANSLATION
#: ../glade/property.c:103
msgid "Selected"
msgstr "选定"

# SUN NEW TRANSLATION
#: ../glade/property.c:103
msgid "Insens"
msgstr "Insens"

# SUN NEW TRANSLATION
#: ../glade/property.c:467
msgid "When the window needs redrawing"
msgstr "当窗口需要重绘时"

# SUN NEW TRANSLATION
#: ../glade/property.c:468
msgid "When the mouse moves"
msgstr "当鼠标移动时"

# SUN NEW TRANSLATION
#: ../glade/property.c:469
msgid "Mouse movement hints"
msgstr "鼠标移动提示"

# SUN NEW TRANSLATION
#: ../glade/property.c:470
msgid "Mouse movement with any button pressed"
msgstr "在按下任何按钮时移动鼠标"

# SUN NEW TRANSLATION
#: ../glade/property.c:471
msgid "Mouse movement with button 1 pressed"
msgstr "在按下按钮 1 时移动鼠标"

# SUN NEW TRANSLATION
#: ../glade/property.c:472
msgid "Mouse movement with button 2 pressed"
msgstr "在按下按钮 2 时移动鼠标"

# SUN NEW TRANSLATION
#: ../glade/property.c:473
msgid "Mouse movement with button 3 pressed"
msgstr "在按下按钮 3 时移动鼠标"

# SUN NEW TRANSLATION
#: ../glade/property.c:474
msgid "Any mouse button pressed"
msgstr "按下任意鼠标按钮"

# SUN NEW TRANSLATION
#: ../glade/property.c:475
msgid "Any mouse button released"
msgstr "释放任意鼠标按钮"

# SUN NEW TRANSLATION
#: ../glade/property.c:476
msgid "Any key pressed"
msgstr "按下任意键"

# SUN NEW TRANSLATION
#: ../glade/property.c:477
msgid "Any key released"
msgstr "释放任意键"

# SUN NEW TRANSLATION
#: ../glade/property.c:478
msgid "When the mouse enters the window"
msgstr "当鼠标进入窗口时"

# SUN NEW TRANSLATION
#: ../glade/property.c:479
msgid "When the mouse leaves the window"
msgstr "当鼠标离开窗口时"

# SUN NEW TRANSLATION
#: ../glade/property.c:480
msgid "Any change in input focus"
msgstr "输入焦点中的任何更改"

# SUN NEW TRANSLATION
#: ../glade/property.c:481
msgid "Any change in window structure"
msgstr "窗口结构中的任何更改"

# SUN NEW TRANSLATION
#: ../glade/property.c:482
msgid "Any change in X Windows property"
msgstr "X Windows 属性中的任何更改"

# SUN NEW TRANSLATION
#: ../glade/property.c:483
msgid "Any change in visibility"
msgstr "可见性的任何更改"

# SUN NEW TRANSLATION
#: ../glade/property.c:484 ../glade/property.c:485
msgid "For cursors in XInput-aware programs"
msgstr "对于识别 XInput 的程序中的光标"

#: ../glade/property.c:596
msgid "Properties"
msgstr "属性"

# SUN NEW TRANSLATION
#: ../glade/property.c:620
msgid "Packing"
msgstr "打包"

# SUN NEW TRANSLATION
#: ../glade/property.c:625
msgid "Common"
msgstr "通用"

# SUN NEW TRANSLATION
#: ../glade/property.c:631
msgid "Style"
msgstr "样式"

# SUN NEW TRANSLATION
#: ../glade/property.c:637 ../glade/property.c:4640
msgid "Signals"
msgstr "信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:700 ../glade/property.c:721
msgid "Properties: "
msgstr "属性："

# SUN NEW TRANSLATION
#: ../glade/property.c:708 ../glade/property.c:732
msgid "Properties: <none>"
msgstr "属性：<无>"

# SUN NEW TRANSLATION
#: ../glade/property.c:778
msgid "Class:"
msgstr "类："

# SUN NEW TRANSLATION
#: ../glade/property.c:779
msgid "The class of the widget"
msgstr "构件类"

# SUN NEW TRANSLATION
#: ../glade/property.c:813
msgid "Width:"
msgstr "宽度："

#: ../glade/property.c:814
msgid ""
"The requested width of the widget (usually used to set the minimum width)"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/property.c:816
msgid "Height:"
msgstr "高度："

#: ../glade/property.c:817
msgid ""
"The requested height of the widget (usually used to set the minimum height)"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/property.c:820
msgid "Visible:"
msgstr "可见："

# SUN NEW TRANSLATION
#: ../glade/property.c:821
msgid "If the widget is initially visible"
msgstr "构件最初是否可见"

# SUN NEW TRANSLATION
#: ../glade/property.c:822
msgid "Sensitive:"
msgstr "敏感："

# SUN NEW TRANSLATION
#: ../glade/property.c:823
msgid "If the widget responds to input"
msgstr "构件是否响应输入"

# SUN NEW TRANSLATION
#: ../glade/property.c:825
msgid "The tooltip to display if the mouse lingers over the widget"
msgstr "当鼠标滑过构件时要显示的工具提示"

# SUN NEW TRANSLATION
#: ../glade/property.c:827
msgid "Can Default:"
msgstr "可以默认："

# SUN NEW TRANSLATION
#: ../glade/property.c:828
#, fuzzy
msgid "If the widget can be the default action in a dialog"
msgstr "该构件是否可以接受输入焦点"

# SUN NEW TRANSLATION
#: ../glade/property.c:829
msgid "Has Default:"
msgstr "有默认值："

# SUN NEW TRANSLATION
#: ../glade/property.c:830
#, fuzzy
msgid "If the widget is the default action in the dialog"
msgstr "该构件是否具有输入焦点"

# SUN NEW TRANSLATION
#: ../glade/property.c:831
msgid "Can Focus:"
msgstr "可以聚焦："

# SUN NEW TRANSLATION
#: ../glade/property.c:832
msgid "If the widget can accept the input focus"
msgstr "该构件是否可以接受输入焦点"

# SUN NEW TRANSLATION
#: ../glade/property.c:833
msgid "Has Focus:"
msgstr "有焦点："

# SUN NEW TRANSLATION
#: ../glade/property.c:834
msgid "If the widget has the input focus"
msgstr "该构件是否具有输入焦点"

# SUN NEW TRANSLATION
#: ../glade/property.c:836
msgid "Events:"
msgstr "事件："

# SUN NEW TRANSLATION
#: ../glade/property.c:837
msgid "The X events that the widget receives"
msgstr "该构件接收的 X 事件"

# SUN NEW TRANSLATION
#: ../glade/property.c:839
msgid "Ext.Events:"
msgstr "扩展事件："

# SUN NEW TRANSLATION
#: ../glade/property.c:840
msgid "The X Extension events mode"
msgstr "X 扩展事件模式"

# SUN NEW TRANSLATION
#: ../glade/property.c:843
msgid "Accelerators:"
msgstr "加速键："

# SUN NEW TRANSLATION
#: ../glade/property.c:844
msgid "Defines the signals to emit when keys are pressed"
msgstr "定义当按键时要发出的信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:845
msgid "Edit..."
msgstr "编辑..."

# SUN NEW TRANSLATION
#: ../glade/property.c:867
msgid "Propagate:"
msgstr "传播："

# SUN NEW TRANSLATION
#: ../glade/property.c:868
msgid "Set True to propagate the style to the widget's children"
msgstr "设置为 True 会将样式传播到该构件的子构件"

# SUN NEW TRANSLATION
#: ../glade/property.c:869
msgid "Named Style:"
msgstr "命名的样式："

# SUN NEW TRANSLATION
#: ../glade/property.c:870
msgid "The name of the style, which can be shared by several widgets"
msgstr "样式的名称，可以由多个构件共享"

# SUN NEW TRANSLATION
#: ../glade/property.c:872
msgid "Font:"
msgstr "字体："

# SUN NEW TRANSLATION
#: ../glade/property.c:873
msgid "The font to use for any text in the widget"
msgstr "构件中的任意文本要使用的字体"

# SUN NEW TRANSLATION
#: ../glade/property.c:898
msgid "Copy All"
msgstr "全部复制"

# SUN NEW TRANSLATION
#: ../glade/property.c:926
msgid "Foreground:"
msgstr "前景："

# SUN NEW TRANSLATION
#: ../glade/property.c:926
msgid "Background:"
msgstr "背景："

# SUN NEW TRANSLATION
#: ../glade/property.c:926
msgid "Base:"
msgstr "基数："

# SUN NEW TRANSLATION
#: ../glade/property.c:928
msgid "Foreground color"
msgstr "前景色"

# SUN NEW TRANSLATION
#: ../glade/property.c:928
msgid "Background color"
msgstr "背景色"

# SUN NEW TRANSLATION
#: ../glade/property.c:928
msgid "Text color"
msgstr "文本颜色"

# SUN NEW TRANSLATION
#: ../glade/property.c:929
msgid "Base color"
msgstr "基色"

# SUN NEW TRANSLATION
#: ../glade/property.c:946
msgid "Back. Pixmap:"
msgstr "背景像素图："

# SUN NEW TRANSLATION
#: ../glade/property.c:947
msgid "The graphic to use as the background of the widget"
msgstr "用作构件背景的图形"

# SUN NEW TRANSLATION
#: ../glade/property.c:999
msgid "The file to write source code into"
msgstr "将源代码写入其中的文件"

# SUN NEW TRANSLATION
#: ../glade/property.c:1000
msgid "Public:"
msgstr "公共："

# SUN NEW TRANSLATION
#: ../glade/property.c:1001
msgid "If the widget is added to the component's data structure"
msgstr "是否将构件添加到组件的数据结构中"

# SUN NEW TRANSLATION
#: ../glade/property.c:1012
msgid "Separate Class:"
msgstr "单独的类："

# SUN NEW TRANSLATION
#: ../glade/property.c:1013
msgid "Put this widget's subtree in a separate class"
msgstr "将此构件的子树放入单独的类中"

# SUN NEW TRANSLATION
#: ../glade/property.c:1014
msgid "Separate File:"
msgstr "单独的文件："

# SUN NEW TRANSLATION
#: ../glade/property.c:1015
msgid "Put this widget in a separate source file"
msgstr "将此构件放入单独的源文件中"

# SUN NEW TRANSLATION
#: ../glade/property.c:1016
msgid "Visibility:"
msgstr "可见性："

# SUN NEW TRANSLATION
#: ../glade/property.c:1017
msgid "Visibility of widgets. Public widgets are exported to a global map."
msgstr "构件的可见性。公共构件将导出到全局映射中。"

# SUN NEW TRANSLATION
#: ../glade/property.c:1126
msgid "You need to select a color or background to copy"
msgstr "需要选择要复制的颜色或背景"

# SUN NEW TRANSLATION
#: ../glade/property.c:1145
msgid "Invalid selection in on_style_copy()"
msgstr "on_style_copy() 中的所选内容无效"

# SUN NEW TRANSLATION
#: ../glade/property.c:1187
msgid "You need to copy a color or background pixmap first"
msgstr "需要先复制颜色或背景像素图"

# SUN NEW TRANSLATION
#: ../glade/property.c:1193
msgid "You need to select a color to paste into"
msgstr "需要选择要粘贴到的颜色"

# SUN NEW TRANSLATION
#: ../glade/property.c:1203
msgid "You need to select a background pixmap to paste into"
msgstr "需要选择要粘贴到的背景像素图"

# SUN NEW TRANSLATION
#: ../glade/property.c:1455
msgid "Couldn't create pixmap from file\n"
msgstr "无法从文件创建像素图\n"

# SUN NEW TRANSLATION
#. List of current signal handlers - Signal/Handler/Data/Options
#: ../glade/property.c:1497
msgid "Signal"
msgstr "信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:1499
msgid "Data"
msgstr "数据"

# SUN NEW TRANSLATION
#: ../glade/property.c:1500
msgid "After"
msgstr "之后"

# SUN NEW TRANSLATION
#: ../glade/property.c:1501
msgid "Object"
msgstr "对象"

# SUN NEW TRANSLATION
#: ../glade/property.c:1532 ../glade/property.c:1696
msgid "Signal:"
msgstr "信号："

# SUN NEW TRANSLATION
#: ../glade/property.c:1533
msgid "The signal to add a handler for"
msgstr "为以下对象添加处理程序的信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:1547
msgid "The function to handle the signal"
msgstr "处理信号的函数"

# SUN NEW TRANSLATION
#: ../glade/property.c:1550
msgid "Data:"
msgstr "数据："

# SUN NEW TRANSLATION
#: ../glade/property.c:1551
msgid "The data passed to the handler"
msgstr "传递给处理程序的数据"

# SUN NEW TRANSLATION
#: ../glade/property.c:1552
msgid "Object:"
msgstr "对象："

# SUN NEW TRANSLATION
#: ../glade/property.c:1553
msgid "The object which receives the signal"
msgstr "接收信号的对象"

# SUN NEW TRANSLATION
#: ../glade/property.c:1554
msgid "After:"
msgstr "之后："

# SUN NEW TRANSLATION
#: ../glade/property.c:1555
msgid "If the handler runs after the class function"
msgstr "处理程序是否在类函数之后运行"

#: ../glade/property.c:1568
msgid "Add"
msgstr "添加"

# SUN NEW TRANSLATION
#: ../glade/property.c:1574
msgid "Update"
msgstr "更新"

#: ../glade/property.c:1586
msgid "Clear"
msgstr "清除"

# SUN NEW TRANSLATION
#: ../glade/property.c:1636
msgid "Accelerators"
msgstr "加速键"

# SUN NEW TRANSLATION
#. List of current accelerators - Mods/Keys/Signals
#: ../glade/property.c:1649
msgid "Mod"
msgstr "Mod"

# SUN NEW TRANSLATION
#: ../glade/property.c:1650
msgid "Key"
msgstr "键"

# SUN NEW TRANSLATION
#: ../glade/property.c:1651
msgid "Signal to emit"
msgstr "要发出的信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:1695
msgid "The accelerator key"
msgstr "加速键"

# SUN NEW TRANSLATION
#: ../glade/property.c:1697
msgid "The signal to emit when the accelerator is pressed"
msgstr "当按下加速键时要发出的信号"

#: ../glade/property.c:1846
msgid "Edit Text Property"
msgstr ""

#: ../glade/property.c:1884
msgid "<b>_Text:</b>"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/property.c:1894
#, fuzzy
msgid "T_ranslatable"
msgstr "可翻译字符串："

#: ../glade/property.c:1898
msgid "Has Context _Prefix"
msgstr ""

#: ../glade/property.c:1924
msgid "<b>Co_mments For Translators:</b>"
msgstr ""

# SUN NEW TRANSLATION
#: ../glade/property.c:3886
msgid "Select X Events"
msgstr "选择 X 事件"

# SUN NEW TRANSLATION
#: ../glade/property.c:3895
msgid "Event Mask"
msgstr "事件掩码"

# SUN NEW TRANSLATION
#: ../glade/property.c:4025 ../glade/property.c:4074
msgid "You need to set the accelerator key"
msgstr "需要设置加速键"

# SUN NEW TRANSLATION
#: ../glade/property.c:4032 ../glade/property.c:4081
msgid "You need to set the signal to emit"
msgstr "需要设置要发出的信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:4308 ../glade/property.c:4364
msgid "You need to set the signal name"
msgstr "需要设置信号名称"

# SUN NEW TRANSLATION
#: ../glade/property.c:4315 ../glade/property.c:4371
msgid "You need to set the handler for the signal"
msgstr "需要设置信号的处理程序"

# SUN NEW TRANSLATION
#. This groups the signals by class, e.g. 'GtkButton signals'.
#: ../glade/property.c:4574
#, c-format
msgid "%s signals"
msgstr "%s 信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:4631
msgid "Select Signal"
msgstr "选择信号"

# SUN NEW TRANSLATION
#: ../glade/property.c:4827
msgid "Value:"
msgstr "值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4827
msgid "Min:"
msgstr "最小值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4827
msgid "Step Inc:"
msgstr "步进增量："

# SUN NEW TRANSLATION
#: ../glade/property.c:4828
msgid "Page Inc:"
msgstr "页面增量："

# SUN NEW TRANSLATION
#: ../glade/property.c:4828
msgid "Page Size:"
msgstr "页面大小："

# SUN NEW TRANSLATION
#: ../glade/property.c:4830
msgid "H Value:"
msgstr "水平值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4830
msgid "H Min:"
msgstr "水平最小值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4830
msgid "H Max:"
msgstr "水平最大值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4830
msgid "H Step Inc:"
msgstr "水平步进增量："

# SUN NEW TRANSLATION
#: ../glade/property.c:4831
msgid "H Page Inc:"
msgstr "水平页面增量："

# SUN NEW TRANSLATION
#: ../glade/property.c:4831
msgid "H Page Size:"
msgstr "水平页面大小："

# SUN NEW TRANSLATION
#: ../glade/property.c:4833
msgid "V Value:"
msgstr "垂直值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4833
msgid "V Min:"
msgstr "垂直最小值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4833
msgid "V Max:"
msgstr "垂直最大值："

# SUN NEW TRANSLATION
#: ../glade/property.c:4833
msgid "V Step Inc:"
msgstr "垂直步进增量："

# SUN NEW TRANSLATION
#: ../glade/property.c:4834
msgid "V Page Inc:"
msgstr "垂直页面增量："

# SUN NEW TRANSLATION
#: ../glade/property.c:4834
msgid "V Page Size:"
msgstr "垂直页面大小："

# SUN NEW TRANSLATION
#: ../glade/property.c:4837
msgid "The initial value"
msgstr "初始值"

# SUN NEW TRANSLATION
#: ../glade/property.c:4838
msgid "The minimum value"
msgstr "最小值"

# SUN NEW TRANSLATION
#: ../glade/property.c:4839
msgid "The maximum value"
msgstr "最大值"

# SUN NEW TRANSLATION
#: ../glade/property.c:4840
msgid "The step increment"
msgstr "步进增量"

# SUN NEW TRANSLATION
#: ../glade/property.c:4841
msgid "The page increment"
msgstr "页面增量"

# SUN NEW TRANSLATION
#: ../glade/property.c:4842
msgid "The page size"
msgstr "页面大小"

# SUN NEW TRANSLATION
#: ../glade/property.c:4997
msgid "The requested font is not available."
msgstr "所需的字体不可用。"

# SUN NEW TRANSLATION
#: ../glade/property.c:5046
msgid "Select Named Style"
msgstr "选择命名的样式"

# SUN NEW TRANSLATION
#: ../glade/property.c:5057
msgid "Styles"
msgstr "样式"

# SUN NEW TRANSLATION
#: ../glade/property.c:5116
msgid "Rename"
msgstr "重命名"

#: ../glade/property.c:5144
msgid "Cancel"
msgstr "取消"

# SUN NEW TRANSLATION
#: ../glade/property.c:5264
msgid "New Style:"
msgstr "新样式："

# SUN NEW TRANSLATION
#: ../glade/property.c:5278 ../glade/property.c:5399
msgid "Invalid style name"
msgstr "无效的样式名称"

# SUN NEW TRANSLATION
#: ../glade/property.c:5286 ../glade/property.c:5409
msgid "That style name is already in use"
msgstr "该样式名称已被使用"

# SUN NEW TRANSLATION
#: ../glade/property.c:5384
msgid "Rename Style To:"
msgstr "将样式重命名为："

# SUN NEW TRANSLATION
#: ../glade/save.c:139 ../glade/source.c:2771
#, c-format
msgid ""
"Couldn't rename file:\n"
"  %s\n"
"to:\n"
"  %s\n"
msgstr ""
"无法将文件：\n"
"  %s\n"
"重命名为：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/save.c:174 ../glade/save.c:225 ../glade/save.c:947
#: ../glade/source.c:358 ../glade/source.c:373 ../glade/source.c:391
#: ../glade/source.c:404 ../glade/source.c:815 ../glade/source.c:1043
#: ../glade/source.c:1134 ../glade/source.c:1328 ../glade/source.c:1423
#: ../glade/source.c:1643 ../glade/source.c:1732 ../glade/source.c:1784
#: ../glade/source.c:1848 ../glade/source.c:1895 ../glade/source.c:2032
#: ../glade/utils.c:1147
#, c-format
msgid ""
"Couldn't create file:\n"
"  %s\n"
msgstr ""
"无法创建文件：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/save.c:848
msgid "Error writing XML file\n"
msgstr "编写 XML 文件时出错\n"

# SUN NEW TRANSLATION
#: ../glade/save.c:953
#, c-format
msgid ""
"/*\n"
" * Translatable strings file generated by Glade.\n"
" * Add this file to your project's POTFILES.in.\n"
" * DO NOT compile it as part of your application.\n"
" */\n"
"\n"
msgstr ""
"/*\n"
" * Glade 生成的可翻译字符串文件。\n"
" * 将此文件添加到项目的 POTFILES.in 中。\n"
" * 不要将它作为应用程序的一部分进行编译。\n"
" */\n"
"\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:184
#, c-format
msgid ""
"Invalid interface source filename: %s\n"
"%s\n"
msgstr ""
"无效的界面源文件名：%s\n"
"%s\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:186
#, c-format
msgid ""
"Invalid interface header filename: %s\n"
"%s\n"
msgstr ""
"无效的界面头文件名：%s\n"
"%s\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:189
#, c-format
msgid ""
"Invalid callbacks source filename: %s\n"
"%s\n"
msgstr ""
"无效的回调源文件名：%s\n"
"%s\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:191
#, c-format
msgid ""
"Invalid callbacks header filename: %s\n"
"%s\n"
msgstr ""
"无效的回调头文件名：%s\n"
"%s\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:197
#, c-format
msgid ""
"Invalid support source filename: %s\n"
"%s\n"
msgstr ""
"无效的支持源文件名：%s\n"
"%s\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:199
#, c-format
msgid ""
"Invalid support header filename: %s\n"
"%s\n"
msgstr ""
"无效的支持头文件名：%s\n"
"%s\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:418 ../glade/source.c:426
#, c-format
msgid ""
"Couldn't append to file:\n"
"  %s\n"
msgstr ""
"无法追加到文件：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/source.c:1724 ../glade/utils.c:1168
#, c-format
msgid ""
"Error writing to file:\n"
"  %s\n"
msgstr ""
"写入文件时出错，文件为：\n"
"  %s \n"

# SUN NEW TRANSLATION
#: ../glade/source.c:2743
msgid "The filename must be set in the Project Options dialog."
msgstr "文件名必须在“项目选项”对话框中设置。"

# SUN NEW TRANSLATION
#: ../glade/source.c:2746
msgid ""
"The filename must be a simple relative filename.\n"
"Use the Project Options dialog to set it."
msgstr ""
"文件名必须是一个简单的相对文件名。\n"
"使用“项目选项”对话框进行设置。"

# SUN NEW TRANSLATION
#: ../glade/tree.c:78
msgid "Widget Tree"
msgstr "构件树"

# SUN NEW TRANSLATION
#: ../glade/utils.c:900 ../glade/utils.c:940
msgid "Widget not found in box"
msgstr "在框中找不到构件"

# SUN NEW TRANSLATION
#: ../glade/utils.c:920
msgid "Widget not found in table"
msgstr "在表中找不到构件"

# SUN NEW TRANSLATION
#: ../glade/utils.c:960
msgid "Widget not found in fixed container"
msgstr "在固定容器中找不到构件"

# SUN NEW TRANSLATION
#: ../glade/utils.c:981
msgid "Widget not found in packer"
msgstr "在打包程序中找不到构件"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1118
#, c-format
msgid ""
"Couldn't access file:\n"
"  %s\n"
msgstr ""
"无法访问文件：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1141
#, c-format
msgid ""
"Couldn't open file:\n"
"  %s\n"
msgstr ""
"无法打开文件：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1158
#, c-format
msgid ""
"Error reading from file:\n"
"  %s\n"
msgstr ""
"读取文件时出错，文件为：\n"
"  %s \n"

# SUN NEW TRANSLATION
#. This happens under WIN32 when stat is confused by the filename, but this is
#. harmless, since we know that the directory exists after all.
#: ../glade/utils.c:1225
#, c-format
msgid ""
"Couldn't create directory:\n"
"  %s\n"
msgstr ""
"无法创建目录：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1232
#, c-format
msgid ""
"Couldn't access directory:\n"
"  %s\n"
msgstr ""
"无法访问目录：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1240
#, c-format
msgid ""
"Invalid directory:\n"
"  %s\n"
msgstr ""
"无效的目录：\n"
"  %s\n"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1611
msgid "Projects"
msgstr "项目"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1628
msgid "project"
msgstr "项目"

# SUN NEW TRANSLATION
#: ../glade/utils.c:1634
#, c-format
msgid ""
"Couldn't open directory:\n"
"  %s\n"
msgstr ""
"无法打开目录：\n"
"  %s\n"
