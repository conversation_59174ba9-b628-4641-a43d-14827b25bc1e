2007-12-09  <PERSON>  <<EMAIL>>

	* glade/gnome/gnomecontrol.c: Don't define
	BONOBO_[UI_]DISABLE_DEPRECATED, or we'll get an undefined symbol "_".
	Fallout from bug #419535.

2007-04-26  <PERSON>  <<EMAIL>>

	* configure.in: Added "oc" (Occitan) to ALL_LINGUAS.

2007-03-19  Pema Geyleg  <<EMAIL>>

	*configure.in: Added 'dz' to the list

2007-03-07  Priit <PERSON>  <<EMAIL>>

	* configure.in: Added et to ALL_LINGUAS

2006-12-29  <PERSON><PERSON>  <<EMAIL>>

	* configure.in: Added fi to ALL_LINGUAS

2006-05-22  <PERSON><PERSON>  <<EMAIL>>

	* glade-2.desktop.in:
	Update Comment, as per http://live.gnome.org/UsabilityTeam/Menu.

2006-04-17  <PERSON><PERSON><PERSON>  <<EMAIL>>

	* configure.in: Remove obsolete entry for no_NO.
	* po/no.po: And the translation.

2005-10-09  <PERSON>  <<EMAIL>>

	* Released Glade 2.12.1

2005-09-16  <PERSON> Moya <<EMAIL>>

	* glade/gnome-db/gladegnomedblogindlg.c: added missing header file.

2005-09-14  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.12.0

2005-09-14  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbwindow.c: 
	* glade/gbwidgets/gbfilechooserdialog.c: 
	* glade/gbwidgets/gbfileselection.c: 
	* glade/gbwidgets/gbdialog.c: 
	* glade/gbwidgets/gbfontselectiondialog.c: 
	* glade/gbwidgets/gbinputdialog.c: 
	* glade/gbwidgets/gbcolorselectiondialog.c: 
	* glade/gbwidgets/gbaboutdialog.c: 
	* glade/gnome/gnomemessagebox.c: 
	* glade/gnome/gnomeapp.c: 
	* glade/gnome/bonobowindow.c: 
	* glade/gnome/gnomeabout.c:
	* glade/gnome/gnomedialog.c:
	* glade/gnome/gnomepropertybox.c:
	* glade/gnome-db/gnomedblogindlg.c:
	* glade/gnome-db/gnomedberrordlg.c: added support for "urgency_hint".

	* glade/gbwidgets/gbaboutdialog.c: (gb_about_dialog_write_source): 
	call gb_window_write_standard_source() to output code for
	"destroy_with_parent" property.

	* glade/named_icons.c (GladeNamedIcons): added 9 new stock GNOME icons.

	* tools/geticons.pl: 
	* tools/getstockitems.pl: only output the lists, not the bits at the
	top and bottom.

	* glade/named_icons.c (GladeNamedIcons): added GTK_STOCK_INFO,
	GTK_STOCK_FULLSCREEN and GTK_STOCK_LEAVE_FULLSCREEN.

	* glade/gbwidgets/gbmenubar.c: added support for "pack_direction"
	and "child_pack_direction".

	* glade/gbwidgets/gbfilechooserbutton.c: 
	* glade/gbwidgets/gbfilechooserdialog.c: added support for
	"do_overwrite_confirmation". (I don't think it is useful for
	GtkFileChooserWidget, though I'm not sure.)

	* glade/gbwidgets/gbiconview.c: added support for "reorderable".

	* glade/gbwidgets/gbaboutdialog.c: added support for "wrap_license".

	* glade/gbwidgets/gbwindow.c (gb_window_set_standard_properties): use
	icon_name_p rather than IconName, so it works for widgets other than
	GtkWindow itself.

2005-09-13  Damon Chaplin  <<EMAIL>>

	* configure.in: 
	* README: bumped version info ready for 2.12.0.

	* NEWS: added 2.10.1 info.

	* glade/gbwidgets/gbradiobutton.c (gb_radio_button_set_properties): 
	* glade/gbwidgets/gbradiomenuitem.c
	(gb_radio_menu_item_set_properties): 
	* glade/gbwidgets/gbradiotoolbutton.c
	(gb_radio_tool_button_set_properties): output a warning if an unknown
	radio group is found in the XML file. (#110181)

	* glade/main.c (main): patch from Bastian Kleineidam to use
	gtk_init_with_args() to parse options, so --help works. (#309213)

	* glade/glade_menu_editor.c: use the stock id as the item string for
	the "Stock Item" property in GTK+ menus, and the "Icon" property
	in GTK+ and GNOME menus, since the old label text wasn't unique.

	* glade/property.c (fill_stock_combo): use the stock id as the item
	string rather than the (translated) item text, since that isn't unique.
	(find_stock_id, find_stock_item): ifdef'ed out - not used now.
	(property_get_stock_item): return the combo text, or NULL if empty.
	(property_set_stock_item): set the combo text to the stock id.
	(property_get_icon): if the combo text matches the basename of the last
	selected file, return the complete filename, otherwise return the combo
	text as it is.
	(property_set_icon): check if the icon is a stock id, if not assume
	it is a filename. (#303105)

2005-09-12  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (get_standard_properties): GtkEventBoxes can
	always have tooltips. (#312622)

	* glade/gbwidgets/gblabel.c (gb_label_get_focus_targets): add custom
	widgets and file chooser buttons to the combo. (#311804)

	* glade/gnome/gnomecanvas.c: use "aa" rather than "anti_aliased",
	otherwise libglade doesn't load the property. (##309412)

	* glade/glade_palette.c (glade_palette_set_show_gnome_widgets): show
	the main palette page and make the selector active, to make sure we
	don't show GNOME widgets or allow them to be added to GTK+ projects.
	(#148423)

	* glade/glade_project_window.c: use a global for the GladeProjectWindow
	to avoid problems finding it. (#170758)

	* glade/gbwidget.c (add_standard_bottom_menu_items): add a separator
	before the Cut/Copy/Paste items. (#300261)

	* autogen.sh: 
	* glade/data/gtk/autogen.sh: don't use -path option of find, as
	Solaris doesn't have it. (#301929)

	* glade/gbwidget.c (copy_signals): 
	* glade/load.c (load_date): output a warning if the date is invalid,
	and reset the error.

2005-08-30  Damon Chaplin  <<EMAIL>>

	* glade/property.c (create_language_specific_properties): don't
	create a table for Perl as we don't support it now. Avoids invalid
	write.

2005-07-31  Pawan Chitrakar  <<EMAIL>>

	* configure.in: Added ne in ALL_LINGUAS

2005-07-08  Rodrigo Moya <<EMAIL>>

	* configure.in: require libgda/libgnomedb-2.0.

	* glade/glade_gnomedblib.c:
	* glade/gnome-db/Makefile.am: removed browser, connection selector,
	list and query builder widgets, not present anymore on GNOME-DB.

2005-06-14  Rodrigo Moya <<EMAIL>>

	* glade-2.desktop.in: added MIME type information.

2005-04-13  Abel Cheung  <<EMAIL>>

	* configure.in: Added "zh_TW" to ALL_LINGUAS.

2005-04-01  Steve Murphy  <<EMAIL>>

        * configure.in: Added "rw" to ALL_LINGUAS.

2005-03-16  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.10.0

2005-03-16  Damon Chaplin  <<EMAIL>>

	* configure.in: turn off gnome-db by default, since it still breaks
	backwards compatability occasionally so we can't rely on its API.

	* FAQ: removed some out-of-date info.
	* README: updated languages section.

2005-02-01  Damon Chaplin  <<EMAIL>>

	* tools/getstockitems.pl: 
	* tools/geticons.pl: added the 2 little scripts used to get the stock
	items and named icons to go in named_icons.c.

2005-01-31  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.9.0

2005-01-31  Damon Chaplin  <<EMAIL>>

	* configure.in: bump GTK+ requirement to 2.6.0, and libgnomeui
	requirement to 2.9.0.

	* glade/gbwidgets/gbwindow.c: added support for "icon_name" and
	"focus_on_map" properties.

	*  glade/gbwidgets/gbcolorselectiondialog.c:
	*  glade/gbwidgets/gbdialog.c:
	*  glade/gbwidgets/gbfilechooserdialog.c:
	*  glade/gbwidgets/gbfileselection.c:
	*  glade/gbwidgets/gbfontselectiondialog.c:
	*  glade/gbwidgets/gbimage.c:
	*  glade/gbwidgets/gbinputdialog.c:
	*  glade/gbwidgets/gbwindow.c:
	*  glade/gnome/bonobowindow.c:
	*  glade/gnome/gnomeabout.c:
	*  glade/gnome/gnomeapp.c:
	*  glade/gnome/gnomedialog.c:
	*  glade/gnome/gnomemessagebox.c:
	*  glade/gnome/gnomepropertybox.c:
	*  glade/gnome-db/gnomedberrordlg.c:
	*  glade/gnome-db/gnomedblogindlg.c: used above support for "icon_name"
	and "focus_on_map".
	
	* glade/gbwidgets/gbimage.c: added support for "icon_name" and
	"pixel_size" properties.

	* glade/named_icons.c: new file, containing an array of standard named
	icons from GTK+ and GNOME.

	* glade/Makefile.am (glade_2_SOURCES): added named_icons.c.

	* glade/gb.h: replaced wmname_p and wmclass_p with icon_name_p and
	focus_on_map_p for all the gb_window_* functions.

	* glade/gbwidget.c (gb_widget_input_named_icon) 
	(gb_widget_output_named_icon): 
	* glade/property.c (property_add_named_icon) 
	(property_get_named_icon, property_set_named_icon): added support
	for named icons (themed icons).

2005-01-19  Damon Chaplin  <<EMAIL>>

	* glade/gbsource.c (gb_widget_write_accelerators_source): add cast to
	keep C++ compiler happy with generated code. Patch from Daniel Pirkl.

2005-01-01  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbfilechooserbutton.c (gb_file_chooser_button_new): 
	fixed call to gtk_file_chooser_button_new() - arguments changed.
	(gb_file_chooser_button_write_source): use new arguments.

2004-12-21  Damon Chaplin  <<EMAIL>>

	* glade/glade_atk.c (glade_atk_create_property_page): use an int
	instead of AtkRelationType to avoid problems with optimizing compilers.
	#152008.

2004-12-13  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbcomboboxentry.c: added support for "add_tearoffs",
	"has_frame", and "focus_on_click".

	* glade/gbwidgets/gbcombobox.c: added support for "add_tearoffs" and
	"focus_on_click". I don't think "has_frame" is useful here.

	* glade/gbwidgets/gbtreeview.c: added support for "fixed_height_mode",
	"hover_selection" and "hover_expand", and added a few example rows.

	* glade/gbwidgets/gbiconview.c (gb_icon_view_new): unref the store.

	* glade/gbwidgets/gbprogressbar.c: added support for "ellipsize".

2004-12-08  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbaccellabel.c: 
	* glade/gbwidgets/gblabel.c: added support for "ellipsize",
	"width_chars", "single_line_mode" and "angle".

	* glade/gb.h: updated label functions to take new properties.

	* glade/gbwidgets/gbfilechooserbutton.c (gb_file_chooser_button_create_properties): use an int range for "width_chars".

2004-12-07  Damon Chaplin  <<EMAIL>>

	* glade/glade_palette.c (glade_palette_set_show_gnome_widgets): hide
	the recently deprecated widgets as appropriate.

	* glade/gbsource.c (init_signal_hash): add signals for GtkIconView.
	(init_signal_hash): add GtkRange::change_value,
	GtkCellRenderer::editing_started.

	* glade/gbwidgets/gbmenutoolbutton.c: 
	* glade/gbwidgets/gbcellview.c: 
	* glade/gbwidgets/gbiconview.c: 
	* glade/gbwidgets/gbaboutdialog.c: added support for new GTK+ 2.6
	widgets.

	* glade/gbwidgets/gbfilechooserbutton.c: added support for new widget,
	based on patch from James M. Cape.

	* glade/glade_gtk12lib.c: added new widgets to the palette and
	rearranged a bit.

	* glade/glade_gnomelib.c: rearranged a bit and moved deprecated widgets
	to deprecated page.

	* glade/editor.c (placeholder_finish_replace): set scrollbar policy
	for scrolled windows for new GtkIconView widgets to automatic.

2004-12-01  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomehref.c (gb_gnome_href_set_properties): only look
	for the old "label" property when loading, otherwise it crashes.

2004-11-30  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.6.6

2004-11-29  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbspinbutton.c (gb_spin_button_write_source): 
	* glade/gbwidget.c (gb_widget_output_adjustment): 
	* glade/save.c (save_float): use "%.12g" when outputting floats, so
	we keep as many digits as possible. #156016.
	We could also update all other places '%g' appears.

	* glade/gbwidgets/gbbutton.c (gb_button_normal_write_source): use the
	translation properties from the child label.

	* glade/utils.c (glade_util_copy_translation_properties): new function
	to copy translation properties from one widget to another.

	* glade/property.c (copy_translation_properties): use above.

	* glade/gbwidgets/gbbutton.c (gb_button_normal_get_properties) 
	(gb_button_normal_set_properties): copy the translation properties
	from the button to the label and vice versa when necessary, so that
	our special code for handling the label from within the button works.
	#156350.

2004-11-27  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomehref.c: use "text" rather than "label" as the
	property name for the text in the button. #112470.

2004-11-16  Kjartan Maraas  <<EMAIL>>

	* configure.in: Add «nb» to ALL_LINGUAS.

2004-11-06 Amanpreet Singh Alam <<EMAIL>>

	* configure.in: ALL_LINUGUAS is changed by Adding pa Language

2004-10-24  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.6.5

2004-10-24  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (editor_idle_handler): skip any selected widgets
	that aren't realized, or we get invalid windows. #156190.

2004-10-20  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.6.4

2004-10-20  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (paint_widget, editor_idle_handler): changed again
	to not ref the widget, just check if it is NULL instead. It was
	crashing when scrolled windows were being deleted. #155916.

2004-10-13  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.6.3

2004-10-19  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (paint_widget, editor_idle_handler): ref the widget
	as well, just to be safe. Apparently this code was causing crashes on
	Glade 3, though I haven't seen any in Glade 2.

	* glade/property.c (property_add_string, property_set_string): use a
	flag to distinguish string properties from others (e.g. dialog
	properties), and only hide the button fro string properties.
	It was being hidden for properties like 'Signal:'.

2004-10-13  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.6.2

2004-10-13  Damon Chaplin  <<EMAIL>>

	* mkinstalldirs: rebuilt with newer tools to get this as well.

2004-10-12  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.6.1

2004-10-07  Damon Chaplin  <<EMAIL>>

	* glade/glade_atk.c: added new relation type strings.

	* glade/editor.c (editor_idle_handler): ignore the allocation of
	toplevel widgets, since these are not useful. This caused the selection
	rectangles to be offset by the windows position on the desktop.

	* glade/glade_atk.c (save_atk_property): save the extra translation
	properties.
	(glade_atk_load_atk_properties): load the translation properties.

	* glade/glade-parser.c (glade_parser_start_element): init
	state->prop_context_prefix to FALSE before reading property and
	atkproperty elements. It wasn't being reset sometimes.
	Also load the "comments" and "context" attributes for atkproperty.
	(glade_parser_end_element): copy the "translatable", "comments" and
	"context" attributes over to the GladeProperty, and reset
	prop_translator_comments to NULL.

	* glade/gnome/gnomeabout.c: set the initial comments for translator
	credits. Also updated to use the new translation properties.

	* glade/source.c (source_add_translator_comments_to_buffer): new func.

	* glade/property.c (run_text_property_dialog): only show/set the
	translation properties if needed, and always call on_property_changed()
	since it may not be called if the main text isn't changed.

	* glade/gbwidget.c (gb_widget_output_child_label): 
	* glade/gbwidgets/gbbutton.c (gb_button_gnome_get_properties) 
	(gb_button_normal_get_properties): 
	* glade/gbwidgets/gbtoolbutton.c
	(gb_tool_button_get_standard_properties): Always use
	gb_widget_output_translatable_text() for consistency.

	* glade/editor.c (placeholder_finish_replace): set the initial shadow
	of the text widget's scrolled window to In.

	* glade/main.c (glade_log_handler): install a tiny log handler to
	ignore those warnings about scrolled windows.

	* glade/gbwidgets/gbdialog.c (on_dialog_ok): use Apply, Cancel, OK
	order rather than Cancel, Apply, OK. This comes from the HIG 2.0
	in the section Windows/Utility Windows/Explicit Apply Windows.

2004-09-30  Damon Chaplin  <<EMAIL>>

	This is all for #115635. It adds support for 3 extra attributes for
	string & text properties: (1) a translatable flag, so you can turn off
	translation of a string, (2) a context flag if the string has a context
	prefix, and (3) a comments string to help translators.
	It also provides a bigger area to enter text, which is useful if you
	need to enter a lot of it. I'm not sure about the ATK properties yet.

	* glade/property.c (run_text_property_dialog): new function to show
	a dialog for editing string & text properties and their extra
	translation properties.
	(property_add_string, property_add_text): add a '...' button beside
	the property and call above function when it is clicked.
	(property_get_string, property_set_string) 
	(property_set_translatable_string, property_get_text) 
	(property_set_translatable_text): handle extra translation properties.

	* glade/utils.c (glade_util_get_translation_properties): gets the
	extra translation properties which are stored inside the widget's data.
	(glade_util_set_translation_properties): set the above.

	* glade/source.c (source_write_support_files): define the Q_ macro
	to mark translatable strings that start with a context prefix.
	(source_add_translator_comments): new function to output a C comment
	containing comments for translators, to be called just before a
	translatable string is output.
	(source_make_string_internal): updated to take a context flag to
	indicate that the Q_ macro should be used.

	* glade/save.c (save_string_internal): save extra translation
	properties.
	(save_translatable_string_internal): new function to get the extra
	translation properties from the widget data and save them as well.
	(save_translatable_string, save_translatable_text):
	(save_translatable_text_in_lines): call the above.

	* glade/load.c (load_get_value_full): new function to read extra
	translation attributes as well.
	(load_string, load_text): use the above, and store the extra
	translation properties in the widget.

	* glade/glade-parser.h (struct _GladeProperty): add extra translation
	properties.

	* glade/glade-parser.c: read the extra translation properties in from
	the XML file.

	* glade/gbwidget.h (struct _GbWidgetGetArgData)
	(struct _GbWidgetSetArgData): put a pointer to the widget in here as
	well, since it meant fewer changes were needed to handle the extra
	translation properties.

	* glade/gbwidget.c: set the widget pointer above, and use the new
	property_set_translatable_string/text() functions where appropriate.

	* glade/glade_atk.c (glade_atk_source_add_translator_comments): new
	function to output a C comment containing comments for translators.
	(glade_atk_write_atk_properties_source): use the extra translation
	properties when generating the C code.

	* glade/gbsource.c (gb_widget_write_standard_source): 
	* glade/gbwidgets/gbaccellabel.c:
	* glade/gbwidgets/gbbutton.c:
	* glade/gbwidgets/gbcheckbutton.c:
	* glade/gbwidgets/gbcheckmenuitem.c:
	* glade/gbwidgets/gbcolorbutton.c:
	* glade/gbwidgets/gbcolorselectiondialog.c:
	* glade/gbwidgets/gbcombo.c:
	* glade/gbwidgets/gbcombobox.c:
	* glade/gbwidgets/gbcomboboxentry.c:
	* glade/gbwidgets/gbentry.c:
	* glade/gbwidgets/gbfilechooserdialog.c:
	* glade/gbwidgets/gbfileselection.c:
	* glade/gbwidgets/gbfontbutton.c:
	* glade/gbwidgets/gbfontselection.c:
	* glade/gbwidgets/gbfontselectiondialog.c:
	* glade/gbwidgets/gbimagemenuitem.c:
	* glade/gbwidgets/gblabel.c:
	* glade/gbwidgets/gblistitem.c: 
	* glade/gbwidgets/gbmenuitem.c:
	* glade/gbwidgets/gbnotebook.c:
	* glade/gbwidgets/gbprogressbar.c:
	* glade/gbwidgets/gbradiobutton.c:
	* glade/gbwidgets/gbradiomenuitem.c:
	* glade/gbwidgets/gbradiotoolbutton.c:
	* glade/gbwidgets/gbtext.c:
	* glade/gbwidgets/gbtextview.c:
	* glade/gbwidgets/gbtogglebutton.c:
	* glade/gbwidgets/gbtoggletoolbutton.c:
	* glade/gbwidgets/gbtoolbutton.c:
	* glade/gbwidgets/gbtreeitem.c:
	* glade/gbwidgets/gbwindow.c:

	* glade/gnome/bonobowindow.c:
	* glade/gnome/gnomeabout.c:
	* glade/gnome/gnomeapp.c:
	* glade/gnome/gnomecolorpicker.c: 
	* glade/gnome/gnomedialog.c:
	* glade/gnome/gnomedruidpageedge.c:
	* glade/gnome/gnomedruidpagestandard.c:
	* glade/gnome/gnomefileentry.c:
	* glade/gnome/gnomefontpicker.c:
	* glade/gnome/gnomehref.c: 
	* glade/gnome/gnomeiconentry.c:
	* glade/gnome/gnomemessagebox.c:
	* glade/gnome/gnomepixmapentry.c:
	
	* glade/gnome-db/gnomedblogindlg.c: 
	* glade/gnome-db/gnomedbgraybar.c: 
	* glade/gnome-db/gnomedberrordlg.c: 
	* glade/gnome-db/gnomedbeditor.c: use the extra translation properties
	when generating the source code.

2004-09-25  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_window.c (glade_project_window_new): don't
	set the View checkmenuitems here. We do that later.
	(glade_project_window_show_quit_dialog): call glade_save_settings()
	to save the window geometries and visibility status.
	(glade_project_window_refresh_menu_items): new function to set the
	View checkmenuitems status according to whether the windows are shown
	or not.

	* glade/main.c (final_setup_from_main_loop): call glade_load_settings()
	and merge the results with the command-line arguments to get the
	windows to show. Also call glade_project_window_refresh_menu_items()
	to setup all the checkmenuitems in the View menu according to if the
	window is shown or not.

	* glade/palette.c (palette_init): renamed from palette_create and
	made public.
	(palette_add_gbwidget, palette_show): don't call palette_create as it
	is always created when Glade is started now.

	* glade/property.c (property_init): renamed from property_create and
	make public.
	(property_show): don't call property_create as it is always created
	when Glade is started now.

	* glade/glade.c (glade_load_settings, glade_save_settings): new
	functions to save and restore window geometries and visibility.
	We use a simple XML file in ~/.glade2 for now. #149510.
	(Some of the code is from a glade3 patch from Paolo Borelli #142176.)
	(glade_app_init): call palette_init() and property_init() to make
	sure all windows are created here.

2004-09-24  Damon Chaplin  <<EMAIL>>

	* glade/editor.c: ported newer drawing code from glade3, since that
	also works on Windows. There may be a few problems with it though,
	especially with odd containers like GtkFixed, GtkLayout,
	GtkScrolledWindow.

	* glade/glade_menu_editor.c (glade_menu_editor_update_menu): only set
	the GladeIconKey if we created an icon. #137386.

	* glade/gbwidget.c (gb_widget_replace_child): When inserting a
	GtkToolItem above a widget, add it to the widget tree as well.
	(gb_widget_replace_child): If the user tries to insert a GtkToolItem
	inside another GtkToolItem, just replace it instead. #151314.

2004-09-23  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gblabel.c (gb_label_find_mnemonic_widget): for
	labels inside a GtkExpander, only return TRUE if the label is the
	expander's label. Otherwise access keys for all labels inside a
	GtkExpander simply open/close the expander. #141183.

	* glade/gbwidget.c (get_position_properties): Don't just return if
	the widget's area hasn't been allocated. It may never be if the
	window is not shown. But we must still show the properties. #152362.

	* glade/gbwidgets/gbcomboboxentry.c (gb_combo_box_entry_write_source): 
	* glade/gbwidgets/gbcombobox.c (gb_combo_box_write_source): always
	create the combo with a model. It can cause problems otherwise.
	#143563.

2004-09-23  Mariano Suárez-Alvarez  <<EMAIL>>

	* glade/glade_project_window.c(glade_project_window_on_open_project):
	add "Glade Files" and "All Files" filters to the file selector dialog.
	#145072.

2004-09-23  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_window.c (FileMenu): 
	* glade/gnome/bonobowindow.c (FileMenu): use GNOMEUIINFO_MENU_QUIT_ITEM
	instead of GNOMEUIINFO_MENU_EXIT_ITEM. Fixes part of #142993.
	I was going to change it in the .glade files but then saw that
	libglade-gnome doesn't support QUIT_ITEM so didn't.

2004-09-22  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomefileentry.c: added support for the
	"filechooser_action" property.

	* glade/editor.c (placeholder_finish_replace): check we don't try to
	add a GtkToolItem to anything other than a toolbar, since it causes
	problems otherwise.

	* glade/gbwidgets/gbmenubar.c (gb_menu_bar_write_source): 
	* glade/gbwidgets/gbmenu.c (gb_menu_write_source): pass an accel_group
	to gnome_app_fill_menu() so accelerators work (or are shown at least)
	in GtkWindow and GtkMenu when used in a GNOME app. For popup menus
	it is up to the developer to make sure the accelerators have been
	added to the main windows as well so they actually work.

	* glade/utils.c (glade_util_get_next_free_project_directory): 
	* glade/main.c (main): check for NULL from g_get_home_dir() (Win9x)

2004-06-26  Damon Chaplin  <<EMAIL>>

	* doc/C/glade-user-guide/glade-user-guide-C.omf: updated 'rights'
	tag to use only one 'holder' attribute.

2004-06-23  Damon Chaplin  <<EMAIL>>

	* doc/C/glade-user-guide/glade-user-guide.html: 
	* doc/C/glade-user-guide/glade-user-guide.xml: 
	* doc/C/glade-user-guide/Makefile.am (figs): 
	* doc/C/glade-user-guide/glade-user-guide-C.omf: updates from
	Pat Costello to ship glade-windows.png and fix a few other errors.

2004-05-25  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_write_source): use
	GtkIconSize for the icon size rather than gint.
	Also #include "../tree.h".

2004-05-19  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbcomboboxentry.c
	(gb_combo_box_entry_set_properties): 
	* glade/gbwidgets/gbcombobox.c (gb_combo_box_set_properties): 
	* glade/gbwidgets/gbcombo.c (gb_combo_set_properties): leave the
	property string as it was, in case it is used somewhere else as well.
	Fixes bug #142746.

2004-05-10  Damon Chaplin  <<EMAIL>>

	* glade/save.c (save_translatable_text_in_lines): check if tag_value
	is NULL. Fixes bug #142251.

2004-04-15  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.6.0

2004-04-15  Damon Chaplin  <<EMAIL>>

	* glade/utils.c (glade_util_get_next_free_project_directory): use
	g_strdup() to create the project dir on windows, since it is freed
	later.

	* glade/utils.c: 
	* glade/main.c: don't include <unistd.h> on win32.

	* README: update links for various languages and mailing list info,
	and version requirements.

2004-04-06  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_options.c: 
	* glade/glade_menu_editor.c: include <gtk/gtkmain.h> so plain GTK+
	version compiles.

2004-03-29  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.5.1

2004-03-29  Damon Chaplin  <<EMAIL>>

	* glade/source.c: don't output acconfig.h, and use AC_DEFINE with all
	3 args, to get rid of warnings about AC_DEFINE.

	* doc/C/glade-faq/glade-faq.xml (type): 
	* doc/C/glade-user-guide/glade-user-guide.xml: 
	* FAQ (http): remove references to acconfig.h.

	* configure.in: use AC_DEFINE with all 3 args.
	
	* acconfig.h: removed, to get rid of warnings about AC_DEFINE.

2004-03-29  Damon Chaplin  <<EMAIL>>

	* configure.in: bump GTK+ and GNOME version requirements.

	* glade/gnome-db/gnomedblogindlg.c (gb_gnome_db_logindlg_new): 
	* glade/gnome-db/gnomedberrordlg.c (gb_gnome_db_errordlg_new): 
	* glade/gnome/gnomepropertybox.c (on_gnome_property_box_dialog_ok): 
	* glade/gnome/gnomemessagebox.c (gb_gnome_message_box_new): 
	* glade/gnome/gnomedialog.c (gb_gnome_dialog_new): 
	* glade/gnome/gnomeabout.c (gb_gnome_about_new): 
	* glade/gbwidgets/gbfilechooserdialog.c (gb_file_chooser_dialog_new): 
	* glade/gbwidgets/gbinputdialog.c (gb_input_dialog_new): 
	* glade/gbwidgets/gbfontselectiondialog.c (gb_font_selection_dialog_new): 
	* glade/gbwidgets/gbfileselection.c (gb_file_selection_new): 
	* glade/gbwidgets/gbdialog.c (create_dialog): 
	* glade/gbwidgets/gbcolorselectiondialog.c (gb_color_selection_dialog_new): set the initial type hint to dialog. Bug #135893.

	* glade/gb.h (GLADE_TYPE_HINT_DIALOG_INDEX): added a define to
	contain the index of the dialog type hint.

	* glade/gbwidgets/gbwindow.c (GbTypeHintChoices): added a note to
	make sure the above define is updated if the array is changed.

	* glade/gbwidgets/gbaspectframe.c (gb_aspect_frame_new): 
	* glade/gbwidgets/gbframe.c (gb_frame_new): try to set up the frame
	according to the HIG. i.e. set the shadow to none, use a bold label,
	and add an alignment with 12 pixels padding on the left. Bug #118183.

2004-03-28  Damon Chaplin  <<EMAIL>>

	* glade/load.c (real_load_project_file): if we don't find a project
	options file, guess if it is a GTK+ or GNOME project by looking at
	the <requires> tags.

	* glade/glade_project.c (glade_project_load_options): return a bool
	indicating the options were found & loaded OK.

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_add_child): insert a
	GtkToolItem when loading child widgets that aren't plain/radio/toggle
	items.

2004-03-25  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbbutton.c (gb_button_normal_find_child_widgets): 
	return FALSE if the child is a label GbWidget, since we handle that
	separately. Fixes bug #127860.

	* glade/main.c (main): try to load gail-gnome as well as gail.

2004-03-23  Damon Chaplin  <<EMAIL>>

	* glade/property.c (show_filesel_dialog): 
	* glade/utils.c (glade_util_set_file_selection_filename) 
	(glade_util_get_file_selection_filename): 
	* glade/glade_project_window.c: 
	(glade_project_window_on_open_project): 
	* glade/glade_project_options.c
	(glade_project_options_show_file_selection): 
	* glade/glade_menu_editor.c (on_icon_button_clicked): use filechooser
	rather than old file selection dialog. Bug #132905.

2004-03-21  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbimagemenuitem.c
	(gb_image_menu_item_set_properties): translate the stock menuitem
	label. Fixes bug #136229.

	* glade/gbwidgets/gbtext.c (gb_text_write_source): use -1 for text
	length, so it works for translated strings. This was bug #62531.

2004-03-17  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.5.0

2004-03-17  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtoolbar.c: include gtktoolbutton.h.

	* configure.in (AC_OUTPUT): removed turbo start Makefile.

	* NEWS: 
	* HACKING: 
	* FAQ: updated a bit.

	* glade/gbwidgets/gbfilechooserwidget.c: removed FolderMode.
	Added support for new actions.

	* doc/C/glade-faq/glade-faq-C.omf: 
	* doc/C/glade-faq/glade-faq.xml: updated a bit.

	* doc/C/glade-user-guide/glade-user-guide-C.omf: use one 'holder'
	attribute rather than 2 which is invalid.

	* glade/glade_project_window.c: removed turbo start manual.

	* glade.spec.in (BuildRequires): removed glade-turbo-start.*.

	* doc/C/Makefile.am (SUBDIRS): removed glade-turbo-start directory.

	* doc/C/topic.dat: removed Quick-Start Guide.

	* doc/C/glade-user-guide/glade-user-guide.xml: added 'id' attributes
	of a few 'sect1' elements so yelp displays them OK.
	Also reworded a few bits and removed old stuff.

2004-03-08  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbcombobox.c (gb_combo_box_write_source): if no
	items have been added, just create a combo box without a model.

	* glade/gbwidgets/gbcomboboxentry.c: use new convenience API in
	generated code, and handle no items as above.

	* glade/glade_menu_editor.c (glade_menu_editor_update_menu): 
	patch from Harsh Jaitly to use GtkSeparatorMenuItem for separators.
	Bug #130570.

2004-02-27  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbfilechooserdialog.c: removed FolderMode.
	Added support for new actions.

2004-02-25  Adam Weinberger  <<EMAIL>>

	* configure.in: Added 'en_CA' (Canadian English) to ALL_LINGUAS.

2004-02-24  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbwindow.c (gb_window_destroy): remove the icon
	pixmap from the project.

	* glade/gnome-db/gnomedblogindlg.c (gb_gnome_db_logindlg_init): 
	* glade/gnome-db/gnomedberrordlg.c (gb_gnome_db_errordlg_init): 
	* glade/gnome/gnomepropertybox.c (gb_gnome_property_box_init): 
	* glade/gnome/gnomemessagebox.c (gb_gnome_message_box_init): 
	* glade/gnome/gnomedialog.c (gb_gnome_dialog_init): 
	* glade/gnome/gnomeapp.c (gb_gnome_app_init): 
	* glade/gnome/gnomeabout.c (gb_gnome_about_destroy): 
	* glade/gnome/bonobowindow.c (gb_bonobo_window_init): 
	* glade/gbwidgets/gbfilechooserdialog.c (gb_file_chooser_dialog_init): 
	* glade/gbwidgets/gbinputdialog.c (gb_input_dialog_init): 
	* glade/gbwidgets/gbfontselectiondialog.c (gb_font_selection_dialog_init): 
	* glade/gbwidgets/gbfileselection.c (gb_file_selection_init): 
	* glade/gbwidgets/gbdialog.c (gb_dialog_init): 
	* glade/gbwidgets/gbcolorselectiondialog.c
	(gb_color_selection_dialog_init): set destroy function to
	gb_window_destroy() or call it.

	* glade/gb.h: added declarations of gb_tool_button_destroy and
	gb_window_destroy.

	* glade/gbwidgets/gbtoggletoolbutton.c (gb_toggle_tool_button_init): 
	* glade/gbwidgets/gbradiotoolbutton.c (gb_radio_tool_button_init): 
	* glade/gbwidgets/gbtoolbutton.c (gb_tool_button_destroy): 
	* glade/gnome/gnomepixmap.c (gb_gnome_pixmap_destroy): remove the
	pixmap from the project.

2004-02-24  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbcustom.c (gb_custom_write_source) 
	(gb_custom_get_properties): treat NULL and "" string arguments as
	the same, since we can't differentiate within Glade. Bug #120474.

	* glade/gbwidgets/gbwindow.c (GbPositionSymbols): add support for
	GTK_WIN_POS_CENTER_ALWAYS and GTK_WIN_POS_CENTER_ON_PARENT.

	* glade/gbwidgets/gbradiomenuitem.c
	(gb_radio_menu_item_create_properties): make Group property invisible
	since it is only used for loading & saving.

2004-02-23  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_options.c (glade_project_options_init): 
	add note recommending libglade for large apps, (bug #129772),
	and a note deprecating translatable strings file.

	* glade/gbwidgets/gblabel.c (gb_label_get_focus_targets): allow
	GtkComboBox/GtkComboBoxEntry widgets to be focus targets.

	* glade/gnome/gnomefileentry.c (gb_gnome_file_entry_write_source): 
	terminate g_object_set() calls with NULL.

2004-02-21  Paolo Borelli  <<EMAIL>>

	* glade/glade_project_window.c: implement open project by drag&drop.
	* glade/utils.c: add glade_util_uri_list_parse, needed for the above.
	Bug #134152.
	
2004-02-21  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomepixmapentry.c: Add support for "use_filechooser".

	* glade/gnome/gnomefileentry.c: generate code for "use_filechooser".

2004-02-19  Fernando Herrera  <<EMAIL>>

	* glade/gnome/gnomefileentry.c:: Add support for "use_filechooser"
	property. Bug #133885.

2004-02-19  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_options.c (glade_project_options_generate_program_name): removed cast in lvalue. Bug #131645.

	* glade/gbwidget.c (set_standard_properties): 
	* glade/property.c (property_update_title): update the title of the
	property editor window when the widget's name is changed. Bug #131871

	* configure.in: quote AC_MSG_ERROR call properly. Patch from
	Harsh Jaitly. Fixes bug #129253.

	* glade/gbwidgets/gbtoolbar.c: practically rewritten from scratch
	to use the new toolbar items. Old XML files are migrated over
	when loading.

	* glade/gnome/gnomeapp.c (gb_gnome_app_add_toolbar_button): use
	new toolbar buttons.

	* glade/gbwidgets/gbtogglebutton.c: 
	* glade/gbwidgets/gbradiobutton.c: 
	* glade/gbwidgets/gbbutton.c: removed code to handle old toolbar
	buttons (yippee!).

	* glade/gbwidgets/Makefile.am: 
	* glade/utils.h: 
	* glade/utils.c: 
	* glade/glade_gtk12lib.c: 
	* glade/gbwidget.c: 
	* glade/gbsource.c:
	* glade/gb.h: 
	* glade/gb.c:
	* glade/editor.c: updates to handle new toolbar items.

	* glade/graphics/radiotoolbutton.c: 
	* glade/graphics/separatortoolitem.c: 
	* glade/graphics/toggletoolbutton.c: 
	* glade/graphics/toolbutton.c: 
	* glade/graphics/toolitem.c: new files to support toolbar items.

	* glade/graphics/radiotoolbutton.xpm: 
	* glade/graphics/separatortoolitem.xpm: 
	* glade/graphics/toggletoolbutton.xpm: 
	* glade/graphics/toolbutton.xpm: 
	* glade/graphics/toolitem.xpm: new icons.

2004-02-05  Robert Sedak  <<EMAIL>>

        * configure.in: Added "hr" (Croatian) to ALL_LINGUAS.

2003-11-28  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbfilechooserwidget.c: 
	* glade/gbwidgets/gbfilechooserdialog.c: new files to support
	GtkFileChooserDialog and GtkFileChooserWidget.

	* glade/graphics/filechooserwidget.xpm: 
	* glade/graphics/filechooserdialog.xpm: new icons, basically copied
	from old GtkFileSelection icon.

	* glade/glade_gtk12lib.c: added GtkFileChooserWidget and
	GtkFileChooserDialog and moved GtkFileSelection to deprecated page.

	* glade/gbwidgets/Makefile.am (libgbwidgets_a_SOURCES): 
	added gbfilechooserwidget.c and gbfilechooserdialog.c.

	* glade/source.c (source_make_string_internal): just return "" if
	the string is NULL.

2003-11-28  Damon Chaplin  <<EMAIL>>

	* glade/utils.c (glade_util_flags_from_string): update to handle
	spaces in flag strings and output a warning for invalid flags.
	See bug #128013.

2003-11-28  Damon Chaplin  <<EMAIL>>

	* glade-2.desktop.in: added Encoding property, and used "false"
	rather than "0" for Terminal property. Fixes bug #127747.

2003-11-24  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (editor_on_event): use the "event" signal instead
	of "button_press" and "button_release", since it is emitted before
	them and gives us a better chance of intercepting them and doing our
	own thing. I needed this to support selecting the GtkComboBox widget.
	With the old method, the widget gets the signal before we do.
	Also removed calls to stop signals. We can just return TRUE now.

	* glade/glade_gtk12lib.c: moved GtkCombo and GtkOptionMenu to the
	deprecated page, and replaced with GtkComboBoxEntry/GtkComboBox.

	* glade/gbwidgets/gbcomboboxentry.c: new file to support
	GtkComboBoxEntry.

	* glade/gbwidgets/gbcombobox.c: new file to support GtkComboBox.

	* glade/gbwidgets/Makefile.am: added gbcombobox.c, gbcomboboxentry.c.

	* glade/graphics/comboboxentry.xpm: 
	* glade/graphics/combobox.xpm: copied old combo & optionmenu pixmaps.

	* glade/gbsource.c (gb_widget_write_source): rearranged code to
	create the standard widgets used for default values, to make it
	easier to create GTK+ widgets here. (I did need this for the new
	combo widgets at one point, but not any more.)

2003-11-11  Damon Chaplin  <<EMAIL>>

	* configure.in: update version to 2.5.0, and require GTK+ 2.3.0.

	* glade/glade_gtk12lib.c: add GtkExpander, GtkFontButton and
	GtkColorButton.

	* glade/glade_gnomelib.c: move GnomeColorPicker and GnomeFontPicker
	to deprecated page.

	* glade/glade_palette.c (glade_palette_set_show_gnome_widgets): 
	hide GnomeColorPicker and GnomeFontPicker in GTK+ projects.

	* glade/gbwidget.c (gb_widget_replace_child): handle GtkExpander's
	label widget.

	* glade/gnome/gnomepropertybox.c: 
	* glade/gnome/gnomemessagebox.c: 
	* glade/gnome/gnomedialog.c: 
	* glade/gnome/gnomeapp.c: 
	* glade/gnome/bonobowindow.c: 
	* glade/gnome/gnomeabout.c: 
	* glade/gnome-db/gnomedberrordlg.c: 
	* glade/gnome-db/gnomedblogindlg.c: 
	* glade/gbwidgets/gbcolorselectiondialog.c: 
	* glade/gbwidgets/gbfontselectiondialog.c: 
	* glade/gbwidgets/gbfileselection.c: 
	* glade/gbwidgets/gbdialog.c: 
	* glade/gbwidgets/gbinputdialog.c: 
	* glade/gbwidgets/gbwindow.c: added support for "role", "type-hint",
	"skip-taskbar-hint", "skip-pager-hint", "decorated" and "gravity"
	properties.

	* glade/gnome/gnomehref.c: 
	* glade/gnome/gnomefontpicker.c: 
	* glade/gnome/gnomecolorpicker.c: 
	* glade/gbwidgets/gbradiobutton.c: 
	* glade/gbwidgets/gbcheckbutton.c: 
	* glade/gbwidgets/gbtogglebutton.c: 
	* glade/gbwidgets/gbbutton.c: added support for "focus-on-click"
	property.

	* glade/gb.h: updated declarations of functions to account for
	above properties.

	* glade/gbwidgets/gbtextview.c: added support for "overwrite" and
	"accepts-tab" properties.

	* glade/gbwidgets/gbeventbox.c: added support for "visible-window"
	and "above-child" properties.

	* glade/gbwidgets/gbalignment.c: added support for 4 padding
	properties.

	* glade/gbwidgets/gbfontbutton.c: 
	* glade/gbwidgets/gbcolorbutton.c: added support for new widgets,
	almost the same as the Gnome widgets.

	* glade/gbwidgets/gbexpander.c: patch from Mark McLoughlin to add
	support for this widget.

	* glade/gbwidgets/Makefile.am: added above 3 files.

	* glade/graphics/expander.xpm: 
	* glade/graphics/fontbutton.xpm: 
	* glade/graphics/colorbutton.xpm: added icons for new widgets.

2003-11-09  Damon Chaplin  <<EMAIL>>

	* NEWS: updated.

	* glade/utils.c: Remove wrong/unneeded Win32 specific code.
	Patch from Arnaud Charlet.

	* README: add link to glademm mailing list.

2003-11-07  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.0.1

2003-11-07  Damon Chaplin  <<EMAIL>>

	* glade/glade_project.c (glade_project_write_ada95_source): patch
	from Ishan Chattopadhyaya to turn Ada support back on and chdir to
	the project directory before calling gate to generate source code
	in Windows.

2003-11-05  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbvbuttonbox.c (gb_vbutton_box_set_properties): 
	* glade/gbwidgets/gbhbuttonbox.c (gb_hbutton_box_set_properties): 
	* glade/gbwidgets/gbvbox.c (gb_vbox_set_properties): 
	* glade/gbwidgets/gbhbox.c (gb_hbox_set_properties): ignore the size
	property when loading. Fixes bug #126264.

2003-10-22  Damon Chaplin  <<EMAIL>>

	* glade/utils.c (glade_util_get_next_free_project_directory): on
	win32 use 'C:\\Projects' as projects directory. Patch from
	Ishan Chattopadhyaya.

	* doc/C/glade-faq/glade-faq.xml: 
	* FAQ: few minor fixes from Ishan Chattopadhyaya.

	* glade/gbwidgets/gblabel.c (gb_label_create_standard_properties): 
	use the term 'underlined access key' rather than 'underlined
	accelerator character' which may get confused with the other type
	of accelerators. Fixes bug #93746.

2003-10-21  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomedruidpagestandard.c
	(gb_gnome_druid_page_standard_get_properties): handle GdkColor
	properties properly. Patch from David Hampton.
	
	* glade/glade-parser.c (glade_parser_parse_file) 
	(glade_parser_parse_buffer): patch from Josh Parsons to replace XML
	entities while parsing. Fixes bug #124026.

	* glade/glade_project_window.c (glade_project_window_open_project): 
	clear the project view before loading the project, to ensure we don't
	show an invalid project. Hopefully fixes bug #113089.

	* glade/glade_project.c (glade_project_destroy): clear the widget
	tree after destroying all the widgets, to be on the safe side.
	(Otherwise widgets may have pointers to invalid nodes in the tree.)

	* glade-2.desktop.in (X-GNOME-Bugzilla-Component): removed
	X-GNOME-DocPath as it seems we don't need after all. See bug #124818.

2003-10-20  Damon Chaplin  <<EMAIL>>

	* glade/glade-parser.c (create_widget_info): check info->name is not
	NULL. Fixes bug #123015.

	* glade/editor.c (editor_delete_widget): If we are deleting a
	GtkTextView set the text to "". Fixes bug #111604.

	* glade/gbwidgets/gblabel.c (gb_label_create_standard_properties): 
	* glade/glade_menu_editor.c (glade_menu_editor_construct): don't
	set 'value_in_list' of a GtkCombo to TRUE, as it can result in stuch
	pointer grabs if we're not careful. Fixes bug #124276.

2003-10-17  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_window.c: use Ctrl+B accelerator to write
	source code instead of Ctrl+W, which is a standard GNOME accelerator
	used to close the window.

2003-10-17  Shakti  <<EMAIL>>

	*glade-2.desktop.in:
	Set the X-GNOME-DocPath to provide the link for 'help'.
	Fix the bug #124818

2003-10-15  Damon Chaplin  <<EMAIL>>

	* glade/palette.c (palette_create): set the window type hint of the
	palette to UTILITY. I'm not sure if any other windows should have
	the same type - metacity always places UTILITY windows in front,
	which we may not want for the property editor/widget tree as they can
	be pretty big windows. Fixes bug #114240.

	* glade/gbwidgets/gblabel.c (gb_label_write_standard_source): the
	default justification is now GTK_JUSTIFY_LEFT.

2003-10-12  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_show_properties): hide the packing page
	for toplevels. Fixes bug #119345.

	* glade/gbwidgets/gbspinbutton.c (gb_spin_button_create_properties): 
	updated 'Numeric' tooltip.

	* glade/gbwidgets/gbdialog.c (show_dialog_creation_dialog): 
	* glade/utils.c (glade_util_spin_button_new): 
	* glade/property.c (property_add_int_range) 
	(property_add_optional_int_range, property_add_float_range): set
	all spinbuttons to numeric to fix bugs #122914 and #119697.

2003-10-10  Damon Chaplin  <<EMAIL>>

	* glade-2.desktop.in (_Comment): changed comment to agree with HIG.

	* glade/glade_project_window.c (glade_project_window_about): 
	make sure we only show one About dialog. Patch from Steve Chaplin.
	Fixes bug #119410.

 	* glade/property.c: (on_signals_dialog_response):
	replace '-' with '_' in widget name when constructing handler name.
	Patch from Thomas Vander Stichele. Fixes bug #118876.

	* glade/gbwidgets/gbradiobutton.c (get_radio_button_groups): 
	* glade/gbwidgets/gblabel.c (gb_label_get_focus_targets): use
	g_utf8_collate() rather than g_str_equal() to handle UTF-8 properly.
	Patch from Doug Quale. Fixes bug #124270.

 	* glade/glade_project_options.c: added accessible descriptions.
	Patch from Muktha <<EMAIL>>. Fixes bug #90939.

	* glade/editor.c (clear_child_windows): applied patch from bug
	#107450 to stop using X-specific calls.

	* glade/glade_menu_editor.c (glade_menu_editor_construct): Removed
	long description from tooltip. Fixes bug #106055.

2003-10-08  Damon Chaplin  <<EMAIL>>

	* glade.spec.in: applied patch from Rolando Nieves. Bug #113360.

2003-10-08  Narayana Pattipati  <<EMAIL>>

       * glade/glade/editor.c (editor_on_button_press):
       Beep if user tries to add a widget over an existing widget.
       Bugzilla bug#115543.

2003-10-08  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbhbox.c (gb_box_set_size): don't show a warning
	dialog if we can't delete any children. It causes weird problems
	with the spinbutton in the property editor. Fixes bug #113637.

	* glade/gbwidgets/gbbutton.c (gb_button_toolbar_write_source): fixed
	tooltips of stock buttons. Fixes bug #120396.

	* glade/gbsource.c (gb_widget_write_standard_source): use -1 to leave
	the size unchanged rather than -2 when calling set_size_request().
	Fixes bug #112772.

	* README: add libxml to requirements.

	* autogen.sh: copy the standard autogen.sh stuff here, so we don't
	rely on gnome-common.

2003-09-02  Hasbullah Bin Pit <<EMAIL>>
                                                                                
        * configure.in: Added 'ms' (Malay) to ALL_LINGUAS.

2003-08-21  Daniel Baeyens <<EMAIL>>

	* glade/glade_gnomedblib.c:
	* glade/graphics/gnome-db-query-builder.xpm:
	* glade/gnome-db/Makefile.am:
	* glade/gnome-db/gnomedbquerybuilder.c: added support
	for GnomeDbQueryBuilder widget

2003-08-17  Daniel Baeyens <<EMAIL>>

	* glade/glade_gnomedblib.c:
	* glade/graphics/gnome-db-connection-properties.xpm:
	* glade/gnome-db/Makefile.am:
	* glade/gnome-db/gnomedbconnectprop.c: added support for
	GnomeDbConnectionProperties widget
	
2003-07-25  Pablo Saratxaga  <<EMAIL>>

	* configure.in: Added Macedonian (mk) to ALL_LINGUAS

2003-07-14  Daniel Baeyens <<EMAIL>>

	* glade/glade_gnomedblib.c:
	* glade/graphics/gnome-db-editor.xpm:
	* glade/gnome-db/Makefile.am:
	* glade/gnome-db/gnomedbeditor.c: added support for GnomeDbEditor
	widget

2003-07-11  Daniel Baeyens <<EMAIL>>

	* glade/glade_gnomedblib.c:
	* glade/graphics/gnome-db-gray-bar.xpm:
	* glade/gnome-db/Makefile.am:
	* glade/gnome-db/gnomedbgraybar.c: added support for GnomeDbGrayBar
	widget.

	* configure.in: incremented libgda/libgnomedb required versions.

2003--6-17  Guntupalli Karunakar  <<EMAIL>>

	* configure.in: Added "ml" to ALL_LINGUAS.

2003-05-31  Danilo Šegan  <<EMAIL>>

	* configure.in: Added "sr" and "sr@Latn" to ALL_LINGUAS.

2003-05-21  Yogeesh MB  <<EMAIL>>

        * glade-2.desktop.in:Added %F parameter to handle dnd glade file 
	on launcher.
	Fixes bug #112978 

2003-04-11  Damon Chaplin  <<EMAIL>>

	* Released Glade 2.0.0

2003-04-10  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_new_toolbar_button): init
	class_name to NULL.

	* glade/gbwidget.c (gb_widget_replace_child): get x & y of current
	child and use for new child.

	* glade/glade_project.c (check_deprecated_widget): new function to
	check for deprecated widgets.
	(glade_project_write_cxx_source): use the above function and return
	an error if deprecated widgets found, since gtkmm-2 doesn't support
	them at all.

	* glade/main.c: try to use on-disk or UTF-8 filenames where
	appropriate. I'm a bit unsure about what GnomeClient returns though.
	I'm assuming it returns on-disk encoded paths.

	* glade/glade.c: removed glade_current_directory global variable
	as it wasn't used.

	* glade/utils.c (glade_util_set_file_selection_filename) 
	(glade_util_get_file_selection_filename): new functions to convert
	filenames between UTF-8 and on-disk encoding when using a
	GtkFileSelection.
	(glade_util_fopen): same as fopen but takes a UTF-8 filename and
	converts to on-disk encoding.

	* glade/utils.c (glade_util_file_exists)
	(glade_util_file_last_mod_time) 
	(glade_util_copy_file, glade_util_ensure_directory_exists): assume
	filenames are input as UTF-8 and convert to on-disk encoding when
	necessary.

	* glade/save.c: 
	* glade/source.c: 
	* glade/property.c:
	* glade/glade_project_window.c:
	* glade/glade_project_options.c:
	* glade/glade_menu_editor.c: use the above utility functions.
	We now assume all filenames are UTF-8 internally, and only convert to
	on-disk encoding when we make system calls like fopen/stat/mkdir,
	or we need to pass filenames to GtkFileSelection which expects
	on-disk encoding. Fixes part of bug #107696.

	* glade/source.c (source_write_support_files): add a cast to avoid
	syntax warning in support.c. Fixes bug #104159.

	* glade/property.c (show_stock_items): only show stock GTK+ items
	in GTK+ projects.
	(property_set_icon): call show_stock_items() here to make sure we
	only show available stock items. (This means we may call it twice
	as we call it when setting the icon size as well, but it shouldn't
	matter too much.)

	* glade/glade_palette.c (glade_palette_set_show_gnome_widgets): new
	function to show/hide the GNOME widgets according to whether we are
	editing a GNOME project or not.

	* glade/glade_project_window.c (glade_project_window_setup_interface): 
	new function to set up the interface according to the project being
	edited. Called when a project is created/loaded.

	* glade/glade_gnomelib.c: 
	* glade/glade_gtk12lib.c: 
	* configure.in: removed the alternative palette layout option, since
	I want to hide GNOME widgets in GTK+ projects and that will make it
	more complicated to do.

	* glade/glade_atk.c (glade_atk_load_relations): init relationship.

	* glade/gnome/gnomedruidpageedge.c (gb_gnome_druid_page_edge_create_properties): 
	* glade/gnome/gnomemessagebox.c (gb_gnome_message_box_create_properties): 
	* glade/gnome/gnomeabout.c (gb_gnome_about_create_properties): 
	* glade/gbwidgets/gblabel.c (gb_label_create_standard_properties): 
	increased height of text fields.
	
	* glade/property.c (property_add_text): never show the horizontal
	scrollbar as it takes up too much vertical space.

	* glade/glade_gnomedblib.c: added GnomeDBDataSourceSelector back.

	* glade/property.c (add_signals_for_type, show_signals_dialog): for
	accelerator signals only show ACTION signals, since others won't
	work.

	* glade/glade_project.c (glade_project_write_ada95_source) 
	(glade_project_write_perl_source) 
	(glade_project_write_eiffel_source): improved error message a bit,
	though they still aren't enabled in glade-2 so it doesn't matter yet.

	* glade/save.c (save_requires_tags): require GNOME for all GNOME
	projects, even if they don't contain GNOME widgets. We do this as
	they may still use GNOME stock items.

	* glade/tree.c (tree_init): use SINGLE selection mode rather than
	MULTIPLE.
	* glade/editor.c (editor_select_widget): don't allow ctrl-selection
	to select multiple widgets, as it causes problems, isn't very useful,
	and is a bit confusing since you can't change properties of multiple
	widgets or cut & paste them. Should fix bug #99282.

	* glade/gbwidgets/gbimage.c (check_visible_sizes) 
	(check_icon_size): 
	* glade/glade_menu_editor.c (glade_menu_editor_construct) 
	(glade_menu_editor_construct): 
	* glade/property.c (create_stock_items): check if the icon_set
	returned by gtk_icon_factory_lookup_default() is NULL.
	May fix bug #105694, a nasty hang bug.

	* glade/property.c (on_signals_dialog_response): check for NULL
	widget name (which can happen if you open the signals dialog, then
	select a placeholder so no widget is shown in the property editor,
	then select a signal and hit OK). Fixes bug #98267.

	* glade/gbwidgets/gbinputdialog.c (gb_input_dialog_write_source): 
	fixed code to handle the buttons. We were creating new buttons rather
	than using the existing ones.

	* glade/gbwidget.c (gb_widget_save): for widgets in toolbars save
	the 'new_group' property as a normal property as well as a packing
	property, as libglade expects it to be a normal property.

	* glade/property.c: 
	* glade/palette.c: 
	* glade/tree.c: 
	* glade/glade_clipboard.c: 
	* glade/glade_project_window.c: patch from paolo borelli to make
	'Show XXX' menuitems on View menu into checkmenuitems. Bug #93744.

	* glade/gnome/gnomeapp.c: 
	* glade/gnome/gnomeiconlist.c (gb_gnome_icon_list_new): 
	* glade/gbwidgets/gbbutton.c: 
	* glade/gbwidgets/gbscrolledwindow.c (gb_scrolled_window_set_properties): 
	* glade/gbwidgets/gbnotebook.c (gb_notebook_set_properties): 
	* glade/main.c (final_setup_from_main_loop): got rid of warnings.

	* glade/glade_gnomelib.c: 
	* glade/gnome/Makefile.am (libgnomewidgets_a_SOURCES): removed
	BonoboWindow since it isn't used (it was never finished).

	* glade-2.desktop.in: patch from Fernando Herrera to add bugzilla
	info.

	* glade/gbwidget.c (gb_widget_new_full): changed 'gint type' to
	'GType type'.

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_add_child): init class_name
	to NULL. May fix bug #98987. (From Glynn Foster.)

	* doc/Makefile.am (EXTRA_DIST): added xmldocs.make.

	* glade/gbwidgets/gbaccellabel.c (gb_accel_label_write_source): added
	GTK_LABEL() macro to fix compile warning (Dermot Musgrove).

2003-03-26  Christian Rose  <<EMAIL>>

	* configure.in: Added "yi" to ALL_LINGUAS.

2003-02-05  Daniel Yacob  <<EMAIL>>

	* configure.ac: Added "am" (Amharic) to ALL_LINGUAS.

2003-01-20  Pablo Saratxaga  <<EMAIL>>

	* configure.in: Added Dutch (nl) to ALL_LINGUAS

2003-01-14  Rodrigo Moya <<EMAIL>>

	* configure.in: require CVS version of libgda/libgnomedb.

	* glade/glade_gnomedblib.c:
	* glade/gnome-db/gnomedbsqleditor.c: removed GnomeDbSqlEditor widget.

2002-12-22  Marius Andreiana  <marius galuna.ro>

	* configure.in: added doc/ro/Makefile
	* doc/Makefile.am: added ro subdir
	* doc/ro: added romanian translation

2002-11-14  Dmitry G. Mastrukov  <<EMAIL>>

	* configure.in: Added Belarusian to ALL_LINGUAS.

2002-11-02  Damon Chaplin  <<EMAIL>>

	* Released Glade 1.1.3

2002-11-02  Damon Chaplin  <<EMAIL>>

	* glade/property.c: include gdkkeysyms.h to fix GTK+ compilation.

	* glade/gbsource.c (lookup_signal_arg_names): rewrote to use a new,
	more compact, table of signal argument name data. Updated the name
	data for GTK+ 2, and also added names for libgnomeui and libgnomedb.
	(get_type_name): set is_pointer to TRUE for POINTER subtypes, and
	return GParamSpec* for G_TYPE_PARAM.
	(gb_widget_write_signal_handler_source): updated to use new signal
	argument name data.

	* glade/editor.c (editor_on_key_press_event): disabled the mouse-over
	typing hack. It is difficult to guarantee it will work OK.

	* glade/source.c: use our autogen.sh for GTK+ and GNOME projects,
	so generated projects don't depend on gnome-common.
	Also removed GNOME_COMPILE_WARNINGS from generated configure.in,
	and added back $(INTLLIBS) to the project's LDADD.

	* glade/data/gtk/autogen.sh: add a few bits from the current
	autogen.sh in gnome-common.

	* glade/gbwidgets/gbwindow.c (on_toggle_default_width):
	(on_toggle_default_height): fixed to pass -1 to reset values, and
	to set the width/height again if the toggle is selected.
	(Reported by Akihiko Yamawaki.)

	* glade/glade/glade_gnomedblib.c: hid GnomeDBDataSourceSelector as
	it can't really be used with Glade - it requires a provider to be
	set at init time.
	
	* glade/glade/gnome-db/*.c: added code generation to the new widgets.

	* glade/glade/graphics/gnome-db-*.xpm: updated a few icons.
	
2002-10-16  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtextview.c (gb_text_view_init): use the GtkCList
	emission hook thing to redraw whenever the text view is scrolled.
	Otherwise our selection rectangle messes the display up.

	* glade/gbwidgets/gbclist.c (gb_set_scroll_adjustments_hook): 
	renamed this and made it public so GtkTextView can use it.

	* glade/glade_palette.c (on_notebook_switch_page): make sure the
	button corresponding to the new page is depressed, in case accel
	keys are used to switch pages.
	(new_section): rearranged a little so the deprecated page is always
	the last notebook page, so using accel keys to step through pages
	steps through in correct order.

	* glade/property.c (propagate_key_press_to_main_notebook): new
	function used to propagate Ctrl+PgDn/Up keypresses to the main
	notebook to switch pages. Fixes part of bug #90938.

2002-10-15  Damon Chaplin  <<EMAIL>>

	* glade/main.c (final_setup_from_main_loop): only use the saved
	session file if it is newer than the real project file.

	* glade/source.c (source_write_main_c): don't translate comments, as
	it causes problems with UTF-8. See bug #95435.
	(source_write_no_editing_warning): same here.

	* glade/tree.c (tree_on_button_press): show popup menu on right-click.
	(tree_add_widget): added icons back.

	* glade/gbwidget.h (struct _GbWidget): added pixbuf field, since we
	need pixbufs for the GtkTreeView.

	* glade/gbwidget.c (gb_widget_init_struct): init pixbuf field to NULL.

	* glade/source.c (source_write_gtk_configure_in): bump up the version
	check of GTK+ to 2.0.0.

	* glade/glade_project_window.c (glade_project_window_new): use the
	global accel group.

	* glade/glade.c (glade_get_global_accel_group): new function to return
	the global accelerator group to add to all windows, so that global
	accelerators like Ctrl+S work in all windows.

	* glade/tree.c (tree_init): 
	* glade/palette.c (palette_create): 
	* glade/property.c (property_create): 
	* glade/glade_clipboard.c (glade_clipboard_init): 
	* glade/gbwidget.c (gb_widget_real_initialize): add the global accel
	group to any new windows created.

	* glade/glade_project_window.c: when setting the palette sensitive or
	insensitive, set the window's child sensitivity rather than the window
	so that accelerator keys still work.

2002-10-10  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbnotebook.c: ifdef'ed out Tab H Border & V Border
	properties, as they seem to be deprecated.

	* glade/gbwidgets/gbwindow.c: ifdef'ed out WM_NAME and WM_CLASS
	properties, since they aren't needed and we've been using them
	incorrectly for years. Oops! Should add Role instead at some point.

	* glade/gnome/gnomemessagebox.c: 
	* glade/utils.c (glade_util_gtk_combo_func): 
	* glade/gbwidgets/gblistitem.c (gb_list_item_write_source): 
	* glade/gbwidgets/gbtreeitem.c (gb_tree_item_write_source): 
	* glade/gbwidgets/gbvbuttonbox.c: 
	* glade/gbwidgets/gbhbuttonbox.c: 
	* glade/gbwidgets/gbradiobutton.c (gb_radio_button_create_properties): 
	* glade/gbwidgets/gblabel.c (gb_label_create_standard_properties): 
	* glade/gbwidgets/gbentry.c (gb_entry_write_source) 
	(gb_entry_set_properties): 
	* glade/property.c: 
	* glade/gbwidgets/gbhbox.c (gb_box_set_size): 
	* glade/gbwidgets/gbradiomenuitem.c: 
	* glade/glade_project_options.c (glade_project_options_init): 
	* glade/glade_palette.c (new_section): 
	* glade/glade_menu_editor.c: 
	* glade/editor.c (editor_show_grid_settings_dialog): 
	* glade/gbwidgets/gbdialog.c (show_dialog_creation_dialog): 
	* glade/gbwidgets/gbcombo.c: 
	* glade/gbsource.c (gb_widget_write_standard_source): 
	* glade/source.c: replaced deprecated code with new functions.

	* glade/gbwidgets/Makefile.am (libgbwidgets_a_SOURCES): removed
	gbpixmap.c as we don't use it any more. GtkPixmap is deprecated and
	GtkImage replaces it.

	* glade/glade_project_window.c (ToolBar): added 'New' command back to
	the toolbar, since it is needed frequently now.

2002-10-08  Damon Chaplin  <<EMAIL>>

	* doc/C/glade-user-guide/glade-user-guide-C.omf: 
	* doc/C/glade-turbo-start/glade-turbo-start-C.omf: 
	* doc/C/glade-faq/glade-faq-C.omf: patch from Frederic Crozat to
	update to newer format.

	* glade.spec.in: Updated help file installation, from Dermot Musgrove.
	Also updated the description & URL.

2002-10-06  Damon Chaplin  <<EMAIL>>

	* configure.in (ALL_LINGUAS): removed ar. The po file has no
	translated strings, an unset charset, no translator email address,
	and no ChangeLogs were added.

2002-09-25  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbstatusbar.c (gb_statusbar_write_source): output
	FALSE to turn resize grip off, not TRUE. Fix from Luc Dufresne.

2002-09-16  Damon Chaplin  <<EMAIL>>

	* Released Glade 1.1.2

2002-09-16  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_options.c (glade_project_options_init): added
	Language option back. glade-- now works with Glade 2 XML files.

	* glade/glade_project.c: Set GladeNumLanguages to 2 (C & C++) for
	now.

	* glade/glade_project.c (glade_project_write_cxx_source): updated
	error message to suggest running glade-- in a terminal. It is often
	intalled but outputs errors and fails.

	* glade/Makefile.am (glade_2_LDADD): added $(INTLLIBS) back.
	Fixes bug #82688.

2002-09-15  Damon Chaplin  <<EMAIL>>

	* glade/gnome-db/gnomedblogindlg.c (gb_gnome_db_logindlg_write_source): 
	use gnome_db_login_dialog_new() rather than old function name.

	* glade/gnome-db/gnomedbcombo.c (gb_gnome_db_combo_new): return a
	new combo widget.

	* glade/source.c (source_write_interface_c_preamble): use
	<libgnomedb/libgnomedb.h> rather than the old gda-ui.h.

	* glade/editor.c (editor_set_show_grid) 
	(on_grid_settings_response): check if there is a project. Patch from
	Jody Goldberg. Fixes bug #92733.

	* glade/gbwidgets/gbctree.c: Changed "columns" to "n_columns". Patch
	from Owen Taylor.
	(gb_ctree_new): Load "columns" or "n_columns" for compatability with
	1.1.1. Fixes bug #92522.

	* glade/gbwidgets/gbctree.c (gb_ctree_add_child): 
	* glade/gbwidgets/gbclist.c (gb_clist_add_child): Output a warning if
	too many column title widgets are found, and skip them. This isn't
	ideal, but shouldn't happen anyway.

	* glade/gbwidgets/gbmenubar.c (gb_menu_bar_setup_initial_menus): 
	* glade/glade_project.c (glade_project_add_pixmap) 
	(glade_project_remove_pixmap): 
	* glade/glade_clipboard.c (glade_clipboard_cut_or_copy): 
	* glade/gbwidget.c (on_widget_destroy): 
	* glade/editor.c (on_placeholder_destroy): 
	* glade/debug.c (glade_log_handler): removed debugging messages.
	Fixes bug #92736.

2002-09-12  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_replace_child): remove toolbar children
	from the tree here, rather than waiting for them to be destroyed.
	Something is holding a ref to them which causes a crash later when
	they do eventually get destroyed and we try to remove them from the
	tree. This is a temporary fix for bug #91116. Hopefully I'll figure
	out what is holding the ref at some point.
	
	* glade/gbwidgets/gbradiomenuitem.c (gb_radio_menu_item_new): 
	* glade/gbwidgets/gbradiobutton.c (gb_radio_button_new): only set the
	default group when creating the button/menuitem, not when loading.
	Fixes bug #91101.

	* glade/property.c (property_redirect_key_press): copy the entire
	GdkEventKey struct. There are a few new fields in GTK+ 2 that we were
	missing.

	* glade/data/gtk/autogen.sh: skip configure.in files in CVS subdir,
	since these shouldn't be processed. gnome-common also needs to be fixed
	before GNOME projects will be OK.

	* glade/editor.c (editor_select_widget): 
	(on_size_allocate): 
	(editor_do_drag_action): GtkFixed doesn't normally have a window now,
	so we need to subtract its allocation when handling coordinates of
	its children.

	* glade/gbwidgets/gboptionmenu.c (gb_menu_bar_on_edit_menu): reset
	the option menu's menu, after creating the menu editor dialog.

	* glade/gbwidgets/gbmenu.c (gb_menu_write_source): create the menu if
	it is attached to an option menu. Fixes bug #90042.

	* glade/gbwidgets/gblabel.c (gb_label_write_standard_source): output
	GTK_LABEL(%s) to avoid compiler warning.

	* glade/gnome/gnomeabout.c (gb_gnome_about_write_source): create a
	pixbuf for the logo, rather than passing a string. Fixes bug #90044.

	* glade/gbwidgets/gbprogressbar.c: removed XAlign & YAlign properties
	since GtkProgress is deprecated and GtkProgressBar doesn't allow you
	to set these.
	Removed 'Show Text' property since it is automatically shown if you
	set the 'Text' property.
	Removed the 'Activity Mode' property since it is automatically set to
	activity mode when the app calls gtk_progress_bar_pulse().

	* glade/tree.c (tree_init): 
	* glade/property.c (property_create): 
	* glade/palette.c (palette_create): 
	* glade/glade_project_window.c (glade_project_window_new): use
	gtk_window_move() to set the initial positions again. Fixes part of
	bug #88020 (all windows popping up in the same place). Though there
	is still some confusion over what apps are supposed to do about
	window positions.

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_write_source): output
	gtk_toolbar_set_orientation() rather than gtk_toolbar_set_style() to
	set the orientation. Fixes bug #86252.

	* glade/glade_project_window.c (glade_project_window_show_help_doc): 
	added #ifdef USE_GNOME around the functions for showing help.
	Fixes bug #90009.

	* glade/gbwidget.c (get_standard_properties): output the tooltip for
	menuitems. They have GTK_WIDGET_NO_WINDOW set now, but have an
	input-only window so they can still have tooltips.

	* glade/glade_project_window.c (HelpMenu): added mnemonics for Turbo
	Start and FAQ menuitems. Fixes bug #89984.

	* doc/xmldocs.make ($(docname).xml): changed spaces to tabs.
	From Martin Schulze. Fixes bug #90008.

2002-07-15  Damon Chaplin  <<EMAIL>>

	* Released Glade 1.1.1

2002-07-15  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbhbuttonbox.c
	(gb_hbutton_box_is_dialog_action_area): new function to check if the
	hbbox is the GtkDialog action area.
	(gb_hbutton_box_get_properties): 
	(gb_hbutton_box_set_properties): 
	(gb_hbutton_box_write_source): don't handle the spacing property for
	the GtkDialog action area.

	* glade/utils.c (glade_util_uses_border_width): return FALSE for the
	GtkDialog action area button box. It uses a style property.

	* glade/gbwidget.c (gb_widget_create_properties): 
	(get_standard_properties): 
	(get_standard_properties): always create the border width property,
	but hide it for specific widgets that don't need it.

2002-07-03  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_load): skip the "hscrollbar" and
	"vscrollbar" children of scrolled windows, as we don't support settings
	for them. libglade-convert outputs them.
	(gb_widget_replace_child): get rid of any "vscrollbar" or "hscrollbar"
	child name. They shouldn't be there, but may be in bad 1.1.0 files.
	Fixes bug #84010.

	* glade/utils.c (glade_util_uses_border_width): return FALSE for the
	vbox child of a GtkDialog. The border width is set by a GTK+ style
	property here. Fixes bug #86307.

2002-07-02  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbimagemenuitem.c: Use "label" and "use_stock"
	properties for stock items, to be compatable with libglade. We still
	load in the old "stock" property so 1.1.0 files can be loaded.
	The label for the "New" stock item can't be changed now, though.

	* glade/glade_menu_editor.c: stop user editing "New" stock item label.
	Also removed lots of debugging messages.

Tue Jun 25 15:20:33 2002  Jonathan Blandford  <<EMAIL>>

	* glade/property.c (add_signals_for_type): Add support for
	interfaces in the list of possible signals.

2002-06-18  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_remove_alignment): 
	(gb_widget_remove_event_box): 
	(gb_widget_remove_scrolled_window): remove the alignment/event box
	from the tree first, before rearranging the widgets.

	* glade/tree.c (tree_remove_widget_parent): get rid of this function,
	as it doesn't work.

	Fixes bug #85483.

2002-06-18  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_load): ignore the old
	"BonoboDock:contents" child name. It is implicit now.

	* glade/gnome/gnomeapp.c: 
	* glade/gnome/bonobowindow.c: 
	* glade/gnome/bonobodock.c: don't use GladeChildBonoboDockContents.
	The contents widget is implicit now, to be compatable with libglade.

	* glade/gb.[hc]: remove GladeChildBonoboDockContents child name.

	* glade/editor.c (delete_placeholder): check if parent is a BonoboDock
	and don't delete it, rather than checking for the old child name.

	Fixes bug #85637.

2002-06-16  Havoc Pennington  <<EMAIL>>

	* glade/Makefile.am (dist-hook): add simple srcdir != builddir
	fix

2002-06-10  Damon Chaplin  <<EMAIL>>

	* glade/gnome/bonobodock.c: use GladeChildBonoboDockContents rather
	than the old "BonoboDock:contents". Note that this is still broken wrt
	libglade, since libglade creates a vbox automatically.

	* glade/gbwidgets/gbwindow.c (gb_window_write_standard_source): fixed
	gtk_window_set_icon() call in generated code. Fixes bug #84434.

	* glade/editor.c (editor_delete_widget): updated code to handle
	deleting GnomeDruid pages.

2002-06-07  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomecontrol.c (do_query): changed the query to just
	"glade:show". So controls have to set that attribute if they want to
	appear in Glade. e.g. in the <control>.server.in.in:

	<oaf_attribute name="glade:show" type="boolean" value="TRUE"/>

	Sort of avoids bug #78060, as controls only get shown if they are
	intended to be used in Glade.
	
2002-06-06  Damon Chaplin  <<EMAIL>>

	* glade/Makefile.am (INCLUDES): 
	* glade/source.c (source_write_gtk_makefile_am): 
	(source_write_gnome_makefile_am): use $(prefix)/$(DATADIRNAME)/locale
	so Glade & the generated apps work with Solaris gettext. Fixes #83646.

2002-06-04  Damon Chaplin  <<EMAIL>>

	* doc/C/glade-user-guide/glade-user-guide.xml: use article rather than
	book, as that is what most other apps do and the result in yelp is
	nicer. Also changed chapter -> sect1, sect1 -> sect2, sect2 -> sect3.
	And fixed the paths to the figures.

	* doc/xmldocs.make: set appid and use that as the directory name
	rather than docname. We need this as we have 3 docs.

	* glade/main.c (main): use 'glade-2' as the app-id. It is used to
	find the help files, so we have to make sure we install them with
	the same directory name.

	* glade/glade_project_window.c (glade_project_window_show_help_doc): 
	(glade_project_window_show_turbo_start): 
	(glade_project_window_show_glade_faq): functions to show the help
	docs.
	(HelpMenu): added menuitems for them.

	Fixes bug #79480, I think.
	
	* glade/gbwidget.c (gb_widget_save): 
	* glade/gbsource.c (gb_widget_write_source): 
	* glade/editor.c (delete_placeholder): use proper child names.
	Probably fixes bug #84010.

2002-06-02  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gblabel.c (gb_label_create_standard_properties): 
	fix typo in tooltip. Bug #83876.

2002-06-01  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_insert_toolbar_child):
	switch the order of the arguments. Fixes bug #83299.
	(gb_toolbar_write_source): generate code to set the style and
	orientation.

2002-05-29  Damon Chaplin  <<EMAIL>>

	* glade/property.c: 
	* glade/glade_project_view.c: 
	* glade/glade_project_options.c: 
	* glade/glade_project.c: 
	* glade/glade_palette.c: 
	* glade/glade_menu_editor.c: 
	* glade/glade_keys_dialog.c: 
	* glade/glade_clipboard.c: Use GType rather than guint, so 64-bit
	systems are happy. Patch from George <<EMAIL>>.

2002-05-24  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbimage.c (check_visible_sizes): use G_N_ELEMENTS
	macro here instead of GladeIconSizeChoicesSize. The Sun C 5.0 compiler
	doesn't seem to like that. Fixes bug #82667.

2002-05-21  Damon Chaplin  <<EMAIL>>

	* glade.spec.in: applied update from Dermot Musgrove.

2002-05-17  Damon Chaplin  <<EMAIL>>

	* Released Glade 1.1.0

2002-05-17  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_window.c (HelpMenu): removed help contents item
	for now as it doesn't work.

2002-05-16  Damon Chaplin  <<EMAIL>>

	* glade/main.c (final_setup_from_main_loop): in the GNOME version,
	if no project is loaded from the command-line arg, set the current
	project to NULL and make the palette insensitive.

	* glade/glade_project_window.c (glade_project_window_new_project): 
	don't prompt about creating a new project if we don't currently have
	one, as there is no chance of losing data. And destroy the dialog
	before calling on_new_project_ok() in case that pops up another dialog.
	(glade_project_window_on_new_project_ok): in the GNOME version show
	a dialog for the user to choose between a GNOME and GTK+ project.
	Also make the palette sensitive after creating a new project.
	(glade_project_window_open_project): make the palette sensitive or not
	according to whether we now have a project.
	(glade_project_window_show_no_project_warning): new function to show
	a warning dialog when the user clicks Save/Options/Build commands when
	there is no current project.

	* glade/glade_project_view.c (glade_project_view_set_project): handle
	project being NULL. (May need fixing in a few other places as well.)

	* glade/glade_project_options.c (glade_project_options_init): make
	Gnome support option insensitive. It can't be changed after creating
	the project now.

	* glade/gbsource.c (get_type_name): updated to handle a few new GTK+
	types, so generated signal handler functions should compile.

	* glade/gnome/gnomeabout.c (gb_gnome_about_response_cb): connect our
	own response handler to stop the dialog being destroyed. Fixes #81997.

2002-05-15  Pablo Saratxaga  <<EMAIL>>

	* configure.in: Added Vietnamese (vi) to ALL_LINGUAS

2002-05-13  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_save): only check for the Moniker in
	the Gnome version.

	* glade/main.c (get_session_file): wrap in '#ifdef USE_GNOME' as it
	uses GnomeClient, from Felix Natter <<EMAIL>>.

2002-05-13  Damon Chaplin  <<EMAIL>>

	* glade/gladeconfig.h (_): remove extraneous comma in dgettext call,
	from Felix Natter <<EMAIL>>.

2002-05-08  Duarte Loreto <<EMAIL>>

        * configure.in: Added Portuguese (pt) to ALL_LINGUAS.

2002-04-22  Damon Chaplin  <<EMAIL>>

	* glade/property.c (property_get_choice): use gtk_menu_get_active()
	to get the last selected item when the menu is showing. The old way
	didn't seem to work any more (if it ever did).

	* glade/glade_project_window.c: Use "Project" instead of "File" in
	the menubar, and changed a few item labels.

	* glade/glade_project_options.c (glade_project_options_init): hide
	the language option. We'll add it back when generators have been
	ported to GTK+/GNOME 2 and the new XML format.

	* glade/glade_palette.c (new_section): create the button label with
	a mnemonic accelerator, and place the "Deprecated" section at the end.

	* glade/glade_gnomedblib.c: 
	* glade/glade_gnomelib.c: 
	* glade/glade_gtk12lib.c: moved deprecated widgets onto a "Deprecated"
	page, rearranged a little, and added mnemonics to the page buttons.

	* glade/gbwidgets/gbaspectframe.c: 
	* glade/gbwidgets/gbframe.c: use a GbWidget child for the label widget,
	rather than just showing a 'Label' text property. We save a special
	"type" packing property set to "label_item" to match libglade.

	* glade/gb.h: declare a few gb_frame_XXX() functions, so
	gbaspectframe.c can use them.

	* glade/editor.c (delete_placeholder): handle frame's label widget,
	which can be deleted on its own.

	* glade/gnome/gnomecontrol.c: use new icon. It isn't very good, but
	I couldn't think of anything better.

	* glade/graphics/*: resaved a few of the icons which weren't being
	displayed properly. Updated radio/checkbutton to new GTK+ 2 look.
	Fixed gnome-db-dsnconfig.xpm which had some strange characters in it.
	Updated gnome-about.xpm. Add gnome-control.xpm.
	
2002-04-19  jacob berkman  <<EMAIL>>

	* COPYING: add

2002-04-19  Damon Chaplin  <<EMAIL>>

	* glade/save.c (save_xml_file_internal): init GbWidgetGetArgData to
	{ 0 }.
	(save_string_internal): save any agent parameter.

	* glade/property.c: removed C++ 'Use Heap' property, and moved
	'Separate Class' above 'Separate File' since the latter depends on
	the former.

	* glade/load.c (real_load_project_file): init GbWidgetSetArgData to
	{ 0 }.
	(load_get_value): if data->agent is set, only check for properties with
	the same agent, e.g. "glademm".

	* glade/glade_widget_data.[hc] (struct _GladeWidgetData): removed
	cxx_use_heap as it isn't used any more. And removed code to init/copy
	it.

	* glade/glade_clipboard.c (glade_clipboard_cut_or_copy): init the
	GbWidgetGetArgData to { 0 }.
	(glade_clipboard_paste): same for the GbWidgetSetArgData here.

	* glade/glade-parser.c (struct _GladeParseState): added prop_agent,
	and basically copied what we do for prop_name in most of the code.

	* glade/glade-parser.h (struct GladeProperty): added agent field, to
	store any agent parameter.

	* glade/gbwidget.c (get_lang_specific_properties): set data->agent
	while we save the C++ properties. Also make the 'Separate File'
	property insensitive if 'Separate Class' is off.
	(set_lang_specific_properties): set data->agent again, and update
	sensitivity of 'Separate File' property if appropriate.
	(gb_widget_replace_child): handle the GtkFrame label widget. Not used
	yet.

	* glade/gbwidget.h (struct _GbWidgetGetArgData): 
	(struct _GbWidgetSetArgData): added agent field, to use when loading
	or saving properties that need an agent parameter, e.g. "glademm".

2002-04-18  Damon Chaplin  <<EMAIL>>

	* glade/glade_gnomelib.c (gnome_page): moved Bonobo control onto the
	Gnome page and removed the Bonobo page.

	* glade/gbwidget.c (gb_widget_load): after calling gb_widget_new_full()
	check if the widget was created and just return an error if creation
	failed.	It could fail for Bonobo controls.

	* glade/gbwidgets/gboptionmenu.c (gb_option_menu_write_source): only
	set the history id if it is > 0. It will be -1 if there is no menu.

	* glade/glade_gnome.c (glade_gnome_setup_initial_menus): moved here
	from gnomeapp.c. GtkMenuBar uses it as well now.

	* glade/gbwidgets/gbmenubar.c (gb_menu_bar_new): in GNOME projects
	use glade_gnome_setup_initial_menus() to add the initial items, since
	we need to use the GNOME stock system rather than the GTK+ one.

	* glade/gnome/gnomecontrol.c (on_control_dialog_ok): check if a widget
	is returned, and show a warning dialog if it couldn't be created.
	Fixes bug #77471.

	* glade/gnome/gnomeapp.c (gb_gnome_app_setup_initial_app): moved the
	code to create the initial menus to glade_gnome.c, so we can share it
	with GtkMenuBar.

2002-04-17  Damon Chaplin  <<EMAIL>>

	* glade/glade_menu_editor.c (set_submenu): check image is not NULL
	before trying to get icon_name from it.

	* glade/glade_gnome.c (glade_gnome_write_menu_item_source): use the
	child menu name to create the uiinfo name. In the old Glade the menus
	were created automatically and the names were based on the menuitems.
	But they are proper GbWidgets now, so we can't assume any names.

	* glade/gbsource.c (gb_widget_write_source): don't create the standard
	widget (that we use to get a few default values from) for Bonobo
	controls.

	* glade/glade-parser.[hc]: fix comments.

	* glade/gbwidgets/gbhbuttonbox.c (gb_hbutton_box_write_add_child_source): check child_name is not NULL before calling strcmp() with it.

2002-04-15  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_main_c): use GETTEXT_PACKAGE rather
	than PACKAGE for the textdomain stuff, and call
	bind_textdomain_codeset (GETTEXT_PACKAGE, "UTF-8") to make sure we
	get UTF-8.
	
2002-04-15  Damon Chaplin  <<EMAIL>>

	* glade/glade-parser.[hc]: changed to GPL, as I'm not sure what the
	implications of leaving LGPL code in a GPL app are. (The LGPL license
	lets you change code to GPL if you want to.)

2002-04-15  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_load): Use the GbWidget pointer in the
	widget data, after creating the widget, as it may be different from
	the one we started with (for Bonobo controls).
	(gb_widget_save): Save Bonobo controls with a class of 'BonoboWidget'.
	The 'Moniker' property will store the type of the control.

	* glade/utils.c (glade_util_uses_border_width): new function to return
	TRUE if the widget needs a 'Border Width' property. Most containers
	do, but Bonobo controls don't.

	* glade/gbwidget.c (set_standard_properties): 
	(get_standard_properties): 
	(gb_widget_create_properties): use the above.

	* glade/gnome/gnomecontrol.c: create a GbWidget for each type of
	Bonobo control, as needed. We have to do this for the property editor
	to work properly.

	* glade/gbwidget.c (add_standard_bottom_menu_items): don't add the
	add/remove alignment menu commands for menus or menuitems. Fixes
	bug #77361.

2002-04-11  Damon Chaplin  <<EMAIL>>

	* glade/glade_gnome.c (glade_gnome_write_menu_item_source): check
	image is not NULL before trying to get the icon name.

	* glade/gbsource.c (gb_widget_write_source): check the menu exists
	before checking it is a GbWidget.

	* glade/gb.c: added GladeParentKey, which we use if we need to store
	a pointer to the widget's parent.

	* glade/gbwidgets/gbcombo.c (gb_combo_new): save a pointer to the
	combo inside the popup window, so we can step up the widget tree.
	(gb_combo_write_source): generate code to do the same, so
	lookup_widget() will work OK when passed a combo popup window/list.

	* glade/source.c (source_write_support_files): in lookup_widget() use
	the special parent pointer if no normal parent is found.

	* glade/gbwidgets/gbbutton.c (gb_button_set_properties): only update
	the response id property if it has changed due to a stock id change.
	Otherwise it gets confused and doesn't change at all.

	* glade/utils.c (glade_util_check_is_stock_id): keep a static hash
	table of all stock ids and check if the given stock id is in there.
	gtk_stock_lookup() checks if it is a stock item with a label, which
	we don't want.

	* glade/glade_project.c (glade_project_load_option): 
	(glade_project_load_options): only use the base directory if we have
	one. When loading a saved session we may not have an xml filename so
	we won't have a base directory.

2002-04-10  Damon Chaplin  <<EMAIL>>

	* glade/glade_atk.c: removed table caption property for now, as it
	doesn't work due to a bug in ATK.

2002-04-10  Damon Chaplin  <<EMAIL>>

	* glade/gbsource.c (gb_widget_write_source): check the option menu's
	menu is a GbWidget. It won't be for an empty menu. Fixes part of bug
	#77973.

	* glade/gnome/gnomepixmap.c (gb_gnome_pixmap_write_source): check
	filename is NULL before checking *filename. Fixes part of bug #77973.

	* glade/glade_gnomelib.c (bonobo_page): hide BonoboWindow, until we
	support it.

2002-04-09  Damon Chaplin  <<EMAIL>>

	* glade/tree.c: changed to use GtkTreeView rather then GtkCTree.
	This should avoid bug #61752. I need to add back the widget icons and
	the popup menu at some point.

	* glade/save.c: save to GladeSessionFile if it is set. This is a new
	global variable that we only set when saving or loading a session.
	When saving a session, we don't backup the files or save translatable
	strings.

	* glade/property.c: made win_property global, as we need it for SM.

	* glade/main.c: added support for Session Management. I added new
	command-line options to show/hide windows, and code to save the
	project files in ~/.gnome2/glade-2.d/session/<session-id>.

	* glade/load.c (real_load_project_file): use GladeSessionFile if it is
	set.

	* glade/glade_project_window.c (glade_project_window_open_project):
	only update the current directory if filename is set. When loading
	the session it won't be.

	* glade/glade_project.c (glade_project_load_options): use
	GladeSessionFile if it is set, and don't change any of the settings
	if it is set.

	* glade/editor.c (editor_set_selection): new function to set all the
	selected widgets, which the widget tree now uses.

	* doc/C/glade-user-guide/glade-user-guide.xml: fixed XML.

2002-04-07  Zbigniew Chyla  <<EMAIL>>

	glade/main.c (main): Added bind_textdomain_codeset() call to make sure
	that gettext returns UTF-8 strings, used GETTEXT_PACKAGE for
	localization (not PACKAGE).
	glade/gladeconfig.h: s/PACKAGE/GETTEXT_PACKAGE/

2002-04-03  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbimage.c: removed debugging messages, one of which
	caused a SEGV. Fixes bug #77470.

2002-04-02  jacob berkman  <<EMAIL>>

	* doc/C/glade-faq/legal.xml: 
	* doc/C/glade-turbo-start/legal.xml: 
	* doc/C/glade-user-guide/legal.xml: add missing files to fix build

2002-04-01  Damon Chaplin  <<EMAIL>>

	* doc/C/glade-turbo-start/*: 
	* doc/C/glade-user-guide/*: 
	* doc/C/glade-faq/*:
	* doc/C/Makefile.am:
	* Makefile.am: 
	* configure.in: updated docs for GNOME 2. I've converted the doc
	headers to the XML stuff, but I'm not sure if the docs are valid yet,
	as I can't test.

	* omf-install/Makefile.am (omf_dest_dir): change to 'glade-2'.

2002-04-01  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbbutton.c (gb_button_toolbar_write_source): output
	a hack to set the "use_underline" property of toolbar button labels,
	so mnemonic accelerators work.

	* glade/main.c: started adding session management support. Doesn't
	do much yet except restart Glade.

	* glade/tree.c: 
	* glade/property.c: 
	* glade/palette.c: 
	* glade/editor.c: 
	* glade/glade_project_window.c: commented out set_uposition() calls
	to set the initial position of the windows. I'm not sure we should do
	that, or if it will cause problems with SM. It should maybe use
	gtk_window_move() instead now, if it does set the positions.

	* glade/glade_menu_editor.c (glade_menu_editor_construct): added
	mnemonic accels for the buttons.

	* glade/glade_gnome.c (glade_gnome_is_first_radio_menu_item_in_group): 
	updated so it just checks if the group GSLists are the same.

	* glade/glade_clipboard.c (glade_clipboard_cut_or_copy): if the widget
	being cut/copied isn't a toplevel, we insert a dummy toplevel window
	so we can still store the packing properties.
	(glade_clipboard_paste): init the all_widgets hash so it contains
	pointers to all widgets already in the component. Thus ATK relations
	to targets in the same component will still work after cut & paste.
	Also if a dummy toplevel window was inserted in cut/copy, skip it when
	pasting by using the first child.

	* glade/gbwidget.c (gb_widget_load): use widget_info->name when saving
	pointers to the widgets in the all_widgets hash, so that when pasting
	we store the original names of the widgets, not the name after
	renaming.

	* glade/gnome/gnomecontrol.c: defined BONOBO_DISABLE_DEPRECATED and
	BONOBO_UI_DISABLE_DEPRECATED to make sure we don't use deprecated
	stuff.

	* glade/gbwidgets/gbimage.c (gb_image_set_properties): 
	(gb_image_get_properties): use "pixbuf" rather than "file" property,
	so libglade works OK.

2002-03-29  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomedateedit.c (gb_gnome_date_edit_set_properties): 
	removed some 'ifdef'ed out code that we don't need now.

	* glade/gnome/gnomeapp.c: use "New" rather than "New File" for the
	initial menu item. Moved "Preferences" from Settings menu to Edit menu,
	and removed Settings menu. However we now have 3 menuitems on the Edit
	menu using 'p' as the accelerator - Paste, Properties and Preferences.
	I don't know what we should do.
	Removed the stock toolbar stuff, and created using normal functions
	instead. Also don't set the menubar border width.

	* glade/gnome/bonobowindow.c: remove the stock toolbar stuff, and
	don't set the menubar border width.

	* glade/gnome/bonobodockitem.c (gb_bonobo_dock_item_add_child): 
	removed code to setup menubar/toolbar. I don't think we need it, but
	I'm not sure yet. We still have the problem of how to handle specific
	settings vs using user default settings.

2002-03-29  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gblabel.c (gb_label_find_mnemonic_widget): only
	return TRUE for labels whose parents have an activate_signal method
	(i.e. buttons), or menuitems. We were returning TRUE for too many
	things.

2002-03-28  jacob berkman  <<EMAIL>>

	* configure.in: add AC_PATH_XTRA check

2002-03-28  Damon Chaplin  <<EMAIL>>

	* glade/gb.h: added declarations for gb_button_find_radio_group()
	and gb_button_toolbar_find_child_widgets(), and removed old
	gb_toolbar_get_toolbar_radio_group_widget().

	* glade/glade_gnome.c: removed stock pixmap stuff. We use GTK+ stock
	pixmaps now instead.

	* glade/gbwidgets/gbtoolbar.c: removed lots of old code for handling
	toolbar buttons. gbbutton.c now does this.

	* glade/gbwidgets/gbradiobutton.c: 
	* glade/gbwidgets/gbbutton.c (gb_button_find_radio_group): moved this
	here from gbradiobutton.c so the code generation stuff can use it.
	(gb_button_toolbar_write_source): updated to use the above rather than
	the old gb_toolbar_get_toolbar_radio_group_widget().

2002-03-27  Damon Chaplin  <<EMAIL>>

	* glade/utils.c (glade_util_string_from_flags): fixed this so it
	handles enum values of 0. It would get into an infinite loop before.

	* glade/load.c (load_parse_date): check for NULL or empty string
	and return 0.

	* glade/glade_menu_editor.c: updated to handle GNOME stock items.
	We use the old GNOME stock system for menus in GNOME projects, and
	use the GTK+ stock system when Glade is built without GNOME support
	or the project has GNOME support turned off.
	Also updated the radio group code to use widget names for the groups
	rather than the old group names.
	This all needs quite a bit more testing.

	* glade/glade_gnome.c: removes stock menu pixmap and stock button
	stuff, since we use the GTK+ stock for that now.
	(glade_gnome_write_menu_item_source): check for GtkSeparatorMenuItem
	as well as empty menuitems now.
	(glade_gnome_write_menu_item_source): updated to use GTK+ stock ids
	for the pixmaps, rather than our old array.

	* glade/glade-parser.c (handle_signal): load "last_modification_time".

	* glade/glade-parser.h (GladeSignalInfo): added last_modification_time.

	* glade/gbwidget.c (copy_signals): load the last_modification_time.
	(gb_widget_save): call gb_bonobo_dock_item_save_packing_properties()
	for GnomeDockItems.

	* glade/gbwidgets/gbimage.c (gb_image_write_source): don't generate
	code for the GtkImage if it is in a GtkImageMenuItem in a GNOME
	project. It is handled by the GnomeUIInfo structs.

	* glade/gbwidgets/gbseparatormenuitem.c: 
	* glade/gbwidgets/gbimagemenuitem.c: just call
	glade_gnome_write_menu_item_source() to generate the code if it is a
	GNOME project.

	* glade/gbwidgets/gbimagemenuitem.c: 
	* glade/gbwidgets/gbmenuitem.c: don't create a 'Label' property in
	the property editor. For now we only support editing menu stuff in the
	menu editor.

	* glade/gbwidgets/gbimagemenuitem.c: 
	* glade/gbwidgets/gbradiomenuitem.c: 
	* glade/gbwidgets/gbcheckmenuitem.c: 
	* glade/gbwidgets/gbmenuitem.c: don't create a popup menu command
	to remove the label. For now we only support editing menu stuff in the
	menu editor.

	* glade/gbwidgets/gbradiomenuitem.c: 
	* glade/gbwidgets/gbcheckmenuitem.c: removed "always_show_toggle"
	property since it is deprecated and useless.

	* glade/gbwidgets/gblabel.c: 
	* glade/gbwidgets/gbaccellabel.c: don't store the label text in the
	object's data. Just use the actual label text. I was thinking of
	displaying an error message in the label when the markup was invalid,
	in which case we needed to store the label text elsewhere. But I
	didn't do that, so we can use the normal label text. This was causing
	a problem with buttons, which knew nothing about the label in the
	object's data. Fixes bug #75621.

	* glade/gnome/bonobodockitem.c (gb_bonobo_dock_item_get_properties): 
	only output the behavior properties when showing, and don't save
	the packing properties here.
	(gb_bonobo_dock_item_save_packing_properties): new function to save
	the packing properties. gb_widget_save() calls this explicitly.
	(gb_bonobo_dock_item_set_properties): when loading, load the behavior
	flags from the "behavior" packing property.

	* glade/gnome/bonobodock.c (gb_bonobo_dock_add_child): load the dock
	item properties from the packing properties rather than normal
	properties.

2002-03-21  Damon Chaplin  <<EMAIL>>

	* glade/glade_atk.c (glade_atk_update_relation_dialogs): new function
	that the property editor calls when the widget shown is changed.
	It refreshes all the relations dialogs to reflect the relations for
	the new widget.
	(glade_atk_write_relations_source): generate code for relations.

	* glade/property.c (property_set_widget): call
	glade_atk_update_relation_dialogs().

2002-03-20  Damon Chaplin  <<EMAIL>>

	* glade/glade_gnome.h: removed declaration of GnomeDialogPrivate, as
	we don't need it.

	* glade/glade_gnome.c: removed glade_gnome_is_gnome_dialog_button().
	The gbbutton code figures this out now.

	* glade/gbwidget.c (get_standard_properties): allow all GtkButton
	subclasses to have tooltips. These use InputOnly windows now, so
	tooltips work, even though they are NO_WINDOW widgets.

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	* glade/gbsource.c (gb_widget_write_source): removed use of old
	"GnomeDialog:action_area" child name.

	* glade/editor.c (editor_set_cursor): only set mouse_over_widget to
	GbWidgets, or the pointer will become invalid if the widget is
	destroyed. Fixes bug #74386.
	Removed a lot of debug messages.
	
	* glade/gbwidgets/gbdialog.c (add_button): make the help button
	secondary.
	Removed the 'No/Yes' buttons option, as people are not supposed to use
	that.
	
	* glade/gbwidgets/gbbutton.c (gb_button_gnome_find_child_widgets): 
	check for hbox inside hbox, which is what GNOME uses in buttons.
	(gb_button_gnome_recreate_children): create hbox in hbox, rather than
	just one hbox, to match what GnomeDialog does.
	(gb_button_set_properties): when the response_id changes, set the
	child_secondary flag, TRUE for HELP, FALSE otherwise.

	* glade/gnome/gnomemessagebox.c (gb_gnome_message_box_write_source): 
	removed the hack to create the message box with an OK button and then
	remove it. It isn't needed any more. Also removed the other hacks to
	handle the action area. We can just do the same as GnomeDialog now.

	* glade/gbwidgets/gbhbuttonbox.c (gb_hbutton_box_write_source): removed
	old hack for GnomeMessageBox source code.

2002-03-18  Damon Chaplin  <<EMAIL>>

	* glade/load.c (real_load_project_file): create the all_widgets hash
	before loading, and call new function, load_atk_properties(), to load
	ATK properties for each component after creating all widgets.

	* glade/glade_widget_data.h (struct _GladeWidgetData): added relations
	field, which is a GList of GladeRelation*.

	* glade/glade_widget_data.c: added support for relations.

	* glade/glade_atk.c: added support for relations. Doesn't generate
	code yet, though.

	* glade/gbwidget.h: added declaration of GladeWidgetInfoKey, which we
	use to store a pointer to the GladeWidgetInfo inside each widget while
	loading. We need it to set ATK properties after creating everything.
	Also added all_widgets hash to GbWidgetSetArgData which contains all
	the widgets in the interface, keyed by their name. We need this to
	resolve ATK relations.
	
	* glade/gbwidget.c (set_standard_properties): remove call to
	glade_atk_load_properties(). We now set them after creating all the
	widgets, so we can resolve ATK relations.
	(gb_widget_load): add the new widget to the all_widgets hash, and
	store a pointer to the GladeWidgetInfo in it.

	* glade/glade_clipboard.c (glade_clipboard_paste): initialize
	all_widgets to NULL.

	* glade/glade_project.c: 
	* glade/gbwidgets/gbscrolledwindow.c: 
	* glade/gbwidgets/gbbutton.c: removed debugging messages.

2002-03-15  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_component): output the GLADE_ATK_SOURCE
	buffer after the accelerators.
	(source_write_support_files): output the declaration of a new support
	function, glade_set_atk_action_description().
	(source_write_support_files): output the function here.

	* glade/save.c (save_buffer_add_indent): renamed from save_indent(),
	and now takes a buffer and an indent value so we can call it from the
	ATK code.
	Made save_add_translatable_string(), save_buffer_add_indent(), and
	save_buffer_add_string() global so the ATK code can use them.

	* glade/property.c (property_create): set tab vborder to 0, so it
	doesn't get too big when we add the ATK icon.
	(property_create): call glade_atk_create_property_page() to create
	the a11y page.

	* glade/main.c (main): add "gail" to GTK_MODULES to ensure it is
	loaded. We need it loaded to query for accessibility properties.

	* glade/gbwidget.c (set_standard_properties): added call to 
	glade_atk_set_properties() or glade_atk_load_properties().
	(gb_widget_save): added call to glade_atk_save_properties().
	(get_standard_properties): added call to glade_atk_get_properties().

	* glade/gbsource.h (enum): added GLADE_ATK_SOURCE buffer.

	* glade/gbsource.c (gb_widget_write_standard_source): added call to
	glade_atk_write_source().

	* glade/Makefile.am: added glade_atk.[hc]

2002-03-13  Damon Chaplin  <<EMAIL>>

	* glade/property.c (property_set_icon_filesel): new function to
	turn on/off the file selection in icon properties.

	* glade/glade_project_window.c (FileMenu): use the same icons as the
	GTK+ version, GTK_STOCK_CONVERT for 'Build Source' and
	GTK_STOCK_PROPERTIES for 'Project Options'.

	* glade/gbwidgets/gbbutton.c: updated to handle GnomeDialog buttons.
	These can be stock items or be stock icons with a label. But they can't
	have user-defined icons or have user-defined contents. Ugh.

	* glade/gbwidgets/gbimage.c: save the icon size as an integer rather
	than the enum symbol string. This sucks, since the saved integer is
	the enum value, which is pretty meaningless.

	* glade/gnome/gnomemessagebox.c (gb_gnome_message_box_new): 
	* glade/gnome/gnomedialog.c (gb_gnome_dialog_new): updated to set
	GladeButtonStockIDKey with the stock id strings rather than the old
	integer indexes.

	* glade/glade_project_window.c (glade_project_window_about): updated
	the GTK+ About dialog to match the GNOME one fairly closely, but
	without the Credits button.

	* glade/gbwidgets/gbhbuttonbox.c: include <gtk/gtkbutton.h>.

2002-03-11  Johan Dahlin  <<EMAIL>>

	* glade/gnome/gnomecontrol.c (do_query): Changed 
	bonobo_activation_query to skip all panel applets,
	because most of them crash at the moment.

2002-03-11  Damon Chaplin  <<EMAIL>>

	* configure.in: removed stuff to set GLADE_LOCALE_DIR and
	GLADE_DATA_DIR. We use -D flags instead now.

	* acconfig.h: removed GLADE_LOCALE_DIR and GLADE_DATA_DIR.

	* glade/Makefile.am (INCLUDES): set GLADE_LOCALEDIR here.
	(GLADE_DATADIR was already being set here.)

	* glade/source.c (source_write_main_c): use gnome_program_init() rather
	than old gnome_init(), and set the APP_DATADIR so we can use it to
	find pixmaps.
	Removed code to add the source directory as a pixmaps directory for
	now. Need to pass in a -DPACKAGE_SRC_DIR and use that if we want to.
	(source_write_gtk_autogen_sh): updated to use new GLADE_DATADIR.
	(source_write_gtk_configure_in): removed all the rubbish to set
	the PACKAGE_LOCALE_DIR and PACKAGE_DATA_DIR in config.h.
	(source_write_gtk_makefile_am): We now just use -DPACKAGE_DATA_DIR and
	-DPACKAGE_LOCALE_DIR here like everyone else does.
	(source_write_gnome_configure_in): ditto for GNOME apps.
	(source_write_gnome_makefile_am): ditto for GNOME apps.
	(source_write_acconfig_h): removed PACKAGE_LOCALE/DATA/SOURCE_DIR.
	(source_write_gnome_create_pixmap_functions): use
	gnome_program_locate_file() rather than gnome_pixmap_file().
	(source_write_gnome_create_pixmap_functions): output a create_pixbuf()
	function as well.
	(source_create_pixbuf): new utility function to output a call to
	create_pixbuf() in the code. The output is slightly different for
	GTK+ and GNOME apps.

	* glade/save.c (save_color): use %04x rather than %4x for the colors,
	so they are zero-padded.

	* glade/main.c: include bonobo.h, use GLADE_LOCALEDIR rather than old
	GLADE_LOCALE_DIR.

	* glade/glade_project_window.c (glade_project_window_about): removed
	unused hbox, href vars.

	* glade/glade-parser.c: few updates from libglade code. Still a few
	changes to incorporate. Though we may have to use more of our own code
	to handle agent parameters.

	* glade/gbwidget.h: updated comment to note that the returned value
	of gb_widget_input_pixmap_filename() should be freed when loading.
	I'd forgotten about that one.

	* glade/gbwidget.c (gb_widget_load): 
	* glade/gb.c: 
	* glade/editor.c (editor_can_delete_widget): updated final GladeChild*
	names.

	* glade/utils.c: compile warning fixes.

	* glade/gbwidgets/gbwindow.c (gb_window_set_standard_properties): 
	free the pixmap filename when loading.
	(gb_window_write_standard_source): use create_pixbuf() function to
	output the code to create the pixbuf.

	* glade/gbwidgets/gbframe.c (gb_frame_get_properties): 
	* glade/gbwidgets/gbaspectframe.c (gb_aspect_frame_get_properties): 
	use gtk_frame_get_label() rather than old GTK_FRAME (widget)->label.

	* glade/gnome/gnomepropertybox.c: compile warning fixes.

	* glade/gnome/gnomepixmap.c: finished. use gnome_program_locate_file()
	rather than gnome_pixmap_file() in generated code.

	* glade/gnome/gnomedruidpagestandard.c: 
	* glade/gnome/gnomedruidpageedge.c: 
	* glade/gnome/gnomedruid.c: finished.

	* glade/gnome/gnomeappbar.c (gb_gnome_appbar_set_properties): use
	GTK_WIDGET() to avoid compile warning.

	* glade/gnome/gnomeabout.c: finished. Note that we do special stuff
	for the "translator_credits" property. If it is left empty, we save
	it with the value "translator_credits". So the translator then replaces
	that with their name in each po file.

	* glade/gnome/bonobowindow.c: use GladeChildXXX names.

2002-03-07  Damon Chaplin  <<EMAIL>>

	* glade/save.c (save_requires_tags): added "canvas" lib, used when
	GnomeCanvas is in the interface.
	(save_color): save color in format '#rrrrggggbbbb'.

	* glade/load.c (load_parse_color): use '#rrrrggggbbbb' format now, as
	libglade and some GnomeDruid properties expect. Use gdk_color_parse()
	to parse it. If an error occurs set the error code and return white.
	(It used to return NULL and then load_color() would SEGV.)

	* glade/glade_gnomelib.c: removed GnomePaperSelector.

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	* glade/gbwidget.c (gb_widget_load): 
	* glade/gb.c: removed GladeChildGnomeFileEntry. GnomePixmapEntry is
	now a subclass, so doesn't need this internal child.

	* glade/editor.c (paint_widget): only draw the grid for GtkLayout
	and GtkFixed, not subclasses.

	* glade/gnome/gnomepropertybox.c: use GladeChildGnomePBoxNotebook.

	* glade/gnome/gnomepixmapentry.c: renamed "preview" to "do_preview".
	Added "history_id", "max_saved", "browse_dialog_title", "modal".
	It used to have a child GnomeFileEntry where these were set, but it
	is now a subclass instead.

	* glade/gnome/gnomeiconlist.c: removed extended selection option, as
	it isn't used any more. Output gnome_icon_list_new() rather than
	gnome_icon_list_new_flags().

	* glade/gnome/gnomedruid.c: used gtk_container_children() rather than
	including private GnomeDruid struct.

	* glade/gnome/Makefile.am (libgnomewidgets_a_SOURCES): removed
	gnomeprintpaperselector.c. It is now in libgnomeprintui, doesn't
	support properties, and has no libglade support.

2002-03-06  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomeiconentry.c: renamed "title" to
	"browse_dialog_title". Stored MaxSaved ourselves. Finished off.
	use GLADE_DEFAULT_MAX_HISTORY_SAVED constant rather than '10'.

	* glade/gnome/gnomefileentry.c: renamed "directory" to
	"directory_entry", and "title" to "browse_dialog_title"..
	use GLADE_DEFAULT_MAX_HISTORY_SAVED constant rather than '10'.
	
	* glade/gnome/gnomeentry.c (gb_gnome_entry_write_source): use
	GLADE_DEFAULT_MAX_HISTORY_SAVED constant rather than '10'.

2002-03-05  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtable.c: use "x_padding", "y_padding" rather than
	"xpad" & "ypad" for children. Fixes bug #73355.

2002-03-05  Damon Chaplin  <<EMAIL>>

	* glade/source.c: updated the build files that we generate to use
	pkg-config etc.

	* glade/save.c (save_requires_tags): oops. I forgot the '/' at the
	end of the <requires lib="XXX"/> tags.

	* glade/glade_project_window.c (glade_project_window_about): don't
	make this window modal, or the 'Credits' dialog won't close.

	* glade/gbsource.c (gb_widget_write_source): only do the special
	option menu and menu code for GbWidgets, not for internal widgets we
	know nothing about.

	* glade/gbwidgets/gbtoolbar.c: use gtk_toolbar_remove_space() instead
	of our old kludge. Used GladeChild* names. Removed some of the radio
	group code, but this is unfinished.

	* glade/gbwidgets/gbradiobutton.c: removed
	gb_radio_button_reset_radio_group() as GTK+ does handle this now
	(though it doesn't update buttons perfectly).

	* glade/gbwidgets/gbdialog.c: for the standard dialogs use 'okbutton1'
	etc. for the names instead of 'ok_button1', or it will get an
	underlined accelerator key when switching to a normal button.

	* glade/gnome/gnomehref.c: don't call gnome_href_set_url() with NULL.
	Fixed source code output.
	
	* glade/gnome/gnomefontpicker.c: renamed "use_font" to
	"use_font_in_label" and "use_font_size" to "label_font_size".
	Only save the title and preview text if different to the default.

	* glade/gnome/gnomepixmapentry.c: 
	* glade/gnome/gnomeiconentry.c: 
	* glade/gnome/gnomefileentry.c: 
	* glade/gnome/gnomeentry.c: use GladeChild* child names. Keep the
	history_id property ourselves, as GnomeEntry doesn't let you change it
	once it is set.

	* glade/gb.c: 
	* glade/editor.c: 
	* glade/gbwidget.c: 
	* glade/glade_clipboard.c: 
	* glade/glade_menu_editor.c: 
	* glade/gbwidgets/gbclist.c: 
	* glade/gbwidgets/gbctree.c: 
	* glade/gnome/gnomedialog.c: 
	* glade/gnome/gnomemessagebox.c: 
	* glade/gnome/gnomeapp.c: use GladeChild* child names defined in gb.c
	rather than hard-coded strings or per-file variables. It makes the
	special child code much easier to find.

	* glade/gnome/gnomeabout.c: set Modal to NULL, since modal GnomeAbout
	dialogs do not work, as we found out with Glade itself. (The child
	'Credits' dialog causes problems.)

	* glade/gnome/bonobowindow.c (gb_bonobo_window_setup_initial_app): use
	GladeChildToolbarButton rather than "Toolbar:button".

	* glade/gbwidgets/gbtreeview.c (gb_tree_view_write_source): fixed the
	code output. It was using 'text' instead of 'tree' everywhere.

2002-03-01  Damon Chaplin  <<EMAIL>>

	* glade/save.c: 
	* glade/glade_clipboard.c (glade_clipboard_cut_or_copy): updated the
	SYSTEM identifiers used in the XML.

	* glade/save.c: output the <requires> tags if we need the gnome,
	gnomedb or bonobo libraries.

	* glade/glade_gnome.h: added GLADE_LIBGNOMEUI_GETTEXT_PACKAGE.
	* glade/glade_gnome.c: used above.

	* glade/gbwidget.c (add_standard_bottom_menu_items): only add commands
	to add/remove alignments/eventboxes if the widget can be deleted.

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	* glade/editor.c (editor_can_delete_widget): check the new font/color
	selection children.

	* glade/gbwidgets/gbradiobutton.c: updated to use widget names for
	the group rather than group names, as that is what libglade wants now.
	Not quite finished.
	
	* glade/gbwidgets/gboptionmenu.c (gb_option_menu_add_child): use
	g_type_name (G_OBJECT_TYPE (child)) rather than
	g_type_name_from_instance(), as it is what GTK+ does, and doesn't
	output a compile warning.

	* glade/gnome/gnomecolorpicker.c: used GLADE_LIBGNOMEUI_GETTEXT_PACKAGE
	when getting translated GNOME strings.

	* glade/gnome/gnomefontpicker.c: used GLADE_LIBGNOMEUI_GETTEXT_PACKAGE
	when getting translated GNOME strings.
	(gb_gnome_font_picker_write_source): possibly translate the preview
	text now, since that is what GnomeFontPicker does.

2002-02-27  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (combo_foreach): call the callback for the button
	as well. I thought this may help the selection problems in fixed/layout
	but it didn't.

	* glade/gb.h: added declarations of GladeFixedChildX/Y and
	GladeLayoutChildX/Y.

	* glade/editor.c: update to cope with GtkFixed/GtkLayout. We now have
	to do our own pointer grab when moving/resizing or we don't get the
	motion notify events. The coordinate calculation in GtkLayout has
	changed as well. Note that moving/resizing still isn't perfect. But
	it is about as good as Glade 1, I think.

	* glade/gbwidgets/gbscrolledwindow.c: removed "hupdate_policy" and
	"vupdate_policy" since they are not really useful. If we still want
	them we'd need to use internal children for the scrollbars and use
	the "update_policy" of each of those.

	* glade/gbwidgets/gbfixed.c: renamed ChildX/Y to GladeFixedChildX/Y,
	and moved to gb.c so editor code can update them.

	* glade/gbwidgets/gblayout.c: renamed ChildX/Y to GladeLayoutChildX/Y,
	and moved to gb.c so editor code can update them.

	* glade/main.c (parse_command_line): added support for --version
	option, in GTK+ version of Glade (merged from head).

	* glade/gnome/gnomepixmap.c (gb_gnome_pixmap_set_properties): when
	loading, set Scaled flag if the width or height are set (merged from
	head).

	* glade/gbwidgets/gbgammacurve.c: 
	* glade/gbwidgets/gbcurve.c: I've taken the 'Curve Type' property out
	since GTK+ crashes if this is set to certain values (merged from head).

2002-02-26  Damon Chaplin  <<EMAIL>>

	* glade/source.c: merged in my code to recognize configure.ac files,
	and to not output a configure.in if it is found.

	* glade/property.c: merged in my on_property_focus_out() fixes from
	head, so int & float properties get updated when they lose focus.
	Also used "value_changed" signal of spinbuttons rather than "changed".

2002-02-26  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_replace_child): don't use set_uposition
	for GtkFixed children.
	(option_menu_foreach): make the temp widget a GtkOptionMenu. That
	makes the code generator output the correct code to add the menu to
	the option menu.

	* glade/gbsource.c (gb_widget_write_source): added hack to add the menu
	to the option menu.

	* glade/editor.c: don't call gtk_widget_set_uposition() for children
	of GtkFixed containers any more.

	* glade/gbwidgets/gboptionmenu.c (gb_option_menu_write_add_child_source): 
	we don't output the code to add the menu to the option menu here. It
	must be done after all children are added so I've added a hack for it
	in gb_widget_write_source().

	* glade/gbwidgets/gbfixed.c: added support for child X & Y properties.

	* glade/gnome/gnomecontrol.c (do_query): changed debugging message so
	it checked if servers is non-NULL before using it. This is why it was
	crashing for me.

2002-02-26  Johan Dahlin  <<EMAIL>>

	* glade/gnome/gnomecontrol.c (do_query): Sort by description instead
	of iid

2002-02-25  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_component): output new
	GLADE_OBJECT_HOOKUP buffer, and move the tooltips hookup code next to
	it.
	(source_write_interface_c_preamble): output GLADE_HOOKUP_OBJECT
	and GLADE_HOOKUP_OBJECT_NO_REF macros, to make it obvious in the code
	output that this is Glade stuff, and make it a bit tidier.
	(source_write_main_c): output the PACKAGE_DATA_DIR as the last pixmaps
	dir, as it will be checked first.
	(source_write_gtk_configure_in): added GETTEXT_PACKAGE and AC_SUBST it.
	That is what the glib-gettextize stuff uses. Added it to acconfig.h.
	Also switched to AM_GLIB_GNU_GETTEXT, so we don't need the intl/ dir
	any more. Yeehaa!
	(source_write_gtk_create_pixmap_functions): output find_pixmap_file()
	a static support function to find a pixmap file for GTK+ apps, using
	the pixmaps_directories. It uses g_file_test() instead of our old
	check_file_exists() function.
	Updated create_pixmap() to use it.
	Added create_pixbuf() which is similar, but returns a GdkPixbuf.

	Removed intl from all SUBDIRS output, and from any -I flags, and from
	AC_OUTPUT.
	Removed all GNOME macros stuff. Apps don't need them now as they are
	provided in gnome-common.
	Removed old deprecated get_widget() macro.
	
	* glade/glade_widget_data.[hc] (struct _GladeWidgetData): removed x & y
	fields, and the GLADE_X_SET and GLADE_Y_SET flags. In GtkFixed and
	GtkLayout, X & Y are now packing properties. (We may add X & Y
	properties for windows at some point, but they will be widget-specific
	properties.)

	* glade/property.c: removed code related to the old X & Y properties.

	* glade/glade_gnome.c (glade_gnome_write_menu_item_standard_source): 
	use new	GLADE_HOOKUP_OBJECT() macros in generated code.

	* glade/gbwidget.c: removed code related to the common X & Y properties

	* glade/gbsource.h: added GLADE_OBJECT_HOOKUP source code buffer,
	where we output all the code to store the pointers to the widgets.
	This means all the code is put in the same place, so yuo can remove
	it easily.

	* glade/gbsource.c (gb_widget_write_standard_source): use new
	GLADE_HOOKUP_OBJECT() macros in generated code.
	removed code to output gtk_widget_set_uposition().

	* glade/gb.c: added GladeDialogResponseIDKey, and GladeStockResponses
	array.

	* glade/editor.c: removed code related to the common X & Y properties.
	They have been removed now.
	(editor_paint_selections): tried to fix for GtkLayout. No luck yet.
	(draw_grid): fixed for GtkLayout.

	* glade/data/gtk/autogen.sh: updated to use glib-gettextize, and to
	support intltool.

	* glade/gbwidgets/gbwindow.c: added "resizable", "destroy_with_parent"
	and "icon". Removed "allow_grow", "allow_shrink", as they are
	deprecated and "allow_grow" is practically the same as "resizable".
	"allow_shrink" can be replaced by setting the requested size of the
	window to 0x0.
	
	* glade/gbwidgets/gblayout.c: added support for child X & Y properties.
	We now use these instead of X & Y properties on the 'Common' page.

	* glade/gbwidgets/gbhbuttonbox.c (gb_hbutton_box_write_add_child_source):
	added special code to output code to add dialog buttons to the dialog.

	* glade/gbwidgets/gbdialog.c: finished, and added functions to convert
	a response id to/from a string.

	* glade/gnome/gnomemessagebox.c (gb_gnome_message_box_write_source): 
	use GLADE_HOOKUP_OBJECT() in source code.

	* glade/gbwidgets/gbbutton.c: use gtk_label_get_label() rather than
	gtk_label_get_text(), or we will lose underlined accelerators. Fixes
	bug #72397.
	Added support for dialog buttons - almost the same as normal buttons
	but with a response id.

2002-02-22  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbnotebook.c: finished this. We now use a special
	"type" packing property set to "tab" for the tab widgets.

	* glade/gbwidgets/gbdialog.c: used a creation dialog so you can select
	standard button layouts or choose the number of buttons. Unfinished.

	* glade/property.c (property_create): made it a little wider.

	* glade/debug.h: don't use G_GNUC_PRETTY_FUNCTION as a static string.
	I think the newer gcc doesn't support that. Use it as a function arg
	instead.

	* glade/gbwidget.c (set_standard_properties): reserve the widget names
	when loading.

	* glade/gb.c: removed GladeChildNotebookTab. We use a special "type"
	packing property in the XML now set to "tab". Within Glade we figure
	it out from the widget's position.
	Moved GladeButtonStockIDKey here so GtkDialog code can use it.
	
	* glade/gnome/gnomepropertybox.c (gnome_property_box_new_tab_label): 
	don't set the child name of the tab.

	* glade/gbwidgets/gbcolorselection.c (gb_color_selection_write_source):
	fix output of set_has_opacity_control().

2002-02-20  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (editor_select_widget): use our own function
	get_notebook_page() to find the correct notebook page. The GTK+
	function doesn't work with the tab label widgets.

	* glade/gbwidget.c: made GladeMenuItemStockIDKey different from
	GladeMenuItemStockIndexKey, as they are used for different things.
	This stops the crash in bug #69959 (though GnomeApp still doesn't work)

	* glade/gb.c: added GladeChildNotebookTab.

	* glade/gbwidgets/gbnotebook.c: added support for "tab_expand",
	"tab_fill", "tab_pack" & "menu_label" packing properties, and added
	a "position" packing property which is only used within Glade.

	* glade/gnome/gnomepropertybox.c (gnome_property_box_new_tab_label): 
	use GladeChildNotebookTab as the child name.

	* glade/gnome/gnomeapp.c (gb_gnome_app_add_child): 
	* glade/gnome/bonobowindow.c (gb_bonobo_window_add_child): 
	* glade/gnome/bonobodock.c (gb_bonobo_dock_add_child): use the
	GladeChildInfo internal_child field to get the child name rather than
	load_get_value ("child_name"), since that doesn't work any more.

	* glade/gbwidgets/gbbutton.c: made sure all debugging g_print's do not
	pass NULL as a string. Fixes bug #70273.

2002-02-20  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (editor_add_draw_signals): don't try to change event
	mask if realized. GtkCombo realizes widgets when it is created, so
	this generated warnings. Maybe we should set the mask on the windows
	directly, but for GtkCombo it doesn't really matter as the popup
	isn't really editable directly anyway.

	* glade/gbwidget.c (option_menu_foreach): tried to fix source code
	output, but failed.
	(combo_foreach): call callback for list as well.
	(gb_widget_children_foreach): use combo_foreach.

	* glade/utils.c (glade_util_gtk_combo_find): 
	(glade_util_gtk_combo_func): use gpointer rather than GtkListItem, as
	that is deprecated, so we'll only declare it when needed, I think.

	* glade/gb.c: added child names for the combo, fontsel & colorsel.

	* glade/gbwidgets/gboptionmenu.c: tried to fix code output. failed.

	* glade/gbwidgets/gbcombo.c: made the popup list an internal child and
	used GbWidgets for the listitems, so they now get saved individually,
	which is what libglade wants now. We only support simple text items,
	though.

	* glade/gbwidgets/gbtreeview.c: finished this. GTK+ seems to have been
	fixed so it doesn't crash on creation now.

	* glade/gbwidgets/gbtable.c (gb_table_set_child_properties): fixed
	packing properties - use GTK_EXPAND/FILL/SHRINK flags rather than
	TRUE and FALSE.

	* glade/gbwidgets/gbfontselectiondialog.c: made "font_selection" an
	internal child, so its properties can be changed.

	* glade/gbwidgets/gbcolorselectiondialog.c: made "color_selection" an
	internal child, so its properties can be changed.

	* configure.in (GLADE_DEPRECATION_CFLAGS): added this, but it isn't
	finished and we don't use it yet. It will use a --disable-deprecated
	configure flag eventually.

	* glade/gnome-db/Makefile.am (INCLUDES): 
	* glade/gnome/Makefile.am (INCLUDES): 
	* glade/gbwidgets/Makefile.am (INCLUDES): 
	* glade/Makefile.am (INCLUDES): added GLADE_DEPRECATION_CFLAGS.

2002-02-06  Damon Chaplin  <<EMAIL>>

	* glade/gb.c: use const for all the size ints, avoids error with gcc 3.

2002-02-05  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtextview.c: added source code generation, fixed
	a minor bug, use '_' instead of '-' in property names, and changed
	a few property names so they fit in the normal property editor.

2002-02-05  Damon Chaplin  <<EMAIL>>

	* glade/property.c (create_signals_property_page): hide the signal
	'Data' field, as it isn't in the DTD and shouldn't really be used.

	* glade/load.c (load_get_value): return NULL if widget_info is NULL,
	which can now happen for placeholders.

	* glade/glade-parser.c: updated to latest libglade version, which
	supports the new <placeholder/> tags. Except we leave the empty
	childinfo struct there, so we can detect placeholders. Special Glade
	code has 'GLADE:' comments.

	* glade/gbwidget.c (gb_widget_load): handle widget_info being NULL for
	placeholders.
	Add the child names for clist/ctree titles, so old code still works.
	(gb_widget_save): don't save the child name for clist/ctree titles.
	Save <placeholder/> tags for placeholders.

	* glade/save.c (save_placeholder): new function to save a
	<placeholder/> tag.

	* glade/gbwidgets/gbctree.c (gb_ctree_add_child): 
	* glade/gbwidgets/gbclist.c (gb_clist_add_child): don't check the child
	name, as it won't be set at this point. It shouldn't matter, as the
	titles are the only children of clist/ctree.

2002-02-04  Damon Chaplin  <<EMAIL>>

	* glade/utils.c: remove find_parent_button() since gblabel.c does that
	itself now.
	(glade_util_find_default_accelerator_target): this now assumes the
	label is not inside a button/menuitem, and it tries to find the widget
	to the right of the label to set the focus to. It doesn't return the
	signal to emit any more, since that is implicit.

	* glade/glade_gtk12lib.c: added GtkTreeView. Removed GtkPacker and
	GtkPixmap completely.

	* glade/glade_gnomedblib.c: changed '//' comments to /* */. We've had
	portability problems with '//' before.

	* glade/gbwidgets/gblabel.c: added "use_markup", "use_underline",
	"selectable". Renamed "focus_target" to "mnemonic_widget".
	Removed "default_focus_target" from output to XML.
	If the user selects "use_markup" we check if the text is valid markup
	each time we set it. If it isn't we turn "use_markup" off for the
	widget. This avoids warnings after each character is typed.
	Also used common code so we can share it with gbaccellabel.c
	
	* glade/gbwidgets/gbaccellabel.c: call the shared gblabel.c functions.

	* glade/gb.h: added declarations for shared label functions.

	* glade/gbwidgets/Makefile.am (libgbwidgets_a_SOURCES): added
	gbtreeview.c.

	* glade/gbwidgets/gbtreeview.c: new file. Doesn't work yet, though.

	* tools/mkskel.pl: updated a few bits of text.

2002-01-29  Seth Nickell  <<EMAIL>>

	* Makefile.am:

	Install .desktop file into the proper location for GNOME2
	(PREFIX/share/applications).
	
	* glade-2.desktop.in:

	Add a Categories field for GNOME 2 panel.
	Tweak the Name to follow the HIG.	

2002-01-28  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtextview.c (gb_text_view_set_properties): free
	the value returned from gb_widget_input_text() if action is GB_APPLYING
	rather than GB_LOADING.

	* glade/glade_menu_editor.c: use a child GtkImage GbWidget for
	non-stock items, so they get saved as children. Also allow the stock
	'New' item to have its label changed, e.g. to 'New Project' etc.

	* glade/glade_gnome.c (glade_gnome_write_menu_item_source): get the
	icon_name from the GtkImage widget, as that is where it is always kept
	now. But hopefully this code will be removed anyway.

	* glade/gbwidget.c (gb_widget_output_child_label): output
	"use_underline" for menuitem labels.

	* glade/gbwidget.c (set_standard_properties): set the widget name from
	the GladeParser info when loading.

	* glade/gbwidgets/gbmenubar.c: add some initial items to the menubar,
	like we do in GNOME.

	* glade/gbwidgets/gbimagemenuitem.c: use a proper GtkImage child for
	non-stock items, so it is written out as a child in the XML, which
	libglade expects.

	* glade/gbwidgets/gbcolorselection.c: added "has_opacity_control" and
	"has_palette".

	* glade/gbwidgets/gbbutton.c (gb_button_normal_get_properties): don't
	output "use_underline" for stock items as it is implied.
	(gb_button_toolbar_get_properties): save stock icon as "stock_pixmap",
	to agree with libglade.
	(gb_button_toolbar_set_properties): check for "stock_pixmap" when
	loading.

	* glade/gbwidget.c (gb_widget_input_child_label): 
	* glade/gbwidgets/gbmenuitem.c (gb_menu_item_set_properties): 
	* glade/gbwidgets/gblabel.c (gb_label_set_properties): 
	* glade/gbwidgets/gbaccellabel.c (gb_accel_label_set_properties): use
	gtk_label_set_text_with_mnemonic() instead of gtk_label_parse_uline()
	which is deprecated.

	* glade/gbwidgets/gbdialog.c: added "has_separator".

	* glade/gbwidgets/gbwindow.c: 
	* glade/gbwidgets/gbinputdialog.c: 
	* glade/gbwidgets/gbfontselectiondialog.c: 
	* glade/gbwidgets/gbfileselection.c: 
	* glade/gbwidgets/gbdialog.c: 
	* glade/gbwidgets/gbcolorselectiondialog.c: 
	* glade/gnome/bonobowindow.c: 
	* glade/gnome/gnomeapp.c: 
	* glade/gnome/gnomepropertybox.c: 
	* glade/gnome/gnomemessagebox.c: 
	* glade/gnome/gnomeabout.c: 
	* glade/gnome/gnomedialog.c: 
	* glade/gnome-db/gnomedblogindlg.c: 
	* glade/gnome-db/gnomedberrordlg.c: started adding support for new
	window properties "resizable", "destroy_with_parent" & "icon".

2002-01-26  Rodrigo Moya <<EMAIL>>

	* glade/glade_gnomedblib.c:
	* glade/gnome-db/Makefile.am:
	* glade/gnome-db/gnomedbdsnconfig.c: re-enabled GnomeDbDsnConfig
	widget.

2002-01-23  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_input_enum): don't g_free the value
	returned from load_string(). You never need to free loaded values.
	Also check data->apply before using the results. Jody reported a crash.

2002-01-22  jacob berkman  <<EMAIL>>

	* glade/gbwidgets/gboptionmenu.c: get rid of the
	options-as-text-list and use the menu editor instead.  this makes
	it compat with libglade and is also nicer in general

	* glade/gbwidget.c (option_menu_foreach): iterate through the menu
	of an option menu

2002-01-21  jacob berkman  <<EMAIL>>

	* glade/utils.[ch] (glade_enum_from_string): 
	(glade_string_from_enum): steal from libglade

	* glade/gbwidget.[ch] (gb_widget_output_enum): 
	(gb_widget_input_enum): some more useful functions for using
	choice inputs

	* glade/glade_gtk12lib.c (gtk_standard): add textview

	* glade/gbwidgets/gbtextview.c: implement GtkTextView (sans signals)

	* glade/gbwidgets/Makefile.am (libgbwidgets_a_SOURCES): add textview

	* glade/gnome-db/Makefile.am (INCLUDES): set correctly

2002-01-21  Damon Chaplin  <<EMAIL>>

	* tools/mkskel.pl (OutputWidgetFile): split 'volatile GtkType type'
	line in two, otherwise the XXX_get_type() function may not be called.
	(GTK+ 2.0 strangeness)

	* glade/gnome/gnomedateedit.c (gb_gnome_date_edit_set_properties): 
	don't free the string returned by gb_widget_input_string().
	I think the only time you need to free a string is after
	gb_widget_input_text() when data->action == GB_APPLYING.
	(gb_gnome_date_edit_set_properties): Also checked for data->apply
	after getting properties.

	* glade/gbwidgets/gbscrolledwindow.c: added 'window_placement',
	'shadow_type'.

	* glade/gbwidgets/gbprogressbar.c: added 'fraction', 'pulse_step'
	and 'text'. Removed 'value', 'lower', 'upper', 'bar_style', 'format'.

	* glade/gbwidgets/gbentry.c: added 'has_frame', 'invisible_char',
	'activates_default' and 'width_chars'.

	* glade/gbwidgets/gbcalendar.c: save all 5 boolean options in a single
	XML property.

	* glade/glade_gtk12lib.c: removed GtkAccelLabel from palette, since
	it is practically unusable, except in menus.

	* glade/gb.[hc]: added Shadow and Corner choices and GLADE_EPSILON
	#define for comparing floats.

	* glade/gbwidgets/gbarrow.c: use the common Shadow choices.

2002-01-14  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbstatusbar.c: added "has_resize_grip".

	* glade/gbwidgets/gbvscrollbar.c: 
	* glade/gbwidgets/gbhscrollbar.c: 
	* glade/gbwidgets/gbvscale.c: 
	* glade/gbwidgets/gbhscale.c: added "inverted".

	* glade/gbwidgets/gbfontselection.c: added "preview_text".

	* glade/gbwidgets/gbtogglebutton.c: 
	* glade/gbwidgets/gbradiobutton.c: 
	* glade/gbwidgets/gbcheckbutton.c: added "inconsistent".

	* glade/gbwidgets/gbbutton.c (gb_button_set_standard_properties): use
	relief_p instead of Relief, so subclasses that call this work OK.

	* TODO: updated.

2002-01-13  Rodrigo Moya <<EMAIL>>

	* configure.in: enabled gnome-db support by requiring a current
	version, not a future one

	* glade/gnome-db/gnomedbbrowser.c:
	* glade/gnome-db/gnomedberror.c:
	* glade/gnome-db/gnomedberrordlg.c:
	* glade/gnome-db/gnomedbgrid.c:
	* glade/gnome-db/gnomedblist.c:
	* glade/gnome-db/gnomedblogin.c:
	* glade/gnome-db/gnomedblogindlg.c:
	* glade/gnome-db/gnomedbtableeditor.c:
	* glade/gnome-db/gnomedbwindow.c:
	* glade/gnome-db/gnomedbcombo.c: ported to libgnomedb-2

	* glade/gnome-db/Makefile.am: disabled GnomeDbDesigner,
	GnomeDbEntry, GnomeDbExport, GnomeDbLabel, GnomeDbLogViewer,
	GnomeDbIconList, GnomeDbDsnConfig, GnomeDbDataset, and
	GnomeDbReport, not yet available in libgnomedb-2

	* glade/glade_gnomedblib.c: removed disabled widgets

2002-01-10  jacob berkman  <<EMAIL>>

	* glade/gnome/gnomehref.c (gb_gnome_href_set_properties): call
	gnome_href_set_text as _set_label is deprecated and prints a
	warning

	* glade/gnome/gnomedruidpagestandard.c
	(gb_gnome_druid_page_standard_create_properties): add contents
	background color and top_watermark properties
	(gb_gnome_druid_page_standard_get_properties): get new props
	(gb_gnome_druid_page_standard_set_properties): set new props

	* glade/gnome/gnomedruidpageedge.c
	(gb_gnome_druid_page_edge_create_properties): add top_watermark
	property
	(gb_gnome_druid_page_edge_get_properties): save the position, and
	top_watermark
	(gb_gnome_druid_page_edge_set_properties): set the top_watermark

	* glade/gnome/gnomedruid.c: remove FIXMEs around stuff.  for now,
	just include the definition of GnomeDruidPrivate so we can poke at
	the children.  also, add show-help property to turn on / off the
	help button

	* glade/gnome/gnomecontrol.c (gb_bonobo_control_get_properties):
	save the moniker property

	* glade/gnome/gnomeabout.c (gb_gnome_about_create_properties): add
	logo padding, background, and background opacity / position
	properties
	(gb_gnome_about_get_properties): add new properties
	(gb_gnome_about_set_properties): add new properties and fixup to
	actually work
	also removed some un-needed code now that there are properties

	* glade/editor.c (paint_widget): only draw the grid for GbWidgets

	* glade/glade_gnomelib.c (bonobo_page): call the bonobo widget
	BonoboWidget like libglade expects

	* TODO: add status of GNOME / Bonobo widgets

2002-01-08  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_create_pixmap): fixed generated call to
	create_pixmap().

	* glade/glade_project_window.c: #ifdef'd out unused code.

	* glade/gbwidgets/gbnotebook.c: renamed 'popup_enable' to
	'enable_popup'.

	* glade/save.c: 
	* glade/gbwidgets/gbbutton.c: 
	* glade/gbwidgets/gbradiobutton.c: removed unused variables.
	(I am now using -Wall again!)

	* glade/gbwidgets/gbvbuttonbox.c: 
	* glade/gbwidgets/gbhbuttonbox.c: removed old min width/height &
	child padding properties.

2002-01-07  jacob berkman  <<EMAIL>>

	* glade/glade_gnomelib.c (sections): add a bonobo tab with
	BonoboWindow and BonoboControl on it

	* glade/gnome/gnomecontrol.c: create only one palette entry, and
	pop up a dialog with a list of controls.  this is better than
	looking through tooltips.  also, you get new controls right away
	if they are installed while gnome is running.

	* glade/utils.c (glade_util_show_message_box): re-implement with
	GtkMessageDialog
	(glade_util_create_dialog): remove gnome version and simplify a
	lot

	* glade/glade_palette.c (glade_palette_init): remove the border of
	the notebook
	(glade_palette_init): turn off this unnecessary border

	* glade/gbwidget.c (gb_widgets_init): remove call to initialize
	bonobo controls, as they are done a little differently now.

2002-01-07  Damon Chaplin  <<EMAIL>>

	* glade/glade-parser.c (create_widget_info): only insert the info into
	the hash if the info->name is not NULL. Stops it crashing. I'm not
	sure if it is a memory leak. And don't warn about missing id for
	placeholders.
	(handle_signal): ignore last_modification_time attribute.

	* glade/gbwidgets/gbimage.c: added support for this, including stock
	icons.

	* glade/gbwidgets/gbbutton.c: now works for normal buttons and buttons
	in toolbars. Still need to to buttons in dialogs.

	* glade/gbwidget.c (gb_widget_input_adjustment): 
	(gb_widget_output_adjustment): save as a single property in the XML,
	to match libglade. Also updated the widgets that use these.

	* */*: lots of changes everywhere. Mainly property name changes,
	and changes in the 'internal-child' names. See TODO for status of each
	widget.

2002-01-03  jacob berkman  <<EMAIL>>

	* glade/gnome/gnomecontrol.c (widget_get_uic): util to get the ui
	container from the toplevel bonobo window
	(control_create): accept a ui container param
	(gb_bonobo_control_new): pass the uic when creating the control

2002-01-02  jacob berkman  <<EMAIL>>

	* glade/gnome/Makefile.am (libgnomewidgets_a_SOURCES): add
	bonobowindow.c

	* glade/glade_gnomelib.c (gnome_page): add a BonoboWindow.

	* glade/gnome/bonobowindow.c: initial hack at BonoboWindow support

	* glade/gnome/gnomeapp.c: s/GnomeDock/BonoboDock/

	* acconfig.h: remove ENABLE_BONOBO and USING_OAF

	* glade/gnome/gnomecontrol.c (gb_bonobo_control_create_properties): 
	(gb_bonobo_control_get_properties):
	(gb_bonobo_control_set_properties): small porting fixes
	(gb_bonobo_control_init): the bonobo api we used got removed so
	use bonobo_activation_query() directly instead

	* glade/glade_gnomelib.h: always declare gb_bonobo_control_init()

	* glade/gbwidget.c (gb_widgets_init): init bonobo controls if
	gnome is enabled

	* glade/main.c (main): run bonobo_main() rather than gtk_main() if
	we are gnome enabled

	* Makefile.am (install-data-local): install into pixmaps/glade-2

	* glade/glade_project_window.c (glade_project_window_about): use
	glade-2.png for now, as the other icon is too big

	* glade/main.c (main): use the gnome_program_init stuff, and set
	the default window icon

	* glade/Makefile.am (INCLUDES): define GLADE_DATADIR

2001-12-29  jacob berkman  <<EMAIL>>

	* glade-2.desktop.in: forgot to add -2 to the icon and exec lines

2001-12-28  jacob berkman  <<EMAIL>>

	* glade/utils.[ch] (glade_util_flags_from_string): util function
	from libglade to parse flags
	(glade_util_string_from_flags): util function which is the inverse
	of the above

	* glade/gnome/gnomedateedit.c (gb_gnome_date_edit_get_properties):
	remove FIXME's, and only have 1 flags property in the XML whilst
	showing 3 toggles in the gui
	(gb_gnome_date_edit_set_properties): ditto
	(gb_gnome_date_edit_write_source): remove FIXME's

	* glade/gnome/gnomecanvas.c (gb_gnome_canvas_write_source): we
	don't need to push/pop colormap stuff any more

2001-12-27  jacob berkman  <<EMAIL>>

	* glade/utils.c (gnome_dialog_button_clicked): ok button is now
	button 1
	(glade_util_create_dialog): use the new button layout

	* glade/gnome/gnomedialog.c (gb_gnome_dialog_new): create the
	dialog with the new button layout style (ok at end)

	* glade/gnome/gnomefontpicker.c: remove FIXMEs by using new api

	* glade/gnome/gnomedialog.c:
	* glade/gnome/gnomemessagebox.c: fixup for deprivatization of
	GnomeDialog

2001-12-21  jacob berkman  <<EMAIL>>

	* glade/gnome/gnomeapp.c (gb_gnome_app_new): BonoboDockLayout is
	not a GtkObject.

	* glade/gnome/gnomedialog.c:
	* glade/gnome/gnomemessagebox.c: un-FIXME stuff that needed the
	private fields from GnomeDialog, and fix GnomeMessageBox icon
	handling.

	these things should mostly work now.

	* glade/glade_gnome.h: the definition of _GnomeDialogPrivate from
	libgnomeuiP.h here until it gets unprivatized.

2001-12-19  jacob berkman  <<EMAIL>>

	* glade/gnome/bonobodockitem.c (gb_bonobo_dock_item_add_child):
	removed a bit of FIXME

	* glade/gnome/gnomeabout.c: make the properties work

	* configure.in: may as well turn gnome support on now as it should
	build and link

	* glade/glade_menu_editor.c: comment out a bunch of GnomeStock
	stuff, as it will be the same as GtkStock
	
	* glade/glade_project_window.c: port to new GnomeAbout API
	
	* glade/glade_palette.c: remove references to gnome_preferences
	
	* glade/glade_gnomelib.c: fixup to reflect removed / renamed
	widgets
	
	* glade/glade_gnome.c: replace GnomeDock with BonoboDock

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_set_stock_child_icon):
	comment this out, as it will use the same stuff as the gtk stock
	toolbar icons (when that works)

	* glade/gnome/Makefile.am (libgnomewidgets_a_SOURCES): remove
	gnomestock.c

	* glade/gnome/gnomeentry.c (gb_gnome_entry_write_source): 
	(gb_gnome_entry_set_properties):
	s/gnome_entry_max_saved/gnome_entry_get_max_saved/

	* glade/gnome/gnomedruidpagestandard.c: make it build

	* glade/gnome/gnomeappbar.c (gb_gnome_appbar_get_properties): fix
	tyop

	* glade/gnome/bonobodockitem.c: comment out gnome_preferences_*
	references

2001-12-18  jacob berkman  <<EMAIL>>

	* glade-2.desktop.in (_Name): add a '2.0' to differentiate from
	glade 1

	* Makefile.am (install-data-local): install into glade-2 rather
	than glade

	* glade/gnome/Makefile.am (libgnomewidgets_a_SOURCES): add back
	gnomedruidpagestandard.c even though it doesn't build since it's
	referenced in po/POTFILES.in

	* configure.in: fixup gnome support checking etc.  do not turn on
	by default as we don't link
	
	* various files: make it parallel installable with glade 1, i
	think
	
	* glade/*.c:
	* glade/gnome/*.c: initial make-it-compile-with-gnome-support
	port. it doesn't link, though.

2001-12-10  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/*.c:
	* glade/gnome/*.c:
	* glade/gnome-db/*.c: used 'volatile GtkType type' to make sure the
	type init functions get called. This is a quirk in GTK+ 2. It meant
	most of the dialogs didn't work for us, as we couldn't get the type
	from the class name, as the type init function never got called.

	* glade/editor.c: stopped turning buffered drawing off for widgets,
	and used an idle handler to draw the selection rectangles, like we used
	to do for GnomeCanvas. Chema's idea, and seems to work fine.

	* lots of other minor changes. I'm in the middle of updating buttons to
	handle stock stuff.

2001-12-10  jacob berkman  <<EMAIL>>

	* glade/load.c (load_parse_bool): accept yes/no for bools

2001-12-07  Damon Chaplin  <<EMAIL>>

	* glade-gnome2-branch created.

2001-12-05  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_check_file_exists_function): output
	'static'. From Rob Lahaye <<EMAIL>>.

2001-09-19  José Antonio Salgueiro Aquino <<EMAIL>>

	* glade/gnome-db/gnomedbdesigner.c:
	* glade/gnome-db/gnomedbentry.c:
	* glade/gnome-db/gnomedbexport.c:
	* glade/gnome-db/gnomedblabel.c:
	* glade/gnome-db/gnomedblogviewer.c:
	* glade/gnome-db/gnomedbtableeditor.c:
	* glade/gnome-db/gnomedbwindow.c: added support for missing GNOME-DB
	widgets

	* glade/glade_gnomedblib.c: initialize new widgets

2001-09-13  John Gotts  <<EMAIL>>

	* glade.spec.in: Should close bug #58220.

2001-09-01  Wang Jian  <<EMAIL>>

	* configure.in(ALL_LINGUAS): Added zh_CN for Simplified Chinese

2001-08-18  Damon Chaplin  <<EMAIL>>

	* glade/palette.c (palette_add_gbwidget): translate the section name
	(but after we compare it to "NotShown").

	* glade/glade_gtk12lib.c (sections): removed N_() from "NotShown"
	since it is not meant for display.

	* glade/utils.c (gnome_dialog_button_clicked): added (GtkSignalFunc)
	cast. Patch for <NAME_EMAIL>.

2001-08-14  Marius Andreiana  <<EMAIL>>

	* configure.in: Added ro (Romanian) to ALL_LINGUAS

2001-08-14  Kjartan Maraas  <<EMAIL>>

	* doc/C/faq.sgml: Fix up sgml a bit.
	* doc/C/user-guide.sgml: Same here.
	
2001-08-03  jacob berkman  <<EMAIL>>

	* glade/main.c (main): 
	* glade/glade.c (glade_app_init):
	* glade/glade.h: rename glade_init() to glade_app_init(), so that
	it doesn't conflict with the libglade function when using shlib
	bonobo components which use libglade

2001-07-30  Rodrigo Moya <<EMAIL>>

	* configure.in: s/$gnomedb_config/gnomedb-config, and require
	GNOME-DB >= 0.2.90 for GNOME-DB support

	* glade/gnome-db/*: updated to the CVS version of GNOME-DB

2001-06-25  Damon Chaplin  <<EMAIL>>

	* FAQ: fixed link to http://glade.gnome.org

2001-05-24  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (get_widget_window): return the widget owning the
	window as well, so we know which gc to use.
	(editor_paint_selections): use the correct gc for the window, to avoid
	BadMatch errors on multi-depth type displays. Patch from Dave Camp
	<<EMAIL>>

2001-05-09  Rodrigo Moya <<EMAIL>>

	* glade/graphics/: added new pixmaps by Daniel Wagner <<EMAIL>>

2001-05-02  Rodrigo Moya <<EMAIL>>

	* configure.in: check for gnome-db >= 0.2.4
	* glade/gnome-db/: updated to match the recent changes in
	gnome-db CVS

2001-04-19  Dan Mueth  <<EMAIL>>

	Changing web site URL from http://glade.pn.org to 
	http://glade.gnome.org

	* FAQ:
	* README:
	* doc/C/faq.sgml:
	* doc/C/turbo-start.sgml:
	* doc/C/user-guide.sgml:
	* doc/it/faq.sgml:
	* doc/it/turbo-start.sgml:
	* doc/it/user-guide.sgml:
	* glade/glade_project_window.c: (glade_project_window_about):

2001-03-28  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.6.2 (for Gnome 1.4)

2001-03-25  Dan Mueth  <<EMAIL>>

        Setting up OMF metadata and integration with scrollkeeper

	* Makefile.am:
	* configure.in:
	* doc/C/Makefile.am:
	* doc/C/faq-C.omf:
	* doc/C/turbo-start-C.omf:
	* doc/C/user-guide-C.omf:
	* glade.spec.in:
	* omf-install/Makefile.am:

2001-03-25  Dan Mueth  <<EMAIL>>

        Fixing some of the markup to be gnome-db2html2 compliant. It
        doesn't spew errors now, but still TOC extraction is broken.

	* doc/C/user-guide.sgml:

2001-03-21  Dan Mueth  <<EMAIL>>

	Fixed FAQ so it renders with gnome-db2html2 properly.

	* doc/C/faq.sgml:

2001-03-12  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.6.1 (for Gnome 1.4 RC1)

2001-03-12  Damon Chaplin  <<EMAIL>>

	* doc/C/turbo-start.sgml: 
	* doc/C/user-guide.sgml: changed my email <NAME_EMAIL>

2001-02-23  Carlos Perelló Marín <<EMAIL>>

	* Makefile.am: Updated to really use xml-i18n-tools
	* glade.desktop: Removed, now is automaticly generated.

2001-02-22    <<EMAIL>>

	* INSTALL_FROM_CVS: added xml-i18n-tools to requirements.

2001-02-22  Damon Chaplin  <<EMAIL>> applied patch from
	Carlos Perelló Marín <<EMAIL>>

	* glade/gnome-db/gnomedbiconlist.c: Added
	* glade/gnome-db/gnomedbdsnconfig.c: Added
	* glade/gnome-db/gnomedbdataset.c: Added
	* glade/gnome-db/gnomedbbrowser.c: Updated to the new Gnome-DB version
	  (a Pau <<EMAIL>>'s patch modification)
	* Makefile.am, configure.in, glade.desktop.in: Adapted to use the
	  xml-i18n-tools

2001-02-16  Damon Chaplin  <<EMAIL>>

	* glade/utils.c (glade_util_close_window): check the window exists.

2001-02-16  Christopher R. Gabriel  <<EMAIL>>

	* configure.in (AC_OUTPUT): added italian translation of the Glade
	FAQ.
	
2001-02-15  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.6.0 (for Gnome 1.4 beta 1)

2001-02-15  Damon Chaplin  <<EMAIL>>

	* FAQ: added a few more questions & answers.

	* doc/C/faq.sgml: updated.

	* glade/glade_project_window.c (glade_project_window_about): added
	Martijn to the authors list, and added a link to Glade's home page.

	* glade/utils.c: added functions to set and reset the TZ environment
	variable.

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	* glade/load.c (load_project_file): used the above functions for
	setting TZ, and tidied up a little.

	* glade/utils.c (glade_util_ensure_directory_exists): check that
	it has a parent directory before trying to create it.

	* glade/utils.c (glade_util_parent_directory): handle case where the
	parent directory is the root directory.

	* glade/property.c (property_create): changed "Basic" property page
	to "Common", since these are properties common to all widgets.

	* glade/utils.c (glade_util_get_label_text): fixed to handle '_'
	characters which are part of the label. We need to repeat these since
	GTK+ uses them to denote keyboard accelerators.

	* glade/gbwidgets/gbcombo.c (write_items_source_callback): used a
	(gpointer) cast to keep g++ happy.

	* glade/gnome/gnomeanimator.c: set widget_data->width & height when
	created, and use these when creating the source code, NOT the widget's
	allocation which may not even be set if the dialog hasn't been shown.

	* glade/gbwidgets/gbbutton.c (gb_button_get_properties): don't show
	the Icon property for normal buttons in the GTK+ version.

2001-01-15  Stanislav Brabec  <<EMAIL>>

        * glade.desktop: Updated cs. Changed da->de for German.

2001-01-06  Damon Chaplin  <<EMAIL>>

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	* glade/load.c (load_project_file): reverted most of Morten's changes
	since it wasn't compiling.
	I did remove the 'g_free (new_timezone)' since putenv() doesn't make a
	copy of it. This means we leak a bit of	memory at the moment, but I'll
	fix it later.
	I left the tzset() calls in, though I'm not sure we need it.

	* glade/gbwidgets/gbmenuitem.c (gb_menu_item_write_accel_source): 
	* glade/gbwidgets/gblabel.c (gb_label_write_source): 
	* glade/gbwidgets/gbbutton.c (gb_button_write_uline_accel_source): 
	add cast to (GtkAccelFlags) to avoid problems compiling the Glade C
	output with a C++ compiler.

	* glade/gbsource.c (get_type_name): updated to use the type numbers
	rather than the type names, which were changed in GTK+ 1.2.

	* glade/gbwidgets/gbfixed.c (gb_fixed_write_add_child_source): output
	the position using wdata->x and wdata->y rather than the allocation,
	which won't even be set if the window hasn't been shown.

	* configure.in (using_oaf): fixed the AM_CONDITIONAL test arg and typo.

2000-12-27  Morten Welinder <<EMAIL>>

	* glade/gbwidgets/gbbutton.c (gb_button_set_stock_button): Plug
 	leak.

	* glade/gbwidget.c (gb_widget_create_from_full): Plug leak.

	* glade/gnome/gnomecontrol.c (gb_bonobo_control_init): Plug leaks.

	* glade/editor.c (clear_child_windows): Properly free children,
 	not the empty list.

	* glade/load.c (load_project_file): Better way of restoring TZ.
  	(Leaving freed pointers in the environment is inadvisable.)
	Also call tzset as needed.  Avoid g_strdup-ing NULLs.

	* glade/glade_clipboard.c (glade_clipboard_paste): Ditto.

2000-12-22  Stanislav Visnovsky <<EMAIL>>

	* configure.in: Added Slovak [sk] to ALL_LINGUAS.

Tue Nov 07 21:14:27 2000  George Lebl <<EMAIL>>

	* glade/glade_project_view.c
	  (glade_project_view_clear_component_selection):  copy the
	  selection list first as it will be modified during the iteration.

2000-11-02  Michael Meeks  <<EMAIL>>

	* configure.in: require Bonobo >= 0.27

2000-10-25  Federico Mena Quintero  <<EMAIL>>

	* glade/glade_project_window.c (EditMenu): The menu item should
	say "Delete", not "Clear", since it deletes the selected widget.
	Also, give it a trash stock icon.  And stupid GTK+ won't display
	the GDK_DELETE accelerator even though we have set it to be just
	that.

2000-10-22  John Gotts  <<EMAIL>>

	* glade.spec.in: Changed group from X11/Libraries to Development/Tools.

2000-10-21  Damon Chaplin  <<EMAIL>>

	* glade/property.c (property_add_int):
	(property_add_float): connect to the "activate"
	signal of the entry rather than the "changed" signal, since we only
	want to be notified after the user has finished typing in the value.
	We may also need to think some more about other types of properties,
	e.g. it may not be wise to update some string properties after each
	letter is added, as we do now. Maybe we need an option for this.
	(property_create): changed "Place" page label to "Packing", since that
	is more descriptive of what those properties are for.

2000-10-17  Szabolcs BAN <<EMAIL>>

	* po/hu.po, configure.in: Added Hungarian support.

2000-10-13  Damon Chaplin  <<EMAIL>>

	* glade/utils.c (glade_util_show_message_box): 
	(glade_util_create_dialog_with_buttons): 
	(glade_util_show_entry_dialog): added transient_widget argument to
	make it easy to set the transient_parent.

	* *.c: updated all calls to glade_util_show_message_box to set the
	transient widget if we can.
	
	* glade/gbwidgets/gbmenubar.c (dialogize): 
	* glade/glade_project_window.c (glade_project_window_new_errors_dialog): 
	* glade/editor.c (editor_show_grid_settings_dialog): 
	(editor_show_snap_settings_dialog): 
	* glade/tree.c (tree_init): 
	* glade/property.c (show_style_dialog): 
	(property_create): 
	* glade/glade_project_options.c (glade_project_options_init): 
	* glade/glade_palette.c (glade_palette_init): 
	* glade/glade_clipboard.c (glade_clipboard_init): used a TOPLEVEL
	window instead of a DIALOG. DIALOG windows should only be used for
	short-lived modal dialogs (i.e. hardly ever).

	* glade/glade_clipboard.c (glade_clipboard_init): set a reasonable
	default size.

	* glade/glade_project_window.c (glade_project_window_show_quit_dialog):
	changed button labels to 'Quit' & 'Cancel'.

	* glade/glade_project_window.c (glade_project_window_new_project): 
	changed button labels to 'OK' & 'Cancel'.

	* glade/glade_menu_editor.c (glade_menu_editor_update_menu): update
	the gbwidget in the widget data since we may have changed the menu item
	type.

2000-10-11  Christophe Merlet  <<EMAIL>>

	* glade/property.c: Marked somes strings for translations.

2000-10-01  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.11

2000-10-01  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomemessagebox.c: added workaround for bug in gnome-libs
	1.2.3. We create the GnomeMessageBox with one button and then remove
	it. Otherwise it will SEGV. Also changed the generated code to do this.

	* glade/gbwidgets/gbbutton.c: 
	* glade/glade_gnome.c: patch from Fernando Pereira
	<<EMAIL>> to avoid warnings when using g++.

	* glade/property.c (create_signals_property_page): 
	* glade/gbsource.c (gb_widget_write_signals_source): added
	gtk_widget_grab_focus() and gtk_widget_activate_default() to the
	lists of standard signal handlers.

2000-09-25  Federico Mena Quintero  <<EMAIL>>

	* glade/tree.c (select_node): New function to select the node in
	the tree in addition to ensuring it is visible on the tree view.
	This makes the widget tree much more usable.
	(tree_select_widget): Use select_node().

2000-09-24  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.10

2000-09-24  Damon Chaplin  <<EMAIL>>

	* configure.in: changed version to 0.5.10.

	* NEWS: 
	* TODO: updated.

	* README: 
	* AUTHORS: changed <EMAIL>.<NAME_EMAIL>
	There are still a few references in the code but I don't want to
	change a translated string just before a release.

2000-09-24  Carlos Perelló Marín <<EMAIL>>

	* glade/glade_gtk12lib.c: Marked some strings to translate.

2000-09-23  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_gtk_makefile_am): in bin_PROGRAMS use
	the actual program name, not the name used as the Makefile.am target.
	This should fix the bug where a program name given as e.g. glade-editor
	ended up as glade_editor.

2000-09-22  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (get_standard_properties): allow GtkCheckButton
	and GtkRadioButton widgets to have tooltips, since they do work even
	though these are sometimes NO_WINDOW widgets. (They create their own
	InputOnly windows and still get enter/leave events which tooltips use.)

2000-09-21  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (add_standard_bottom_menu_items): Only show the
	Cut, Paste & Delete items if the widget can be deleted.

	* glade/editor.c (editor_can_delete_widget): don't let child menus
	be deleted.

2000-09-18  Damon Chaplin  <<EMAIL>>

	* doc/C/.cvsignore: added *.junk

	* glade/editor.c (editor_on_widget_destroyed): new function to remove
	any references to a widget being destroyed. Fixes bug where the
	mouse_over_widget was destroyed leaving us with an invalid pointer.

	* glade/gbwidget.c (on_widget_destroy): call the above function.

	* glade/gbwidgets/gblabel.c (gb_label_new): set labels in tables to
	be left-aligned by default.

	* glade/gbwidget.c (gb_widget_replace_child): when adding labels/
	buttons etc. to	tables set the default x options to GTK_FILL, since
	I think that is more useful (especially when you want to left-align
	labels.

2000-09-17  Damon Chaplin  <<EMAIL>>

	* doc/C/faq.sgml: 
	* FAQ: added Q2.3 about problem trying to use Gnome stock items in
	a GTK+ app.

	* glade/glade_project_options.c (glade_project_options_check_valid): 
	accept an empty source directory, used when the source files are
	output in the project's toplevel directory.

	* glade/source.c: output EXTRA_DIST in the project's toplevel
	Makefile.am to contain the XML file and autogen.sh.

	* glade/glade_menu_editor.c (glade_menu_editor_init): added a "None"
	item first, so it is easy to reset the icon to none.
	(on_entry_changed): checked if "None" was selected, and if it was
	set the entry text to "".

2000-09-16  Damon Chaplin  <<EMAIL>>

	* glade/glade_menu_editor.c (set_interface_state): made the Icon
	property insensitive for check/radio items since it can't be used.

	* glade/gbwidgets/gbtogglebutton.c (gb_toggle_button_get_properties): 
	* glade/gbwidgets/gbradiobutton.c (gb_radio_button_get_properties): 
	hid the Icon property when not in a toolbar, since it can't be used.

	* glade/Makefile.am (glade_DEPENDENCIES): added this since the one
	automatically generated by automake would not include
	$(GLADE_GNOME_LIB) etc. So the glade app would not be automatically
	rebuilt when the files in gnome/ or gnome-db/ were changed.

	* glade/property.c (create_widget_property_page): Make the Class
	property sensitive but not editable.

	* glade/gbwidget.c (gb_widget_redisplay_window): 
	* glade/editor.c (editor_on_key_press_event): queued a resize when
	Crl+R or the Redisplay popup command is used so it works again.
	(It is supposed to redisplay the window in roughly what will be its
	final size. Of course it may be slightly messed up by placeholders
	and custom widgets.)

	* glade/gbwidgets/gbbutton.c: added "relief" property, but this is not
	used for toolbar button items or Gnome dialog buttons.

	* glade/gbwidgets/gbtogglebutton.c: added "relief" property, but not
	for toolbar items.

2000-09-15  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomedruid.c: allowed the start & finish pages to be
	removed and added again, so people can use their own start/end pages.

	* glade/glade_widget_data.[hc]: new files. Moved the code related to
	the GbWidgetData struct here, and added a copy function.

	* *.[hc]: renamed GbWidgetData to GladeWidgetData, and all the flags
	from GB_xxx to GLADE_xxx.

2000-09-14  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_output_translatable_text_in_lines): new
	function like gb_widget_output_translatable_text() but the text is
	split into lines when output to the translatable strings file.

	* glade/save.c (save_translatable_text_in_lines): similar to above.

	* glade/gbwidgets/gboptionmenu.c (gb_option_menu_get_properties): 
	* glade/gbwidgets/gbcombo.c (gb_combo_get_properties): use
	gb_widget_output_translatable_text_in_lines() to split the items into
	single lines in the translatable strings file.

	* glade/glade_menu_editor.c (glade_menu_editor_update_menu): reserved
	the names of the new menu widgets. Previously it was destroying the old
	widgets, which resulted in the old names being released, but it wasn't
	reserving the new names, which meant that name clashes happened
	occasionally. I noticed this problem ages ago but couldn't spot it.

	* glade/glade_project.c (glade_project_new_widget_name): removed any
	existing id on the end of the name. Otherwise if you pass "label1"
	as the base_name, it would return "label11" and set the last id of
	"label1" to 1, which could mess up other label names.

	* glade/gnome/gnomeabout.c (find_gnome_about_info): declared
	gtk_handler_quark as a GQuark.

	* configure.in: turned Bonobo off by default (again!). I don't want
	to turn it on until Bonobo is stable, or we'll just cause problems
	for users with different versions of Bonobo lying around, and I'll
	have to release new versions of Glade each time Bonobo changes.

	* glade/glade_gnome.c: added #include "glade_project.h".

2000-09-13  Damon Chaplin  <<EMAIL>>

	Applied most of patch from Jon K Hellan  <<EMAIL>>, except
	I think <Ret> should only activate the default action in small dialogs
	with only 1 or 2 fields, so I took out the changes to the project
	options & menu editor dialogs. Also changed glade_util_spin_button_new
	so it uses climb_rate & digits.

	Jon's patch comments:
	
	A bunch of changes to
	- make sure that dialogs become transient children of the windows
	  they are invoked from.
	- make the GUI easier to use from the keyboard: <Esc> works as a
 	  synonym for cancel, and <Ret> in entry fields and spinbuttons
	  invoke the default action for the dialog. Finally, keyboard
	  focus should be set when a dialog is popped up, but we've only
	  just started fixing up that.
	Still to be done is assigning underline accelerators to buttons
	and fields in dialogs.

	* glade/utils.h: Include gtkwindow.h and gtkobject.h.
	(GladeEscAction): New typedef.

	* glade/utils.[ch] (glade_util_check_key_is_esc): New keypress
	event handler: Make <Esc> work as Cancel in dialog.
	(glade_util_entry_new): New constructor. Returns a new entry ready
	to insert in a dialog.  The entry is set up so that <Return> will
	invoke the default action. The returned widget must be added to a
	container in the dialog.
	(glade_util_spin_button_new): New constructor. Returns a new
	spinbutton ready to insert in a dialog.  A pointer to the spin
	button is added as object data to the dialog. The spinbutton is
	set up so that <Return> will invoke the default action. The
	returned widget must be added to a container in the dialog.

	* glade/utils.c (glade_util_show_message_box,
	glade_util_show_entry_dialog): Added FIXME comment.
	(glade_util_show_message_box): Set initial focus to OK button.
	(glade_util_create_dialog_with_buttons): Set initial focus to
	default button.
	(glade_util_show_message_box): Make <Esc> work as Cancel in dialog
	- non Gnome. Gnome case was already OK.
	(glade_util_create_dialog_with_buttons,
	glade_util_show_entry_dialog, glade_util_create_dialog): Make
	<Esc> work as Cancel in dialog.
	(glade_util_show_entry_dialog): Make <Ret> in entry fields invoke
	default by using glade_util_entry_new constructor.

	* glade/editor.c (editor_show_grid_settings_dialog,
	editor_show_snap_settings_dialog): Make dialog a transient child
	of window called from. Add widget parameter to make this possible.
	Rename window variable to dialog.  Make <Esc> work as Cancel.
	(editor_show_grid_settings_dialog): Make <Ret> in spinbuttons
	invoke default by using glade_util_spin_button_new constructor.
	Set initial focus to first spinbutton.
	(editor_show_snap_settings_dialog): Set initial focus to first
	checkbutton.  Make <Esc> work as Cancel.
	(on_grid_settings_ok, on_snap_settings_ok): Rename window variable
	to dialog.

	* glade/editor.h (editor_show_grid_settings_dialog,
	editor_show_snap_settings_dialog): Declare new signature with
	widget parameter.

	* glade/glade.[ch] (glade_show_grid_settings,
	glade_show_snap_settings): These functions call
	editor_show_(grid|snap)_settings_dialog, but those now take a
	widget parameter. Here, we don't know any widgets. Since the
	functions are unused, they're commented out. I guess it would be
	even better to remove them outright.

	* glade/glade_clipboard.c (glade_clipboard_init): Added "FIXME"
	comment.

	* glade/glade_menu_editor.c (glade_menu_editor_init): Make <Ret>
	in entry fields invoke default by using glade_util_entry_new
	constructor.
	(on_icon_button_clicked): Make <Esc> work as Cancel in dialog.
	Make it a transient child of window called from.

	* glade/glade_project_options.c (glade_project_options_init): Make
	<Ret> in entry fields invoke default by using glade_util_entry_new
	constructor. Make <Esc> work as Cancel in dialog.
	(glade_project_options_show_file_selection): Make dialog a
	transient child of window called from.

	* glade/glade_project_window.c
	(glade_project_window_on_open_project,
	glade_project_window_edit_options,
	glade_project_window_save_project_as,
	glade_project_window_show_error): Make dialog a transient child of
	project window.
	(glade_project_window_on_open_project,
	glade_project_window_new_errors_dialog,
	glade_project_window_save_project_as): Make <Esc> work as Cancel
	in dialog.
	(glade_project_window_save_project_as): Make <Esc> work as Cancel
	in dialog.
	(glade_project_window_edit_grid_settings): Call
	editor_show_grid_settings_dialog with widget parameter.
	(glade_project_window_edit_snap_settings): s/grid/snap/.
	(glade_project_window_about): Make dialog a transient child of
	project window. Gnome only - non Gnome cases uses
	glade_util_show_message_boxm where this is impossible until the
	signature is changed.

	* glade/property.c (property_create, create_accelerators_dialog):
	Added FIXME comment.
	(show_colorsel_dialog, show_filesel_dialog,
	create_accelerators_dialog, show_events_dialog, show_keys_dialog,
	show_signals_dialog, show_font_dialog, show_style_dialog): Make
	<Esc> work as Cancel in dialog. Make dialog a transient child of
	window called from.

	* glade/tree.c (tree_init): Added FIXME comment.

	* glade/gbwidgets/gbclist.c (show_clist_dialog): Make <Ret> in
	spinbuttons invoke default by using glade_util_spin_button_new
	constructor.

	* glade/gbwidgets/gbmenubar.c (dialogize): New function - make
	window behave like a dialog. For use when called from the
	properties window.
	(gb_menu_bar_on_edit_menu, gb_menu_bar_on_edit_menu_activate): Use
	it.

	* glade/gbwidgets/gbctree.c (show_ctree_dialog): Ditto.

	* glade/gbwidgets/gbhbox.c (show_hbox_dialog): Ditto

	* glade/gbwidgets/gbhbuttonbox.c (show_hbbox_dialog): Ditto

	* glade/gbwidgets/gbimage.c (show_image_dialog): Ditto.

	* glade/gbwidgets/gbnotebook.c (show_notebook_dialog):  Ditto.

	* glade/gbwidgets/gbtable.c (show_table_dialog): Ditto.

	* glade/gbwidgets/gbtoolbar.c (show_toolbar_dialog): Ditto.

	* glade/gbwidgets/gbvbox.c (show_vbox_dialog): Ditto.

	* glade/gbwidgets/gbvbuttonbox.c (show_vbbox_dialog): Ditto.

	* glade/gnome/gnomedruid.c (show_druid_dialog): Ditto.

	* glade/gnome/gnomepropertybox.c (show_gnome_property_box_dialog):
	Ditto.

	* glade/property.[ch]: Add GbClass property name for widget class.

	* glade/property.c (property_set_widget,
	create_widget_property_page): Add widget class to property editor
	display. Make it insensitive.

	* glade/gbwidget.c (get_standard_properties): 
	Ditto.

2000-09-13  Damon Chaplin  <<EMAIL>>

	* glade/utils.c (glade_util_ensure_directory_exists): 
	* glade/save.c: 
	* glade/editor.c: applied most of patch from Arnaud Charlet
	<<EMAIL>>	to compile on Win32.

2000-09-12  Damon Chaplin  <<EMAIL>>

	* configure.in: removed setting of PACKAGE_PIXMAPS_DIR.
	* Makefile.am: install the logo in $(datadir)/pixmaps/glade rather
	than using PACKAGE_PIXMAPS_DIR (`gnome-config --datadir`/pixmaps/glade)
	This avoids problems when people want to install in their home
	directories etc.

	* glade/source.c (source_write_gnome_configure_in): 
	(source_write_gnome_makefile_am_pixmaps_targets): remove use of
	PACKAGE_PIXMAPS_DIR, and just install pixmaps in
	$(datadir)/pixmaps/$(PACKAGE). This means the person installing the
	package will have to make sure pixmaps get picked up by Gnome.

2000-08-26  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_show_popup_menu): unref the menu on
	the "hide" signal. Should fix leak spotted using Purify by
	SigWait <<EMAIL>>.

	* glade/glade_project.c (glade_project_destroy): free program_name,
	fix from SigWait <<EMAIL>>.

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_write_add_child_source): 
	patch from Josh Green <<EMAIL>> to add space before toolbar
	widgets, if needed.

	* glade/main.c (write_source): patch from Rick Niles <<EMAIL>>
	to handle relative paths to the XML file.

2000-08-12  Michael Meeks  <<EMAIL>>

	* glade/gnome/gnomecontrol.c: Revise for new Bonobo.
	(gb_bonobo_control_set_properties): add ugly hack to get some items
	to have icons.

2000-08-09  Dan Mueth <<EMAIL>>

	* doc/C/user-guide.sgml: Put Michael Vance as author and
	updated license to FDL.

2000-08-03  JP Rosevear  <<EMAIL>>

	* configure.in : Check for bonobo > 0.15 and use bonobox to build. 

2000-07-21  Fatih Demir  <<EMAIL>> 

	* glade.desktop: Added the Turkish desktop entries.

2000-07-15  Michael Meeks  <<EMAIL>>

	* configure.in (Configuration): add std. configuration summary.
	update bonobo check so it works with recent bonobos & depend on
	0.15 at least. (try_bonobo): use unless called --without-bonobo

2000-06-25  Damon Chaplin  <<EMAIL>>

	* doc/C/.cvsignore: added.

2000-06-25  Damon Chaplin  <<EMAIL>>

	* configure.in (ALL_LINGUAS): added ko (Korean).

2000-06-10  Fatih Demir	<<EMAIL>>
	
	* configure.in: Added tr to ALL_LINGUAS.
	
2000-06-09  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbwindow.c (gb_window_write_standard_source): 
	check widget->title is non-NULL. Fixes SEGV in GnomePropertyBox.

2000-05-31  Carlos Perelló Marín <<EMAIL>>

	* Updated Spanish translation

2000-05-23  Damon Chaplin  <<EMAIL>>

	* doc/C/turbo-start.sgml: added to CVS. This was in 0.5.9.

2000-05-21  Damon Chaplin  <<EMAIL>>

	* glade/property.c (create_standard_property_page): mark GbX tip for
	translation.

2000-05-20  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.9

2000-05-20  Damon Chaplin  <<EMAIL>>

	* glade/gnome/gnomepropertybox.c: 
	* glade/gnome/gnomemessagebox.c: patch from James M. Cape
	<<EMAIL>> to allow setting of the Title in
	GnomeMessageBox, and remove the Type property, and allow setting of
	all the standard window properties in GnomePropertyBox except Type.

	* glade/glade_palette.c: patch from James M. Cape
	<<EMAIL>> to use the toolbar relief set in the Gnome
	user preferences (i.e. the Control Panel) for the palette.

	* doc/C/turbo-start.sgml: 
	* doc/C/Makefile.am: 
	* doc/C/topic.dat: added Turbo-Start from Paul J. Drongowski 
	<<EMAIL>>.

2000-05-14  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.8

2000-05-13  Damon Chaplin  <<EMAIL>>

	* doc/file_format.txt: 
	* glade/glade_project_options.[hc]: 
	* glade/glade_project.[hc]: added gnome_help_support project option.
	  Currently we just output the GNOME_UIINFO_HELP macro at the top of
	  the Help GnomeUIInfo structs, but we will output a template help
	  file with build files in future.

	* glade/gnome/gnomedockitem.c (gb_gnome_dock_item_write_add_child_source): 
	install the menu's hints in the status bar if the GnomeApp has one.

	* glade/glade_gnome.c (glade_gnome_is_app_dock_item): changed it so
	it returned the GnomeApp rather than TRUE.

	* glade/main.c: applied modified version of a patch from Jan Kratochvil
	<<EMAIL>> to support a --write-source command-line option.
	Note that it will need an X connection to work. Jan also sent a patch
	to set up batch building of Glade projects, though I haven't applied
	that.
	
2000-05-11  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_window.c (HelpMenu): added GNOMEUIINFO_HELP
	macro, to automatically add the stuff from topic.dat.

	* doc/C/*: Glade User Guide & FAQ added.

	* doc/Makefile.am: added SUBDIRS = C

	* configure.in (AC_OUTPUT): added doc/C/Makefile

2000-05-10  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (set_position_properties): workaround for GTK+ bug
	 - when loading, hide widgets before calling set_uposition() or we may
	get a warning like this:
		Gtk-WARNING **: gtk_widget_size_allocate(): attempt to allocate widget with width 65519 and height 65535

2000-05-01  Michael Meeks  <<EMAIL>>

	* glade/gnome/gnomecontrol.c: kill nasty hacked prototypes, include
	bonobo-object-directory.h

	* glade/main.c (main): oafize.

	* configure.in (have_bonobo): Move check around.

2000-05-01  Anders Carlsson  <<EMAIL>>

	* glade/main.c: oafize.
	
2000-04-23  JP Rosevear  <<EMAIL>>

	* glade/glade_project_window.c (glade_project_window_about): Combine
	separate NLS strings into one (compiler doesn't concat them like
	regular const strings).

2000-04-20  Martin Norbäck  <<EMAIL>>

	* glade/glade_project_window.c: made more strings translatable

2000-04-18  Pablo Saratxaga <<EMAIL>>

	* configure.in (ALL_LINGUAS): added Catalan

2000-04-16  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_get_class_id): If the widget does not
	have an associated GbWidget, just return the widget's class name.

2000-04-15  Pablo Saratxaga <<EMAIL>>

	* configure.in (ALL_LINGUAS): Added Greek language to list

2000-04-08  Ruben Lopez  <<EMAIL>>

	* glade.desktop: Added Galician (gl) translation.
        * configure.in: Added Galician (gl) translation to ALL_LINGUAS.

2000-04-02  Damon Chaplin  <<EMAIL>>

	* configure.in: added GnomeDB support and tidied up a bit.

	* glade/gnome-db/*: New directory to support GnomeDB.
	
	* glade/gnome/gnomemessagebox.c (gb_gnome_message_box_write_source): 
	patch from Ralf Corsepius <<EMAIL>> to fix default
	click_closes property (the default is TRUE for GnomeMessageBox).

	* glade/gbwidgets/gbpixmap.c (gb_pixmap_write_source): always create
	a pixmap, even if the filename isn't set. Otherwise there are problems
	in widgets like notebooks where all children must exist or the order
	of the children is messed up.

	* glade/source.c (source_create_pixmap): handle empty filenames.

	* glade/source.c (source_write_gnome_create_pixmap_functions): 
	(source_write_gtk_create_pixmap_functions): if the filename is empty
	return a dummy pixmap.

	* glade/gbwidget.c (gb_widget_lookup): Added a fallback to the original
	way we looked up the GbWidget*, for cases in which widgets do not have
	a GbWidgetData attached (e.g. popup menus seems to have a problem when
	the source code is generated).

	* glade/source.c (source_write_gnome_macro_files): added gperf-check.m4
	to the macros files to fix problem with 'make dist' on a project.

	* glade/gnome/gnomeiconlist.c (gb_gnome_icon_list_write_source): patch
	from Jens Finke <<EMAIL>> to output code to set selection
	mode if needed.

	* glade/gbsource.c (gb_widget_write_signals_source): 
	* glade/property.c (create_signals_property_page): updated lists of
	built-in GTK+ signal handler functions. We shouldn't duplicate these,
	and they should change according to the project's language.

	* glade/glade_gnome.c (glade_gnome_write_menu_item_standard_source): 
	set check/radiomenuitems active if needed. Also set sensitivity.

	* glade.spec.in: patch from John GOTTS <<EMAIL>>.

	* tools/mkskel.pl (OutputWidgetFile): output "#include <config.h>"

2000-04-01  Michael Meeks  <<EMAIL>>

	* configure.in (have_bonobo): Ask for 0.10.

	* glade/gnome/gnomecontrol.c (gb_bonobo_control_create_properties),
	(gb_bonobo_control_set_propertiesm gb_bonobo_control_get_properties): 
	Update for CVS bonobo, hopefully this finishes property API breakage
	for good.

2000-03-28  Michael Meeks  <<EMAIL>>

	* glade/gnome/gnomecontrol.c: conditional compile whole file.

	* glade/gnome/Makefile.am: back out yesterday's fix: Damon
	doesn't like it.

2000-03-27  Michael Meeks  <<EMAIL>>

	* glade/gnome/Makefile.am (BONOBO_FILES): add.

	* configure.in (have_bonobo): fixup.

2000-03-13  Alastair McKinstry  <<EMAIL>>

	* glade.desktop: Added Irish (ga) translation.
	* configure.in (ALL_LINGUAS): Added Irish (ga).

2000-03-10  Michael Meeks  <<EMAIL>>

	* glade/gnome/gnomecontrol.c (gb_bonobo_control_init): Use
	IDL:Bonobo/Control; doh.

2000-03-10  Michael Meeks  <<EMAIL>>

	* glade/gnome/gnomecontrol.c (create_prop_name): kill case bending
	since case problems have disappeared.

2000-03-08  Michael Meeks  <<EMAIL>>

	* glade/main.c: include bonobo headers.
	(final_setup): create, (main): farm final setup bits off into a
	one shot idle handler, since we can't do CORBA stuff until we
	hit the main loop. On load we need to use CORBA.

	* glade/load.c (real_load_project_file): correct case on GTK-Interface.
	(load_token): kill g_strdown fixing xml case problems.

	* glade/glade_project.c: s/class_name/class_id/

	* glade/glade_palette.c: s/classname/classid/

	* glade/glade_gnomelib.h: add gb_bonobo_control_init

	* glade/gbwidget.h: kill gb_widget_real_initialize

	* glade/gbwidget.c (gb_widgets_init): add gb_bonobo_control_init
	(gb_widget_register_gbwidget): store the class_id in the gbwidget.
	(gb_widget_lookup): Use the object_data & GbWidgetData gbwidget
	pointer. Kill special Custom code.
	(gb_widget_get_type_name): rename to (gb_widget_get_class_id): re-write.
	(gb_widget_init_struct): init class_id.
	(gb_widget_create_from): Setup class_id & fix custom widgets.
	(gb_widget_new_widget_data): add gbwidget parameter.
	(gb_widget_real_initialize): moved up and made static.
	(set_special_child_properties): add debug.
	(gb_widget_new_full): add warnings & pass gbwidget to gb_widget_new_widget_data
	s/class_name/class_id/
	
	* glade/gbsource.c: s/class_name/class_id/

	* glade/glade.[ch]: s/class_name/class_id/

	* configure.in: add bonobo support.

	* acconfig.h: #undef ENABLE_BONOBO

	* glade/gnome/gnomecontrol.c: Created.

	* glade/gnome/Makefile.am: Add gnomecontrol.c

2000-02-20  Andreas Hyden  <<EMAIL>>

	* src/utils.c: Added _() around buttons[i]
	in gtk_button_new_with_label () so they
	get marked for translation.

2000-02-19  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.7

2000-02-19  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_gnome_macro_files): commented out
	gnome-gettext.m4 since it has been removed.

2000-02-12  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.6

2000-02-12  Damon Chaplin  <<EMAIL>>

	* glade/editor.c (editor_on_button_press): patch from 
         <<EMAIL>> to show a message & tip about containers
	when the user tries to add a widget in an invalid position.

	* README: 
	* TODO:
	* INSTALL: updated.

2000-02-06  Damon Chaplin  <<EMAIL>>

	* README: updated.
	
2000-02-04  Damon Chaplin  <<EMAIL>>

	* FAQ: update answer about m4 files, to suggest using ACLOCAL_FLAGS
	instead of copying m4 files around.

	* glade/gbwidget.c (gb_widget_children_foreach): use new function
	box_foreach() so that children are saved and the source code written
	in the correct order.

2000-02-01  Damon Chaplin  <<EMAIL>>

	* glade/utils.[hc] (glade_util_is_component): new function to test if
	a widget is a project component (window/dialog/popup menu).

	* glade/gbwidget.c (get_lang_specific_properties):
	* glade/gbwidget.c (set_standard_properties): use new function
	glade_util_is_component() to test for toplevel widgets.

	* glade/glade_project_options.c (glade_project_options_check_valid): 
	check that the translatable strings filename has been set if needed.

2000-01-29  Damon Chaplin  <<EMAIL>>

	* doc/file_format.txt: new section describing defaults for the project
	options. Future Glade versions will only output the options in the XML
	file if	they differ from the defaults, so code generators should be
	updated if necessary.

	* glade/glade_project.c (glade_project_save_options): Only use default
	values for C options which won't affect Glade versions 0.5.0-0.5.5.

2000-01-22  Damon Chaplin  <<EMAIL>>

	* glade/editor.c
	* glade/glade_plugin.c
	* glade/glade_project.c
	* glade/save.c
	* glade/source.c
	* glade/source_os2.c
	* glade/utils.c: Patch from Arnaud Charlet <<EMAIL>>
	to compile Glade on Win32.

	* glade/glade_gnome.c (glade_gnome_write_menu_item_source): fixed a
	few bugs when using normal pixmaps in menuitems.

	* glade/glade/editor.c
	* glade/glade/glade_clipboard.c
	* glade/glade/glade_keys_dialog.c
	* glade/glade/glade_menu_editor.c
	* glade/glade/glade_project_options.c
	* glade/glade/glade_project_window.c
	* glade/glade/property.c
	* glade/glade/tree.c:
	* glade/glade/palette.c:
	patch from James M. Cape <<EMAIL>> to set the WM_NAME
	& WM_CLASS properties of all windows, so window managers can remember
	their positions. Plus a few changes from me.

	* glade/glade_project_window.c: added a confirm dialog before quitting
	so people don't lose work accidentally.

	* glade/gbwidget.c (get_lang_specific_properties): only output
	cxx_use_heap if it is different from the default value of 0, otherwise
	it is output for all widgets in projects for other languages.

	* glade/glade_project.c (glade_project_save_options): 
	(glade_project_load_options): used defaults for most project options
	so most will not appear in the XML. When loading also made sure that
	any unset options are set to the same defaults.

	* glade/gbwidgets/gbhpaned.c (gb_paned_write_add_child_source): 
	* glade/gbwidgets/gbvpaned.c (gb_vpaned_init): 
	* glade/gb.h: wrote out proper code to add widgets to paned containers.

	* glade/gbwidgets/gbtext.c (gb_text_set_properties): 
	(gb_text_write_source): don't realize the text widget before inserting
	text, since that isn't needed any more, and it causes problems with
	notebooks (a bug in GTK+ I think).

	* glade/property.c (on_signal_clear, on_signal_add): set "signal after"
	button to FALSE when signal added and when cleared, such behavior is
	more intuitive. (Patch from Arturo Tena  <<EMAIL>>)

	* glade/main.c (parse_command_line): const fix.

	* glade/gbwidgets/gbprogressbar.c (gb_progress_bar_set_properties): 
	fixed one-line bug 'min = adjustment->upper', should be max.

1999-12-23  Matthias Warkus  <<EMAIL>>

	* glade.png: Added icon by Eduardo da Silva

1999-11-22  Damon Chaplin  <<EMAIL>>

	* glade/glade_gtk12lib.c (standard): added Custom widget to Martijn's
	palette on the standard page.

1999-11-18  Damon Chaplin  <<EMAIL>>

	* configure.in: fixed setting of GLADE_DATA_DIR, so --datadir works.

1999-11-24  Yuri Syrota  <<EMAIL>>

	* configure.in (ALL_LINGUAS): Added uk to ALL_LINGUAS

1999-11-14  Damon Chaplin  <<EMAIL>>

	* glade/gbsource.c (gb_widget_write_source): set data->write_children
	to TRUE near top of function, in case the current widget is skipped.

1999-11-09  Martijn van Beers  <<EMAIL>>

	* glade/source.c (source_create_valid_identifier):
	  prepend an _ when the identifier starts with a number, so that
	  widgets 4thwidget and 5thwidget are still unique

1999-11-11  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.5

1999-11-11  Damon Chaplin  <<EMAIL>>

	* glade/property.c (create_accelerators_dialog): fixes for GTK+
	version.

	* glade/source.c (source_write_component): used (void) for the function
	prototypes in interface.c, for use with -Wstrict-prototypes.

1999-11-06  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.4

1999-11-06  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbcombo.c (gb_combo_get_properties): 
	(add_label): 
	* glade/source.c (source_write_gnome_makefile_am): 
	* glade/gbsource.c (gb_widget_write_signal_handler_source): 
	* glade/gbwidget.c (gb_widget_create_child_properties): 
	* glade/gbwidgets/gbhbox.c (gb_box_set_child_properties): 
	fix a few warnings for Mips 10000, some of which were minor bugs.

	* glade/glade_gnome.c (glade_gnome_write_menu_item_source): use
	source_make_static_string() for the tooltips since they are used in the
	GNOME_UI_INFO structs.

	* glade/load.c (load_parse_color): multiply colors by 257 rather than
	256 so that ff gets mapped to ffff.

	* glade/gbwidgets/gbpacker.c (gb_packer_write_add_child_source): always
	use GtkPackerOptions cast in the source code (for g++).

	* glade/gnome/gnomedruidpagefinish.c (gb_gnome_druid_page_finish_get_properties): 
	* glade/gnome/gnomedruidpagestart.c (gb_gnome_druid_page_start_get_properties): 
	* glade/gnome/gnomedruidpagestandard.c (gb_gnome_druid_page_standard_get_properties): Output titles and text as translatable.

	* glade/gbwidget.c (gb_widget_load_style): return OK status so styles
	are skipped properly.

	* glade/editor.c (editor_can_delete_widget): allow labels in buttons/
	menuitems to be deleted.
	Stop the child widgets of GtkCombo, GnomeEntry etc. from being deleted.
	(editor_on_button_press): don't allow widgets to be added to a
	GnomeCanvas, since we don't support that.

	* glade/utils.c (glade_util_parent_directory): few _() added.

	* FAQ: added question on connecting to adjustment signals.

	* glade/source.c (source_write_main_c): wrap calls to bindtextdomain()
	and textdomain in #ifdef ENABLE_NLS to get rid of warnings about
	statements with no effect when NLS is disabled.
	(source_write_support_files): include config.h in GTK+ apps to make
	sure ENABLE_NLS is set.

	* glade/property.c: moved accelerators from a property page to a
	dialog box, since they are very rarely needed.

	* glade/property.c (show_signals_dialog): sort signals alphabetically,
	added ' signals' to group names, e.g. 'GtkButton signals', and
	skipped classes with no signals.

	* glade/glade_gnome.c: updated the stock arrays to latest gnome-libs
	(i.e. added the Midi pixmaps, which weren't in gnome-libs 1.0.0)

	* glade/gnome/gnomeanimator.c: supported the easy properties. Haven't
	added support for the frames of the animation yet, though.

	* glade/gbwidget.c (gb_widget_write_standard_source): don't output code
	to set the size of GnomeAnimator widgets since it is set when creating
	the widget. I didn't want to have 2 separate 'Width' & 'Height'
	properties so I overloaded the basic properties.

	* FAQ: added question about scrolled window warnings.

1999-10-27  Martin Norbäck  <<EMAIL>>

	* glade.desktop: added swedish desktop entry

Wed Oct 13 08:50:57 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source_os2.c: Adding GNOME support for makefile.os2.

1999-10-10  Martijn van Beers  <<EMAIL>>

	* glade/gbsource.[ch]: New files
	* glade/gbwidget.[ch]: Moved all source-generating stuff
	                       to gbsource.[ch]
	* glade/Makefile.am: put new files in build.

Sun Oct  3 15:47:34 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source.c (source_write_include_files): Add this function
	for printout of header files. Add also <sys/types.h> to remove
	compiler warning under OS/2.

1999-09-27  Anders Carlsson  <<EMAIL>>

	* configure.in (ALL_LINGUAS): Added sv to ALL_LINGUAS

1999-09-12  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.3

1999-09-10  Damon Chaplin  <<EMAIL>>

	* glade/glade_clipboard.c: set pointers to a project to NULL when the
	project is destroyed, so we never have invalid pointers.

	* glade/gbwidgets/gblayout.c: 
	* glade/gbwidgets/gbtext.c: 
	* glade/gbwidgets/gbctree.c: 
	* glade/gbwidgets/gbclist.c: changed the way we connect handlers to
	the adjustments. It should be more reliable now.

	* glade/editor.c (placeholder_finish_replace): Automatically insert
	scrolled windows or viewports depending on the widget being added.

	* glade/gbwidget.c (add_standard_bottom_menu_items): 
	(gb_widget_add_scrolled_window): 
	(gb_widget_remove_scrolled_window): new popup menu commands to add or
	remove a scrolled window from widgets which can handle scrolling.

	* glade/tree.c (tree_remove_widget_parent): changed parent argument to
	grandparent so it can be called after the widget hierarchy has been
	rearranged.

	* FAQ: added a few more questions.

	* glade/glade_gnome.c (glade_gnome_gettext): checked for ENABLE_NLS.

1999-09-05  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.2

1999-09-05  Damon Chaplin  <<EMAIL>>

	* FAQ: added a few more common questions & answers.

	* glade/gbwidgets/gbhscale.c: 
	* glade/gbwidgets/gbvscale.c: 
	* glade/gbwidgets/gbhscrollbar.c: 
	* glade/gbwidgets/gbvscrollbar.c: 
	* glade/gbwidgets/gbspinbutton.c: update all the adjustment property
	names so all of them use 'value', 'lower', 'upper', 'step', 'page'
	and 'page_size' instead of 'hvalue', 'hlower' etc. and 'vvalue',
	'vlower' etc. That made no sense for GtkSpinButton, GtkVScale was using
	'hvalue' etc., and the 'h's and 'v's aren't really necessary.

	Glade will accept old & new property names on input and	write out the
	new ones, so external tools should just support the new	names.

1999-09-05  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gblabel.c: added support for focus_target and
	default_focus_target properties. The focus_target property is the
	widget which should get the keyboard focus when the underlined
	accelerator key in the label is used. It is selected from a combo
	containing all the widgets in the component which can accept the focus.

	The first option in the list is 'Auto' which tells Glade to try to
	find the target itself. If this is used, focus_target will not appear
	in the XML file, but a default_focus_target property may be saved
	containing the name of the default target widget found by Glade.
	(Though it may not be able to find one.)

1999-09-04  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c: 
	* glade/palette.c: 
	* glade/glade_palette.h: 
	* glade/glade_palette.c: 
	* glade/glade_project.h: 
	* glade/glade_project.c: 
	* glade/glade_project_options.h: 
	* glade/glade_project_options.c: reverted Martin's changes since there
	were a few problems which need to be fixed first.

	* glade/gbwidgets/gbpixmap.c: added build_insensitive boolean property.

	* glade/gbwidgets/gbcombo.c: added value_in_list & ok_if_empty
	boolean properties.

	* glade/*.h: got rid of all the '#pragma }' lines and renamed the top
	#ifdef's since we shouldn't really use names starting with '__' as
	that is reserved.
	
	* glade/editor.c: use gdk_window_get_pointer() instead of
	gtk_widget_get_pointer() since the latter will subtract the allocation
	if the widget has no window, which confuses our code.

	* glade/editor.c (editor_get_event_widget): it now looks up the
	widget hierarchy for a GbWidget to direct the event to as well.
	If we don't do this, then a widget may pinch the event before we get
	it. This meant that GnomeIconEntry and GnomeIconSelection couldn't be
	selected easily.

	* glade/gnome/gnomedruid.c: 
	* glade/gnome/gnomedruidpagestart.c: 
	* glade/gnome/gnomedruidpagestandard.c: 
	* glade/gnome/gnomedruidpagefinish.c: 
	* glade/gnome/gnomepixmap.c: 
	* glade/gnome/gnomepropertybox.c: 
	* glade/gnome/gnomeiconselection.c: 
	* glade/gnome/gnomeiconlist.c: new widgets supported.

	* glade/property.c (create_signals_property_page): hid the signal
	data & object properties. I hope not too many people have been using
	them. They make signals too complicated. The 'After' flag can cause
	problems too, but I don't think we can get rid of that.

	* glade/gbwidgets/gbwindow.c (gb_window_write_standard_source): fixed
	output of gtk_window_set_wmclass(). Remember source_make_string() uses
	the same buffer for every call! I think this should be fixed somehow
	since it often causes problems. Maybe allocate memory for each string,
	but then it would have to be freed after use. Maybe use one GString
	to contain all the strings for one widget, then free it all, but I'm
	not sure GString supports that. Or maybe keep a pool of GStrings.

	* glade/glade_gnome.c (glade_gnome_write_radio_menu_item_source): 
	incremented the uiinfo index so the GNOME_APP_UI_RADIOITEMS item is
	skipped properly.

	* glade/gbwidget.h: got rid of GB_INITIAL_EXPOSE flag since it didn't
	work properly. Some widgets never got an expose event since their area
	was completely covered by their child widgets. We connect to
	size_allocate and update the position properties there if necessary,
	so we should be OK. We now use GB_SIZE_NOT_ALLOCATED flag instead.

	* glade/gbwidget.c: removed code which the set size of a GtkCombo's
	entry whenever the combo's size was changed. Also removed it from the
	source code output as well. It seems to be fixed in GTK+ now.

	* glade/property.c: 
	* glade/gbwidget.c: cut out a lot of the style-related code using
	'#ifdef GLADE_STYLE_SUPPORT'. It isn't finished and it is buggy so it
	is probably best to take it out for now.

	* glade/editor.c (editor_delete_widget): don't rely on the placeholder
	still being there after gb_widget_replace_child(). If it is added to
	a table, and there is already a widget in the same position, it will
	be destroyed immediately.

	* glade/gbwidgets/gbtable.c (gb_table_set_child_properties): fixed
	updating of table child's position.

	* glade/gbwidgets/gbpacker.c (gb_packer_write_add_child_source): 
	output "(GtkPackerOptions) 0" rather than just "0" to keep g++ happy.

	* glade/gbwidgets/gblayout.c: new widget supported, like GtkFixed
	but with builting support for scrolling.

	* glade/gbwidgets/gbctree.c: 
	* glade/gbwidgets/gbclist.c: improved redrawing when scrolling.

	* glade/utils.c (glade_util_find_toolbar_accelerator_target): new
	function to find default accelerator target in a toolbar.
	(glade_util_find_default_accelerator_target): initialized target to
	NULL.

	* glade/gbwidgets/gblabel.c (gb_label_write_source): forgot to output
	code to create the label when the accelerator target wasn't found.

	* glade/utils.c (glade_util_find_focus_child): returns the child of
	a widget which should get the keyboard focus when an accelerator is
	setup.

	* glade/gbwidgets/gbarrow.c (gb_arrow_set_properties): fixed setting
	of direction and shadow type properties.

1999-09-02  Martin Baulig  <<EMAIL>>

	* glade/glade_palette.h (GladePaletteClass): Added `create_item'
	as new signal function.

	* glade/glade_palette.c (on_palette_button_toggled): Emit
 	"create_item" signal if the user pressed the shift button so
 	you can create arbitrary widgets by holding down the shift button.

	* glade/palette.c (on_palette_create_item): New function to
	create arbitrary widgets.

	* glade/gbwidget.c (gb_widget_save): Don't save placeholders if
	we disabled them in the project options.

	* glade/glade_project.h (GladeProject): Added `save_placeholders'
	(glade_project_get_save_placeholders): New function.
	(glade_project_set_save_placeholders): New function.

	* glade/glade_project.c (glade_project_show_component):
	If the component is neither a menu nor a toplevel, add it to
	a newly created toplevel window and show it.

	* glade/glade_project_options.h (GladeProjectOptions):
 	Added `save_placeholders'.

Wed Sep  1 16:01:49 1999  Pablo Saratxaga <<EMAIL>>

	* configure.in, po/da.po: added Danish language file

Thu Aug 26 19:20:01 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/save.c (save_project_file_internal): remove backup file
	before rename the original to it. Only for OS/2 !

Fri Aug  6 21:46:39 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source_os2.c (source_write_makefile_simple): New makefile.os2 format.

Thu Aug  5 22:07:14 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source.c (source_write_build_files): Patch for OS/2 version
 	only. Parameter missing.

1999-08-01  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.1

1999-08-01  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbcombo.c: 
	* glade/gnome/gnomeentry.c: 
	* glade/gnome/gnomefileentry.c: 
	* glade/gnome/gnomenumberentry.c: 
	* glade/gnome/gnomepixmapentry.c: made the GtkEntry widgets special
	children, so that its properties can be set and signal handlers can
	be added. For GnomePixmapEntry the GnomeFileEntry is also made a
	special child.

	* glade/gbwidget.c (gb_widget_write_standard_source): for toplevel
	widgets we don't ref the widget since if we do it never gets destroyed.

	* glade/utils.c (glade_util_get_label_text): used the wide char version
	of the label text and converted to multi-byte when returning.

	* glade/editor.c (editor_delete_widget): widgets in a GtkPacker get
	deleted completely, just like widgets in a GtkFixed.

	* glade/gnome/*.c: included <config.h> first so i18n works OK.

	* glade/gnome/gbpixmapmenuitem.c (gb_pixmap_menu_item_destroy): remove
	the pixmap from the project when the menuitem is destroyed.

	* glade/glade_menu_editor.c (on_stock_item_entry_changed): call
	set_interface_state after changing the stock item so that the label
	and icon become editable if appropriate.
	(glade_menu_editor_update_menu): always use a GtkPixmapMenuItem when
	an icon has been specified, even it it couldn't be loaded, since the
	pixmap file is added to the project and is only removed in the destroy
	function of GtkPixmapMenuItem.

	* glade/tree.c (tree_add_widget): If the widget has already been
	added, check if its name needs updating.

	* glade/gbwidget.h (struct _GbWidgetWriteSourceData): added
	focus_widget and default_widget so we can set these after creating the
	entire component. GnomeDialog messes the default up otherwise.
	We can also check if several widgets have these flags set, so we can
	output warnings.

	* glade/gbwidgets/gbbutton.c (gb_button_set_stock_button): when setting
	back to normal button check if it is a normal button or a GnomeDialog
	button and recreate as appropriate.

	* glade/gbwidgets/gbradiobutton.c (gb_radio_button_write_source): 
	* glade/gbwidgets/gbtogglebutton.c (gb_toggle_button_write_source): 
	* glade/gbwidgets/gbcheckbutton.c (gb_check_button_write_source): 
	* glade/gbwidgets/gbbutton.c (gb_button_write_source):
	* glade/gbwidgets/gblabel.c (gb_label_write_source): setup accelerator
	key if the label has an underlined character.

	* glade/gbwidgets/gbhbox.c: added Position child packing property,
	so children of a box can be reordered easily. Though it may be a little
	confusing due to the PACK_START/END flags. It isn't saved in the XML.

	* glade/gbwidgets/gboptionmenu.c: fixed the initial choice property.

	* glade/save.c (save_project_file_internal): backup existing XML file.

	* glade/gbwidgets/gbcolorselection.c (gb_color_selection_write_source):
	* glade/gbwidgets/gbmenuitem.c (gb_menu_item_write_accel_source): two
	bugs found thanks to G_GNUC_PRINTF and fixed.

	* glade/gbwidgets/gbpacker.c: added spacing, default border width,
	default padx, pady, ipadx, ipady properties.
	Added use_default child property.
	Added child position property which isn't saved in the XML.

	* glade/editor.c (editor_on_button_press): do selection/add widget
	only on single button press, not double-clicks.

	* glade/gbwidget.c (gb_widget_input_adjustment): 
	* glade/gbwidgets/gbtable.c (gb_table_set_properties): 
	(gb_table_set_child_properties): 
	* glade/gbwidgets/gbscrolledwindow.c (gb_scrolled_window_set_properties): 
	* glade/gbwidgets/gbprogressbar.c (gb_progress_bar_set_properties): 
	* glade/gbwidgets/gbpixmap.c (gb_pixmap_set_properties): 
	* glade/gbwidgets/gbpacker.c (gb_packer_set_child_properties): 
	* glade/gbwidgets/gbwindow.c (gb_window_set_standard_properties): 
	* glade/gbwidgets/gbimage.c (gb_image_set_properties): 
	* glade/gbwidgets/gbvbuttonbox.c (gb_vbutton_box_set_properties): 
	* glade/gbwidgets/gblabel.c (gb_label_set_properties): 
	* glade/gbwidgets/gbvruler.c (gb_vruler_set_properties): 
	* glade/gbwidgets/gbhruler.c (gb_hruler_set_properties): 
	* glade/gbwidgets/gbhbuttonbox.c (gb_hbutton_box_set_properties): 
	* glade/gbwidgets/gbhbox.c (gb_box_set_child_properties): 
	* glade/gbwidgets/gbgammacurve.c (gb_gamma_curve_set_properties): 
	* glade/gbwidgets/gbcurve.c (gb_curve_set_properties): 
	* glade/gbwidgets/gbaspectframe.c (gb_aspect_frame_set_properties): 
	* glade/gbwidgets/gbarrow.c (gb_arrow_set_properties): 
	* glade/gbwidgets/gbalignment.c (gb_alignment_set_properties): 
	* glade/gbwidgets/gbaccellabel.c (gb_accel_label_set_properties): 
	corrected code which sets multiple properties at once, so that any
	properties which aren't set retain their old values.
	
	* glade/gnome/gnomedock.c (gb_gnome_dock_write_source): 
	(gb_gnome_dock_write_add_child_source): floating dock items are set
	to initially appear cascaded from 100,100 in 50x50 increments.

	* glade/gbwidgets/gbhandlebox.c: added shadow_type, handle_position,
	and snap_edge properties.

	* glade/gbwidgets/gbmenubar.c: added shadow_type property.

	* glade/source.h: used G_GNUC_PRINTF () so gcc can type-check calls
	to source_add() etc.

	* glade/gbwidgets/gbarrow.c (GbShadowChoices): in GTK+ 1.2 arrow now
	supports all shadow choices - none, in, out, etched in, etched out.

	* glade/glade_project_window.c (glade_project_window_update_title): 
	Display "<untitled>" if the project name hasn't been set.

	* glade/tree.c (tree_add_widget): set expanded to FALSE when adding
	new widgets to the tree. The tree really needs buttons or something
	to expand/collapse all widgets.

	* glade/gbwidgets/gbwindow.c: 
	* glade/gbwidgets/gbcolorselectiondialog.c: 
	* glade/gbwidgets/gbdialog.c: 
	* glade/gbwidgets/gbfontselectiondialog.c: 
	* glade/gbwidgets/gbfileselection.c: 
	* glade/gbwidgets/gbinputdialog.c: 
	* glade/gnome/gnomeapp.c: 
	* glade/gnome/gnomedialog.c: 
	* glade/gnome/gnomemessagebox.c: 
	* glade/gnome/gnomeabout.c: used common functions in gbwindow.c for
	all the common window properties - title, type, position, modal,
	default w & h, shrink, grow, autoshrink, wmname & wmclass.
	Some of these properties weren't supported by some of the widgets, so
	I've added support for them where they are appropriate.
	Changed position so we store it separate from the widget, so it doesn't
	affect the window position while in Glade.
	The Modal property is a new property, also stored separately so it
	doesn't affect Glade.
	The Default Width & Height properties are also new.
	
	* glade/gbwidget.c (gb_widget_redisplay_window): 
	* glade/editor.c (editor_on_key_press_event): added accelerator key,
	Ctrl-R,	and popup menu command, "Redisplay", to redisplay a window.
	This resets the window's size so hopefully the window now appears at
	the exact size it will be in the final interface.
	
	* glade/gnome/gnomemessagebox.c: XML change - changed "type" property
	to "message_box_type" since it clashed with the standard GtkWindow
	"type" property which I've added.

	* glade/gbwidgets/gbtoolbar.c: 
	* glade/gbwidgets/gbhbox.c: 
	* glade/gbwidgets/gbvbox.c: 
	* glade/gbwidgets/gbpacker.c: 
	* glade/gbwidgets/gbtable.c: 
	* glade/gbwidget.[hc]: 
	* glade/property.[hc]: Moved all code to do with creating child
	packing properties to the individual GbWidgets. Added 2 new functions
	to the GbWidget struct - gb_widget_create_child_properties() and
	gb_widget_get_child_properties(). (We already had
	gb_widget_set_child_properties.)

	* glade/gbwidgets/gbvpaned.c: 
	* glade/gbwidgets/gbhpaned.c: added Position property, and Shrink
	and Resize child packing properties.

	* glade/gbwidgets/*.c: got rid of gb_widget_init_struct2() calls,
	since it's not really better than gb_widget_init_struct() and we were
	left with some calls to one and some to the other making it confusing.

	* glade/utils.c (glade_util_create_pixmap_using_colormap): unref the
	gdkpixmap & mask since the GtkPixmap adds refcounts to them.

	* glade/gbwidget.c (gb_widget_input_child_label): when creating a label
	for a menuitem call set_accel_widget() so any accelerator appears.

1999-07-22  Herbert Valerio Riedel  <<EMAIL>>

	* glade.spec.in: changed configure options in order to 
	build on all alphas

Sat Jul  3 20:08:10 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source_os2.h (source_write_os2_files): 
	* glade/source_os2.c (source_write_os2_files): Add parameters.

1999-06-29  Nat Friedman  <<EMAIL>>

	* glade.spec: Removed this autogenerated file.

1999-06-29  Jose Mercado  <<EMAIL>>

	* glade.spec.in (Source): Changed glade-0.4.1.tar.gz to
 	glade-%{ver}.tar.gz.

	Changed %{prefix}/share/apps/Development/glade.desktop to
 	%{prefix}/share/gnome/apps/Development/glade.desktop.

1999-06-28  Damon Chaplin  <<EMAIL>>

	* Released patch to Glade 0.5.0

1999-06-28  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_gnome_makefile_am_pixmaps_targets): 
	(source_write_gtk_makefile_am_pixmaps_targets): used $(srcdir) when
	installing the app's pixmaps, just like Nuno's fix for Glade below.

	* glade/gbwidgets/gbentry.c (gb_entry_create_properties): renamed
	'Visible' property to 'Text Visible' and updated description.
	(The property is still saved as "text_visible" so the XML is unchanged)

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_create_properties): added
	button relief property (choice of normal, none, half).

	* glade/gbwidgets/gbmenuitem.c (gb_menu_item_write_source): 
	(gb_menu_item_write_accel_source): 
	* glade/gbwidgets/gbradiomenuitem.c (gb_radio_menu_item_write_source): 
	* glade/gbwidgets/gbcheckmenuitem.c (gb_check_menu_item_write_source): 
	* glade/gb.h: setup accelerator signals for underlined keys.

	* glade/gbwidget.c (gb_widget_write_standard_source): set separator
	menuitems insensitive so they are skipped when moving up/down with the
	cursor keys.

	* glade/glade_clipboard.c (glade_clipboard_paste): added checks to
	make sure we don't try to paste things where they shouldn't go.
	Shows popup dialogs to indicate why a Paste command was not done.
	Integrated with project window so you can select windows there and
	copy/paste them (though we should eventually let GladeProject control
	widget selection and make it emit signals which views can act upon).
	
	* glade/gbwidgets/gbhbox.c (gb_box_set_size): added call to
	tree_add_widget() when new buttons are added to button boxes.

	* glade/gbwidgets/gbnotebook.c (gb_notebook_insert_next): added calls
	to tree_add_widget() to add new tab labels to the tree.
	Tidied up the code which updated the number of pages in the property
	editor after adding/removing pages.

	* glade/gnome/gnomedock.c (gb_gnome_dock_add_band): added call to
	tree_add_widget() to add new dock items to the tree.

	* glade/gnome/gnomeapp.c (gb_gnome_app_new): set the GbStockIcon index
	properly for the toolbar icons. We don't need to add 1 to it any more,
	since 'None' was added which is equivalent to no setting.
	Removed the GnomeApp's accel_group from the window, since we don't use
	it and it interferes with our accelerators.

	* glade/gbwidgets/gbtoolbar.c: updated GtkToolbarChild struct when we
	change the icon.

	* configure.in: 
	* Makefile.am: install Glade logo so it appears in GnomeAbout dialog.

	* glade/glade_gnome.c (glade_gnome_write_menu_item_source): output
	empty GnomeUIInfo struct for stock subtree items with no child menu.

	* glade/gnome/gnomeless.c (gb_gnome_less_write_source): called
	gb_widget_write_standard_source().

	* glade/gnome/gnomehref.c (gb_gnome_href_write_source): used
	source_make_string() to output the URL.

	* glade/gnome/gnomemessagebox.c: added properties from GnomeDialog
	and GtkWindow, and added the write_source() function.

1999-06-24  Nuno Ferreira  <<EMAIL>>

	* Makefile.am (install-data-local): Install files from $(srcdir).

	* glade/data/gtk/Makefile.am (install-data-local): Install
	autogen.sh from $(srcdir).

1999-06-20  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.5.0

1999-06-20  Damon Chaplin  <<EMAIL>>
	
	* glade/glade_project_window.c: functions to save the XML and write
	the source now show the Project Options dialog if all the needed
	options haven't been set, instead of showing file selection dialogs.
	The project options dialog will fill in default values, so simply
	clicking 'OK' will save the project or build the source code.

	* glade/glade_project_options.[hc]: rewritten to make it easier to
	setup projects.

	* several other fixes and Gnome additions.

1999-06-17  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbcalendar.c: XML change - changed property names to
	lower case.

1999-06-13  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_options.[hc]: added option to save translatable
	strings, so interfaces loaded by libglade can be translated.
	I'm going to rearrange the project options a bit so that everything
	can be set relative to the project directory.

	* glade/gbwidgets/gbbutton.c: added support for buttons in GnomeDialog
	and GnomeMessageBox - these can be stock buttons or a stock icon
	and a label.

	* glade/gbwidgets/gbmenubar.c (gb_menu_bar_write_source): 
	* glade/gbwidgets/gbmenu.c (gb_menu_write_source): added support for
	using gnome_app_fill_menu() to fill a menu with using GnomeUIInfo
	data. We need this for popup menus and for menubars which aren't in
	a GnomeApp. Unfinished - need to support accelerators better.

	* glade/gbwidgets/gblabel.c: fixed justify property. Added wrap.

1999-06-07  Damon Chaplin  <<EMAIL>>

	* glade/glade_project_window.c (glade_project_window_new): set
	show grid and snap to grid toggle menu items.

	* glade/gbwidgets/gbprogressbar.c: added a few properties -
	Value, Min, Max, Format, XAlign, YAlign.

	* glade/glade_project.c (glade_project_init): fixed typo.

1999-06-06  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_component): initialized data->parent to
	NULL, so we don't get SEGVs.

	* glade/save.[hc]
	* glade/gbwidget.[hc]
	* glade/gbwidgets/*.c
	* glade/gnome/*.c: added support for saving translatable string
	properties, so that we can save a C file containing all the
	translatable strings in the interface. When using libglade, this file
	can be added to an app's POTFILES.in, and thus the interface can be
	translated easily. This is not quite finished - I need to add the file
	as a project option.
	
1999-06-05  Damon Chaplin  <<EMAIL>>

	* Lots of Gnome changes.

	* glade/source.c: major reorganization, to split the code into
	decent functions and handle errors better.

	* glade/glade.[hc]: added GladeError data type which holds an error
	code, a possible errno code for system errors, and an error message.
	Also function to create errors and free them.
	GladeStatusCode didn't really contain enough info to give the user
	decent error messages, and we were duplicating system error codes.
	GladeError is used mainly in source.c for now, but I may update other
	parts of Glade to use it.

	*glade/graphics/*: updated most Gnome icons, and a few GTK+ ones.
	Still not perfect, but adequate.
	
1999-05-11  Damon Chaplin  <<EMAIL>>

	* updated lots of Gnome-related code - loading & saving should work
	  for most things.
	* added const to many function arguments.
	* in source output connected signals last, so any widget names used
	  in the object/data fields will be OK.
	* added support for using the same handler in several places - we only
	  output it once. Though handler prototypes may not match - we leave
	  that problem to the user for now.
	* added "gtk_widget_hide" as a standard signal handler.
	* fixed selection of viewport children.
	* added initial (empty) DocBook manual.
	* changed default fill/expand options for widgets in boxes/tables, and
	  made sure placeholders don't expand and mess up other widgets' sizes.
	* connected to menubar "size_request" to ensure it's a reasonable size.
	
	* glade/load.c: 
	* glade/property.[hc]: stopped using GtkPreview to display style
	colors.	We weren't using it properly, which led to problems on some
	systems. To use it properly we have to set the colormap of the entire
	property editor window to the GtkPreview's desired colormap, which can
	mean lots of colormap swapping on low-color displays. So instead we
	just use the closest color we can. (The colorsel dialog may still use
	its own colormap but that's not so bad as it is a temporary dialog.)
	Also changed it to use GdkColors internally so we can save colors as
	16-bit values in future, i.e. using the format "RGB:rrrr/gggg/bbbb".

Mon May 10 11:54:33 1999  <EMAIL>  (Erik Walthinsen)

	* removed glade/glade.desktop, made sure the top-level one is up
	to date.

Thu May  6 13:47:23 1999  <EMAIL>  (Erik Walthinsen)

	* put the glade.desktop file back in place (where'd it go??)

Wed May  5 17:08:00 1999  <EMAIL>  (Erik Walthinsen)

	* glade.spec.in: added line for new m4 files

1999-05-05  Jacob Berkman  <<EMAIL>>

	* glade/Makefile.am (Developmentdir): install the .desktop in
	in the correct directory

Sun May  2 14:04:45 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/utils.c: Add <sys/types.h>

1999-04-28  Martijn van Beers  <<EMAIL>>

	* glade/gbwidget.[ch]: Added a gb_widget_set_child_props function
	  pointer to GbWidget, and have set_special_child_properties use it.
	* tools/mkskel.pl: added the gb_widget_set_child_props here
	* glade/gbwidgets/gbtable.c:
	* glade/gbwidgets/gb[hv]box.c:
	* glade/gbwidgets/gbtoolbar.c:
	* glade/gbwidgets/gbpacker.c: Copied code from
	  set_special_child_properties to here.
	* utils.[ch]: added a glade_widget_to_gb function that gets the
	  gbwidget for a widget. Gets used where appropriate.

1999-04-27  Martijn van Beers  <<EMAIL>>

	* acconfig.h:
	* configure.in:
	* glade/Makefile.am: Added configure flag to decide what kind
	  of palette layout you want
	* gbwidget.c(gb_widget_init): Put adding items to palette here
	  (gb_widget_init_widget_lib): New function for adding the widgets
	  in one lib
	* palette.[ch]: Changed to have widgets added from gbwidget.c
	* glade_gtk11lib.[ch]: renamed/split  to glade_gtk12lib.[ch] and
	  glade_gnomelib.[ch]

1999-04-25  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbvbuttonbox.c (on_vbbox_dialog_ok): 
	* glade/gbwidgets/gbhbuttonbox.c (on_hbbox_dialog_ok): 
	* glade/gbwidget.c (gb_widget_update_box_size): set GTK_CAN_DEFAULT
	flag for new buttons in a button box.

	* glade/glade_project_window.c: Updated Gnome code to make it conform
	more to the Gnome style guide - gnome-libs/devel-docs/suggestions.txt,
	by using stock menu items where possible, adding underlined accelerator
	keys to all menu items, and adding a 'Settings' menu for the grid and
	snap settings. Let me know of anything which doesn't conform.
	Also set up the status bar so it shows tips for each menuitem, though
	unfortunately these are often hidden by the menus.
	
	* configure.in: 
	* glade/Makefile.am: added BUILD_GNOME conditional to build gnome
	GbWidgets only if Gnome is installed and --disable-gnome isn't used.

	* glade/gnome/*: added new directory to support Gnome widgets.
	* glade/glade_gtk11lib.c: added Gnome widgets to a new section.

	* glade/glade_menu_editor.[hc]: added support for stock Gnome menuitems
	and icons. Got rid of handler data and object entries. These would be
	quite awkward to use, and I don't think are essential. Gnome doesn't
	support setting the object, and using the data field would be quite
	difficult as well.
	Made more self-contained - all the caller has to do now is pass it
	the project & menu to edit. It updates the menu itself.
	Added support for underlining the labels, for keyboard shortcuts.
	
	* glade/gbwidget.c: 
	* glade/gbwidgets/*.c: got rid of 'child' arguments. I don't think
	these would have worked, and having separate functions for handling
	child properties is cleaner anyway.

	* glade/*.[hc]:
	* glade/gbwidgets/*.c: Got rid of '#include <gtk/gtk.h>' from most
	files, and included only those headers which are needed by each file.
	This speeds up compilation quite a bit.
	
	* glade/gladeconfig.h: Got rid of '#include <gnome.h>' and replaced it
	with files needed for i18n, to speed up compilation for those files
	which don't need all of the Gnome headers included.

1999-04-25  Martijn van Beers  <<EMAIL>>

	* glade/gbwidget.[ch]:
	* glade/gbwidgets/*.c: finally reverted my 03-11 commit

Thu Apr 24 00:22:15 1999  <EMAIL> (Erik Walthinsen)

	* new spec.in file, changes to Makefile.am and configure.in
	* glade.desktop file, appropriate changes to glade/Makefile.am

Mon Apr  5 11:13:46 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidgets/gbtext.c (gb_text_write_source): Remove some
	debug printouts.

Thu Apr  1 15:51:20 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source.c (source_write_makefile): Open files in TEXT mode.
	"Simplify" the parameter passing of source_write_makefile(),,

Wed Mar 31 20:54:59 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidgets/gbtoolbar.c (gb_toolbar_init): 
	* glade/gbwidgets/gbtogglebutton.c (gb_toggle_button_init): 
	* glade/gbwidgets/gbtext.c (gb_text_init): 
	* glade/gbwidgets/gbtable.c (gb_table_init): 
	* glade/gbwidgets/gbstatusbar.c (gb_statusbar_init): 
	* glade/gbwidgets/gbspinbutton.c (gb_spin_button_init): 
	* glade/gbwidgets/gbscrolledwindow.c (gb_scrolled_window_init): 
	* glade/gbwidgets/gbradiomenuitem.c (gb_radio_menu_item_init): 
	* glade/gbwidgets/gbradiobutton.c (gb_radio_button_init): 
	* glade/gbwidgets/gbprogressbar.c (gb_progress_bar_init): 
	* glade/gbwidgets/gbpreview.c (gb_preview_init): 
	* glade/gbwidgets/gbpixmap.c (gb_pixmap_init): 
	* glade/gbwidgets/gbpacker.c (gb_packer_init): 
	* glade/gbwidgets/gboptionmenu.c (gb_option_menu_init): 
	* glade/gbwidgets/gbnotebook.c (gb_notebook_init): 
	* glade/gbwidgets/gbmenuitem.c (gb_menu_item_init): 
	* glade/gbwidgets/gbmenubar.c (gb_menu_bar_init): 
	Use gb_widget_init_struct2().
	
1999-03-28  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.4.1
	
1999-03-28  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_write_standard_source): updated code to
	set the widget size & position.
	(set_position_properties): set GB_X_SET, GB_WIDTH_SET etc. when
	loading widgets.

	* glade/property.c (property_set_sensitive_full): set the toggle
	buttons active according to if the value is sensitive.

	* glade/glade_project.[hc]: added Ada95 language, and separate
	functions to output the source code for each language.

	* glade/utils.c: changed glade_util_show_dialog() to
	glade_util_create_dialog_with_buttons() and updated slightly.

	* glade/glade.[hc]: added glade_current_directory variable which
	contains the current directory when Glade was started. We need to
	remember it since that we use chdir when writing the source.

	* glade/gbwidgets/gbprogressbar.c (gb_progress_bar_write_source): a
	couple of source output fixes.

	* glade/gbwidgets/gbvruler.c (gb_vruler_write_source): 
	* glade/gbwidgets/gbhruler.c (gb_hruler_write_source): minor fix to
	source output to use 'GTK_RULER ()'.

	* glade/Makefile.am: added source_os2.[hc]

	* glade/glade_project_window.c: added confirmation dialog when creating
	a new project, so the user doesn't accidentally lose the current one.
	Added support for running external source code generators.

	* glade/glade_project_options.c (glade_project_options_ok): ordered
	calls to set project options so that defaults are used if some options
	are not set.

	* glade/gbwidget.[hc]: added GB_X_SET and GB_Y_SET to GbWidgetData
	flags, similar to GB_WIDTH_SET and GB_HEIGHT_SET.
	Updated get/set_position_properties.
	Fixed tooltips bug which meant all tooltips were lost.
	Fixed problem pasting toolbar buttons into other containers.
	
	* glade/editor.c (add_widget_to_fixed_finish): set GB_X_SET and
	GB_Y_SET when adding new widgets.

	* glade/property.c: added property_add_optional_int_range() to use for
	the x, y, width and height properties, and callbacks used when the
	properties are turned on/off.
	Added property page for Ada95 options (though there aren't any yet).
	
	* glade/gbwidgets/gbcolorselectiondialog.c (gb_color_selection_dialog_set_properties): 
	* glade/gbwidgets/gbdialog.c (gb_dialog_set_properties): 
	* glade/gbwidgets/gbfileselection.c (gb_file_selection_set_properties): 
	* glade/gbwidgets/gbfontselectiondialog.c (gb_font_selection_dialog_set_properties): 
	* glade/gbwidgets/gbwindow.c (gb_window_set_properties): update the
	position properties after the window position property is changed.
	
	* glade/gbwidgets/gbcalendar.c: use '::' in property names instead
	of ':'.

	* examples/editor/gladesrc.c (create_pixmap): fixed bug which caused
	infinite loops when the app was installed.

1999-03-21  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.4.0
	
1999-03-20  Damon Chaplin  <<EMAIL>>

	* glade/source.c (source_write_main_c_preamble): fixed searching of
	pixmap directories.

Mon Mar 15 16:03:51 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidgets/gbwindow.c (gb_window_init): 
	* glade/gbwidgets/gbvseparator.c (gb_vseparator_init): 
	* glade/gbwidgets/gbvscrollbar.c (gb_vscrollbar_init): 
	* glade/gbwidgets/gbvscale.c (gb_vscale_init): 
	* glade/gbwidgets/gbvruler.c (gb_vruler_init): 
	* glade/gbwidgets/gbvpaned.c (gb_vpaned_init): 
	* glade/gbwidgets/gbviewport.c (gb_viewport_init): 
	* glade/gbwidgets/gbvbuttonbox.c (gb_vbutton_box_init): 
	* glade/gbwidgets/gbvbox.c (gb_vbox_init): 
	* glade/gbwidgets/gbtreeitem.c (gb_tree_item_init): 
	* glade/gbwidgets/gbtree.c (gb_tree_init): 
	Use gb_widget_init_struct2().

1999-03-14  Damon Chaplin  <<EMAIL>>

	* glade/graphics/calendar.xpm: 
	* glade/gbwidgets/gbcalendar.c: New files for GtkCalendar.

	* glade/gbwidget.c (set_position_properties): 
	* glade/editor.c (add_widget_to_fixed_finish): for children of fixed
	containers we were using the allocation to keep the width & height.
	But this doesn't work since it is only calculated when the widget is
	shown. So we use wdata->w & wdata->h instead.

	* glade/load.c: use a few macros for better performance.

	* glade/utils.c (glade_util_strstr): fixed for loops which used
	strlen() and so were very slow (also a few other places as well).

	* glade/glade_project.c (glade_project_real_ensure_widgets_named): 
	forgot to pass project to gb_widget_children_foreach().

	* glade/glade_project_window.c (glade_project_window_new): changed
	all uses of _ to N_ in the GnomeUIInfo structs.

	* glade/main.c (parse_command_line): Don't do this for Gnome, since
	they use popt.

	* glade/glade_project_options.c (glade_project_options_ok): don't
	destroy the filesel here since it is destroyed in
	glade_project_options_destroy().

	* glade/glade_project_options.[hc]: added xml_filename_entry, for
	setting the XML filename.

	* glade/utils.[hc]: add glade_util_copy_string() which is similar to
	g_strdup() but returns NULL if the string is empty.

	* glade/glade_project.c: use glade_util_copy_string() when loading
	project options, so empty directories are set to NULL.

	* glade/glade_project_window.c: handle directories set to NULL or "",
	just in case. Also include <gnome.h> when USE_GNOME set.

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	* glade/gbwidget.c (gb_widget_add_child): 
	* glade/gbwidget.h: we now use the real parent when replacing a widget,
	otherwise widget creation functions may not work. We store a pointer
	to the widget being replaced instead of the original boolean.

	* glade/property.c (property_redirect_key_press): only allow short
	one-line labels to be edited via type-over, since GtkText can crash if
	it hasn't already been realized etc.

	* glade/gbwidget.c (gb_widget_output_child_icon): if the widget is not
	a toolbar button we clear the icon property and set in insensitive.

1999-03-11  Martijn van Beers  <<EMAIL>>

	* gbwidget.h: added an extra param to gb_widget_*_properties
	* gbwidget.c: add the extra param to gb_widget_*properties
	* gbwidgets/*.c: add the new param here too
	* gbwidgets/gbctree.c: use gbclist's _add_child function

Wed Mar 10 16:46:25 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidget.c (gb_widget_init_struct2): Add this new function.
	* glade/gbwidgets/gbprogressbar.c (gb_progress_bar_init): Use it.
	
Tue Mar  9 20:22:47 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidget.c (gb_widget_add_child): Add return
	when parent == NULL otherwise glade crash !

1999-03-09  Martijn van Beers  <<EMAIL>>

	* gbwidget.h: Added gb_widget_add_child function to GbWidget
	* gbwidget.c: Make use of new GbWidget function
	put gb_widget_insert_toolbar_child declaration in gbwidget.h.

	* gbwidgets/gbbutton.c:
	* gbwidgets/gbclist.c:
	* gbwidgets/cbmenuitem.c:
	* gbwidgets/gbnotebook.c:
	* gbwidgets/gbtoolbar.c: Implement new GbWidget function

Sun Feb 28 11:58:35 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source_os2.h: 
	* glade/source_os2.c: New files.

	* glade/source.c: Genertate makefiles for OS/2 version.
	Made function create_file_if_not_exist() global.

Fri Feb 26 17:05:08 1999  <EMAIL>  (Asbjorn Pettersen)

	* configure.in: Add check for os2.h (OS/2 version)

Wed Feb 24 16:20:09 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	Use load_init_before_read().
	* glade/load.c (load_init_before_read): Added this function.

	* glade/glade_project_window.c (glade_project_window_new): 
	* glade/glade_menu_editor.c (glade_menu_editor_init): 
	* glade/glade_keys_dialog.c (glade_keys_dialog_init): 
	* glade/glade_clipboard.c (glade_clipboard_init): 
        Use gb_box_clist_auto_policy() function.
	* glade/property.c: 
	* glade/gbwidget.c (gb_box_clist_auto_policy): Moved this function
	from property.c to gbwidget.c.
	

Tue Feb 23 16:28:31 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/property.c (gb_box_clist_auto_policy): Add this function.

Mon Feb 22 08:37:04 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/glade_clipboard.c: 
	* glade/load.c: Added:	
	void load_show_error_list(GList *p);
	void load_free_error_list(GList *p);

Sun Feb 21 11:51:07 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/glade_clipboard.c (glade_clipboard_paste): 
	* glade/load.c (real_load_project_file): 
	use gb_init_load_properties() and gb_free_load_properties().

	* glade/gbwidget.c (gb_add_properties): added this function
	to simplify the handling of the properties.
	Also added:
	void        gb_free_load_properties (GbLoadPropControl *p);
	void        gb_init_load_properties (GbLoadPropControl *p);
	void gb_realloc_properties_if_needed (GbLoadPropControl *p);
	

Sat Feb 20 18:23:49 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/load.c: 
	* glade/gbwidget.c:
	* glade/glade_clipboard.c: 
	* glade/gbwidget.h (struct _GbLoadPropControl): add this struct.

Fri Feb 19 21:58:05 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/glade_project.c (glade_project_open): Fix error so
	source code can be written to "source_directory".

1999-02-18  Damon Chaplin  <<EMAIL>>

	* Major changes to the project-related code and the widget tree.
	This needed changes to most of the main source files.
	
	* glade_project.[hc]: 
	* glade/projconfr.[hc]: 
	* glade/projconf.[hc]: removed.

	* glade/glade_project_window.[hc]: 
	* glade/glade_project_view.[hc]: 
	* glade/glade_project_options.[hc]: 
	* glade/glade_project.[hc]: new files containing a more OO
	implementation of projects. Eventually we will be able to support
	multiple open projects.

	* glade/tree.[hc]: major changes to make it easier to use and more
	reliable. Also changed the way widgets are added to the tree, getting
	rid of gb_widget_add_callback() and the children field of
	GbWidgetNewData, which was previously used for several of the dialogs
	in gbwidgets/.
	
	* glade/graphics/*.xpm: updated all pixmaps so that transparency is
	only used for the edges of the widgets, not inside the widgets. This
	makes the widget tree look better. Also took transparency out of the
	placeholder.xpm since it isn't needed.

	* glade/glade_clipboard.[hc]: New files to support Cut & Paste, though
	this isn't quite finished yet.

	* glade/property.c
	* glade/gbwidget.c: Added support for a few C and C++ source code
	options for each widget. (But they are not used yet.)

	* glade/editor.c
	* glade_property.c: Added experimental support for typing in widget
	labels while the mouse is over the label/button in the interface.
	Unfortunately GtkText SEGVs if the label is a few lines long, so we
	may not be able to use this.
	
	* glade/utils.[hc]: Moved DayNames, MonthNames and
	find_start_of_tag_name() from save.[hc] to here and renamed them.
	Also added/moved more utility functions.

	* glade/gbwidgets/gbscrolledwindow.c: added support for
	GTK_POLICY_NEVER (for GTK 1.1).

	* glade/gbwidgets/gbfontselectiondialog.c: added get_child() function
	so loading works OK.

	* glade/project.c: added icons for project options and writing source,
	& Ctrl-W accelerator to write source.

	* glade/editor.c (editor_on_button_press): 
	(editor_on_motion_notify): ignore events on clist column resize
	windows, so columns can still be resized.
	(editor_on_button_release): remove the grab on the widget just
	moved/resized, i.e. the one we added the grab on.
	(editor_on_key_press_event): use Ctrl-L to refresh the component.
	(add_mouse_signals_recursive): stop 'enter_notify' and 'leave_notify'
	signals getting to widgets while we move/resize them, to cut down
	on flickering (a bit).
	(do_drag_action): we don't need to show the position properties since
	they are now shown in on_size_allocate(). Also, set wdata->x etc.
	before calling gtk functions since they trigger callbacks.

	* glade/gbwidgets/gbdialog.c (gb_dialog_new): better initial size.

	* glade/gbwidgets/gbwindow.c (gb_window_new): set initial size to
	400x300.

	* glade/editor.c (add_widget_to_fixed_finish): use a reasonable
	size for clists/ctrees and notebooks in fixed containers.

	* glade/glade_menu_editor.c: only update the radio group combo when
	an item's properties are shown in the right half of the dialog, or
	when the menu being edited is first set.
	The new GTK 1.1 combo changes the entry text as you drag the mouse
	over the popup menu. This was causing the menu editor to try to
	recreate the popup menu (while it was displayed), which caused a SEGV.

	* glade/gbwidget.c (gb_widget_write_source): For empty notebook
	pages we create a dummy vbox, so it still runs OK. For empty tabs,
	we increment the 'last_child' value so the order isn't messed up.
	We also do this for empty clist/ctree titles.
	(gb_widget_save):
	(gb_widget_load): For placeholders we still load & save the
	'child_name' property.

Sun Feb 14 18:22:33 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/source.c (create_file_if_not_exist): add this function.
	Open all text files with "wt" instead of "w".

Sat Feb 13 11:26:15 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidget.c (gb_widget_save): call save_newline with
	pointer to  GbBuffControl.

	* glade/save.h: new input structure.
 	    save_newline (GbBuffControl *data);

	* glade/save.c (save_buffer_add): use GbBuffControl 

	* glade/project.c (gb_menu_seperator): Adding this function.

Sun Feb  7 20:05:07 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/projconf.c: add #include "project.h"

	* glade/project.c: 
	* glade/main.c (main): current_project variable is seen from the
 	main function and not hidden inside projconfr.c  .

	* glade/glade.c (glade_init): decoupled the
	global variable current_project.

	* glade/projconfr.c (init_current_project): decoupled the
	global variable current_project.

Sat Feb  6 18:03:10 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/load.c (load_entity): added 2 new functions:

	gboolean read_entity(GbWidgetSetArgData * data, gchar *entity);

	void load_entity (GbWidgetSetArgData * data);

	
	load_buffer_add_char() use GbBuffControl instead of GbWidgetSetArgData. 

Thu Feb  4 12:44:21 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidgets/gboptionmenu.c: 
	* glade/gbwidgets/gbpixmap.c:
	* glade/gbwidgets/gbradiobutton.c:
	* glade/gbwidgets/gbradiomenuitem.c:
	* glade/save.c: 
	* glade/source.c: 
	* glade/gbwidget.c: 
	* glade/gbwidget.h (struct _GbWidgetGetArgData): use
	GbBuffControl . 

	* glade/load.c (real_load_project_file): Use the new struct
	GbBuffControl and function gb_init_buffer_struct().

	* glade/gbwidget.c (gb_init_buffer_struct): added new function	
	gb_init_buffer_struct (int pos, int space, GbBuffControl * p);

	Changed all buffer to buffer.ptr, buffer_pos to buffer.pos,,,

Thu Feb  4 12:39:18 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/gbwidget.h (struct _GbBuffControl): add this struct.
        used in struct _GbWidgetSetArgData. 

Mon Feb  1 16:58:04 1999  <EMAIL>  (Asbjorn Pettersen)

	* glade/property.h: GbEventMaskSymbols and GbEventMaskValues
	must be declared external.

1999-01-25  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_add_child): 
	(gb_widget_write_add_child_source): handle CTree column titles
	just like CList column titles.

	* glade/gbwidgets/gbctree.c: updated code to handle column titles.

1999-01-22  Damon Chaplin  <<EMAIL>>

	* glade/glade_menu_editor.c (glade_menu_editor_update_menu): based
	names of submenus on the name of the parent item, rather than
	generating a new name each time (from Elliot Turner).

	* glade/source.c (source_write_component): Put "tooltips" in object's
	datalist, so the developer can access it (from Elliot Turner).

	* glade/main.c: new file to contain main().
	* glade/debug.[ch]: new files for debugging functions/macros.
	* glade/glade.[ch]: moved GladeStatusCode declaration to the header,
	and added functions to be called by gIDE.
	
	* glade/*.h: Added #ifdef __cplusplus tests to several headers.

	* glade/*.[ch]: Changed names of several structs/enums:
	GbProjectStruc -> GladeProject.
	GbStatusCode -> GladeStatusCode.
	glade_palette_section -> GladePaletteSectionData.
	glade_widget -> GladeWidgetInitData.
	Also renamed the status codes to begin with 'GLADE_STATUS_' rather
	than just 'GB_'.
	
	* glade/glade_palette.[hc]: Added 'select_item' and 'unselect_item'
	signals to the palette.
	* glade/palette.c (on_palette_select_item): creates toplevel items
	when they are selected on the palette.
	* glade/glade_gtk10lib.c: 
	* glade/glade_gtk11lib.c: add toplevel items to the palettes.
	* glade/project.c: removed toplevel items from toolbar, and added
	standard 'New', 'Open' & 'Save' buttons instead. Also added status bar,
	with messages when files opened/saved or source is written.

	* glade/projconf.c: removed the Glade support functions get_widget()
	and set_notebook_tab().

	* glade/graphics/text.xpm: Tweaked a bit to look nicer.

1999-01-21  Damon Chaplin  <<EMAIL>>

	* glade/project.c: added support for rc files. Removed code to set
	tooltips colors, since it doesn't work with GTK 1.1. Tidied up a bit.

1999-01-19  Jeff Garzik  <<EMAIL>>

	* glade/source.c:
	Cleaned up autoconf/automake output source a bit.  CFLAGS and LIBS 
	are now computed in Makefile.am, config.h is conditionally
	included in every file, and some other minor cleanups.
	autoheader is now used to generated config.h.in as well.

	* glade/editor.c:
	Declare glade_palette_reset_selection to eliminate warnings.

	* glade/gbwidget.c, glade/projconf.c, glade/projconfr.c,
	  glade/project.c, glade/project.h, glade/save.c,
	  glade/source.c:
	Add intl strings property.

	* glade/gtkfontsel.c, glade/projconf.c:
	Marked more strings for translation.

	* glade/project.c:
	Include string.h to eliminate warnings.
	Small fixes for USE_GNOME support.

	* po/POTFILES.in, po/cat-id-tbl.c, po/de.po, po/es.po,
	  po/fr.po, po/glade.pot, po/pt_BR.po:
	Added glade/glade_palette.c to POTFILES.in.  That and new
	translated strings from the above changes triggered *.po
	updates.

1999-01-19  Damon Chaplin  <<EMAIL>>

	* glade/palette.[ch]: moved most of the code to glade_palette.[ch]
	which also resulted in minor changes in a few other files.

	* glade/glade_gtk11lib.c: 
	* glade/glade_gtk10lib.c: added copyright and tidied up. Also added
	alternative layout for GTK 1.1 so we can decide between them.
	(Note that using windows & dialogs from the palette causes a crash
	at the moment.)

	* glade/glade_palette.[ch]: renamed gladepalette.[ch] to fit in with
	the other widgets, and changed code quite a bit.

1999-01-18  Damon Chaplin  <<EMAIL>>

	* glade/graphics/handlebox.xpm: designed a nicer icon. Now I'm pretty
	happy with all the icons, except maybe the font selection ones.

1999-01-16  Damon Chaplin  <<EMAIL>>

	* doc/developer.txt: added description of how to compile and set up
	Glade for debugging.

	* glade/gladeconfig.h: changed the MSG macros so that they can be
	turned on/off by setting the GLADE_DEBUG env var. Set it to 'messages'
	to see all the debugging output (you must compile with --enable-debug
	as well). If you want Glade to abort on warnings as well, use
	export GLADE_DEBUG='messages:warnings' or similar.

1999-01-15  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtoolbar.c: added dialog for creating toolbars,
	a 'Size' property like the boxes, and menu commands to add items etc.

	I'm afraid the toolbar needed a lot of special code! ...
	
	* glade/gbwidget.c (gb_widget_new_toolbar_button): 
	(gb_widget_insert_toolbar_child): 
	(gb_widget_is_toolbar_button): 
	(gb_widget_get_toolbar_button_widgets): 
	(gb_widget_get_new_toolbar_group): 
	(gb_widget_set_new_toolbar_group): 
	(gb_widget_write_toolbar_button_source): added several utility
	functions for toolbars.

	(gb_widget_add_child): handle loading of toolbar children.
	(gb_widget_replace_child): handle toolbar children. Also, don't copy
	the child name to a toolbar button which is replaced, since it may
	not be a toolbar button.

	(get_special_child_properties): 
	(set_special_child_properties): handle toolbar 'New Group' property.

	(gb_widget_input_child_label): 
	(gb_widget_output_child_label): handle toolbar button labels.

	(gb_widget_output_child_icon): 
	(gb_widget_input_child_icon): handle toolbar button icons.

	(gb_widget_create_toolbar_button_popup_menu):
	(gb_widget_convert_toolbar_button): create menu commands to turn
	toolbar buttons into ordinary widgets in the toolbar.

	(gb_widget_write_standard_source): don't unset CAN_FOCUS for toolbar
	children since that is the default. Also don't set the tooltip for
	toolbar	children, since that is set when they are created.
	(gb_widget_write_add_child_source): handle toolbar children which are
	standard widgets. Toolbar buttons are added to the toolbar when
	they are created instead.

	* glade/gbwidget.c (gb_widget_replace_child): for menubars and
	toolbars in a box, we set expand to FALSE by default as that is what
	is wanted most of the time.

	* glade/gbwidgets/gbtogglebutton.c: 
	* glade/gbwidgets/gbradiobutton.c: 
	* glade/gbwidgets/gbbutton.c: added support for toolbar buttons which
	are a special case - they have a child icon & label.

	* glade/gbwidget.c (get_standard_properties): don't allow widgets with
	no windows to have tooltips, since they won't work anyway. Also
	for toolbar children we use the toolbar's own tooltips.

1999-01-13  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbentry.c: use gtk_entry_get_text () instead of the
	GtkEntry text field which is now GtkWChar rather than gchar.

	* glade/project.c: 
	* glade/project.h: added support for passing debugging options into
	Glade via environment variables. The only option at present is
	'warnings' which will cause an abort() when a WARNING message is issued
	so you can use gdb to see exactly what happened and where.
	(This only works with GTK 1.1.x)

	* glade/property.c (create_style_property_page): 
	(create_style_page): 
	(property_add_adjustment): 
	* glade/palette.c (palette_create): 
	Changed 'gettext' to '_' so that we always specify the domain,
	which I think we need to do if Glade becomes a library.
	(Otherwise we implicitly use the current domain which may be set by
	the main application, e.g. gIDE, so translations won't be found.)
	
1999-01-11  Damon Chaplin  <<EMAIL>>

	* glade/gbwidgets/gbtoolbar.c: some code for editing toolbars, which
	doesn't work yet. I think I am going to create a dummy GtkToolbarItem
	object, since the GtkToolbar isn't very object-oriented.

1999-01-07  Damon Chaplin  <<EMAIL>>

	* glade/load.c (load_project_file): set TZ env var to GMT while
	loading a project, so that we can easily parse RFC1123 dates and
	convert them to time_t using mktime().

	* glade/gbwidget.c (set_standard_properties): made sure CAN_FOCUS is
	always set/unset when XML file is loaded.

1998-12-22  Damon Chaplin  <<EMAIL>>

	* glade/projconfr.c: changed all gtk_signal_connect_object() to
	gtk_signal_connect() since I think that was what was intended.
	It was causing the 'OK' and 'Cancel' buttons on the Filenames page to
	disappear before.

1998-12-14  Elliot Turner  <<EMAIL>>

      * glade/gbwidget.c:
      * glade/projconf.c:
      * glade/projconf.h:
      * glade/projconfr.c:
      * glade/projconfr.h:
      * glade/project.c:
      * glade/project.h:
      * glade/save.c: added support for project directory configuration
        option, with loading/saving in the project XML file.

      * glade/glade_menu_editor.c:
      * glade/glade_menu_editor.h: added signal handler data and object
      configuration options to the glade menu editor.

      * glade/editor.c:
      * glade/editor.h:
      * glade/gbwidget.c:
      * glade/gbwidget.h:
      * glade/project.c:
      * glade/source.c: added "convert to/from embedded component"
        functionality in editor, along with changes to allow for subcomponent
        creation in the  source code generation subsystem.

1998-12-03  Damon Chaplin  <<EMAIL>>

	* configure.in: Changed version to 0.4.0
	* configure.in: added AC_LINK_FILES back since we are now using
	Gnome's intl directory which is gettext 0.10.32. Though I think it
	is going to be removed from Gnome and users must install gettext
	themselves.

	* glade/project.c: 
	* glade/save.c: 
	* glade/source.c: include <sys/types.h> before <sys/stat.h> for OS/2

1998-12-02  Damon Chaplin  <<EMAIL>>

	* glade/source.c: 
	* glade/source.h (source_make_string): added translatable parameter,
	so that we can output gettext macros if desired.
	* glade/*.c glade/gbwidgets/*.c: updated all calls to
	source_make_string().
	* glade/gbwidget.h: added use_gettext boolean option to
	GbWidgetWriteSourceData.
	
1998-12-02  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.3.9
	
1998-11-30  Damon Chaplin  <<EMAIL>>

	* glade/property.c (create_widget_property_page): for GTK 1.1.[45] we
	use GTK_SCROLLED_WINDOW (page)->child, for the latest CVS version
	(the one with GTK_HAVE_FEATURES_1_1_5 defined) we use
	GTK_BIN (page)->child.

	* glade/gbwidget.c (gb_widget_replace_child): handle scrolled window
	children in GTK 1.1.5.

1999-01-18  Martijn van Beers  <<EMAIL>>

	* configure.in: Changes to support checking whether we have gtk-1.1
	in Makefile.am
	* Makefile.am: added new files
	* glade/gbwidgetarray.[ch]: removed in favor of glade_gtk1?lib.[ch]
	* glade/glade.h:
	* glade/glade_gtk10lib.[ch]:
	* glade/glade_gtk11lib.[ch]: new files to support the new palette
	* glade/gladepalette.[ch]: the new palette code
	* glade/gb.h:
	* glade/gbwidget.c:
	* glade/palette.c:
	* po/POTFILES.in: changes to support the new palette
	
1998-12-01  Damon Chaplin  <<EMAIL>>

	* glade/gbwidget.c (gb_widget_replace_child): remove '//' comment.
	* glade/tree.c (tree_init): removed '//' comment.

	* glade/gbwidget.c (gb_widget_write_source): use gtk_object_newv to
	create the standard widgets that we get the default properties from,
	since otherwise GTK 1.1.5 complains when they are destroyed.

	* glade/gbwidgets/gbctree.c: 
	* glade/gbwidgets/gbclist.c: use gtk_clist_set_shadow_type() instead of
	the deprecated gtk_clist_set_border().

	* glade/editor.c (add_mouse_signals_recursive): use
	gb_widget_children_foreach() so we also add signals to the clist
	titles,	which means they can be selected in GTK 1.1.x.

	* configure.in: Changed version to 0.3.9

	* glade/property.c (create_widget_property_page): for GTK 1.1.4 we
	use gtk_scrolled_window_add_with_viewport().

	* glade/editor.c: 
	* glade/gbwidget.c: 
	* glade/gbwidgets/gbtable.c: 
	* glade/gbwidgets/gbvbox.c: 
	* glade/gbwidgets/gbhbox.c: remove gtk_container_block/unblock_resize()
	calls for GTK 1.1.4 since it is deprecated.
	
	* glade/gbwidgets/gbscrolledwindow.c: 
	* glade/gbwidgets/gbnotebook.c: 
	* glade/gbwidgets/gbctree.c: 
	* glade/gbwidgets/gbclist.c: 
	* glade/editor.c: changed all the GTK_HAVE_FEATURES_1_1_5 to
	GTK_HAVE_FEATURES_1_1_4 since GTK_HAVE_FEATURES_1_1_5 was added
	after GTK 1.1.5 was released, which messed us up.

1998-11-30  Damon Chaplin  <<EMAIL>>

	* Released Glade 0.3.8
	
1998-11-29  Damon Chaplin  <<EMAIL>>

	* glade/tree.c: updated code for clist to handle 1.1.5 changes.
	* glade/gbwidgets/gbnotebook.c: replaced border property with hborder
	& vborder for GTK 1.1.5.
	* glade/editor.c: 
	* glade/gbwidgets/gbctree.c: 
	* glade/gbwidgets/gbclist.c: used ifdef to remove calls to
	gtk_widget_draw_children() which isn't available in GTK 1.1.5.
	* glade/project.c (on_project_new): added call to init_current_project
	to reset source code directory.
	(on_component_list_select): show the properties of the toplevel
	widget in the component, so a popup menu name can be changed.
	* glade/load.c: fixed bug parsing dates - was using scanf with %i
	which assumes a number starting with 0 is in octal - used %d instead.
	* glade/gbwidget.c: merged in patch to fix bug writing signal handlers.
	* po/es.po: updated.
	* Makefile.am (SUBDIRS): added macros
	* configure.in: Changed version to 0.3.8, output macros/Makefile

1998-11-24  Jeff Garzik  <<EMAIL>>

	* acinclude.m4: new file, adds gettext macros
	* autogen.sh: script to build from CVS, stolen from gnome-libs
	* configure.in: added libtool and macro subdir support.  GNOME
	  macros apparently want libtool support -- aclocal warns about
	  it -- even though all it adds to Glade, apparently, is a
	  slightly slower compile time.
	* glade/.cvsignore: ignore generated files
	* glade/gbwidgets/.cvsignore: ignore generated files
	* po/.cvsignore: ignore generated files

