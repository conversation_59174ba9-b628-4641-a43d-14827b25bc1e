Notes on the Free Translation Project
*************************************

   Free software is going international!  The Free Translation Project
is a way to get maintainers of free software, translators, and users all
together, so that will gradually become able to speak many languages.
A few packages already provide translations for their messages.

   If you found this `ABOUT-NLS' file inside a distribution, you may
assume that the distributed package does use GNU `gettext' internally,
itself available at your nearest GNU archive site.  But you do _not_
need to install GNU `gettext' prior to configuring, installing or using
this package with messages translated.

   Installers will find here some useful hints.  These notes also
explain how users should proceed for getting the programs to use the
available translations.  They tell how people wanting to contribute and
work at translations should contact the appropriate team.

   When reporting bugs in the `intl/' directory or bugs which may be
related to internationalization, you should tell about the version of
`gettext' which is used.  The information can be found in the
`intl/VERSION' file, in internationalized packages.

Quick configuration advice
==========================

   If you want to exploit the full power of internationalization, you
should configure it using

     ./configure --with-included-gettext

to force usage of internationalizing routines provided within this
package, despite the existence of internationalizing capabilities in the
operating system where this package is being installed.  So far, only
the `gettext' implementation in the GNU C library version 2 provides as
many features (such as locale alias, message inheritance, automatic
charset conversion or plural form handling) as the implementation here.
It is also not possible to offer this additional functionality on top
of a `catgets' implementation.  Future versions of GNU `gettext' will
very likely convey even more functionality.  So it might be a good idea
to change to GNU `gettext' as soon as possible.

   So you need _not_ provide this option if you are using GNU libc 2 or
you have installed a recent copy of the GNU gettext package with the
included `libintl'.

INSTALL Matters
===============

   Some packages are "localizable" when properly installed; the
programs they contain can be made to speak your own native language.
Most such packages use GNU `gettext'.  Other packages have their own
ways to internationalization, predating GNU `gettext'.

   By default, this package will be installed to allow translation of
messages.  It will automatically detect whether the system already
provides the GNU `gettext' functions.  If not, the GNU `gettext' own
library will be used.  This library is wholly contained within this
package, usually in the `intl/' subdirectory, so prior installation of
the GNU `gettext' package is _not_ required.  Installers may use
special options at configuration time for changing the default
behaviour.  The commands:

     ./configure --with-included-gettext
     ./configure --disable-nls

will respectively bypass any pre-existing `gettext' to use the
internationalizing routines provided within this package, or else,
_totally_ disable translation of messages.

   When you already have GNU `gettext' installed on your system and run
configure without an option for your new package, `configure' will
probably detect the previously built and installed `libintl.a' file and
will decide to use this.  This might be not what is desirable.  You
should use the more recent version of the GNU `gettext' library.  I.e.
if the file `intl/VERSION' shows that the library which comes with this
package is more recent, you should use

     ./configure --with-included-gettext

to prevent auto-detection.

   The configuration process will not test for the `catgets' function
and therefore it will not be used.  The reason is that even an
emulation of `gettext' on top of `catgets' could not provide all the
extensions of the GNU `gettext' library.

   Internationalized packages have usually many `po/LL.po' files, where
LL gives an ISO 639 two-letter code identifying the language.  Unless
translations have been forbidden at `configure' time by using the
`--disable-nls' switch, all available translations are installed
together with the package.  However, the environment variable `LINGUAS'
may be set, prior to configuration, to limit the installed set.
`LINGUAS' should then contain a space separated list of two-letter
codes, stating which languages are allowed.

Using This Package
==================

   As a user, if your language has been installed for this package, you
only have to set the `LANG' environment variable to the appropriate
`LL_CC' combination.  Here `LL' is an ISO 639 two-letter language code,
and `CC' is an ISO 3166 two-letter country code.  For example, let's
suppose that you speak German and live in Germany.  At the shell
prompt, merely execute `setenv LANG de_DE' (in `csh'),
`export LANG; LANG=de_DE' (in `sh') or `export LANG=de_DE' (in `bash').
This can be done from your `.login' or `.profile' file, once and for
all.

   You might think that the country code specification is redundant.
But in fact, some languages have dialects in different countries.  For
example, `de_AT' is used for Austria, and `pt_BR' for Brazil.  The
country code serves to distinguish the dialects.

   Not all programs have translations for all languages.  By default, an
English message is shown in place of a nonexistent translation.  If you
understand other languages, you can set up a priority list of languages.
This is done through a different environment variable, called
`LANGUAGE'.  GNU `gettext' gives preference to `LANGUAGE' over `LANG'
for the purpose of message handling, but you still need to have `LANG'
set to the primary language; this is required by other parts of the
system libraries.  For example, some Swedish users who would rather
read translations in German than English for when Swedish is not
available, set `LANGUAGE' to `sv:de' while leaving `LANG' to `sv_SE'.

   In the `LANGUAGE' environment variable, but not in the `LANG'
environment variable, `LL_CC' combinations can be abbreviated as `LL'
to denote the language's main dialect.  For example, `de' is equivalent
to `de_DE' (German as spoken in Germany), and `pt' to `pt_PT'
(Portuguese as spoken in Portugal) in this context.

Translating Teams
=================

   For the Free Translation Project to be a success, we need interested
people who like their own language and write it well, and who are also
able to synergize with other translators speaking the same language.
Each translation team has its own mailing list.  The up-to-date list of
teams can be found at the Free Translation Project's homepage,
`http://www.iro.umontreal.ca/contrib/po/HTML/', in the "National teams"
area.

   If you'd like to volunteer to _work_ at translating messages, you
should become a member of the translating team for your own language.
The subscribing address is _not_ the same as the list itself, it has
`-request' appended.  For example, speakers of Swedish can send a
message to `<EMAIL>', having this message body:

     subscribe

   Keep in mind that team members are expected to participate
_actively_ in translations, or at solving translational difficulties,
rather than merely lurking around.  If your team does not exist yet and
you want to start one, or if you are unsure about what to do or how to
get started, please write to `<EMAIL>' to reach the
coordinator for all translator teams.

   The English team is special.  It works at improving and uniformizing
the terminology in use.  Proven linguistic skill are praised more than
programming skill, here.

Available Packages
==================

   Languages are not equally supported in all packages.  The following
matrix shows the current state of internationalization, as of May 2001.
The matrix shows, in regard of each package, for which languages PO
files have been submitted to translation coordination, with a
translation percentage of at least 50%.

     Ready PO files    bg cs da de el en eo es et fi fr gl hr id it
                     +----------------------------------------------+
     a2ps            |          []             []                   |
     bash            |          []       [] []       []             |
     bison           |          []          [] []    []             |
     clisp           |          []    []    []       []             |
     cpio            |       [] []          []       []             |
     diffutils       |       [] []       [] []       [] []    []    |
     enscript        |          []                   []             |
     error           |                               []             |
     fileutils       |    [] [] [] []       []       [] []          |
     findutils       |       [] []          [] []    [] []    [] [] |
     flex            |       []             []       []             |
     gawk            |                                              |
     gcal            |                                              |
     gcc             |       []                                     |
     gettext         |    [] [] [] []       []       [] []    [] [] |
     gnupg           |          []       []    []    []          [] |
     grep            |                   []    []                [] |
     hello           |       [] []       [] [] [] [] []       [] [] |
     id-utils        |       []                      []             |
     indent          |       [] []             []    [] []          |
     libc            |    [] [] [] []       []       [] []       [] |
     lilypond        |                                              |
     lynx            |    [] [] []                                  |
     m4              |    [] [] [] []                [] []    []    |
     make            |          []          []       [] []          |
     parted          |          []                      []          |
     ptx             |       [] []          [] []    [] []    []    |
     python          |                                              |
     recode          |       [] [] []    [] []       [] []       [] |
     sed             |    []    [] []    []    []    [] []    [] [] |
     sh-utils        |    [] [] [] []       [] []    [] []       [] |
     sharutils       |    [] [] [] []       []       [] []          |
     soundtracker    |                                              |
     sp              |                                              |
     tar             |    [] [] []          [] []    []       [] [] |
     texinfo         |    []    []       []          []             |
     textutils       |    [] [] [] []       []       [] []          |
     util-linux      |    []                                        |
     wdiff           |    [] []             [] []    [] []    []    |
     wget            |    [] [] [] []       [] []    [] []       [] |
                     +----------------------------------------------+
                       bg cs da de el en eo es et fi fr gl hr id it
                        0 14 21 27 10  1  8 20 13  1 28 17  0  9 11
     
                       ja ko lv nl no pl pt pt_BR ru sk sl sv tr zh
                     +----------------------------------------------+
     a2ps            |          []                []    []          |  5
     bash            |                                              |  4
     bison           | []       []                []                |  7
     clisp           |          []                                  |  5
     cpio            |    []    []    []     []   []                |  9
     diffutils       |                []          []       []       | 10
     enscript        |          []           []   []                |  5
     error           |                                              |  1
     fileutils       | [] []    []    []     []   [] [] [] []       | 16
     findutils       |    []    []    []     []   []       []       | 14
     flex            |    []                      []       []       |  6
     gawk            |                                              |  0
     gcal            |                                              |  0
     gcc             | []                                           |  2
     gettext         | [] []    []    []     []   []    [] [] [] [] | 19
     gnupg           | []             []                   []       |  8
     grep            |                                              |  3
     hello           | [] []    []    []     []   [] []    []       | 17
     id-utils        |          []                []       []       |  5
     indent          |    []    []    []          [] []    [] []    | 12
     libc            | [] []       [] []     []      []    []       | 15
     lilypond        |          []                                  |  1
     lynx            | []       []           []   []       []       |  8
     m4              | []       []    []          []       []       | 12
     make            | []       []    []     []   []                |  9
     parted          | []       []                []                |  5
     ptx             |          [] [] [] []       []       []       | 13
     python          |                                              |  0
     recode          |                            []    [] []       | 11
     sed             | []       []           []   [] [] [] []       | 16
     sh-utils        | []       [] [] []     []   [] [] [] []    [] | 19
     sharutils       | []       []                []       []       | 11
     soundtracker    |                                              |  0
     sp              |                                              |  0
     tar             | []       [] [] []     []   []    [] []       | 16
     texinfo         | []                         []                |  6
     textutils       | []       [] [] []     []   [] [] []          | 15
     util-linux      |                       []                     |  2
     wdiff           |          []    []          [] []    []       | 12
     wget            | []          []        []   [] [] [] []    [] | 17
                     +----------------------------------------------+
       29 <USER>        <GROUP> ko lv nl no pl pt pt_BR ru sk sl sv tr zh
       40 domains      18  8  0 23  6 16  1  15   26  9  9 20  2  3  336

   Some counters in the preceding matrix are higher than the number of
visible blocks let us expect.  This is because a few extra PO files are
used for implementing regional variants of languages, or language
dialects.

   For a PO file in the matrix above to be effective, the package to
which it applies should also have been internationalized and
distributed as such by its maintainer.  There might be an observable
lag between the mere existence a PO file and its wide availability in a
distribution.

   If May 2001 seems to be old, you may fetch a more recent copy of
this `ABOUT-NLS' file on most GNU archive sites.  The most up-to-date
matrix with full percentage details can be found at
`http://www.iro.umontreal.ca/contrib/po/HTML/matrix.html'.

Using `gettext' in new packages
===============================

   If you are writing a freely available program and want to
internationalize it you are welcome to use GNU `gettext' in your
package.  Of course the GNU Public License applies to your sources from
then if you include `gettext' directly in your distribution on but
since you are writing free software anyway this is no restriction.

   Once the sources are change appropriately and the setup can handle to
use of `gettext' the only thing missing are the translations.  The Free
Translation Project is also available for packages which are not
developed inside the GNU project.  Therefore the information given above
applies also for every other Free Software Project.  Contact
`<EMAIL>' to make the `.pot' files available to
the translation teams.

