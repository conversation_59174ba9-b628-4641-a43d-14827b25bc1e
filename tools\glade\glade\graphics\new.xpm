/* XPM */
static char * new_xpm[] = {
"24 24 89 1",
" 	c None",
".	c #000000",
"+	c #ADAD9C",
"@	c #959585",
"#	c #DCDCC5",
"$	c #DFDFC8",
"%	c #242424",
"&	c #A7A796",
"*	c #D7D7C1",
"=	c #D9D9C3",
"-	c #DCDCC4",
";	c #DDDDC7",
">	c #E1E1CA",
",	c #A6A694",
"'	c #D6D6BF",
")	c #D8D8C2",
"!	c #DBDBC4",
"~	c #DFDFC7",
"{	c #E3E3CB",
"]	c #B2B29F",
"^	c #7C7C7C",
"/	c #646464",
"(	c #D5D5BD",
"_	c #D5D5BF",
":	c #D7D7C0",
"<	c #DADAC3",
"[	c #DEDEC6",
"}	c #E1E1C9",
"|	c #E5E5CD",
"1	c #E9E9D1",
"2	c #343434",
"3	c #E7E7E7",
"4	c #373736",
"5	c #B8B8A6",
"6	c #D3D3BC",
"7	c #D9D9C2",
"8	c #DDDDC5",
"9	c #E0E0C8",
"0	c #E8E8D0",
"a	c #ECECD4",
"b	c #EEEED5",
"c	c #ECECEC",
"d	c #EDEDED",
"e	c #3D3D37",
"f	c #CECEB7",
"g	c #E6E6CE",
"h	c #EAEAD2",
"i	c #F1F1D8",
"j	c #A5A594",
"k	c #EEEEE5",
"l	c #3C3C3C",
"m	c #8F8F80",
"n	c #D0D0B9",
"o	c #E4E4CB",
"p	c #EAEAD1",
"q	c #ECECD3",
"r	c #EFEFD6",
"s	c #F2F2D9",
"t	c #F3F3DA",
"u	c #EEEEE6",
"v	c #505050",
"w	c #929282",
"x	c #D1D1B9",
"y	c #E7E7CF",
"z	c #EBEBD2",
"A	c #F4F4DB",
"B	c #F5F5DC",
"C	c #969686",
"D	c #D2D2BC",
"E	c #E0E0C9",
"F	c #E9E9D0",
"G	c #EDEDD4",
"H	c #F0F0D7",
"I	c #BEBEAB",
"J	c #797973",
"K	c #D8D8C1",
"L	c #DDDDC6",
"M	c #98988F",
"N	c #E4E4CC",
"O	c #818174",
"P	c #E2E2CA",
"Q	c #BEBEAE",
"R	c #4E4E4E",
"S	c #828274",
"T	c #EBEBD3",
"U	c #BBBBA8",
"V	c #CDCDB8",
"W	c #4D4D45",
"X	c #A6A696",
"                        ",
"                        ",
"             .          ",
"           ..+.         ",
"         ..@#$%         ",
"       ..&*=-;>.        ",
"     ..,'**)!~{].       ",
"    .^/'(_:<[}|12       ",
"    .3456*789|0ab.      ",
"    .cdef_!9g0hbij.     ",
"    .kklmn9opqrist.     ",
"    .uvwx-}yzbittAB.    ",
"    ..CD!E{FGHtAABBI.   ",
"    .JK7L>ypbitBBBBB).  ",
"    .M!#$NyzbitBBBBBO.  ",
"     .~9P|1qrsABBBB..   ",
"     .Q||0hGHtABB<.     ",
"      R01hGrstAsS.      ",
"      .TaGriss<.        ",
"       .rHis1j.         ",
"       .UstVW.          ",
"        .tX.            ",
"         ..             ",
"                        "};
