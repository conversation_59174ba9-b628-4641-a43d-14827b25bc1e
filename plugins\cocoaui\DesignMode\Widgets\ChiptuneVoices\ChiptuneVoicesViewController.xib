<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="21507" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES" customObjectInstantitationMethod="direct">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="21507"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="ChiptuneVoicesViewController">
            <connections>
                <outlet property="view" destination="Hz6-mo-xeY" id="0bl-1N-x8E"/>
                <outlet property="voice1" destination="Md3-ub-vzN" id="ecZ-4x-FmZ"/>
                <outlet property="voice2" destination="fU9-Wt-6GX" id="iCh-we-68I"/>
                <outlet property="voice3" destination="xyJ-7T-qD6" id="vjE-gS-VLX"/>
                <outlet property="voice4" destination="aWi-zD-QFh" id="gOv-S2-ObC"/>
                <outlet property="voice5" destination="Th7-HE-WUJ" id="071-r5-5bZ"/>
                <outlet property="voice6" destination="pNv-lJ-3cB" id="H3E-MN-U27"/>
                <outlet property="voice7" destination="Lf5-B2-g2h" id="wCC-G2-eSD"/>
                <outlet property="voice8" destination="nmU-Vd-EdA" id="OsD-8y-5Xw"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView id="Hz6-mo-xeY">
            <rect key="frame" x="0.0" y="0.0" width="429" height="298"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <stackView distribution="fillProportionally" orientation="horizontal" alignment="top" horizontalStackHuggingPriority="249.99998474121094" verticalStackHuggingPriority="249.99998474121094" detachesHiddenViews="YES" translatesAutoresizingMaskIntoConstraints="NO" id="TCH-86-L7z">
                    <rect key="frame" x="8" y="8" width="413" height="282"/>
                    <subviews>
                        <textField horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="5gV-te-U6w" userLabel="Voices:">
                            <rect key="frame" x="-2" y="266" width="102" height="16"/>
                            <textFieldCell key="cell" lineBreakMode="clipping" title="Voices:" id="99l-8F-laW">
                                <font key="font" metaFont="system"/>
                                <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                            </textFieldCell>
                        </textField>
                        <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Md3-ub-vzN">
                            <rect key="frame" x="104" y="265" width="36" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="rFa-QM-Xsi">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="74I-ay-c5d"/>
                            </connections>
                        </button>
                        <button verticalHuggingPriority="750" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="fU9-Wt-6GX">
                            <rect key="frame" x="144" y="265" width="35" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="5bz-kY-ezJ">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="LLM-Lo-wwe"/>
                            </connections>
                        </button>
                        <button verticalHuggingPriority="750" tag="2" translatesAutoresizingMaskIntoConstraints="NO" id="xyJ-7T-qD6">
                            <rect key="frame" x="183" y="265" width="35" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="Xej-ZI-c0W">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="Rji-nx-FCp"/>
                            </connections>
                        </button>
                        <button verticalHuggingPriority="750" tag="3" translatesAutoresizingMaskIntoConstraints="NO" id="aWi-zD-QFh">
                            <rect key="frame" x="222" y="265" width="36" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="V10-Ur-Ao9">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="mR1-5T-Vjy"/>
                            </connections>
                        </button>
                        <button verticalHuggingPriority="750" tag="4" translatesAutoresizingMaskIntoConstraints="NO" id="Th7-HE-WUJ">
                            <rect key="frame" x="262" y="265" width="35" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="9X6-jn-peV">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="kkQ-8z-O5g"/>
                            </connections>
                        </button>
                        <button verticalHuggingPriority="750" tag="5" translatesAutoresizingMaskIntoConstraints="NO" id="pNv-lJ-3cB">
                            <rect key="frame" x="301" y="265" width="35" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="UXz-aO-5Kt">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="Tu5-VK-6Tt"/>
                            </connections>
                        </button>
                        <button verticalHuggingPriority="750" tag="6" translatesAutoresizingMaskIntoConstraints="NO" id="Lf5-B2-g2h">
                            <rect key="frame" x="340" y="265" width="36" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="VsY-Sy-FuN">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="Jms-Ju-2vw"/>
                            </connections>
                        </button>
                        <button verticalHuggingPriority="750" tag="7" translatesAutoresizingMaskIntoConstraints="NO" id="nmU-Vd-EdA">
                            <rect key="frame" x="380" y="265" width="35" height="18"/>
                            <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="VHq-SJ-gbC">
                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                <font key="font" metaFont="system"/>
                            </buttonCell>
                            <connections>
                                <action selector="voiceToggled:" target="-2" id="74U-Tn-vDg"/>
                            </connections>
                        </button>
                    </subviews>
                    <constraints>
                        <constraint firstItem="Md3-ub-vzN" firstAttribute="centerY" secondItem="fU9-Wt-6GX" secondAttribute="centerY" id="23Q-wg-mGS"/>
                        <constraint firstItem="5gV-te-U6w" firstAttribute="centerY" secondItem="Md3-ub-vzN" secondAttribute="centerY" id="4FM-Jv-prW"/>
                        <constraint firstItem="Th7-HE-WUJ" firstAttribute="centerY" secondItem="pNv-lJ-3cB" secondAttribute="centerY" id="fKn-4K-phF"/>
                        <constraint firstItem="xyJ-7T-qD6" firstAttribute="centerY" secondItem="aWi-zD-QFh" secondAttribute="centerY" id="nOC-z7-2UA"/>
                        <constraint firstItem="fU9-Wt-6GX" firstAttribute="centerY" secondItem="xyJ-7T-qD6" secondAttribute="centerY" id="rgn-Wh-UlO"/>
                        <constraint firstItem="aWi-zD-QFh" firstAttribute="centerY" secondItem="Th7-HE-WUJ" secondAttribute="centerY" id="unZ-0a-1zy"/>
                        <constraint firstItem="Lf5-B2-g2h" firstAttribute="centerY" secondItem="nmU-Vd-EdA" secondAttribute="centerY" id="xOd-TI-qGV"/>
                        <constraint firstItem="pNv-lJ-3cB" firstAttribute="centerY" secondItem="Lf5-B2-g2h" secondAttribute="centerY" id="yYe-6z-Prb"/>
                    </constraints>
                    <visibilityPriorities>
                        <integer value="1000"/>
                        <integer value="1000"/>
                        <integer value="1000"/>
                        <integer value="1000"/>
                        <integer value="1000"/>
                        <integer value="1000"/>
                        <integer value="1000"/>
                        <integer value="1000"/>
                        <integer value="1000"/>
                    </visibilityPriorities>
                    <customSpacing>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                        <real value="3.4028234663852886e+38"/>
                    </customSpacing>
                </stackView>
            </subviews>
            <constraints>
                <constraint firstItem="TCH-86-L7z" firstAttribute="top" secondItem="Hz6-mo-xeY" secondAttribute="top" constant="8" id="GwH-pK-tKK"/>
                <constraint firstAttribute="bottom" secondItem="TCH-86-L7z" secondAttribute="bottom" constant="8" id="aR0-z6-sam"/>
                <constraint firstItem="TCH-86-L7z" firstAttribute="leading" secondItem="Hz6-mo-xeY" secondAttribute="leading" constant="8" id="jEN-Ph-68c"/>
                <constraint firstAttribute="trailing" secondItem="TCH-86-L7z" secondAttribute="trailing" constant="8" id="jEP-jP-WD6"/>
            </constraints>
            <point key="canvasLocation" x="-55.5" y="67"/>
        </customView>
    </objects>
</document>
