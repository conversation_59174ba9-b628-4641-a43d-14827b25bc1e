if HAVE_ADPLUG
adplugpath=@top_srcdir@/plugins/adplug

pkglib_LTLIBRARIES = adplug.la

adplug_la_CFLAGS = $(CFLAGS) -std=c99 -I$(adplugpath)/adplug -I$(adplugpath)/libbinio -I@top_srcdir@/include -fPIC
adplug_la_LDFLAGS = -module -avoid-version $(NOCPPLIB) -lm
adplug_la_CXXFLAGS = $(CXXFLAGS) -std=c++11 -Dstricmp=strcasecmp -DVERSION=\"trunk_a6706ba\" -I$(adplugpath)/adplug -I$(adplugpath)/libbinio -I@top_srcdir@/include

adplug_la_SOURCES = plugin.c\
                   adplug-db.cpp\
				   libbinio/binfile.h\
				   libbinio/binio.h\
				   libbinio/binstr.h\
				   libbinio/binwrap.h\
				   libbinio/binfile.cpp\
				   libbinio/binio.cpp\
				   libbinio/binstr.cpp\
				   libbinio/binwrap.cpp\
				   adplug/adlibemu.c\
				   adplug/debug.c\
				   adplug/depack.c\
				   adplug/fmopl.c\
				   adplug/nukedopl.c\
				   adplug/unlzh.c\
				   adplug/unlzss.c\
				   adplug/unlzw.c\
				   adplug/a2m-v2.cpp\
				   adplug/a2m.cpp\
				   adplug/adl.cpp\
				   adplug/adplug.cpp\
				   adplug/adtrack.cpp\
				   adplug/amd.cpp\
				   adplug/analopl.cpp\
				   adplug/bam.cpp\
				   adplug/bmf.cpp\
				   adplug/cff.cpp\
				   adplug/cmf.cpp\
				   adplug/cmfmcsop.cpp\
				   adplug/coktel.cpp\
				   adplug/composer.cpp\
				   adplug/d00.cpp\
				   adplug/database.cpp\
				   adplug/dfm.cpp\
				   adplug/diskopl.cpp\
				   adplug/dmo.cpp\
				   adplug/dro.cpp\
				   adplug/dro2.cpp\
				   adplug/dtm.cpp\
				   adplug/emuopl.cpp\
				   adplug/flash.cpp\
				   adplug/fmc.cpp\
				   adplug/fprovide.cpp\
				   adplug/got.cpp\
				   adplug/herad.cpp\
				   adplug/hsc.cpp\
				   adplug/hsp.cpp\
				   adplug/hybrid.cpp\
				   adplug/hyp.cpp\
				   adplug/imf.cpp\
				   adplug/jbm.cpp\
				   adplug/kemuopl.cpp\
				   adplug/ksm.cpp\
				   adplug/lds.cpp\
				   adplug/mad.cpp\
				   adplug/mdi.cpp\
				   adplug/mid.cpp\
				   adplug/mkj.cpp\
				   adplug/msc.cpp\
				   adplug/mtk.cpp\
				   adplug/mtr.cpp\
				   adplug/mus.cpp\
				   adplug/nemuopl.cpp\
				   adplug/pis.cpp\
				   adplug/player.cpp\
				   adplug/players.cpp\
				   adplug/protrack.cpp\
				   adplug/psi.cpp\
				   adplug/rad2.cpp\
				   adplug/rat.cpp\
				   adplug/raw.cpp\
				   adplug/realopl.cpp\
				   adplug/rix.cpp\
				   adplug/rol.cpp\
				   adplug/s3m.cpp\
				   adplug/sa2.cpp\
				   adplug/sixdepack.cpp\
				   adplug/sng.cpp\
				   adplug/sop.cpp\
				   adplug/surroundopl.cpp\
				   adplug/temuopl.cpp\
				   adplug/u6m.cpp\
				   adplug/vgm.cpp\
				   adplug/woodyopl.cpp\
				   adplug/xad.cpp\
				   adplug/xsm.cpp\
				   adplug/a2m-v2.h\
				   adplug/a2m.h\
				   adplug/adl.h\
				   adplug/adlibemu.h\
				   adplug/adplug.h\
				   adplug/adtrack.h\
				   adplug/amd.h\
				   adplug/analopl.h\
				   adplug/bam.h\
				   adplug/bmf.h\
				   adplug/cff.h\
				   adplug/cmf.h\
				   adplug/cmfmcsop.h\
				   adplug/coktel.h\
				   adplug/composer.h\
				   adplug/d00.h\
				   adplug/database.h\
				   adplug/debug.h\
				   adplug/depack.h\
				   adplug/dfm.h\
				   adplug/diskopl.h\
				   adplug/dmo.h\
				   adplug/dro.h\
				   adplug/dro2.h\
				   adplug/dtm.h\
				   adplug/emuopl.h\
				   adplug/flash.h\
				   adplug/fmc.h\
				   adplug/fmopl.h\
				   adplug/fprovide.h\
				   adplug/got.h\
				   adplug/herad.h\
				   adplug/hsc.h\
				   adplug/hsp.h\
				   adplug/hybrid.h\
				   adplug/hyp.h\
				   adplug/imf.h\
				   adplug/jbm.h\
				   adplug/kemuopl.h\
				   adplug/ksm.h\
				   adplug/lds.h\
				   adplug/load_helper.h\
				   adplug/mad.h\
				   adplug/mdi.h\
				   adplug/mid.h\
				   adplug/mididata.h\
				   adplug/mkj.h\
				   adplug/msc.h\
				   adplug/mtk.h\
				   adplug/mtr.h\
				   adplug/mus.h\
				   adplug/nemuopl.h\
				   adplug/nukedopl.h\
				   adplug/opl.h\
				   adplug/pch.h\
				   adplug/pis.h\
				   adplug/player.h\
				   adplug/players.h\
				   adplug/protrack.h\
				   adplug/psi.h\
				   adplug/rad2.h\
				   adplug/rat.h\
				   adplug/raw.h\
				   adplug/realopl.h\
				   adplug/rix.h\
				   adplug/rol.h\
				   adplug/s3m.h\
				   adplug/sa2.h\
				   adplug/silentopl.h\
				   adplug/sixdepack.h\
				   adplug/sng.h\
				   adplug/sop.h\
				   adplug/strnlen.h\
				   adplug/surroundopl.h\
				   adplug/temuopl.h\
				   adplug/u6m.h\
				   adplug/unlzh.h\
				   adplug/unlzss.h\
				   adplug/unlzw.h\
				   adplug/version.h\
				   adplug/vgm.h\
				   adplug/wemuopl.h\
				   adplug/woodyopl.h\
				   adplug/xad.h\
				   adplug/xsm.h

endif
